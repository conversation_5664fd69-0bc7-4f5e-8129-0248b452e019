.header {
  padding: 24px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);

  .name {
    color: $gray-800;
  }

  .sub {
    color: $gray-500;
  }
}

.content {
  padding: 24px 0;
  .overviewArea {
    background: #f8fafc;
    padding: 16px;
    border-radius: 8px;
    flex: 1;
    .subHeader {
      color: $gray-800;
    }
    .card {
      display: grid;
      grid-template-columns: 120px 1fr;
      row-gap: 8px;
      .row {
        display: contents;
      }
    }
    .label {
      color: $gray-500;
    }

    .value {
      color: $gray-800;
    }
  }
}

.segment {
  display: inline-flex;

  > :not(:first-child) {
    margin-left: -1px;
  }

  .item {
    :global {
      .ant-btn {
        border-radius: 0;
      }
    }

    &:first-child {
      :global {
        .ant-btn {
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
        }
      }
    }

    &:last-child {
      :global {
        .ant-btn {
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
    }
  }
}

.workable {
  color: $success-700;
  font-weight: 600;
  font-size: 14px;
}

.unworkable {
  color: #eb5146;
  font-weight: 600;
  font-size: 14px;
}

.preWrap {
  white-space: pre-wrap;
}

:global(.ant-table-cell) {
  &:has(.unworkable) {
    background-color: $gray-200 !important;
  }
}

.modalBodyScroll {
  scrollbar-width: thin;
  scrollbar-color: $gray-300 transparent;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: $gray-300;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}
