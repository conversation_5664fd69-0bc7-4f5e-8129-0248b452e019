{"name": "medix-sync-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview", "clean": "rm -rf node_modules/.vite dist"}, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "antd": "^5.24.5", "async-mutex": "^0.5.0", "axios": "^1.8.4", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "fuse.js": "^7.1.0", "history": "^5.3.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "lodash.pick": "^4.4.0", "pako": "^2.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-number-format": "^5.4.3", "react-redux": "^9.2.0", "react-router-dom": "^7.4.0", "redux-persist": "^6.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-svgr": "^4.3.0", "wanakana": "^5.3.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/lodash": "^4.17.16", "@types/lodash.isequal": "^4.5.8", "@types/lodash.pick": "^4.4.9", "@types/node": "^22.13.13", "@types/pako": "^2.0.3", "@types/react": "^18.0.10", "@types/react-dom": "^18.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "sass": "^1.86.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,md}": "prettier --write"}}