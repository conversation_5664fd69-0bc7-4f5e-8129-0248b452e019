import { Outlet, useParams, useLocation, Navigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { selectClinic } from '@/store/auth';

export default function ClinicInit() {
  const { clinicCd } = useParams<{ clinicCd: string }>();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const { clinics, currentClinicCd } = useAppSelector(s => s.auth);

  const isErrorPage = ['/404-clinic-not-found', '/403-clinic-unauthorized'].some(suffix =>
    location.pathname.endsWith(suffix)
  );
  if (isErrorPage) {
    return <Outlet />;
  }

  if (clinicCd && clinicCd !== currentClinicCd && clinicCd !== 'login') {
    dispatch(selectClinic(clinicCd));
  }

  if (!clinicCd || !clinics.some(c => c.clinic_cd === clinicCd)) {
    return <Navigate to={`/${clinicCd}/404-clinic-not-found`} replace />;
  }

  const basePath = `/${clinicCd}`;
  if (location.pathname === basePath || location.pathname === `${basePath}/`) {
    return <Navigate to={`/${clinicCd}/dashboard`} replace />;
  }

  return <Outlet />;
}
