.visitDetailForm {
  .main-content-info {
    flex: 1;
  }
  .contentSection {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .patient_info {
    :global(.ant-form-item .ant-form-item-row) {
      display: grid;
      grid-template-columns: 1fr 4fr;

      :global(.ant-form-item-label) {
        display: flex;
        justify-items: center;

        padding: none;
      }
    }
  }
  .treatment_input {
    flex: 1;
    color: $gray-500
  }
  .medical_assistance_cost_digit {
    margin-top: 30px;
    color: $gray-500;
  }
  .course_section {
    overflow-x: auto;
  }
  .jyosai_flag_checkbox {
    :global(.ant-checkbox-label) {
      color: $gray-700 !important;
    }
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: $gray-300;
}

@media screen and (max-width: 800px) {
  .visitDetailForm {
    .main-content-info {
      flex: 1;
    }
    .contentSection {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .patient_info {
      :global(.ant-form-item .ant-form-item-row) {
        display: grid;
        grid-template-columns: 1fr 4fr;

        :global(.ant-form-item-label) {
          display: flex;
          justify-items: center;

          padding: none;
        }
      }
    }
    .treatment_input {
      flex: 1;
    }
    .course_section {
      overflow-x: auto;
    }
  }
}

.mondaiSectionTable {
  margin-top: 8px;
  border-radius: 8px;
  overflow: auto;
  background-color: var(--white);
  height: 100%;
  .header {
    border-radius: 8px 8px 0 0;
    background-color: var(--white);
    padding: 12px;
    border-bottom: 1px solid $gray-200;
    overflow: auto !important;
    .title {
      color: $gray-800;
      font-weight: 700;
      text-wrap: nowrap;
    }
  }
  .mainTable {
    :global(.ant-table-cell) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
    }
  }
  .mainTable {
    :global(.ant-table-row) {
      text-align: center;
    }

    :global(.ant-table-expanded-row-fixed) {
      height: auto !important;
      background: var(--white) !important;
    }
    :global(.ant-table-expanded-row) {
      :global(> .ant-table-cell) {
        height: auto !important;
        background: var(--white) !important;
        padding-left: unset;
        padding-right: unset;
      }
    }

    :global(.ant-table-cell .ant-table-row-expand-icon-cell) {
      width: 50px !important;
    }
  }
}

.mondaiType {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  > p:nth-child(2) {
    padding-left: 16px;
    border-left: 1px solid var(--gray-200);
  }
}
.mondaiStatus {
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
}
.active {
  width: 72px;
  height: 23px;
  border-radius: 16px;
  color: $success-700;
  background-color: #eef4e2;
  padding: 3px 16px;
}
.inactive {
  width: 72px;
  height: 23px;
  border-radius: 16px;
  color: $error-600;
  padding: 3px 16px;
  background-color: $error-100;
}
.required_icon {
  margin-left: 2px;
  color: $error-600 !important;
}

.visitDetailMondaiListTable {
  min-width: 836px;
  width: 100%;
}
