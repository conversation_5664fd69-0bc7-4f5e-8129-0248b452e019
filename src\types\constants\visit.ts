import { EB<PERSON>den, ECourse, EDetailInsurance, EKohi, EPaymentType } from '../enum';

export const VISIT_STATUS = {
  BOOKING_CANCEL: 1,
  BOOKING: 2,
  NOT_CHECKED_IN: 3,
  RECEIPT: 4,
  TREATMENT: 5,
  TEMP_KARUTE: 6,
  CONFIRM_KARUTE: 7,
} as const;

export const VISIT_TABLE_ACTION = {
  REGISTER_VISIT: 'registerVisit',
  PATIENT_VIEW_AND_EDIT: 'patientViewAndEdit',
  ADD_KARUTE: 'addKarute',
  VISIT_VIEW_AND_EDIT: 'visitViewAndEdit',
  VISIT_DETAIL_EDIT_AND_DELETE: 'visitDetailEditAndDelete',
};
export const JIHI_KEY = 'jihi';
export const JIHI_COURSE_NAME = '自費';

export const DEFAULT_VISIT_VIA = '1';
export const courseMap = {
  [ECourse.SELF_PAID]: JIHI_COURSE_NAME,
  [ECourse.WORK_RELATED]: '労災',
  [ECourse.HOKEN]: '保険',
  [ECourse.AUTO_INSURANCE]: '自賠責',
};
export enum EVisitType {
  FIRST_TIME = 1,
  OLD = 2,
}

export const VisitTypeOption = [
  {
    value: EVisitType.FIRST_TIME,
    label: '初診',
  },
  {
    value: EVisitType.OLD,
    label: '再診',
  },
];

export const paymentTypesMap = {
  [EPaymentType.INSURED_JUDO]: '保険柔整',
  [EPaymentType.INSURED_ACUPUNCTURE]: '保険鍼灸',
  [EPaymentType.INSURED_MASSAGE]: '保険マッサージ',
  [EPaymentType.SELF_PAY]: '自費',
};

export const burdenMap = {
  [EBurden.None]: 'なし',
  [EBurden.PreschoolUnder6_20Percent]: '6歳以下の未就学児『2割負担』',
  [EBurden.Certificate_100Percent]: '資格証明『10割負担』',
  [EBurden.Others_0Percent]: 'その他『0割』',
  [EBurden.Elderly_10Percent]: '前期高齢１割',
  [EBurden.Elderly_20Percent]: '前期高齢２割',
  [EBurden.Elderly_30Percent]: '前期高齢３割',
};

export const detailInsuranceMap = {
  [EDetailInsurance.None]: 'なし',
  [EDetailInsurance.General]: '一般',
  [EDetailInsurance.LateElderly]: '後期高齢',
  [EDetailInsurance.LivelihoodProtection]: '生活保護',
  [EDetailInsurance.OthersSelfPay]: 'その他(自費)',
  [EDetailInsurance.CompulsoryAutoInsurance]: '自賠責',
  [EDetailInsurance.WorkersCompensation]: '労災',
};

export const kohiMap = {
  [EKohi.None]: 'なし',
  [EKohi.Welfare]: '福祉',
  [EKohi.Disability]: '障害',
  [EKohi.Child]: '子ども',
  [EKohi.Infant]: '乳幼児',
  [EKohi.SingleParent]: '一人親',
  [EKohi.AtomicBomb]: '原爆',
  [EKohi.Minamata]: '水俣',
};
