import Image from '@/components/common/core/Image';
import Footer from '@/components/common/layout/Footer';
import { Flex } from 'antd';
import React from 'react';

export const AuthNotFoundPage: React.FC = () => (
  <div style={{ padding: '2rem', textAlign: 'center' }}>
    <h2>404 – Page Not Found</h2>
  </div>
);

export const ClinicNotFoundPage: React.FC = () => {
  return (
    <Flex justify="space-between" vertical style={{ flex: 1 }}>
      <Flex justify="center" align="center" style={{ flex: 1 }}>
        <Image
          name="notFound"
          style={{
            width: '100%',
            height: 'calc(100vh - 82px - 100px)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      </Flex>
      <Footer />
    </Flex>
  );
};

export const ClinicUnauthorizedPage: React.FC = () => (
  <div style={{ padding: '2rem', textAlign: 'center' }}>
    <h2>403 – Clinic Unauthorized</h2>
  </div>
);

export const PageNotAuthorizedPage: React.FC = () => (
  <div style={{ padding: '2rem', textAlign: 'center' }}>
    <h2>403 – Page Not Authorized</h2>
  </div>
);

export const DashboardNotFoundPage: React.FC = () => (
  <Flex justify="space-between" vertical style={{ flex: 1 }}>
    <Flex justify="center" align="center" style={{ flex: 1 }}>
      <Image
        name="notFound"
        style={{
          width: '100%',
          height: 'calc(100vh - 82px - 100px)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      />
    </Flex>
    <Footer />
  </Flex>
);
