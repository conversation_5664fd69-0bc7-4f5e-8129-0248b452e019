{
  /* Example: height is required for Vertical split
 <AppSplit
  sizes={horizontalSizes}
  direction="horizontal"
  onDragEnd={handleHorizontalDragEnd}
  className={styles.verticalSplit}
  style({
    height: '100vh'
  })
>
  <Left/>
  <Right/>
</AppSplit>

<AppSplit
  className={styles.horizalSplit}
  direction="vertical"
  sizes={verticalSizes}
  onDragEnd={handleVerticalDragEnd}
  style({
    height: '100vh'
  })
>
  <Above/>
  <Beneath/>
</AppSplit> 
*/
}
import Split, { SplitProps } from 'react-split';
import styles from './AppSplit.module.scss';
import clsx from 'clsx';

interface AppSplitProps extends SplitProps {
  direction?: 'horizontal' | 'vertical';
  hasBorderSplit?: boolean;
  isHiddenGutter?: boolean;
}

const GUTTER_SIZE = 4;
const SNAP_OFFSET = 0; // avoid snap to border
const DRAG_INTERVAL = 1; // smooth drag effect

export function AppSplit({
  sizes = [50, 50],
  minSize = 50,
  children,
  onDragEnd,
  className = '',
  gutterSize = GUTTER_SIZE,
  snapOffset = SNAP_OFFSET,
  dragInterval = DRAG_INTERVAL,
  direction = 'horizontal',
  hasBorderSplit = false,
  isHiddenGutter = false,
  ...rest
}: AppSplitProps) {
  const isHorizontal = direction === 'horizontal';

  return (
    <Split
      sizes={sizes}
      minSize={minSize}
      expandToMin={false}
      gutterSize={gutterSize}
      gutterAlign="center"
      snapOffset={snapOffset}
      dragInterval={dragInterval}
      direction={direction}
      cursor={isHorizontal ? 'col-resize' : 'row-resize'}
      onDragEnd={onDragEnd}
      className={clsx({
        [styles.splitHorizontal]: isHorizontal,
        [styles.splitVertical]: !isHorizontal,
        [styles.hasBorder]: hasBorderSplit,
        [styles.hiddenGutter]: isHiddenGutter,
        [className]: true,
      })}
      style={
        {
          '--gutter-size': `${gutterSize}px`,
        } as React.CSSProperties
      }
      {...rest}
    >
      {children}
    </Split>
  );
}
