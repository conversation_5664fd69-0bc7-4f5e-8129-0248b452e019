import Button from '@/components/common/core/Button';
import { useRouterPath } from '@/hooks/useRouterPath';
import { routerPaths } from '@/types/constants/routerPath';
import { ESubmitType, ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import { useNavigate } from 'react-router-dom';

interface Props {
  formRef: any;
  onClick?: (type: ESubmitType) => void;
}

export default function PatientCreateAction({ formRef, onClick }: Props) {
  const { getPath } = useRouterPath();
  const patientListPath = getPath(routerPaths.patientManagement.patientList);
  const navigate = useNavigate();

  const handleClickSubmitForm = (type: ESubmitType) => {
    onClick?.(type);
    formRef.current?.submit();
  };

  if (!formRef) {
    return <></>;
  }

  return (
    <Flex gap={8}>
      <Button customType={ButtonType.SECONDARY_COLOR} onClick={() => navigate(patientListPath)}>
        キャンセル
      </Button>
      <Button onClick={() => handleClickSubmitForm(ESubmitType.SAVE)}>登録して完了</Button>
      <Button onClick={() => handleClickSubmitForm(ESubmitType.SAVE_AND_RECEIVE)}>
        登録して受付
      </Button>
    </Flex>
  );
}
