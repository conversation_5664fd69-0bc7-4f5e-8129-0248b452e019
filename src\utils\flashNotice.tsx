import clsx from 'clsx';
import { NotificationInstance } from 'antd/es/notification/interface';
import styles from '@/styles/flashNotice/FlashNotice.module.scss';
import Icon from '@/components/common/core/Icon';

export type FlashType = 'success' | 'error';

export interface FlashNoticeOptions {
  type?: FlashType;
  message: string;
  duration?: number;
}

let flashNoticeApi: NotificationInstance | null = null;

export const registerFlashNoticeApi = (api: NotificationInstance) => {
  flashNoticeApi = api;
};

export const showFlashNotice = ({
  type = 'success',
  message,
  duration = 3,
}: FlashNoticeOptions) => {
  if (!flashNoticeApi) {
    console.error('Flash notice is not registered!');
    return;
  }
  const key = `flash_${Date.now()}`;
  flashNoticeApi.open({
    key,
    message: null,
    description: (
      <div className={clsx(styles.customNotice, styles[type])}>
        <div className={styles.messageWrapper}>
          <Icon name={type === 'success' ? 'check' : 'error'} width={20} height={20} />
          <span className={styles.text}>{message}</span>
        </div>
        <div onClick={() => flashNoticeApi!.destroy(key)} className={styles.closeIcon}>
          <Icon name="closeFilled" width={20} height={20} />
        </div>
      </div>
    ),
    duration,
    className: clsx(styles.customNoticeWrapper, styles[type]),
    placement: 'topRight',
    closeIcon: null,
  });
};
