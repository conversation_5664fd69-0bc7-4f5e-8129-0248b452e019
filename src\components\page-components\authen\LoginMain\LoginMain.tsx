import Button from '@/components/common/core/Button';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppInput } from '@/components/common/core/Input';
import { login, selectClinic, setClinics } from '@/store/auth';
import { useLoginMutation } from '@/store/auth/api';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import styles from './LoginMain.module.scss';
import {
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
} from '@/types/constants/error';
import ForgotPassword from './ForgotPassword';
import { Form } from 'antd';
import { AuthBox } from '../AuthBox';
import { useLazyGetClinicsQuery } from '@/store/clinic/api';
import { ROLE } from '@/types/enum';
import { Clinic } from '@/types/interface/User';
import { prefetchSharedData } from '@/store/shared/thunk';
import { useAppDispatch } from '@/config/redux/store';
import { STATUS_CODES } from '@/types/constants';

const FORM_FIELDS = {
  storeId: {
    label: '治療院ID',
    type: 'text',
  },
  userId: {
    label: 'ユーザーID',
    type: 'text',
  },
  password: {
    label: 'パスワード',
    type: 'password',
  },
} as const;

type FORM_STATE = 'login' | 'forgotPassword';

export default function LoginMain() {
  const [searchParams, setSearchParams] = useSearchParams();
  // use query string to also navigate from /reset-password?state=forgotPassword to /login
  const formState = (searchParams.get('state') as FORM_STATE) || 'login';
  const [form] = Form.useForm<{ storeId: string; userId: string; password: string }>();
  const dispatch = useDispatch();
  const thunkDispatch = useAppDispatch();
  const [handleLogin, { isLoading }] = useLoginMutation();
  const [errorMessage, setErrorMessage] = useState('');
  const firstInputRef = useRef<import('antd').InputRef>(null);
  const [fetchClinics] = useLazyGetClinicsQuery();

  const handleSubmit = async (values: { storeId: string; userId: string; password: string }) => {
    try {
      const res = await handleLogin({
        clinic_cd: values.storeId,
        username: values.userId,
        password: values.password,
      }).unwrap();
      if (res.status === STATUS_CODES.OK && res.data) {
        dispatch(login(res.data));
        window.dispatchEvent(new Event('auth-change'));
        dispatch(selectClinic(values.storeId));
        await dispatch(selectClinic(values.storeId.trim()));

        let clinics: Clinic[] | null = null;
        if (res.data.user.role === ROLE.STAFF) {
          const clinic = res.data?.user?.clinic;
          if (clinic) {
            clinics = [clinic];
          }
        } else {
          clinics = (await fetchClinics().unwrap()).data || [];
        }
        dispatch(setClinics({ clinics: clinics || [] }));
        await thunkDispatch(prefetchSharedData());
      } else {
        setErrorMessage(res.message);
      }
    } catch (error) {
      setErrorMessage(
        (error as { data: { message: string } }).data.message || NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL
      );
    }
  };

  useEffect(() => {
    firstInputRef.current?.focus();
  }, []);

  const setFormStateAndUrl = (state: FORM_STATE) => {
    setSearchParams({ state: state });
  };

  if (formState === 'forgotPassword') {
    return <ForgotPassword returnToLogin={() => setFormStateAndUrl('login')} />;
  }

  return (
    <>
      <AuthBox
        title={<span className="fs30-medium">ログイン</span>}
        footerContent={
          <a
            className={clsx(styles.loginFooter, 'fs14-medium')}
            onClick={() => setFormStateAndUrl('forgotPassword')}
          >
            パスワードをお忘れの方はこちら
          </a>
        }
      >
        <Form
          form={form}
          className={styles.loginInput}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          {Object.entries(FORM_FIELDS).map(([name, field], index) => (
            <AppFormItem
              key={name}
              name={name}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: ERROR_COMMON_MESSAGE.REQUIRED(field.label),
                },
              ]}
              label={field.label}
            >
              <AppInput
                type={field.type}
                placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                width="100%"
                disabled={isLoading}
                ref={index === 0 ? firstInputRef : undefined}
              />
            </AppFormItem>
          ))}
          <AppFormItem>
            <Button
              disabled={isLoading}
              loading={isLoading}
              type="primary"
              customSize="2xl"
              htmlType="submit"
              style={{ width: '100%', marginTop: '16px' }}
            >
              ログイン
            </Button>
          </AppFormItem>
          {errorMessage && (
            <div className={clsx(styles.loginFail, 'fs14-regular')}>{errorMessage}</div>
          )}
        </Form>
      </AuthBox>
    </>
  );
}
