import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo } from 'react';
import { Dayjs } from 'dayjs';
import { DATE_FORMATS, PAGINATION_THRESHOLD } from '@/types/constants';
import { Order } from '@/store/patient/type';
import { EKaruteStatus, ESort } from '@/types/enum';
import { useGetKaruteListQuery } from '@/store/karute/api';
import { ListResponse } from '@/types/interface';
import { KaruteItem } from '@/store/karute/type';

export interface KaruteSearchParams {
  prefix_search?: string | null;
  aimai_search?: string | null;
  status?: EKaruteStatus | null;
  first_submit?: [Dayjs | null, Dayjs | null];
  latest_treatment?: [Dayjs | null, Dayjs | null];
}

export interface KaruteQueryParams {
  limit: number;
  page: number;
  order_by?: KaruteSortBy;
  order_type?: Order;
  prefix_search?: string;
  aimai_search?: string;
  status?: number;
  first_submit_from?: string;
  first_submit_to?: string;
  latest_treatment_from?: string;
  latest_treatment_to?: string;
}

export type KaruteSortBy =
  | 'patient_cd'
  | 'patient_name'
  | 'patient_kana'
  | 'first_submit_date'
  | 'latest_treatment_date'
  | 'status'
  | 'mondai_counts';

interface ListKaruteContextType {
  queryParams: KaruteQueryParams;

  handleSearch: (values: KaruteSearchParams) => void;
  handlePageChange: (page: number) => void;
  handleSortChange: (sortData: { order_by: string; order_type: ESort }[]) => void;
  handleClear: () => void;

  karutes?: ListResponse<KaruteItem> | null;
  isFetching: boolean;

  formValues: KaruteSearchParams;
  setFormValues: React.Dispatch<React.SetStateAction<KaruteSearchParams>>;
}

const ListKaruteContext = createContext<ListKaruteContextType | undefined>(undefined);

const defaultFormValues: KaruteSearchParams = {
  prefix_search: '',
  aimai_search: '',
};

interface ListKaruteProviderProps {
  children: ReactNode;
}

export const ListKaruteProvider: React.FC<ListKaruteProviderProps> = ({ children }) => {
  const [formValues, setFormValues] = useState<KaruteSearchParams>(defaultFormValues);
  const [page, setPage] = useState<number>(1);
  const [sortBy, setSortBy] = useState<KaruteSortBy | undefined>(undefined);
  const [orderBy, setOrderBy] = useState<Order | undefined>(undefined);
  const [shouldFetch, setShouldFetch] = useState<boolean>(true);

  const queryParams = useMemo<KaruteQueryParams>(() => {
    const params: KaruteQueryParams = {
      limit: PAGINATION_THRESHOLD,
      page: page,
      prefix_search: formValues.prefix_search!,
      aimai_search: formValues.aimai_search!,
      status: formValues.status!,
    };

    if (formValues.first_submit && formValues.first_submit[0] && formValues.first_submit[1]) {
      params.first_submit_from = formValues.first_submit[0].format(DATE_FORMATS.DATE_HOUR_MINUTE);
      params.first_submit_to = formValues.first_submit[1].format(DATE_FORMATS.DATE_HOUR_MINUTE);
    }

    if (
      formValues.latest_treatment &&
      formValues.latest_treatment[0] &&
      formValues.latest_treatment[1]
    ) {
      params.latest_treatment_from = formValues.latest_treatment[0].format(
        DATE_FORMATS.DATE_HOUR_MINUTE
      );
      params.latest_treatment_to = formValues.latest_treatment[1].format(
        DATE_FORMATS.DATE_HOUR_MINUTE
      );
    }

    if (sortBy && orderBy) {
      params.order_by = sortBy;
      params.order_type = orderBy;
    }

    return params;
  }, [formValues, page, sortBy, orderBy]);

  const handleSearch = useCallback((values: KaruteSearchParams) => {
    setFormValues(values);
    setPage(1);
    setShouldFetch(true);
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleSortChange = useCallback((sortData: { order_by: string; order_type: ESort }[]) => {
    const newSort = sortData.length > 0 ? sortData[0] : null;

    if (!newSort || !newSort.order_type) {
      setSortBy(undefined);
      setOrderBy(undefined);
    } else {
      setSortBy(newSort.order_by as KaruteSortBy);
      setOrderBy(newSort.order_type);
    }
  }, []);

  const handleClear = useCallback(() => {
    setShouldFetch(false);
    setFormValues(defaultFormValues);
    setPage(1);
    setSortBy(undefined);
    setOrderBy(undefined);
  }, []);

  const { data: karutes, isFetching: isFetchingKaruteList } = useGetKaruteListQuery(queryParams, {
    skip: !shouldFetch,
  });

  const contextValue = useMemo(
    () => ({
      queryParams,
      handleSearch,
      handlePageChange,
      handleSortChange,
      handleClear,
      karutes: karutes?.data,
      isFetching: isFetchingKaruteList,
      formValues,
      setFormValues,
    }),
    [
      queryParams,
      handleSearch,
      handlePageChange,
      handleSortChange,
      handleClear,
      formValues,
      isFetchingKaruteList,
      karutes,
    ]
  );

  return <ListKaruteContext.Provider value={contextValue}>{children}</ListKaruteContext.Provider>;
};

// Custom hook to use the context
export const useListKarute = (): ListKaruteContextType => {
  const context = useContext(ListKaruteContext);
  if (context === undefined) {
    throw new Error('useListKarute must be used within a ListKaruteProvider');
  }
  return context;
};
