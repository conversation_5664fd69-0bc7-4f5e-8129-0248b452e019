import axios, { AxiosRequestConfig } from 'axios';
import { showFlashNotice } from '@/utils/flashNotice';
import { LOCAL_STORAGE_KEY } from '@/types/constants';
import { store, type RootState } from '@/config/redux/store';

const baseURL = import.meta.env.VITE_API_BASE_URL;

function getClinicCdFromStore(): string | null {
  const state: RootState = store.getState();
  return state.auth.currentClinicCd;
}

const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  (config: any) => {
    const token = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    const clinicCd = getClinicCdFromStore();
    if (clinicCd) {
      config.headers = {
        ...config.headers,
        'clinic-cd': clinicCd,
      };
    }

    config.headers = {
      ...config.headers,
      'cache-control': 'no-cache',
    };

    return config;
  },
  error => Promise.reject(error)
);

api.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (!window.navigator.onLine) {
      window.location.reload();
      return Promise.reject(error);
    }

    if (error.response) {
      if (error.response.status === 500) {
        showFlashNotice({
          type: 'error',
          message: '予期せぬエラーが発生しました。',
        });
      }
    } else {
      showFlashNotice({
        type: 'error',
        message: '予期せぬエラーが発生しました。',
      });
    }

    return Promise.reject(error);
  }
);

export default api;
