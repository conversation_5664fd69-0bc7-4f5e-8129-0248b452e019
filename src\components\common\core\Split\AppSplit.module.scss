.splitHorizontal {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
}

.splitVertical {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.hiddenGutter > :global(.gutter) {
  display: none;
}

:global {
  .gutter {
    background-repeat: no-repeat;
    background-position: 50%;
    position: relative;
    background-color: transparent; 

    &:hover::after {
      background-color: $gray-300; 
    }
  }
  // HANDLE GUTTER
  .gutter.gutter-horizontal {
    cursor: col-resize;

    &::after {
      content: '';
      position: absolute;
      top: 10%;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      height: 80%;
      border-radius: 3px;
      transition: background-color 0.2s ease; 
    }
  }

  .gutter.gutter-vertical {
    cursor: row-resize;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 10%;
      transform: translateY(-50%);
      width: 80%;
      height: 100%;
      border-radius: 3px;
      transition: background-color 0.2s ease; 
    }
  }
}