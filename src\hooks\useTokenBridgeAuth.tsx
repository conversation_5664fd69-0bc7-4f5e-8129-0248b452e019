import { useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { setToken, clearToken } from '@/utils/auth';
import { routerPaths } from '@/types/constants';
import { patternToRegex } from '@/utils/helper';

interface Options {
  allowedOrigins: string[];
  timeout?: number;
}
interface Payload {
  token: string | null;
  from: 'karute' | 'booking';
  path: 'dashboard' | 'create' | `edit/${string}` | `dashboard/user/${string}`;
}

export default function useTokenBridgeAuth({ allowedOrigins, timeout = 3000 }: Options) {
  const navigate = useNavigate();

  const allowedOriginRegexes = useMemo(
    () =>
      allowedOrigins.some(p => p === '*' || p === '%')
        ? [/^.*$/]
        : allowedOrigins.map(patternToRegex),
    [allowedOrigins]
  );

  useEffect(() => {
    window.opener?.postMessage({ type: 'READY' }, '*');

    let gotToken = false;

    function handler(e: MessageEvent) {
      const matched = allowedOriginRegexes.some(re => re.test(e.origin));
      if (!matched) return;
      if (e.data?.type !== 'MEDIX_SYNC_TOKEN') return;
      const payload: Payload = e.data.payload || {};
      if (!payload.token) return;

      gotToken = true;
      setToken({ token: payload.token, from: payload.from });
      navigate(`/${payload.path}`, { replace: true });
    }

    window.addEventListener('message', handler);

    const t = setTimeout(() => {
      if (!gotToken) {
        clearToken();
        navigate(routerPaths.notAuthen, { replace: true });
      }
    }, timeout);

    return () => {
      window.removeEventListener('message', handler);
      clearTimeout(t);
    };
  }, [allowedOrigins, timeout, navigate]);
}
