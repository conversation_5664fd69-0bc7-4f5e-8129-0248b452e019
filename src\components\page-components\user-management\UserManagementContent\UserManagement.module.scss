.tableWrapper {
  background-color: var(--white);
  margin-top: 12px;
  border-radius: 8px;
  .filterWrapper {
    overflow-x: auto;
    padding: 12px;
  }

  :global {
    .ant-table-tbody .ant-table-row .ant-table-cell div {
      &:has(.validTag) {
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: unset;
        span {
          border-radius: 16px;
          padding: 2px 24px;
          &.success {
            color: $success-700;
            background-color: #eef4e2;
          }
          &.error {
            color: $error-600;
            background-color: $error-100;
          }
        }
      }
    }
  }

  .recordCount {
    color: $gray-800;
    text-wrap: nowrap;
  }
}

.deleteAction {
  color: $error-600;
}

.buttonRow {
  flex-shrink: 0;
  button {
    width: 100px;
    flex-shrink: 0;
  }
}
