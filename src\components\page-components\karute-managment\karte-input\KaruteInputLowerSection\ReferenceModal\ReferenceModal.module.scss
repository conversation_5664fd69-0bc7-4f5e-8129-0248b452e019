.reference_title {
  :global(.title-modal) {
    margin-bottom: 0px !important;
  }
}
.reference_master_tab {
  border-radius: 8px;
  border: 1px solid $gray-200;
  overflow: hidden;
  min-height: 304px;
  .reference_master_tab_sidebar_container {
    border-right: 1px solid $gray-200;
    padding: 16px 4px 16px 16px !important;
    .content {
      padding-right: 4px;
      overflow-y: auto;
      height: 318px;
    }
    .row_item {
      padding: 8px 10px;
    }
  }
  .reference_master_tab_content_container {
    padding: 16px 4px 16px 16px !important;

    .content {
      padding-right: 4px;
      overflow-y: auto;
      height: 318px;
    }
    .row_item {
      padding: 8px 12px;
    }

    .reference_emty_data {
      padding-right: 12px;
      &:global(.ant-empty) {
        height: 318px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .row_item {
    cursor: pointer;

    color: $gray-500;
    &.active {
      color: $gray-800;
      background-color: $brand-50;
      &:hover {
        color: $gray-900;
        background-color: $brand-100;
      }
    }
    &:not(.active):hover {
      color: $gray-800;
      background-color: $gray-50;
    }
    border-radius: 8px;
    word-break: break-all;
  }
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: $gray-300; // Temporarily set color for easy identifcation
  }
}
.reference_emty_data {
  &:global(.ant-empty) {
    height: 198px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
