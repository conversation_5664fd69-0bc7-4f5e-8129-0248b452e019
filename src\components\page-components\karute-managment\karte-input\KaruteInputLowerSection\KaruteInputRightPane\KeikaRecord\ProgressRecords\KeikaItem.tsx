import Button from '@/components/common/core/Button';
import { MainKeika } from '@/store/karute/type';
import { useGetHistoryKeikasQuery } from '@/store/keika/api';
import { ButtonType, EKaruteUpsertType } from '@/types/enum';
import { Flex, Switch } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useKaruteInput } from '../../../../provider';
import { EStatus } from '../../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import { ProgressRecords } from './ProgressRecords';
import styles from './ProgressRecords.module.scss';
import { RecordInfo } from './RecordInfo';

export const KeikaItem: React.FC<{
  keika: MainKeika;
  isLatest: boolean;
  isEmpty?: boolean;
}> = ({ keika, isLatest, isEmpty }) => {
  const [showHistory, setShowHistory] = useState(false);
  const { openUpsertModal } = useKaruteInput();

  const notHaveHistory = keika.keika_revisions_count <= 1;

  const { data: historyData, isFetching } = useGetHistoryKeikasQuery(
    { keika_id: keika.keika_id, limit: 10 },
    { skip: !showHistory || notHaveHistory }
  );

  useEffect(() => {
    if (showHistory && notHaveHistory) {
      setShowHistory(false);
    }
  }, [notHaveHistory, showHistory]);

  const handleSwitch = (checked: boolean) => {
    if (!notHaveHistory) setShowHistory(checked);
  };

  const historyKeikas = historyData?.data?.data ?? [];
  const isAnyMondaiActive = useMemo(
    () => !!(keika?.keika_details ?? []).find(k => k?.disease_base?.status === EStatus.ACTIVE),
    [keika]
  );

  useEffect(() => {
    setShowHistory(false);
  }, [notHaveHistory]);

  return (
    <>
      {!isEmpty && (
        <Flex
          justify="space-between"
          align="center"
          style={{ padding: '12px 14px', height: '46px' }}
        >
          <RecordInfo visitDate={keika.visit_date} updatedAt={keika.updated_at} />
          <Flex gap={12} align="center">
            <Flex gap={8} align="center">
              <Switch
                style={{ minWidth: '36px' }}
                checked={showHistory}
                onChange={handleSwitch}
                disabled={notHaveHistory}
              />
              <span style={{ fontWeight: 500, fontSize: 14 }}>編集履歴</span>
            </Flex>
            <Button
              style={{
                width: '100%',
                height: '100%',
              }}
              customType={ButtonType.SECONDARY_BRAND}
              disabled={!keika?.keika_id || !isAnyMondaiActive}
              className={styles.customButton}
              onClick={() =>
                /*isLatest &&*/ openUpsertModal(EKaruteUpsertType.KEIKA, String(keika.keika_id))
              }
            >
              編集
            </Button>
          </Flex>
        </Flex>
      )}
      <ProgressRecords keika={keika} showAddImage={isAnyMondaiActive} isEmpty={isEmpty} />

      {showHistory &&
        !isFetching &&
        historyKeikas.map(h => (
          <div key={h.keika_revision_id}>
            <RecordInfo
              visitDate={h.visit_date}
              updatedAt={h.updated_at}
              className={styles.historyRecordInfo}
            />
            <ProgressRecords
              keika={{
                ...h,
                keika_details: h.keika_detail_revisions as any,
                objective_images: h.objective_image_revisions as any,
                treatment_images: h.treatment_image_revisions as any,
                keika_revisions_count: 0,
              }}
              showAddImage={false}
              hideHeader={true}
            />
          </div>
        ))}
    </>
  );
};
