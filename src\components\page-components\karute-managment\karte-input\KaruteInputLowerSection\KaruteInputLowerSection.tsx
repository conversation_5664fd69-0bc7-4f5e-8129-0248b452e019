import { useCallback, useEffect, useMemo } from 'react';
import styles from './KaruteInputLowerSection.module.scss';
import { FullSizeTabs } from '@/components/common/core/Tabs';
import { AppSplit } from '@/components/common/core/Split';
import { KaruteInputFooter } from './KaruteInputFooter';
import { useKaruteInput } from '../provider';
import { FE_DEFINED_META_KEYS, routerPaths } from '@/types/constants';
import { KaruteInputLeftPane } from './KaruteInputLeftPane';
import { EKaruteService, EKaruteServicePriority } from '@/types/enum';
import { useNavigate, useParams } from 'react-router-dom';
import KeikaRecord from './KaruteInputRightPane/KeikaRecord';
import { PatientSummary } from './KaruteInputRightPane/PatientSummary';
import { useUpdateUserMetaMutation } from '@/store/user-karute/api';
export function KaruteInputLowerSection() {
  const {
    lowerHorizontalSizes,
    lowerVerticalSizes,
    mainVerticalSizes,
    setLowerHorizontalSizes,
    setLowerVerticalSizes,
  } = useKaruteInput();
  const [updateUserMetaMutation] = useUpdateUserMetaMutation();
  const {
    id: karuteId,
    serviceId,
    clinicCd,
  } = useParams<{
    serviceId: string;
    clinicCd: string;
    id: string;
  }>();
  const { services } = useKaruteInput();
  const navigate = useNavigate();

  const handleVerticalDragEnd = useCallback(
    (newSizes: number[]) => {
      const updatedMeta = {
        meta: {
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.MAIN]: mainVerticalSizes,
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.MAIN]: lowerHorizontalSizes,
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.RIGHT]: newSizes,
        },
      };
      setLowerVerticalSizes(newSizes);
      // Don't need to handle success | error here
      updateUserMetaMutation(updatedMeta);
    },
    [lowerHorizontalSizes, mainVerticalSizes, updateUserMetaMutation, setLowerVerticalSizes]
  );

  const handleHorizontalDragEnd = useCallback(
    async (newSizes: number[]) => {
      const updatedMeta = {
        meta: {
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.MAIN]: mainVerticalSizes,
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.MAIN]: newSizes,
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.RIGHT]: lowerVerticalSizes,
        },
      };
      setLowerHorizontalSizes(newSizes);
      // Don't need to handle success | error here
      updateUserMetaMutation(updatedMeta);
    },
    [lowerVerticalSizes, mainVerticalSizes, updateUserMetaMutation, setLowerHorizontalSizes]
  );

  const tabServices = useMemo(() => {
    return services
      .slice()
      .sort((a, b) => {
        return (
          EKaruteServicePriority[a.name as keyof typeof EKaruteServicePriority] -
          EKaruteServicePriority[b.name as keyof typeof EKaruteServicePriority]
        );
      })
      .map(service => {
        return {
          label: EKaruteService[service.name as keyof typeof EKaruteService],
          value: service.service_id,
          key: service.service_id,
        };
      });
  }, [services]);
  const changeService = (serviceId: number) => {
    const path = routerPaths.karuteManagement.karuteUpdateByService
      .replace(':clinicCd', String(clinicCd))
      .replace(':id', String(karuteId))
      .replace(':serviceId', String(serviceId));
    navigate(path, { replace: true });
  };
  useEffect(() => {
    if (!serviceId && tabServices.length > 0) {
      changeService(tabServices[0].value);
    }
  }, [tabServices]);

  const handleTabChange = (key: string | number) => {
    changeService(key as number);
  };

  return (
    <>
      <div className={styles.mondaiSection}>
        <div className={styles.tabsContainer}>
          <FullSizeTabs
            items={tabServices}
            activeValues={Number(serviceId) || tabServices[0]?.value}
            onChange={handleTabChange}
          />
        </div>

        <AppSplit
          sizes={lowerHorizontalSizes}
          direction="horizontal"
          onDragEnd={handleHorizontalDragEnd}
          className={styles.mondaiLowerSection}
          gutterSize={5}
        >
          <KaruteInputLeftPane />
          <AppSplit
            sizes={lowerVerticalSizes}
            className={styles.karuteInputRightPane}
            direction="vertical"
            onDragEnd={handleVerticalDragEnd}
            gutterSize={5}
          >
            <PatientSummary />
            <KeikaRecord />
          </AppSplit>
        </AppSplit>

        <KaruteInputFooter />
      </div>
    </>
  );
}
