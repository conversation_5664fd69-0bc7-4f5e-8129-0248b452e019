.list_patient_page_table_header::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.list_patient_page_table_header::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: $gray-300;
}
.list_patient_page_table_header {
  overflow-x: auto;
  min-width: 100%;
  padding-bottom: 12px;
  margin-bottom: 4px;

  .searchRow {
    width: auto;
    flex-shrink: 0;
    .input_common {
      :global {
        .ant-input,
        .ant-picker {
          height: 40px !important;
        }
      }
    }
    .common_form_item {
      margin-bottom: 0 !important;
    }
    .searchInput {
      width: 244px;
      min-width: 244px;
      flex-shrink: 0;

      :global {
        input:-internal-autofill-selected {
          height: auto !important;
          max-height: auto !important;
          line-height: normal !important;
        }
      }
    }

    .datePicker {
      width: 244px;
      min-width: 244px;
      background-color: var(--white);
      border-radius: 8px;
      flex-shrink: 0;
    }

    .radioGroup {
      flex-shrink: 0;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    .checkbox {
      flex-shrink: 0;
      text-wrap: nowrap;
    }
  }

  .secondRowContainer {
    overflow: hidden;
    transition: max-width 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
    opacity: 0;
    max-width: 0;

    &.visible {
      opacity: 1;
      max-width: 100%;
    }
  }

  .buttonRow {
    flex-shrink: 0;
    button {
      width: 100px;
      flex-shrink: 0;
    }
  }
}

.list_patient_page_table_data {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: 8px;

  .total_count {
    font-size: 12px;
    color: $gray-500;
    margin-left: auto;
    flex-shrink: 0;
  }

  .table_header {
    padding: 16px 12px;
    overflow-x: auto !important;
  }
}

.insurance_type_filter {
  display: flex;
  align-items: center;

  .filter_icon {
    display: flex;
    align-items: center;
    margin-right: 16px;
    font-size: 13px;

    color: $gray-700;

    span {
      margin-left: 8px;
    }
  }
}
