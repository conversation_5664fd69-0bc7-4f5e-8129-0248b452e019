import { InputProps } from 'antd';
import { AppInput } from '../AppInput';

interface PhoneInputProps extends Omit<InputProps, 'onChange'> {
  onChange?: (val: string) => void;
}

export const AppInputPhoneNumber = ({ onChange, maxLength = 20, ...rest }: PhoneInputProps) => {
  return (
    <AppInput
      {...rest}
      maxLength={maxLength}
      inputMode="tel"
      onChange={e => {
        const raw = e.target.value;
        const cleaned = raw.replace(/[^0-9-]/g, '');
        onChange?.(cleaned);
      }}
    />
  );
};
