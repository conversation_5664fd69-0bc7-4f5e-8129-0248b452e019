import { OptionItem } from './ListOptionTable';
import Icon from '@/components/common/core/Icon';
import { useWatch } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { Flex } from 'antd';
import { CourseItem } from './ListCourseTable';
import styles from './UpsertOptionListForm.module.scss';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import clsx from 'clsx';

type Props = {
  isEdit: boolean;
  canDelete?: boolean;
  mode: 'option' | 'course';
  handleCancelRecord: () => void;
  handleConfirmEdit: () => void;
  handleOpenEdit: () => void;
  handleDeleteRecord?: () => void;
  record: OptionItem | CourseItem;
  isDisabled?: boolean;
  isCourseRecordNullable?: boolean;
};

export function OptionCourseActionBtn({
  isEdit,
  canDelete,
  mode,
  handleCancelRecord,
  handleConfirmEdit,
  handleOpenEdit,
  handleDeleteRecord,
  record,
  isDisabled,
  isCourseRecordNullable,
}: Props) {
  const [isAllowConfirmed, setIsAllowConfirmed] = useState(false);
  const [isAllowCancel, setIsAllowCancel] = useState(false);
  const form = useFormInstance<Record<string, OptionItem | CourseItem>>();

  const currentData = useWatch(record.rowId, form);

  useEffect(() => {
    const isFormDirty = form.isFieldTouched(record.rowId);

    if (mode === 'option') {
      if (record?.isInit) {
        setIsAllowCancel(false);
        setIsAllowConfirmed(true);
      } else {
        setIsAllowCancel(true);
        setIsAllowConfirmed(isFormDirty);
      }
    }

    if (mode === 'course') {
      setIsAllowCancel(!record?.isInit);
      setIsAllowConfirmed(isFormDirty || !!record?.isInit);
    }
  }, [currentData, isCourseRecordNullable, form]);

  return (
    <Flex align="center" gap={8} justify="end">
      {isEdit && (
        <>
          <button
            className={clsx(styles.action_btn, { [styles.disabled]: !isAllowCancel || isDisabled })}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (!isAllowCancel || isDisabled) return;
              handleCancelRecord();
            }}
          >
            <Icon name="close" color="red" height={16} width={16} />
          </button>
          <button
            className={clsx(styles.action_btn, {
              [styles.disabled]: !isAllowConfirmed || isDisabled,
            })}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (!isAllowConfirmed || isDisabled) return;
              handleConfirmEdit();
            }}
          >
            <Icon name="check" color="green" height={16} width={16} />
          </button>
        </>
      )}
      {!isEdit && (
        <>
          <button
            className={clsx(styles.action_btn, { [styles.disabled]: isDisabled })}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (isDisabled) return;
              handleOpenEdit();
            }}
          >
            <Icon name="edit" color="gray" height={16} width={16} />
          </button>
          {mode === 'option' && (
            <button
              className={clsx(styles.action_btn, { [styles.disabled]: !canDelete || isDisabled })}
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                if (canDelete && !isDisabled) {
                  handleDeleteRecord?.();
                }
              }}
            >
              <Icon name="trash" color="red" height={16} width={16} />
            </button>
          )}
        </>
      )}
    </Flex>
  );
}
