import { useScreen } from '@/hooks/useScreen';
import { Option } from '@/types/interface';
import { RenderWithTooltip } from '../../core/CommonTable/RenderWithTooltip';

const PaidMenuItem = ({
  activeMenu,
  paid,
  onClick,
}: {
  activeMenu: boolean;
  paid: Option<string>;
  onClick: (paid: Option<string>) => void;
}) => {
  const { screen } = useScreen();
  return (
    <div
      className={`${screen === 'desktop' ? 'fs18-medium' : 'fs16-medium'} menu-paid-item ${
        activeMenu ? 'active-menu' : ''
      }`}
      onClick={() => onClick(paid)}
    >
      <RenderWithTooltip text={paid.label} maxLines={3} />
    </div>
  );
};

export default PaidMenuItem;
