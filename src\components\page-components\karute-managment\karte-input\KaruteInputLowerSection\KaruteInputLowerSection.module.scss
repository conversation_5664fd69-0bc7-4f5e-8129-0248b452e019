::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: $gray-300; // Temporarily set color for easy identifcation
}

.karute_input {
  height: 1500px;
  :global(> .gutter) {
    margin: 9px 0 !important;
  }
  :global(> .gutter-vertical) {
    &::after {
      width: 50%;
      left: 25%;
    }
  }
  .mondaiSection {
    background-color: $gray-200;
    padding: 20px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    height: 806px;
    min-height: 650px;
    overflow: hidden;
    .tabsContainer {
      flex-shrink: 0;
    }

    .mondaiLowerSection {
      flex: 1;
      min-height: 0;
      margin-top: 10px;
      overflow: auto;
      display: flex !important;
      :global(> .gutter) {
        margin: 0 5px !important;
      }
      :global(> .gutter-horizontal) {
        &::after {
          height: 50%;
          top: 15%;
        }
      }
    }

    .mondai_footer {
      flex-shrink: 0;
      display: flex;
      height: 44px;
      background-color: var(--white);
      padding: 8px 12px;
      margin-top: 8px;
      border-radius: 4px;
      justify-content: space-between;

      .footer_item {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 8px;

        &:not(:last-child) {
          border-right: 1px solid $gray-200;
        }
      }
      .footer_clamp {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .footer_label {
        color: $gray-500;
        margin-right: 8px;
        white-space: nowrap;
      }

      .footer_value {
        color: $gray-800;
        word-break: break-all;
      }
    }
  }
}

.karuteInputLeftPane {
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 225px;
  // overflow: hidden;
  .database_header {
    .action {
      padding: 12px;
      border-bottom: 1px solid $gray-200;
      overflow: auto !important;
    }
    .mondai {
      height: 62px;
      padding: 12px;
      display: flex;
      align-items: center;

      .tab {
        flex: 1;
        max-width: min(calc(100% - 80px), 320px);
        .mondaiItem {
          text-align: center;
        }
        :global(.pagination_tab_option) {
          width: 80px !important;
        }
      }
    }
  }

  .database_list_table {
    .contentCell {
      display: flex;
      gap: 4px;
      flex-direction: column;
      align-items: flex-start;
      white-space: normal;
      overflow: visible;
      text-wrap: nowrap;
      max-height: 275px;
      word-break: break-all;
    }
    .addImageBtn {
      position: absolute;
      right: 5px;
      bottom: 5px;

      width: 18px;
      height: 18px;
      min-width: 18px;
      padding: 4px;
      border: none;
      background-color: $blue-700;
      color: white;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    :global {
      .ant-table {
        .ant-table-cell {
          padding: 8px 12px;
        }

        .ant-table-tbody > tr > td:first-child {
          border-right: 1px solid $gray-200;

          background-color: $brand-50;

          color: $gray-500;
          min-width: 180px;
        }
        .ant-table-tbody > tr:first-child {
          border-radius: 0 !important;
        }

        .ant-table-tbody > tr > td:nth-child(2) {
          color: $gray-800;
          min-width: 280px;
        }
        .ant-table-tbody > tr > td {
          border-color: $gray-200;
        }
      }
    }
  }
}

.imageGroup {
  height: 130px;
  overflow-x: hidden;
  overflow-y: visible !important;
  width: 100%;
  &::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px !important;
    background-color: $gray-300 !important;
  }
}
.karuteInputRightPane {
  overflow: auto;
  :global(> .gutter) {
    margin: 5px 0 !important;
  }
  :global(> .gutter-vertical) {
    &::after {
      width: 50%;
      left: 25%;
    }
  }
  .patientSummary {
    min-height: 180px;
    .sectionHeader {
      padding: 0 12px;
      border-bottom: 1px solid $gray-200;
      max-width: 100%;
      overflow: auto;
      flex-shrink: 0;
      height: 60px;
    }
    .sectionContent {
      background-color: $gray-100;
    }
    // margin-top: 8px; // causing layout to be off
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--white);
    height: 100%;
    :global(.ant-table-cell) {
      height: 50px;
      padding: 8px 12px;
    }
    > div:nth-child(2) {
      overflow-y: hidden;
      height: calc(100% - 50px);
    }
    .header {
      border-radius: 8px 8px 0 0;
      background-color: var(--white);
      padding: 0 12px;
      height: 60px;
      border-bottom: 1px solid $gray-200;
    }
    :global(.ant-table-cell) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      overflow-wrap: normal;
    }
    :global(.ant-table-thead) {
      :global(.ant-table-cell) {
        height: 34px;
      }
    }
    :global(.ant-table-row) {
      text-align: center;
    }
  }
  .keikaRecord {
    min-height: 180px;

    background-color: var(--white);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    .sectionHeader {
      padding: 0 12px;
      border-bottom: 1px solid $gray-200;

      max-width: 100%;
      overflow: auto;
      flex-shrink: 0;
      height: 60px;
    }
    .sectionContent {
      // flex: 1;
      background-color: $gray-100;
    }

    .keikaSectionContent {
      flex: 1;
      width: 100%;
      overflow: auto !important;
    }
  }
}

.set_from_history_modal {
  overflow: hidden;
  border-radius: 10px;
  .set_from_history_modal_header {
    padding: 20px 24px 24px 24px;
    .input_common {
      max-width: 300px;
      min-width: 150px;
    }
    .submit_btn {
      button {
        width: 100px;
      }
    }
    overflow-x: auto;
  }
  .set_from_history_modal_table {
    :global {
      .ant-table {
        .ant-table-tbody {
          .ant-table-placeholder {
            .ant-table-cell {
              height: 200px !important;
              padding: 0 !important;
              border-bottom: none !important;
            }
          }
        }
      }
      .ant-pagination {
        margin-left: 11px;
        margin-right: 11px;
      }
    }
  }
  .set_from_history_modal_footer {
    padding: 0px 24px 20px 24px;
  }
  :global {
    thead {
      tr {
        th {
          max-height: 17px !important;
          line-height: 17px !important;
          border-bottom: 1px solid $brand-50;

          div {
            max-height: 16px !important;
            line-height: 16px !important;
          }
        }
      }
    }
  }
}

.dividerRow {
  height: 1px;
  background-color: $gray-200;
  margin: 8px 0;
}

.title {
  color: $gray-800;
  text-wrap: nowrap;
}

.fixedRowHeight {
  td {
    max-height: 35px !important;
    line-height: 35px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    overflow: hidden;
    white-space: nowrap;

    div {
      max-height: 19px !important;
      line-height: 19px !important;
    }
  }
}
