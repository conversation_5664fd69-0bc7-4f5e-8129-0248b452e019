import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import Thumbnail from '@/components/common/core/Thumbnail';
import { useAppSelector } from '@/config/redux/store';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { getUserInfo } from '@/store/auth/selectors';
import {
  useDeleteSchemaImageMutation,
  useFavoriteSchemaImageMutation,
  usePostSchemaImageMutation,
  useUnFavoriteSchemaImageMutation,
} from '@/store/schema/api';
import { SchemaImage, SchemaImageType } from '@/store/schema/type';
import {
  ALLOWED_IMAGE_EXTENSIONS,
  ALLOWED_IMAGE_TYPES,
  MAX_IMAGE_SIZE,
  NOTICE_COMMON_MESSAGE,
  SCHEMA_COMMON,
  STATUS_CODES,
} from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { compressImageToSize, getImgKey, showFlashNotice } from '@/utils';
import { Flex, Spin, Upload, type UploadProps } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useSchema } from '../../context/SchemaContext';
import { FAVORITE_CATEGORY_ID, HISTORY_CATEGORY_ID, SchemaMode } from '../../type';
import styles from './SchemaGrid.module.scss';

export interface SchemaGridHandle {
  getSelected: () => SchemaImage[];
  clear: () => void;
}

const MAX_SELECT = 6;
const MAX_ONE_USE = 10;

const SchemaGrid = forwardRef<SchemaGridHandle>((_, ref) => {
  const [favoriteSchemaImage] = useFavoriteSchemaImageMutation();
  const [unFavoriteSchemaImage] = useUnFavoriteSchemaImageMutation();
  const [postSchemaImage] = usePostSchemaImageMutation();
  const [deleteSchemaImage] = useDeleteSchemaImageMutation();
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const { serviceId } = useParams<{ serviceId: string }>();
  const user = useAppSelector(getUserInfo);
  const {
    gridLoading,
    setGridLoading,
    categoryId,
    selectedImages,
    setSelectedImages,
    drawnIds,
    setDrawnIds,
    savedIds,
    mode,
    setMode,
    allImages,
    updateCategorySchemaImages,
    setAllImages,
    isPendingCategory,
    showConfirmRemoveImage,
    showConfirmRemoveMasterImage,
    setSavedIds,
  } = useSchema();
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedImages.map(getImgKey));

  const filtered = useMemo(() => {
    if (categoryId === FAVORITE_CATEGORY_ID) return allImages.filter(i => i.favorite);
    if (categoryId === HISTORY_CATEGORY_ID) return allImages.filter(i => i.isHistory);
    return allImages.filter(i => i.category_schema_id === categoryId);
  }, [allImages, categoryId]);

  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedIds(selectedImages.map(getImgKey));
  }, [selectedImages]);

  const master = useMemo(
    () =>
      filtered
        .filter(
          i => i.type === SchemaImageType.TYPE_DEFAULT || i.type === SchemaImageType.TYPE_CUSTOM
        )
        .sort((a, b) => dayjs(b.created_at).diff(dayjs(a.created_at))),
    [filtered]
  );
  const upload = useMemo(
    () =>
      filtered
        .filter(i => i.type === SchemaImageType.TYPE_ONE_USE)
        .sort((a, b) => dayjs(b.created_at).diff(dayjs(a.created_at))),
    [filtered]
  );
  const displayList = useMemo(() => [...master, ...upload], [master, upload]);

  const originalIds = useMemo(() => selectedImages.map(getImgKey), [selectedImages]);

  const hasChange = useMemo(() => {
    if (originalIds.length !== selectedIds.length) return true;
    return originalIds.some(id => !selectedIds.includes(id));
  }, [originalIds, selectedIds]);

  const isCancelDisabled = originalIds.length === 0;
  const isConfirmDisabled = !hasChange;

  const toSelectMode = () => {
    setMode(SchemaMode.SELECT);
  };
  const toDrawMode = () => {
    setMode(SchemaMode.DRAW);
  };

  const toggleSelection = (id: string) => {
    if (mode !== SchemaMode.SELECT) return;

    const exists = selectedIds.includes(id);
    if (!exists && selectedIds.length >= MAX_SELECT) {
      showFlashNotice({
        type: 'error',
        message: `最大${MAX_SELECT}枚まで選択できます。`,
      });
      return;
    }

    setSelectedIds(prev => (exists ? prev.filter(i => i !== id) : [...prev, id]));
  };

  const toggleFav = async (img: SchemaImage) => {
    const uid = getImgKey(img);
    const nextFav = !img.favorite;

    setAllImages(list => list.map(i => (getImgKey(i) === uid ? { ...i, favorite: nextFav } : i)));

    if (img.isTemp) {
      return;
    }

    setGridLoading(true);
    try {
      if (nextFav) {
        await favoriteSchemaImage({ schema_image_id: Number(img.schema_image_id) });
      } else {
        await unFavoriteSchemaImage({ schema_image_id: Number(img.schema_image_id) });
      }
      await updateCategorySchemaImages(categoryId);
    } catch (e) {
      setAllImages(list =>
        list.map(i => (getImgKey(i) === uid ? { ...i, favorite: !nextFav } : i))
      );
    } finally {
      setGridLoading(false);
    }
  };

  const handleUpload = async (file: File, isOneUse: boolean) => {
    if (isOneUse) {
      const oneUseCount = allImages.filter(i => i.type === SchemaImageType.TYPE_ONE_USE).length;
      if (oneUseCount >= MAX_ONE_USE) {
        showFlashNotice({
          type: 'error',
          message: SCHEMA_COMMON.UPLOAD_LIMIT(MAX_ONE_USE),
        });
        return false;
      }
    }
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.IMAGE_SELECT });
      return;
    }
    if (file.size / 1024 / 1024 >= MAX_IMAGE_SIZE) {
      showWarningDialog({
        message: <div style={{ whiteSpace: 'pre-wrap' }}>{SCHEMA_COMMON.WARNING_COMPRESS}</div>,
        buttons: [
          { label: 'キャンセル', type: 'cancel', onClick: hideWarningDialog },
          {
            label: '続行',
            type: 'confirm',
            onClick: async () => {
              try {
                setGridLoading(true);
                const compressed = await compressImageToSize(file, MAX_IMAGE_SIZE);
                await proceedUpload(compressed, isOneUse);
              } catch (e) {
                showFlashNotice({ type: 'error', message: SCHEMA_COMMON.FAIL_COMPRESS });
              } finally {
                setGridLoading(false);
              }
            },
          },
        ],
      });
      return;
    }
    await proceedUpload(file, isOneUse);
  };

  const proceedUpload = async (file: File, isOneUse: boolean) => {
    setGridLoading(true);
    const newItem: SchemaImage = {
      url: URL.createObjectURL(file),
      category_schema_id: categoryId,
      imageable_type: null,
      type: isOneUse ? SchemaImageType.TYPE_ONE_USE : SchemaImageType.TYPE_CUSTOM,
      favorite: false,
      file,
      created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    } as SchemaImage;
    if (!isOneUse) {
      const res = await postSchemaImage({
        image: newItem,
        service_ids: serviceId ? [Number(serviceId)] : [],
      });
      showFlashNotice({
        type: res.data?.status === STATUS_CODES.OK ? 'success' : 'error',
        message: res.data?.message || '',
      });
      if (res.data?.status === STATUS_CODES.OK) {
        updateCategorySchemaImages(categoryId);
      }
    } else {
      const tempItem: SchemaImage = {
        ...newItem,
        id: String(Date.now()),
        schema_image_id: null,
        isTemp: true,
      };
      setAllImages(prev => [...prev, tempItem]);
      showFlashNotice({
        type: 'success',
        message: SCHEMA_COMMON.UPLOAD_SUCCESS,
      });
    }
    setGridLoading(false);
  };

  const uploadProps = (isOneUse: boolean): UploadProps => ({
    accept: ALLOWED_IMAGE_EXTENSIONS.join(','),
    showUploadList: false,
    maxCount: 1,
    beforeUpload: file => {
      handleUpload(file, isOneUse);
      return false;
    },
    style: { width: '100%' },
  });

  useImperativeHandle(
    ref,
    () => ({
      getSelected: () =>
        selectedIds
          .map(id => displayList.find(i => i.schema_image_id === id))
          .filter(Boolean) as SchemaImage[],
      clear: () => setSelectedIds([]),
    }),
    [selectedIds, displayList]
  );

  const handleConfirm = () => {
    const beforeIds = selectedImages.map(getImgKey);
    const removed = beforeIds.filter(id => !selectedIds.includes(id));
    const blocked = removed.filter(id => drawnIds.includes(id) || savedIds.includes(id));

    const proceed = () => {
      const imgs: SchemaImage[] = selectedIds
        .map(uid => {
          const existed = selectedImages.find(i => getImgKey(i) === uid);
          if (existed) return existed;
          return allImages.find(i => getImgKey(i) === uid);
        })
        .filter(Boolean) as SchemaImage[];

      setSelectedImages(imgs);
      setDrawnIds(prev => prev.filter(id => imgs.map(getImgKey).includes(id)));
      setSavedIds(prev => prev.filter(id => imgs.map(getImgKey).includes(id)));
      toDrawMode();
    };

    if (blocked.length) {
      showConfirmRemoveImage(() => {
        proceed();
      });
    } else {
      proceed();
    }
  };

  const handleCancel = () => {
    if (isCancelDisabled) return;
    setSelectedIds(originalIds);
    toDrawMode();
  };

  const handleRemoveMasterImage = async (img: SchemaImage) => {
    const res = await deleteSchemaImage({ schema_image_id: Number(img.schema_image_id) });
    showFlashNotice({
      type: res.data?.status === STATUS_CODES.OK ? 'success' : 'error',
      message: res.data?.message || '',
    });
    if (res.data?.status === STATUS_CODES.OK) {
      updateCategorySchemaImages(categoryId, img.schema_image_id);
      // setAllImages(list => list.filter(i => getImgKey(i) !== getImgKey(img)));
    }
  };

  const modeBar = useMemo(
    () =>
      mode === SchemaMode.SELECT ? (
        <div className={styles.modeBar}>
          <Flex
            align="center"
            gap={4}
            onClick={isCancelDisabled ? undefined : handleCancel}
            style={{
              cursor: isCancelDisabled ? 'not-allowed' : 'pointer',
              opacity: isCancelDisabled ? 0.5 : 1,
            }}
          >
            <Icon name="close" height={16} width={16} color="var(--error-600)" />
            <span className="fs12-medium">キャンセル</span>
          </Flex>
          <Flex
            align="center"
            gap={4}
            onClick={isConfirmDisabled ? undefined : handleConfirm}
            style={{
              cursor: isConfirmDisabled ? 'not-allowed' : 'pointer',
              opacity: isConfirmDisabled ? 0.5 : 1,
            }}
          >
            <Icon name="check" height={16} width={16} color="var(--success-600)" />
            <span className="fs12-medium">確定</span>
          </Flex>
        </div>
      ) : (
        <div className={styles.modeBar}>
          <Flex align="center" gap={4} onClick={toSelectMode} style={{ cursor: 'pointer' }}>
            <Icon name="workspace" height={16} width={16} />
            <span className="fs12-medium">選択</span>
          </Flex>
        </div>
      ),
    [mode, selectedIds]
  );

  return (
    <div className={styles.wrapper} ref={wrapperRef}>
      <Spin spinning={gridLoading || isPendingCategory}>
        <div className={styles.container}>
          {modeBar}

          <div className={styles.gridWrapper}>
            <div className={clsx(styles.blockTitle, 'fs12-bold')}>マスター画像</div>
            {master.length === 0 ? (
              <div className={clsx(styles.noData, 'fs14-regular', 'mt-2')}>
                {SCHEMA_COMMON.NO_DATA}
              </div>
            ) : (
              <div className={styles.grid}>
                {master.map(img => {
                  const id = getImgKey(img);
                  return (
                    <Thumbnail
                      key={id}
                      data={img}
                      selectable={mode === SchemaMode.SELECT}
                      selected={selectedIds.includes(id)}
                      onSelect={() => toggleSelection(id)}
                      onToggleFav={() => mode === SchemaMode.DRAW && toggleFav(img)}
                      deletable={img.type === SchemaImageType.TYPE_CUSTOM}
                      onDelete={() =>
                        showConfirmRemoveMasterImage(() => {
                          handleRemoveMasterImage(img);
                        })
                      }
                    />
                  );
                })}
              </div>
            )}

            <div className={clsx(styles.blockTitle, 'fs12-bold')}>取込画像</div>
            {upload.length === 0 ? (
              <div className={clsx(styles.noData, 'fs14-regular', 'mt-2')}>
                {SCHEMA_COMMON.NO_DATA}
              </div>
            ) : (
              <div className={styles.grid}>
                {upload.map(img => {
                  const id = getImgKey(img);
                  return (
                    <Thumbnail
                      key={id}
                      data={img}
                      selectable={mode === SchemaMode.SELECT}
                      selected={selectedIds.includes(id)}
                      onSelect={() => toggleSelection(id)}
                      onToggleFav={() => mode === SchemaMode.DRAW && toggleFav(img)}
                    />
                  );
                })}
              </div>
            )}
          </div>
          {categoryId !== FAVORITE_CATEGORY_ID && categoryId !== HISTORY_CATEGORY_ID && (
            <div className={styles.uploadAction}>
              <Upload {...uploadProps(true)}>
                <Button
                  customType={ButtonType.SECONDARY_BRAND}
                  customSize="xs"
                  style={{ width: '100%' }}
                >
                  <Icon name="upload" height={20} width={20} /> 画像取込 (今回のみ使用)
                </Button>
              </Upload>
              <Upload {...uploadProps(false)}>
                <Button
                  customType={ButtonType.SECONDARY_BRAND}
                  customSize="xs"
                  style={{ width: '100%' }}
                >
                  <Icon name="upload" height={20} width={20} /> 画像取込 (マスター登録)
                </Button>
              </Upload>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
});
export default SchemaGrid;
