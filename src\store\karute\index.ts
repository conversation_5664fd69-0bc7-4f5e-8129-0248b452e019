import { PayloadAction, combineReducers, createSlice } from '@reduxjs/toolkit';
import { prefetchKaruteDetails } from './karute-serviceThunk';
import { mondaiReducer } from './mondaiSlice';
import { KaruteItem, KaruteServiceDetail, KarutesParams } from './type';

export type KaruteState = {
  details: KaruteServiceDetail;
  list: KaruteItem[];
  loading: boolean;
  error: boolean;
  queryParams?: Partial<KarutesParams>;
  isChanged: boolean;
};

const initialState: KaruteState = {
  details: {
    patient_summary: [],
    databases: [],
    keika_records: [],
    patientInfo: undefined,
  },
  loading: true,
  error: false,
  list: [],
  queryParams: {
    service_id: undefined,
    patient_id: undefined,
    karute_id: undefined,
  },
  isChanged: false,
};

// slice
export const karuteSlice = createSlice({
  name: 'karute',
  initialState,
  reducers: {
    setKarute: (state, actions: PayloadAction<KaruteState>) => {
      state.list = actions.payload.list;
    },
    setKaruteQueryParams: (state, action: PayloadAction<Partial<KarutesParams>>) => {
      state.queryParams = { ...state.queryParams, ...action.payload };
    },
    resetQueries: state => {
      state.queryParams = initialState.queryParams;
    },

    setLoading: (state, actions: PayloadAction<boolean>) => {
      state.loading = actions.payload;
    },
    setError: (state, actions: PayloadAction<boolean>) => {
      state.loading = actions.payload;
    },
    updatePatientSummaries: (
      state,
      action: PayloadAction<KaruteServiceDetail['patient_summary']>
    ) => {
      state.details.patient_summary = action.payload;
    },

    setPatientInfo: (state, action: PayloadAction<KaruteServiceDetail['patientInfo']>) => {
      state.details.patientInfo = action.payload;
    },

    resetKaruteDetails: state => {
      state.details = initialState.details;
      state.queryParams = initialState.queryParams;
      state.isChanged = initialState.isChanged;
      state.loading = initialState.loading;
    },
    setChanged: state => {
      state.isChanged = true;
    },
  },
  extraReducers: builder => {
    builder.addCase(prefetchKaruteDetails.fulfilled, (state, action) => {
      if (action.payload) {
        const { patient_summary } = action.payload;
        if (state.details) {
          state.details = {
            ...state.details,
            patient_summary: patient_summary,
          };
        }
      }
    });
  },
});

// actions
export const {
  setPatientInfo,
  setKarute,
  setLoading,
  setError,
  updatePatientSummaries,
  resetKaruteDetails,
  setKaruteQueryParams,
  setChanged,
} = karuteSlice.actions;

const karuteReducerCombined = combineReducers({
  mondais: mondaiReducer,
  karute: karuteSlice.reducer,
});
// reducer
export const karuteReducer = karuteReducerCombined;
