import { Flex, Form } from 'antd';
import {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import styles from './UpsertDatabaseForm.module.scss';

import { ButtonType, EKaruteFieldCode, EKaruteServieId, IsReferenceModalOpen } from '@/types/enum';
import clsx from 'clsx';

import Button from '@/components/common/core/Button';
import { AppDatePicker } from '@/components/common/core/Datepicker';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppTextArea } from '@/components/common/core/Input';
import { AppSelect } from '@/components/common/core/Select';

import Icon from '@/components/common/core/Icon';
import {
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
} from '@/types/constants';
import { showFlashNotice, transformTreatersToOptions } from '@/utils';
import dayjs from 'dayjs';
import { useKaruteInput } from '../../../../provider';
import LabelRender from './LabelRender';

import { OptionTableProps } from '../UpsertOptionListForm/ListOptionTable';

import { CourseTableProps } from '../UpsertOptionListForm/ListCourseTable';
import {
  DiseaseImage,
  EStatus,
  PatientCourse,
  PatientMedicalHistory,
  PatientOption,
  RegisteredVisit,
} from './types';
import { DoctorByVisitDate, GetDiseaseBase } from './types/database_form_get';
import { DatabaseSchema, EPaymentType, MondaiItem } from './types/database_schema';

import { AppDivider } from '@/components/common/core/Divider/AppDivider';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { useLazyGetBuiCdByTypeQuery } from '@/store/karute/api';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import { UserItem } from '@/store/user-sync/type';
import { REMOVE_CONFIRM, WARNING_REMOVE_COURSE_OPTION } from '@/types/constants/karute';
import { isNil } from 'lodash';
import { useParams } from 'react-router-dom';
import InjuryNameModal from '../../../../InjuryNameModal';
import { InjuryNameIdentifier, LRType } from '../../../../InjuryNameModal/type';
import { InjuryNamePaymenFormItem } from './UpsertDatabaseFormItem';
import { DatabaseMapContext } from '../UpsertDatabaseModalWrapper';

const MAX_TEXT_LENGTH = 1000;

export interface UpsertDatabaseFormRef {
  scrollToMondai: (mondai?: MondaiItem | null) => void;
}
export type UpsertDatabaseFormSchema = Partial<{
  karute_disease_base_id: number;
  database: Partial<GetDiseaseBase>[];
  patient_medical_history: PatientMedicalHistory;
  patient_options?: PatientOption[];
  patient_courses?: PatientCourse[];
}>;

type Props = {
  // initialValue?: UpsertDatabaseFormSchema
  disease_base_id?: number; // for select detail
  allRegisteredVisit: RegisteredVisit[];
  availableRegisteredVisit: RegisteredVisit[];
  doctorList: Partial<UserItem>[];
  setIsSetFromHistoryModalOpen: React.Dispatch<React.SetStateAction<number | null>>;
  hasSetUp: boolean;
  isFirstActiveMondai: Record<number, MondaiItem | null>;
  calculateFirstActiveMondai: (database: MondaiItem[]) => void;
  getDefaultMondais: () => Record<string, MondaiItem>;
  handleAddCourse: ({
    visitDate,
    visitRegistered,
  }: {
    visitDate: string;
    visitRegistered?: RegisteredVisit;
  }) => void;
  handleAddOption: ({
    value,
    name,
    visitDate,
  }: {
    value: boolean;
    name: number;
    visitDate?: string;
  }) => void;
  // initialValuesMemo:  UpsertDatabaseFormSchema & { karute_id?: number }
  initialDatabaseMap: Record<number, Partial<GetDiseaseBase>>;
  initialDoctorByVisitDate: DoctorByVisitDate;
  isCourseNullable: (treatmentDate: string) => boolean;
  mondaiServiceId?: EKaruteServieId;
  handleOpenReference: (isReferenceModalOpen: IsReferenceModalOpen) => void;
} & Partial<OptionTableProps & CourseTableProps>;

export const UpsertDatabaseForm = forwardRef<UpsertDatabaseFormRef, Props>(
  (
    {
      mondaiServiceId,
      allRegisteredVisit,
      availableRegisteredVisit,
      doctorList,
      initialDoctorByVisitDate,
      courseDataSource,
      setCourseDataSource,
      setCourseSelectedKeys,
      optionDataSource,
      setOptionDataSource,
      setOptionSelectedKeys,
      setIsSetFromHistoryModalOpen,
      disease_base_id,
      hasSetUp,
      isFirstActiveMondai,
      calculateFirstActiveMondai,
      getDefaultMondais,
      handleAddCourse,
      handleAddOption,
      initialDatabaseMap,
      isCourseNullable,
      handleOpenReference,
    },
    ref
  ) => {
    const { serviceId: urlServiceId } = useParams();
    const serviceId = mondaiServiceId || Number(urlServiceId);
    const containerRef = useRef<HTMLDivElement>(null);
    const scrollTargetRef = useRef<HTMLDivElement>(null);
    // State
    const [isInjuyNameModalOpen, setIsInjuyNameModalOpen] = useState<{
      index: number;
      initialValue: InjuryNameIdentifier;
      paymentType: EPaymentType;
    } | null>(null);
    const [selectedMondai, setSelectedMondai] = useState<MondaiItem | null>(null);

    // Hooks
    const form = Form.useFormInstance<DatabaseSchema>();

    const { openSchema, drawnImages, setDrawnImages } = useKaruteInput();
    const { showWarningDialog, hideWarningDialog } = useWarningDialog();
    const { virtualDatabaseMap } = useContext(DatabaseMapContext);

    // API
    const [getBuiCdByTypeQuery] = useLazyGetBuiCdByTypeQuery();
    useImperativeHandle(ref, () => ({
      scrollToMondai: (mondai?: MondaiItem | null) => {
        setSelectedMondai(mondai ?? null);
        setTimeout(() => {
          scrollTargetRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      },
    }));

    // Method
    const handleSchema = useCallback(
      (diseaseType: DiseaseType, index: number) => {
        const currentMondai: MondaiItem = form.getFieldValue(['database', index]) as MondaiItem;
        const edittedImage = {
          [SchemaType.DATABASE]: {
            [String(currentMondai?.disease_base_id ?? currentMondai?.tmp_id)]: {
              [DiseaseType.O]: (currentMondai?.objective_images ?? []).map(item => ({
                id: `${item.disease_base_id}_${item.disease_base_image_id}`,
                disease_base_image_id: item.disease_base_image_id,
                url: item.url,
                schema_image_id: item?.schema_image_id,
              })),
              [DiseaseType.T]: (currentMondai?.treatment_images ?? []).map(item => ({
                id: `${item.disease_base_id}_${item.disease_base_image_id}`,
                disease_base_image_id: item.disease_base_image_id,
                url: item.url,
                schema_image_id: item?.schema_image_id,
              })),
            },
          },
        };
        setDrawnImages(edittedImage);
        openSchema(
          SchemaType.DATABASE,
          diseaseType,
          (currentMondai?.disease_base_id as string | number) ?? currentMondai?.tmp_id
        );
      },
      [form, setDrawnImages, openSchema]
    );
    const handleInjuryName = (name: number) => {
      const selectedPaymentType = form.getFieldValue(['database', name, 'payment_type']);

      if (isNil(selectedPaymentType)) {
        return;
      }

      const selectedMondai: MondaiItem = form.getFieldValue(['database', name]);

      const intialInjuryNameValue: InjuryNameIdentifier = {
        part_type: selectedMondai?.part_type,
        part_id: selectedMondai?.part_id,
        lr_type: selectedMondai?.lr_type,
        name: selectedMondai?.injury_name,
        name_other: selectedMondai?.injury_name_other,
        // bui_cd: selectedMondai?.bui_cd, isn't need
      };
      setIsInjuyNameModalOpen({
        initialValue: intialInjuryNameValue,
        index: name,
        paymentType: selectedPaymentType,
      });
    };
    const handleChangeInjuryName = async (payload: InjuryNameIdentifier) => {
      if (isInjuyNameModalOpen?.index != undefined && isInjuyNameModalOpen?.index >= 0) {
        const injury = {
          part_type: payload.part_type,
          part_id: payload.part_id,
          lr_type: payload.lr_type ?? LRType.None,
          name: payload.name,
          name_other: payload.name_other,
          bui_cd: 0,
        };
        if (!(injury.part_type === 7 || injury.part_type === 8)) {
          const res = await getBuiCdByTypeQuery({
            part_type: payload.part_type,
            part_id: payload.part_id,
            lr_type: payload.lr_type ?? LRType.None,
          });
          injury.bui_cd = res?.data?.data?.bui_cd ?? 0;
        }
        const currentPayment = form.getFieldValue([
          'database',
          isInjuyNameModalOpen?.index,
          'payment_type',
        ]);
        if (
          [EPaymentType.TRANSPORT_ACCIDENT, EPaymentType.WORK_ACCIDENT].includes(currentPayment)
        ) {
          form.setFieldValue(
            ['database', isInjuyNameModalOpen?.index, 'injury_name_other'],
            injury.name_other
          );
        }
        form.setFieldValue(['database', isInjuyNameModalOpen?.index, 'injury_name'], injury.name);
        form.setFieldValue(
          ['database', isInjuyNameModalOpen?.index, 'part_type'],
          injury.part_type
        );
        form.setFieldValue(['database', isInjuyNameModalOpen?.index, 'part_id'], injury.part_id);
        form.setFieldValue(['database', isInjuyNameModalOpen?.index, 'lr_type'], injury.lr_type);
        form.setFieldValue(['database', isInjuyNameModalOpen?.index, 'bui_cd'], injury.bui_cd);
      }
    };
    const checkCourseIsNullable = useCallback(
      (name: number) => {
        const visitDate = form.getFieldValue(['database', name, 'visit_date']);
        if (!isCourseNullable(visitDate)) {
          // setCourseSelectedKeys
          const openNeededKey = (courseDataSource ?? []).find(
            item => item.treatment_date === visitDate
          )?.rowId;
          if (openNeededKey) {
            setCourseSelectedKeys?.(prev => [...new Set([...prev, openNeededKey])]);
          }
        }
        return !visitDate;
      },
      [form, isCourseNullable, courseDataSource, setCourseSelectedKeys]
    );
    const handleSortMondaiItem = useCallback(
      (visitDate: string, index: number) => {
        const current = form.getFieldsValue();
        const newDatabase = [...(current.database || [])];
        const defaultMondais = getDefaultMondais();

        // Get default mondais for the original and target visit_date
        const beforeUpdateMondai = defaultMondais?.[selectedMondai?.visit_date as string];
        const afterUpdateMondai = defaultMondais?.[visitDate as string];
        const defaultMondai = afterUpdateMondai || beforeUpdateMondai || {};

        // Common fields from beforeUpdateMondai for mondais with the same visit_date as selectedMondai
        const commonFieldsForSameDate = {
          objective: beforeUpdateMondai?.objective ?? '',
          plan: beforeUpdateMondai?.plan ?? '',
          treatment: beforeUpdateMondai?.treatment ?? '',
          remarks: beforeUpdateMondai?.remarks ?? '',
          treatment_images: beforeUpdateMondai?.treatment_images ?? [],
          objective_images: beforeUpdateMondai?.objective_images ?? [],
        };

        // Apply common fields to all mondais before leaving
        // Case: if mondai 'bout to move is the active mondai -> need to apply change to all the mondai
        const selectedVisitDate = selectedMondai?.visit_date;

        if (selectedVisitDate) {
          newDatabase.forEach((db, i) => {
            if (db?.visit_date === selectedVisitDate) {
              newDatabase[i] = {
                ...newDatabase[i],
                ...commonFieldsForSameDate,
              };
            }
          });
        }

        // Common fields from defaultMondai for the mondai being moved
        const commonFieldsForMovedMondai = {
          objective: defaultMondai?.objective ?? '',
          plan: defaultMondai?.plan ?? '',
          treatment: defaultMondai?.treatment ?? '',
          remarks: defaultMondai?.remarks ?? '',
          treatment_images: defaultMondai?.treatment_images ?? [],
          objective_images: defaultMondai?.objective_images ?? [],
        };

        // Update the specific mondai at the given index with new visit_date and common fields
        newDatabase[index] = {
          ...newDatabase[index],
          ...commonFieldsForMovedMondai,
          visit_date: visitDate,
        };

        // Sort database by visit_date, placing the updated mondai at the end if on the same day
        newDatabase.sort((a, b) => {
          const aDate = a.visit_date ? dayjs(a.visit_date).unix() : Infinity;
          const bDate = b.visit_date ? dayjs(b.visit_date).unix() : Infinity;

          if (aDate !== bDate) return aDate - bDate;

          // If same day, push the updated mondai to the end
          if (aDate === bDate && a === newDatabase[index]) return 1;
          if (aDate === bDate && b === newDatabase[index]) return -1;

          return 0;
        });

        // Recalculate first active mondai and update form
        calculateFirstActiveMondai(newDatabase);
        form.setFieldsValue({ database: newDatabase });

        // Scroll to the target mondai
        setTimeout(() => {
          scrollTargetRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      },
      [form, selectedMondai, getDefaultMondais, calculateFirstActiveMondai]
    );
    const handleCleanCourse = useCallback(() => {
      if (!setCourseDataSource || !setCourseSelectedKeys) return;

      const database: MondaiItem[] = form.getFieldValue('database') || [];
      const validVisitDates = new Set(
        database
          .map((item, index) => ({ visit_date: item.visit_date, index }))
          .filter(
            item =>
              item.visit_date &&
              (form.getFieldValue(['database', item.index, 'payment_type']) ===
                EPaymentType.SELF_PAID ||
                form.getFieldValue(['database', item.index, 'option_checked']))
          )
          .map(item => item.visit_date)
      );

      setCourseDataSource(prev => {
        const filteredDataSource = prev.filter(item => validVisitDates.has(item.treatment_date));
        const validRowIds = new Set(filteredDataSource.map(item => item.rowId));

        setCourseSelectedKeys(prevKeys => prevKeys.filter(key => validRowIds.has(key)));

        return filteredDataSource;
      });
    }, [form, setCourseDataSource, setCourseSelectedKeys]);
    const handleCleanOption = useCallback(() => {
      if (!setOptionDataSource || !setOptionSelectedKeys) return;
      // Retrieve the database array from the form
      const database: MondaiItem[] = form.getFieldValue('database') || [];

      // Extract visit_dates where option_checked is true
      const validVisitDatesWithOption = database
        .filter((item: any) => item.option_checked)
        .map(item => item.visit_date)
        .filter(Boolean);
      // Check if there is at least one valid visit_date with option_checked true
      const hasValidOption = validVisitDatesWithOption.length > 0;

      // Update optionSelectedKeys to remove rowIds of deleted records
      setOptionDataSource(prev => {
        // If no valid option_checked records, return empty array
        // Otherwise, filter out records where treatment_date is not in validVisitDatesWithOption
        const updatedDataSource = hasValidOption
          ? prev.filter(item => validVisitDatesWithOption.includes(item.treatment_date))
          : [];

        const removedRowIds = prev
          .filter(item => !updatedDataSource.some(opt => opt.rowId === item.rowId))
          .map(item => item?.rowId?.toString());

        if (removedRowIds.length > 0) {
          setOptionSelectedKeys(prevKeys => prevKeys.filter(key => !removedRowIds.includes(key)));
        }

        return updatedDataSource;
      });
    }, [form, setOptionDataSource, setOptionSelectedKeys]);
    const getRemovedOptionDates = useCallback((): string[] => {
      const database: MondaiItem[] = form.getFieldValue('database') || [];

      const validVisitDatesWithOption = new Set(
        database
          .filter(item => item.option_checked)
          .map(item => item.visit_date)
          .filter(Boolean)
      );

      return (optionDataSource ?? [])
        .filter(item => !validVisitDatesWithOption.has(item.treatment_date))
        .map(item => item.treatment_date);
    }, [form, optionDataSource]);

    const getRemovedCourseDates = useCallback((): string[] => {
      const database: MondaiItem[] = form.getFieldValue('database') || [];

      const validVisitDates = new Set(
        database
          .map((item, index) => ({ visit_date: item.visit_date, index }))
          .filter(
            item =>
              item.visit_date &&
              (form.getFieldValue(['database', item.index, 'payment_type']) ===
                EPaymentType.SELF_PAID ||
                form.getFieldValue(['database', item.index, 'option_checked']))
          )
          .map(item => item.visit_date)
      );

      return (courseDataSource ?? [])
        .filter(item => !validVisitDates.has(item.treatment_date))
        .map(item => item.treatment_date);
    }, [form, courseDataSource]);

    const handleCancelHideCourseOption = useCallback(
      ({
        name,
        changeType,
        removedOptionDates,
        removedCourseDates,
      }: {
        name: number;
        changeType: 'visit_date' | 'payment_type' | 'option_checked';
        removedOptionDates: string[];
        removedCourseDates: string[];
      }) => {
        // Handle Option-related cancellations
        if (removedOptionDates.length) {
          if (changeType === 'visit_date') {
            form.setFieldValue(['database', name, 'visit_date'], removedOptionDates[0]);
          }
          if (changeType === 'option_checked') {
            form.setFieldValue(['database', name, 'option_checked'], true);
          }
        }

        // Handle Course-related cancellations
        if (removedCourseDates.length) {
          if (changeType === 'visit_date') {
            form.setFieldValue(['database', name, 'visit_date'], removedCourseDates[0]);
          }
          if (changeType === 'payment_type') {
            form.setFieldValue(['database', name, 'payment_type'], EPaymentType.SELF_PAID);
          }
          if (changeType === 'option_checked') {
            form.setFieldValue(['database', name, 'option_checked'], true);
          }
        }
        calculateFirstActiveMondai(form.getFieldValue('database') || []);
      },
      [form, calculateFirstActiveMondai]
    );
    const handleWarningRemoveCourseOption = useCallback(
      (
        name: number,
        changeType: 'visit_date' | 'payment_type' | 'option_checked'
      ): Promise<boolean> => {
        const removedOptionDates = getRemovedOptionDates();
        const removedCourseDates = getRemovedCourseDates();

        if (!removedOptionDates.length && !removedCourseDates.length) {
          return Promise.resolve(true);
        }

        return new Promise<boolean>(resolve => {
          showWarningDialog({
            message: WARNING_REMOVE_COURSE_OPTION,
            buttons: [
              {
                type: 'cancel',
                label: 'キャンセル',
                onClick: () => {
                  hideWarningDialog();
                  handleCancelHideCourseOption({
                    name,
                    changeType,
                    removedOptionDates,
                    removedCourseDates,
                  });
                  resolve(false);
                },
              },
              {
                type: 'confirm',
                label: REMOVE_CONFIRM,
                onClick: () => {
                  hideWarningDialog();
                  showFlashNotice({
                    message: NOTICE_COMMON_MESSAGE.COURSE_OPTION_UPDATED,
                    type: 'success',
                  });
                  resolve(true);
                },
              },
            ],
            onCancel: () => {
              handleCancelHideCourseOption({
                name,
                changeType,
                removedOptionDates,
                removedCourseDates,
              });

              resolve(false);
            },
          });
        });
      },
      [
        getRemovedCourseDates,
        getRemovedOptionDates,
        hideWarningDialog,
        showWarningDialog,
        handleCancelHideCourseOption,
      ]
    );
    const handleChangeVisitDate = useCallback(
      async (visitDate: string, name: number) => {
        const isVisitDateChangeChangableCourseOption = await handleWarningRemoveCourseOption(
          name,
          'visit_date'
        );
        if (!isVisitDateChangeChangableCourseOption) return;

        const currentVisitRegistered = allRegisteredVisit.find(item => item.value === visitDate);
        form.setFieldValue(['database', name, 'visit_id'], currentVisitRegistered?.visit_id);
        form.setFieldValue(
          ['database', name, 'doctor_id'],
          currentVisitRegistered?.doctor?.user_id
        );
        form.setFieldValue(
          ['database', name, 'option_checked'],
          currentVisitRegistered?.addon_option_checked
        );

        form.setFieldValue(
          ['database', name, 'payment_type'],
          currentVisitRegistered?.payment_type_checked
        );
        if (currentVisitRegistered?.payment_type_checked === EPaymentType.SELF_PAID) {
          handleAddCourse({ visitDate, visitRegistered: currentVisitRegistered });
        }
        // handle tại đây nữa cơ
        if (currentVisitRegistered?.addon_option_checked) {
          handleAddOption({ value: currentVisitRegistered?.addon_option_checked, name });
        }
        if (
          !!currentVisitRegistered?.payment_type_checked &&
          currentVisitRegistered?.payment_type_checked !== EPaymentType.SELF_PAID &&
          currentVisitRegistered?.addon_option_checked
        ) {
          handleAddCourse({ visitDate, visitRegistered: currentVisitRegistered });
        }
        checkCourseIsNullable(name);
        handleCleanCourse();
        handleCleanOption();
        handleSortMondaiItem(visitDate, name);
      },
      [
        allRegisteredVisit,
        form,
        handleAddCourse,
        handleAddOption,
        handleCleanCourse,
        handleCleanOption,
        handleSortMondaiItem,
        checkCourseIsNullable,
        handleWarningRemoveCourseOption,
      ]
    );
    const clearInjuryName = useCallback(
      (oldValue: EPaymentType, newValue: EPaymentType, name: number) => {
        const currentInjuryName = form.getFieldValue(['database', name, 'injury_name']);
        const currentInjuryNameOther = form.getFieldValue(['database', name, 'injury_name_other']);

        const clearInjuryProps = () => {
          form.setFieldValue(['database', name, 'part_type'], null);
          form.setFieldValue(['database', name, 'part_id'], null);
          form.setFieldValue(['database', name, 'lr_type'], null);
          form.setFieldValue(['database', name, 'bui_cd'], null);
        };

        switch (oldValue) {
          case EPaymentType.HEALTH_INSURANCE:
            switch (newValue) {
              case EPaymentType.TRANSPORT_ACCIDENT:
                form.setFieldValue(['database', name, 'injury_name_other'], currentInjuryName);
                break;
              case EPaymentType.WORK_ACCIDENT:
                form.setFieldValue(['database', name, 'injury_name_other'], currentInjuryName);
                break;
              case EPaymentType.SELF_PAID:
                form.setFieldValue(['database', name, 'injury_name'], currentInjuryName);
                break;
              default:
                break;
            }
            break;
          case EPaymentType.TRANSPORT_ACCIDENT:
            switch (newValue) {
              case EPaymentType.HEALTH_INSURANCE:
                form.setFieldValue(['database', name, 'injury_name'], '');
                clearInjuryProps();
                break;
              case EPaymentType.WORK_ACCIDENT:
                form.setFieldValue(['database', name, 'injury_name_other'], currentInjuryNameOther);
                break;
              case EPaymentType.SELF_PAID:
                form.setFieldValue(['database', name, 'injury_name'], currentInjuryNameOther);
                break;
            }
            break;
          case EPaymentType.WORK_ACCIDENT:
            switch (newValue) {
              case EPaymentType.HEALTH_INSURANCE:
                form.setFieldValue(['database', name, 'injury_name'], '');
                clearInjuryProps();
                break;
              case EPaymentType.TRANSPORT_ACCIDENT:
                form.setFieldValue(['database', name, 'injury_name_other'], currentInjuryNameOther);
                break;
              case EPaymentType.SELF_PAID:
                form.setFieldValue(['database', name, 'injury_name'], currentInjuryNameOther);
                break;
            }
            break;
          case EPaymentType.SELF_PAID:
            switch (newValue) {
              case EPaymentType.HEALTH_INSURANCE:
                clearInjuryProps();
                form.setFieldValue(['database', name, 'injury_name'], '');
                break;
              case EPaymentType.TRANSPORT_ACCIDENT:
                clearInjuryProps();
                form.setFieldValue(['database', name, 'injury_name_other'], '');
                break;
              case EPaymentType.WORK_ACCIDENT:
                clearInjuryProps();
                form.setFieldValue(['database', name, 'injury_name_other'], '');
                break;
              default:
                break;
            }
            break;
          default:
            break;
        }
      },
      [form]
    );
    const handleCheckPaymentType = useCallback(
      async (paymentType: EPaymentType, name: number) => {
        const oldValue = virtualDatabaseMap.current?.[name]?.payment_type as EPaymentType;
        const newValue = paymentType;
        const currentFirstVisitDate = form.getFieldValue(['database', name, 'visit_date']);
        calculateFirstActiveMondai(form.getFieldValue('database') || []);
        if (currentFirstVisitDate && paymentType === EPaymentType.SELF_PAID) {
          handleAddCourse({ visitDate: currentFirstVisitDate });
          clearInjuryName(oldValue, newValue, name);
          virtualDatabaseMap.current = (form.getFieldValue('database') || []).map(
            (item: MondaiItem) => ({
              payment_type: item.payment_type,
            })
          );
          return;
        } else {
          const isVisitDateChangeChangble = await handleWarningRemoveCourseOption(
            name,
            'payment_type'
          );
          if (!isVisitDateChangeChangble) return;
        }

        checkCourseIsNullable(name);
        handleCleanCourse();
        form.setFields([{ name: ['database', name, 'injury_name'] as any, errors: [] }]);
        form.setFields([{ name: ['database', name, 'injury_name_other'] as any, errors: [] }]);
        clearInjuryName(oldValue, newValue, name);
        virtualDatabaseMap.current = (form.getFieldValue('database') || []).map(
          (item: MondaiItem) => ({
            payment_type: item.payment_type,
          })
        );
      },
      [
        clearInjuryName,
        form,
        handleAddCourse,
        handleWarningRemoveCourseOption,
        calculateFirstActiveMondai,
        checkCourseIsNullable,
        handleCleanCourse,
        virtualDatabaseMap,
      ]
    );

    const handleFormItemClick = (name: number) => {
      // when click will get the form state before change
      const currentMondai = form.getFieldValue(['database', name]) as MondaiItem;
      setSelectedMondai(currentMondai);
    };
    useEffect(() => {
      if (hasSetUp) {
        const initialDatabase = form.getFieldValue('database') || [];
        calculateFirstActiveMondai(initialDatabase);
        setTimeout(() => {
          scrollTargetRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      }
    }, [hasSetUp, form]);
    useEffect(() => {
      if (!drawnImages.database || !form) return;

      const databases = form.getFieldValue(['database']) ?? [];
      const drawImagesKeys = Object.keys(drawnImages?.database || {});

      // Create a map to group mondai by date
      const dateToIndices: { [key: string]: number[] } = {};
      databases.forEach((item: MondaiItem, index: number) => {
        if (item.visit_date) {
          const dateKey = dayjs(item.visit_date).startOf('day').toISOString();
          if (!dateToIndices[dateKey]) {
            dateToIndices[dateKey] = [];
          }
          dateToIndices[dateKey].push(index);
        }
      });

      // Iterate through each mondai
      databases.forEach((item: MondaiItem, index: number) => {
        const diseaseId = String(item.disease_base_id ?? item.tmp_id);
        if (!drawImagesKeys.includes(diseaseId)) return;
        // Retrieve images from drawnImages
        const objectiveDrawImage = drawnImages.database?.[diseaseId]?.[DiseaseType.O] ?? [];
        const subjectDrawImage = drawnImages.database?.[diseaseId]?.[DiseaseType.T] ?? [];

        const updatedObjectiveImage: DiseaseImage[] = objectiveDrawImage.map(img => ({
          disease_base_image_id: img?.disease_base_image_id as number,
          url: img?.url,
          schema_image_id: img.schema_image_id as number,
        }));
        const updatedSubjectiveImage: DiseaseImage[] = subjectDrawImage.map(img => ({
          disease_base_image_id: img.disease_base_image_id as number,
          url: img.url,
          schema_image_id: img.schema_image_id as number,
        }));

        // Update images for the current mondai
        form.setFieldValue(['database', index, 'objective_images'], updatedObjectiveImage);
        form.setFieldValue(['database', index, 'treatment_images'], updatedSubjectiveImage);

        // Find and update mondai with the same date
        if (item?.visit_date) {
          const currentDate = dayjs(item.visit_date).startOf('day').toISOString();
          const sameDayIndices = dateToIndices[currentDate] || [];

          sameDayIndices.forEach(sameDayIndex => {
            if (sameDayIndex !== index) {
              // Update images for mondai with the same date
              form.setFieldValue(
                ['database', sameDayIndex, 'objective_images'],
                updatedObjectiveImage
              );
              form.setFieldValue(
                ['database', sameDayIndex, 'treatment_images'],
                updatedSubjectiveImage
              );
            }
          });
        }
      });
    }, [drawnImages, form]);
    useEffect(() => {
      const formContainer = containerRef.current;
      if (!formContainer) return;

      let isDragging = false;
      let startX = 0;
      let scrollLeft = 0;

      const handleMouseDown = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        if (
          target instanceof HTMLInputElement ||
          target instanceof HTMLTextAreaElement ||
          target instanceof HTMLSelectElement ||
          target instanceof HTMLSpanElement ||
          target.closest('.ant-select-dropdown') ||
          target.closest('.ant-picker-dropdown')
        ) {
          return;
        }

        isDragging = true;
        startX = e.pageX - formContainer.offsetLeft;
        scrollLeft = formContainer.scrollLeft;
        formContainer.style.cursor = 'grabbing';
      };

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDragging) return;

        const target = e.target as HTMLElement;
        if (
          !(target instanceof HTMLInputElement) &&
          !(target instanceof HTMLTextAreaElement) &&
          !(target instanceof HTMLSelectElement) &&
          !(target instanceof HTMLSpanElement) &&
          !target.closest('.ant-select-dropdown') &&
          !target.closest('.ant-picker-dropdown')
        ) {
          e.preventDefault();
        }

        const x = e.pageX - formContainer.offsetLeft;
        const walk = (x - startX) * 1.5;
        formContainer.scrollLeft = scrollLeft - walk;
      };

      const handleMouseUp = () => {
        isDragging = false;
        formContainer.style.cursor = 'grab';
      };

      const handleMouseLeave = () => {
        isDragging = false;
        formContainer.style.cursor = 'grab';
      };

      formContainer.addEventListener('mousedown', handleMouseDown);
      formContainer.addEventListener('mousemove', handleMouseMove);
      formContainer.addEventListener('mouseup', handleMouseUp);
      formContainer.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        if (formContainer) {
          formContainer.removeEventListener('mousedown', handleMouseDown);
          formContainer.removeEventListener('mousemove', handleMouseMove);
          formContainer.removeEventListener('mouseup', handleMouseUp);
          formContainer.removeEventListener('mouseleave', handleMouseLeave);
        }
      };
    }, []);

    const handleChangeOption = useCallback(
      async (value: boolean, name: number) => {
        if (value) {
          handleAddOption({ value, name });
          handleAddCourse({ visitDate: form.getFieldValue(['database', name, 'visit_date']) });
        } else {
          const isVisitDateChangeChangbleCourseOption = await handleWarningRemoveCourseOption(
            name,
            'option_checked'
          );
          if (!isVisitDateChangeChangbleCourseOption) return;
        }

        checkCourseIsNullable(name);
        handleCleanOption();
        handleCleanCourse();
        calculateFirstActiveMondai(form.getFieldValue('database') || []);
      },
      [
        form,
        handleAddCourse,
        handleAddOption,
        handleCleanCourse,
        handleCleanOption,
        calculateFirstActiveMondai,
        checkCourseIsNullable,
        handleWarningRemoveCourseOption,
      ]
    );
    useEffect(() => {
      if (!hasSetUp) return;
      const checkOverflow = () => {
        const element = containerRef.current;
        if (element) {
          const hasOverflowX = element.scrollWidth > element.clientWidth;
          const hasOverflowY = element.scrollHeight > element.clientHeight;
          if (hasOverflowX || hasOverflowY) {
            element.style.paddingBottom = '16px';
          } else {
            element.style.paddingBottom = '0';
          }
        }
      };
      checkOverflow();
    }, [hasSetUp]);
    return (
      <>
        <Flex gap="middle" className={clsx(styles.upsert_database_form)} ref={containerRef}>
          <Flex vertical gap="middle">
            <Form.List name="database">
              {fields => {
                // (fields, { add, remove }) -> Ant Design add and remove function 😇

                return (
                  <Flex gap="large">
                    {fields.map(({ key, name, ...restField }) => {
                      const isLast = name === (fields ?? []).length - 1;
                      const currentMondai: MondaiItem = form.getFieldValue(['database', name]);
                      const previousMondai: MondaiItem | null =
                        name === 0 ? null : form.getFieldValue(['database', name - 1]);
                      const tmpDbO: DiseaseImage[] = form.getFieldValue([
                        'database',
                        name,
                        'objective_images',
                      ]);
                      const tmpDbT: DiseaseImage[] = form.getFieldValue([
                        'database',
                        name,
                        'treatment_images',
                      ]);
                      // Selected Mondai: use to which mondai is currenntly selected
                      // - Case 1: mondai is created -> use disease_base_id
                      // - Case 2: mondai is temporarily saved -> use tmp_id (added when click add mondai)

                      const isSelectedMondai =
                        (selectedMondai?.tmp_id === currentMondai.tmp_id &&
                          !currentMondai.disease_base_id) ||
                        (selectedMondai?.disease_base_id === currentMondai.disease_base_id &&
                          !!currentMondai.disease_base_id);

                      // isAssignedScrollMondai: to identify which mondai to assigne ref for scroling
                      // - Case 1: Assign last mondai : When  Add new mondai , initial set thus selected mondai is null:
                      // - Case 2: Assign current updating mondai: when selectedMondai is selected  (only when changing visit date )
                      //           For less function calls
                      // - Case 3: Assign to specific mondai contain disease_base_id: for show detail mondai from showing history
                      const isAssignedScrollMondai =
                        (!selectedMondai && isLast && !disease_base_id) ||
                        (selectedMondai && isSelectedMondai) ||
                        (disease_base_id === currentMondai.disease_base_id &&
                          !!currentMondai.disease_base_id);


                      const hasVisitDate = !!currentMondai?.visit_date;

                      const isInjuryNameDisabled = !!currentMondai?.part_type;
                      const isFormDisabled = currentMondai?.status === EStatus.INACTIVE;

                      const isActiveMondai = isFirstActiveMondai[name];
                      const isHideInputs = !isActiveMondai && hasVisitDate;

                      const traumaName = `${currentMondai?.trauma_name ?? ''}${
                        currentMondai?.trauma_counter ?? ''
                      } `;
                      const registeredMondaiName =
                        currentMondai?.trauma_counter &&
                        (currentMondai?.injury_name || currentMondai?.injury_name_other)
                          ? `M${currentMondai.trauma_counter} : ${
                              currentMondai.injury_name || currentMondai.injury_name_other
                            }`
                          : '';
                      let currentVisitDateOptions: RegisteredVisit[] = [];

                      if (
                        currentMondai?.disease_base_id &&
                        allRegisteredVisit &&
                        initialDatabaseMap
                      ) {
                        const mappedVisitDate =
                          previousMondai &&
                          initialDatabaseMap[previousMondai.disease_base_id as number]?.visit_date;
                        const firstKeikaDate = currentMondai.first_keika_date;
                        currentVisitDateOptions = allRegisteredVisit.filter(item => {
                          const itemDate = dayjs(item.value);
                          return (
                            (mappedVisitDate
                              ? itemDate.isAfter(dayjs(mappedVisitDate), 'day') ||
                                itemDate.isSame(dayjs(mappedVisitDate), 'day')
                              : true) &&
                            (firstKeikaDate
                              ? itemDate.isBefore(dayjs(firstKeikaDate), 'day')
                              : true)
                          );
                        });
                      } else {
                        currentVisitDateOptions = availableRegisteredVisit;
                      }

                      const seen = new Set<string>();
                      // to avoid duplicate doctor and to get previous docor
                      const currentDoctor =
                        currentMondai?.visit_date &&
                        currentMondai.disease_base_id &&
                        initialDoctorByVisitDate[currentMondai?.disease_base_id as number]
                          ?.visit_date === currentMondai.visit_date
                          ? [
                              ...doctorList,
                              ...(
                                initialDoctorByVisitDate[
                                  currentMondai?.disease_base_id as number
                                ] ?? []
                              ).doctors,
                            ].filter(doctor => {
                              const key = `${doctor.user_id}`;
                              if (!seen.has(key)) {
                                seen.add(key);
                                return true;
                              }
                              return false;
                            })
                          : doctorList;

                      // }));
                      return (
                        <Flex
                          ref={isAssignedScrollMondai ? scrollTargetRef : undefined}
                          key={key}
                          vertical
                          align="start"
                          gap="middle"
                        >
                          <Button
                            customType={ButtonType.PRIMARY}
                            onClick={() => {
                              setIsSetFromHistoryModalOpen(name);
                            }}
                            disabled={!hasVisitDate || isFormDisabled}
                          >
                            履歴からセット
                          </Button>
                          <div
                            className={clsx(styles.database_form_group, {
                              [styles.first_same_day_active_item]: isActiveMondai,
                              [styles.is_form_disabled]: isFormDisabled,
                              [styles.is_selected_mondai]:
                                disease_base_id && isAssignedScrollMondai,
                            })}
                            data-index={name}
                            id={`database_form_group_${name}`}
                          >
                            <div className={styles.database_form_item}>
                              <LabelRender
                                required
                                label="初診日"
                                mondai_name={registeredMondaiName}
                              />
                              <AppFormItem
                                {...restField}
                                name={[name, 'visit_date']}
                                rules={
                                  currentMondai?.status === EStatus.INACTIVE
                                    ? []
                                    : [
                                        {
                                          required: true,
                                          message: ERROR_COMMON_MESSAGE.REQUIRED('初診日'),
                                        },
                                      ]
                                }
                              >
                                <AppSelect
                                  suffixIcon={<Icon width={20} height={20} name="calendar" />}
                                  options={currentVisitDateOptions}
                                  disabled={isFormDisabled}
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                                  onChange={value => {
                                    handleChangeVisitDate(value, name);
                                  }}
                                  onFocus={() => handleFormItemClick(name)}
                                  allowClear={false}
                                  size="small"
                                  getPopupContainer={triggerNode => {
                                    return triggerNode.parentNode || document.body;
                                  }}
                                />
                              </AppFormItem>
                            </div>

                            {serviceId == EKaruteServieId.Judo && (
                              <>
                                <AppDivider className="my-4" />

                                <div className={clsx(styles.database_form_item)}>
                                  <LabelRender required label="負傷日" />
                                  <AppFormItem
                                    {...restField}
                                    name={[name, 'injury_date']}
                                    rules={
                                      currentMondai?.status === EStatus.INACTIVE || !hasVisitDate
                                        ? []
                                        : [
                                            {
                                              validator: (_, value, callback) => {
                                                const paymentType = form.getFieldValue([
                                                  'database',
                                                  name,
                                                  'payment_type',
                                                ]);

                                                if (
                                                  paymentType === EPaymentType.SELF_PAID &&
                                                  !value
                                                ) {
                                                  callback();
                                                  return;
                                                }
                                                if (!value) {
                                                  callback(ERROR_COMMON_MESSAGE.REQUIRED('負傷日'));
                                                  return;
                                                }
                                                const visitDate = form.getFieldValue([
                                                  'database',
                                                  name,
                                                  'visit_date',
                                                ]);
                                                if (dayjs(value).isAfter(dayjs(visitDate))) {
                                                  callback(
                                                    ERROR_COMMON_MESSAGE.INJURY_DATE_AFTER_VISIT_DATE
                                                  );
                                                  return;
                                                }

                                                callback();
                                              },
                                            },
                                          ]
                                    }
                                  >
                                    <AppDatePicker
                                      className={`${styles.date_picker_input} fs14-regular`}
                                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                                      format={DATE_FORMATS.DATE}
                                      disabled={!hasVisitDate || isFormDisabled}
                                      disabledDate={current =>
                                        current && current > dayjs().endOf('day')
                                      }
                                      inputReadOnly
                                      size="small"
                                      getPopupContainer={triggerNode => {
                                        return triggerNode.parentNode as HTMLElement;
                                      }}
                                    />
                                  </AppFormItem>
                                </div>
                              </>
                            )}
                            <AppDivider className="my-4" />
                            <div className={clsx(styles.database_form_item)}>
                              <LabelRender
                                required
                                label="主訴（S）"
                                secondBtn={{
                                  label: '参照',
                                  onClick: () =>
                                    handleOpenReference({
                                      name,
                                      code: EKaruteFieldCode.S,
                                      label: '主訴（S）',
                                      field: 'subjective',
                                    }),
                                  disabled: !hasVisitDate || isFormDisabled,
                                }}
                              />
                              <AppFormItem {...restField} name={[name, 'subjective']} required>
                                <AppTextArea
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                  maxLength={MAX_TEXT_LENGTH}
                                  disabled={!hasVisitDate || isFormDisabled}
                                  style={{ minHeight: '112px' }}
                                />
                              </AppFormItem>
                            </div>
                            <AppDivider className="my-4" />
                            <div className={clsx(styles.database_form_item)}>
                              <LabelRender
                                required
                                label="現病歴（S）"
                                secondBtn={{
                                  label: '参照',
                                  onClick: () =>
                                    // TO-DO: Ask chi.vu
                                    handleOpenReference({
                                      name,
                                      field: 'present_illness',
                                      label: '現病歴（S）',
                                      code: EKaruteFieldCode.S,
                                    }),
                                  disabled: !hasVisitDate || isFormDisabled,
                                }}
                              />
                              <AppFormItem {...restField} name={[name, 'present_illness']} required>
                                <AppTextArea
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                  maxLength={MAX_TEXT_LENGTH}
                                  disabled={!hasVisitDate || isFormDisabled}
                                  style={{ minHeight: '112px' }}
                                />
                              </AppFormItem>
                            </div>
                            <AppDivider className="my-4" />
                            <div
                              className={clsx(styles.database_form_item)}
                              style={{ minHeight: '150px' }}
                            >
                              <div className={clsx(isHideInputs && styles.disable_form_item)}>
                                <LabelRender
                                  required
                                  label="現症（O）"
                                  firstBtn={{
                                    label: 'シェーマ',
                                    onClick: () => {
                                      handleSchema(DiseaseType.O, name);
                                    },
                                    disabled: !hasVisitDate || isFormDisabled,
                                  }}
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        code: EKaruteFieldCode.O,
                                        label: '現症（O）',
                                        field: 'objective',
                                      }),
                                    disabled: !hasVisitDate || isFormDisabled,
                                  }}
                                />
                                <div
                                  className={clsx(styles.schema_form_item)}
                                  style={{ minHeight: '112px', height: '112px' }}
                                >
                                  <AppFormItem {...restField} name={[name, 'objective']}>
                                    <AppTextArea
                                      style={{ border: 'none' }}
                                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                      maxLength={MAX_TEXT_LENGTH}
                                      disabled={!hasVisitDate || isFormDisabled}
                                      autoSize
                                    />
                                  </AppFormItem>
                                  <div
                                    className={clsx(
                                      styles.imagePreview,
                                      (!hasVisitDate || isFormDisabled) &&
                                        styles.schema_form_item_disabled
                                    )}
                                  >
                                    <figure>
                                      {(tmpDbO ?? []).map(item => (
                                        <img src={item.url} key={item?.disease_base_image_id} />
                                      ))}
                                    </figure>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <AppDivider className="my-4" />
                            <div className={clsx(styles.database_form_item)}>
                              <LabelRender
                                required
                                label="病態把握（A）"
                                secondBtn={{
                                  label: '参照',
                                  onClick: () =>
                                    handleOpenReference({
                                      name,
                                      code: EKaruteFieldCode.A,
                                      label: '病態把握（A）',
                                      field: 'assessment',
                                    }),
                                  disabled: !hasVisitDate || isFormDisabled,
                                }}
                              />
                              <AppFormItem {...restField} name={[name, 'assessment']}>
                                <AppTextArea
                                  style={{ minHeight: '112px' }}
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                  maxLength={MAX_TEXT_LENGTH}
                                  disabled={!hasVisitDate || isFormDisabled}
                                />
                              </AppFormItem>
                            </div>
                            <AppDivider className="my-4" />
                            <div
                              className={clsx(styles.database_form_item)}
                              style={{ minHeight: '150px' }}
                            >
                              <div className={isHideInputs ? styles.disable_form_item : ''}>
                                <LabelRender
                                  required
                                  label="施術計画（P）"
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        code: EKaruteFieldCode.P,
                                        label: '施術計画（P）',
                                        field: 'plan',
                                      }),
                                    disabled: !hasVisitDate || isFormDisabled,
                                  }}
                                />
                                <AppFormItem {...restField} name={[name, 'plan']}>
                                  <AppTextArea
                                    style={{ minHeight: '112px' }}
                                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                    maxLength={MAX_TEXT_LENGTH}
                                    disabled={!hasVisitDate || isFormDisabled}
                                  />
                                </AppFormItem>
                              </div>
                            </div>

                            <AppDivider className="my-4" />

                            <InjuryNamePaymenFormItem
                              currentMondai={currentMondai}
                              name={name}
                              traumaName={traumaName}
                              serviceId={serviceId}
                              handleInjuryName={handleInjuryName}
                              handleCheckPaymentType={handleCheckPaymentType}
                              handleChangeOption={handleChangeOption}
                              restField={restField}
                              hasVisitDate={hasVisitDate}
                              isFormDisabled={isFormDisabled}
                              isInjuryNameDisabled={isInjuryNameDisabled}
                            />

                            <AppDivider className="my-4" />
                            <div
                              style={{ minHeight: '150px' }}
                              className={styles.database_form_item}
                            >
                              <LabelRender
                                required
                                label="施術（T）"
                                firstBtn={{
                                  label: 'シェーマ',
                                  onClick: () => {
                                    handleSchema(DiseaseType.T, name);
                                  },
                                  disabled: !hasVisitDate || isFormDisabled,
                                  hidden: isHideInputs,
                                }}
                                secondBtn={{
                                  label: '参照',
                                  onClick: () =>
                                    handleOpenReference({
                                      name: name,
                                      code: EKaruteFieldCode.T,
                                      label: '施術（T）',
                                      field: 'treatment',
                                    }),
                                  disabled: !hasVisitDate || isFormDisabled,
                                  hidden: isHideInputs,
                                }}
                              />

                              <div
                                className={clsx(
                                  isHideInputs && styles.disable_form_item,
                                  styles.schema_form_item
                                )}
                                style={{ minHeight: '112px', height: '112px' }}
                              >
                                <AppFormItem {...restField} name={[name, 'treatment']} required>
                                  <AppTextArea
                                    style={{ border: 'none' }}
                                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                    maxLength={MAX_TEXT_LENGTH}
                                    disabled={!hasVisitDate || isFormDisabled}
                                    autoSize
                                  />
                                </AppFormItem>
                                <div
                                  className={clsx(
                                    styles.imagePreview,
                                    (!hasVisitDate || isFormDisabled) &&
                                      styles.schema_form_item_disabled
                                  )}
                                >
                                  <figure>
                                    {(tmpDbT ?? []).map(item => (
                                      <img src={item.url} key={item.disease_base_image_id} />
                                    ))}
                                  </figure>
                                </div>
                              </div>
                            </div>
                            <AppDivider className="my-4" />
                            <div
                              className={clsx(styles.database_form_item)}
                              style={{ minHeight: '150px' }}
                            >
                              <div className={clsx(isHideInputs && styles.disable_form_item)}>
                                <LabelRender
                                  label="備考（R）"
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        field: 'remarks',
                                        code: EKaruteFieldCode.R,
                                        label: '備考（R）',
                                      }),
                                    disabled: !hasVisitDate || isFormDisabled,
                                  }}
                                />
                                <AppFormItem {...restField} name={[name, 'remarks']}>
                                  <AppTextArea
                                    style={{ minHeight: '112px' }}
                                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                    maxLength={MAX_TEXT_LENGTH}
                                    disabled={!hasVisitDate || isFormDisabled}
                                  />
                                </AppFormItem>
                              </div>
                            </div>

                            <AppDivider className="my-4" />
                            <div className={clsx(styles.database_form_item)}>
                              <LabelRender required label="施術師" />
                              <AppFormItem {...restField} name={[name, 'doctor_id']}>
                                <AppSelect
                                  listHeight={180}
                                  options={transformTreatersToOptions(currentDoctor)}
                                  disabled={!hasVisitDate || isFormDisabled}
                                  showSearch
                                  filterOption={(input, option) =>
                                    String(option?.label)
                                      .toLowerCase()
                                      .trim()
                                      .startsWith(input.toLowerCase().trim())
                                  }
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                                  getPopupContainer={triggerNode => {
                                    return triggerNode.parentNode || document.body;
                                  }}
                                />
                              </AppFormItem>
                              {/* <UpsertDatabaseDoctorFormItem
                                currentDoctor={currentDoctor}
                                hasVisitDate={hasVisitDate}
                                isFormDisabled={isFormDisabled}
                                restField={restField}
                                name={name}
                              /> */}
                            </div>
                          </div>
                        </Flex>
                      );
                    })}
                  </Flex>
                );
              }}
            </Form.List>
          </Flex>
        </Flex>
        {isInjuyNameModalOpen && (
          <InjuryNameModal
            open
            initialValue={isInjuyNameModalOpen?.initialValue}
            paymentType={isInjuyNameModalOpen?.paymentType}
            onApply={payload => {
              setIsInjuyNameModalOpen(null);
              handleChangeInjuryName(payload);
            }}
            onCancel={() => setIsInjuyNameModalOpen(null)}
          />
        )}
      </>
    );
  }
);
