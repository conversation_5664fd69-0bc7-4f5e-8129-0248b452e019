import Loading from '@/components/common/core/Loading/Loading';
import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import PageLayout from '@/components/layouts/PageLayout';
import { routerPaths } from '@/types/constants/routerPath';
import { useGetDetailsPatientQuery } from '@/store/patient/api';
import { createFormValues } from '@/utils/helper/formValues';
import { Flex } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import FormUpdatePatient from '../../../components/page-components/patient-management/update/FormUpdate/FormUpdatePatient';
import InsuranceInfo from '../../../components/page-components/patient-management/update/InsuranceInfo/InsuranceInfo';
import MedicalBenefits from '../../../components/page-components/patient-management/update/MedicalBenefits/MedicalBenefits';
import PatientUpdateAction from '../../../components/page-components/patient-management/update/PatientUpdateAction/PatientUpdateAction';
import { useRouterPath } from '@/hooks/useRouterPath';
import { InsuranceInfoSchema } from '@/components/page-components/patient-management/update/InsuranceInfo/type';
import { MedicalBenefitsSchema } from '@/components/page-components/patient-management/update/MedicalBenefits/type';
import { ScrollToTopOnMount } from '@/components/common/features/ScrollToTopOnMount/ScrollToTopOnMount';

export default function UpdatePatientPage() {
  const formRef = useRef<any>(null);
  const params = useParams();
  const { getPath } = useRouterPath();
  const [insuranceInfo, setInsuranceInfo] = useState<InsuranceInfoSchema>({});
  const [medicalBenefits, setMedicalBenefits] = useState<MedicalBenefitsSchema>({});
  const { data: patientDetails, isLoading } = useGetDetailsPatientQuery(Number(params.id), {
    refetchOnMountOrArgChange: true,
  });
  const [initialValues, setInitialValues] = useState<any>({});

  useEffect(() => {
    if (!patientDetails || !patientDetails.data) return;

    const data = patientDetails.data;
    setInsuranceInfo({
      hoken_type: data?.defaultInsurance?.hoken_type?.toString(),
      hoken_name: data?.defaultInsurance?.hoken_name,
      hokensya_type: data?.defaultInsurance?.hokensya_type,
      shoukan_flg: data?.defaultInsurance?.shoukan_flg,
      hoken_no: data?.defaultInsurance?.hoken_no,
      futan_ritsu: data?.defaultInsurance?.futan_ritsu,
      futan_shubetsu: data?.defaultInsurance?.futan_shubetsu?.toString(),
      hoken_kigo: data?.defaultInsurance?.hoken_kigo,
      hoken_bango: data?.defaultInsurance?.hoken_bango,
      hoken_shikaku_date: data?.defaultInsurance?.hoken_shikaku_date
        ? dayjs(data?.defaultInsurance?.hoken_shikaku_date)
        : null,
      hoken_yuko_date: data?.defaultInsurance?.hoken_yuko_date
        ? dayjs(data?.defaultInsurance?.hoken_yuko_date)
        : null,
    });
    setMedicalBenefits({
      kohi_type: data?.defaultInsurance?.kohi_type?.toString(),
      kohi_no: data?.defaultInsurance?.kohi_no,
      kohi_jukyusya_no: data?.defaultInsurance?.kohi_jukyusya_no,
      kohi_ritsu: data?.defaultInsurance?.kohi_ritsu,
      kohi_jyosei_flg: data?.defaultInsurance?.kohi_jyosei_flg,
      kohi_start_date: data?.defaultInsurance?.kohi_start_date
        ? dayjs(data?.defaultInsurance?.kohi_start_date)
        : null,
      kohi_end_date: data?.defaultInsurance?.kohi_end_date
        ? dayjs(data?.defaultInsurance?.kohi_end_date)
        : null,
      office_address: data?.defaultInsurance?.office_address,
      remarks: data?.remarks,
      office_name: data.defaultInsurance?.office_name,
      office_post: data.defaultInsurance?.office_post,
    });
    const formValues = createFormValues(
      [
        'patient_id',
        'kana',
        'name',
        'email',
        'patient_cmt',
        'cellphone',
        'post',
        'address1',
        'address2',
        'zokugara',
        'defaultInsurance',
        'cellphone',
      ],
      data
    );
    formValues.push({
      name: 'gender',
      value: data.gender,
    });
    formValues.push({
      name: 'birthday',
      value: data.birthday ? dayjs(data.birthday) : null,
    });

    formRef?.current?.setFields(formValues);
    setInitialValues(
      Object.entries(formValues).reduce(
        (acc, [key, value]) => ({ ...acc, [value.name]: value.value }),
        {}
      )
    );
  }, [formRef, patientDetails]);

  if (isLoading) return <Loading />;

  return (
    <PageLayout
      title={
        <Flex gap={20} align="center">
          <Flex gap={8}>
            患者詳細 <div style={{ fontSize: 7 }}>&#9679;</div> 編集
          </Flex>
          <span
            className="fs16-regular"
            style={{
              color: 'var(--gray-500)',
            }}
          >
            (患者番号: {patientDetails?.data?.patient_cd || ''})
          </span>
        </Flex>
      }
      breadcrumb={
        <AppBreadcrumb
          items={[
            {
              title: <a href={getPath(routerPaths.patientManagement.patientList)}>患者一覧</a>,
            },
            { title: '患者詳細・編集' },
          ]}
        />
      }
      headerRight={<PatientUpdateAction formRef={formRef} />}
    >
      <>
        <Flex gap={16} vertical>
          <ScrollToTopOnMount />
          <FormUpdatePatient ref={formRef} initialValues={initialValues} />
          <InsuranceInfo initialData={insuranceInfo} />
          <MedicalBenefits initialData={medicalBenefits} />
        </Flex>
      </>
    </PageLayout>
  );
}
