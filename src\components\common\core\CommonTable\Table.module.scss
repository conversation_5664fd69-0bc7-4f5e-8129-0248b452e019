.tableWrapper {
  width: 100%;
  overflow-x: auto;

  :global {
    .ant-table-header {
      .ant-table-cell {
        background-color: $brand-50;
        color: $gray-500;
        font-weight: 500;
        font-size: 12px;
        line-height: 18px;
        transition: background-color 0.3s;
      }
      .ant-table-cell {
        padding: 8px 12px !important;
      }
    }
    .ant-table-tbody {
      .ant-table-cell {
        color: $gray-800;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        padding: 8px 12px !important;
      }
    }

    .ant-pagination {
      .ant-pagination-item,
      .ant-pagination-item a {
        transition: none !important;
      }

      .ant-pagination-item-active,
      .ant-pagination-item-active a {
        background-color: $brand-800;
        color: white;
        border: none;
        transition: none !important;
        border-radius: 4px !important;
        box-shadow: none !important;
      }
    }
    .ant-table-header {
      border-top-left-radius: 0 !important;
      border-top-right-radius: 0 !important;
    }

    .ant-table-container table > thead > tr:first-child th:first-child {
      border-top-left-radius: 0 !important;
    }

    .ant-table-container table > thead > tr:first-child th:last-child {
      border-top-right-radius: 0 !important;
    }

    .ant-table {
      border-radius: 0 !important;
    }
    .ant-table-body,
    .ant-table-content {
      scrollbar-width: auto;
      scrollbar-color: auto;
    }

    .ant-table-body::-webkit-scrollbar,
    .ant-table-content::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .ant-table-body::-webkit-scrollbar-thumb,
    .ant-table-content::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: $gray-300;
    }
    .ant-table-expanded-row-fixed {
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      .ant-empty-image {
        height: 100%;
      }
    }
  }
}

.headerCell {
  transition: color 0.2s ease;
  &.sorted {
    scale: 1.01;
    color: $brand-600;
    font-weight: 600;
  }
  color: $gray-500;

  // fs12-medium
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  letter-spacing: 0;
}

.cellContentWrapper {
  color: $gray-800;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  // padding: 0 4px;

  // fs12-regular
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0;

  &.overflowed {
    cursor: pointer;
  }
}
