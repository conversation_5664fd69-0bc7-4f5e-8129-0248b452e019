import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';
import { APIResponse, ListResponse } from '@/types/interface';
import {
  ActiveCourses,
  SelectionMasterData,
  Visit,
  VisitItem,
  VisitListParams,
  VisitRegistrationPayload,
  VisitUpdatePayload,
} from './type';

const cleanParams = (params: VisitListParams) => {
  const cleaned: any = {};
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && (!Array.isArray(value) || value.length > 0)) {
      cleaned[key] = Array.isArray(value) ? value.join(',') : value;
    }
  });
  return cleaned;
};

export const visitApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    getSelectionMasterData: build.query<APIResponse<SelectionMasterData>, null>({
      query: () => ({
        url: 'api/selection',
        method: 'get',
      }),
    }),
    getListVisit: build.query<APIResponse<ListResponse<Visit>>, VisitListParams>({
      query: params => ({
        url: 'api/visits',
        method: 'get',
        params: cleanParams(params),
      }),
      providesTags: ['visit-list'],
    }),
    visitRegistration: build.mutation<APIResponse<any>, VisitRegistrationPayload>({
      query: payload => ({
        url: '/api/visits',
        body: payload,
        method: 'post',
      }),
    }),

    getVisit: build.query<APIResponse<VisitItem>, number>({
      query: param => ({
        url: `/api/visits/${param}`,
        method: 'get',
      }),
      providesTags: ['visit-list'],
    }),
    updateVisit: build.mutation<
      APIResponse<Visit | any>,
      { id: number; payload: VisitUpdatePayload }
    >({
      query: param => ({
        url: `/api/visits/${param.id}`,
        body: param.payload,
        method: 'put',
      }),
      invalidatesTags: ['visit-list'],
    }),
    getActiveCourse: build.query<APIResponse<ActiveCourses[]>, null>({
      query: () => ({
        url: `/api/courses/group-activate`,
        method: 'get',
      }),
    }),
    changeVisitStatusNotYet: build.mutation<APIResponse<any>, { patient_id: number }>({
      query: param => {
        return {
          url: `/api/visits/status/not-yet`,
          method: 'put',
          body: param,
        };
      },
      invalidatesTags: ['visit-list'],
    }),
    changeVisitStatusInProgress: build.mutation<APIResponse<any>, { patient_id: number }>({
      query: param => {
        return {
          url: `/api/visits/status/in-progress`,
          method: 'put',
          body: param,
        };
      },
    }),
  }),
});

export const {
  useGetSelectionMasterDataQuery,
  useLazyGetListVisitQuery,
  useUpdateVisitMutation,
  useLazyGetActiveCourseQuery,
  useVisitRegistrationMutation,
  useGetVisitQuery,
  useChangeVisitStatusNotYetMutation,
  useChangeVisitStatusInProgressMutation,
} = visitApi;
