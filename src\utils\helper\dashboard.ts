import { <PERSON>, OptionItem } from '@/store/visit/type';
import { VISIT_STATUS, VISIT_TABLE_ACTION } from '@/types/constants';
import dayjs from 'dayjs';

const transformTreatersToOptions = (
  treaters: Partial<Doctor>[] | null | undefined
): OptionItem[] => {
  const treaterArray = Array.isArray(treaters) ? treaters : [];
  return treaterArray.map(treater => ({
    label: treater?.kanji_name || '',
    value: treater?.user_id || 0,
    description: null,
  }));
};

const getDisabledAction = (status: number, key: string, filteredDate: dayjs.Dayjs) => {
  const currentDate = dayjs();
  if (filteredDate.isBefore(currentDate)) {
    switch (key) {
      case VISIT_TABLE_ACTION.REGISTER_VISIT: {
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return false;
          case VISIT_STATUS.NOT_CHECKED_IN:
            return false;
          case VISIT_STATUS.RECEIPT:
            return true;
          case VISIT_STATUS.TREATMENT:
            return true;
          case VISIT_STATUS.TEMP_KARUTE:
            return true;
          case VISIT_STATUS.CONFIRM_KARUTE:
            return true;
          default:
            return true;
        }
      }
      case VISIT_TABLE_ACTION.PATIENT_VIEW_AND_EDIT:
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return false;
          case VISIT_STATUS.NOT_CHECKED_IN:
            return false;
          case VISIT_STATUS.RECEIPT:
            return false;
          case VISIT_STATUS.TREATMENT:
            return false;
          case VISIT_STATUS.TEMP_KARUTE:
            return false;
          case VISIT_STATUS.CONFIRM_KARUTE:
            return false;
          default:
            return true;
        }
      case VISIT_TABLE_ACTION.ADD_KARUTE:
         switch (status) {
           case VISIT_STATUS.RECEIPT:
             return false;
           default:
             return true;
         }

      case VISIT_TABLE_ACTION.VISIT_VIEW_AND_EDIT:
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return true;
          case VISIT_STATUS.NOT_CHECKED_IN:
            return true;
          case VISIT_STATUS.RECEIPT:
            return false;
          case VISIT_STATUS.TREATMENT:
            return true;
          case VISIT_STATUS.TEMP_KARUTE:
            return true;
          case VISIT_STATUS.CONFIRM_KARUTE:
            return true;
          default:
            return true;
        }
    }
  } else if (filteredDate.isSame(currentDate, 'day')) {
    switch (key) {
      case VISIT_TABLE_ACTION.REGISTER_VISIT: {
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return false;
          case VISIT_STATUS.NOT_CHECKED_IN:
            return false;
          case VISIT_STATUS.RECEIPT:
            return true;
          case VISIT_STATUS.TREATMENT:
            return true;
          case VISIT_STATUS.TEMP_KARUTE:
            return true;
          case VISIT_STATUS.CONFIRM_KARUTE:
            return true;
          default:
            return true;
        }
      }
      case VISIT_TABLE_ACTION.PATIENT_VIEW_AND_EDIT:
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return false;
          case VISIT_STATUS.NOT_CHECKED_IN:
            return false;
          case VISIT_STATUS.RECEIPT:
            return false;
          case VISIT_STATUS.TREATMENT:
            return false;
          case VISIT_STATUS.TEMP_KARUTE:
            return false;
          case VISIT_STATUS.CONFIRM_KARUTE:
            return false;
          default:
            return true;
        }
      case VISIT_TABLE_ACTION.ADD_KARUTE:
        switch (status) {
          case VISIT_STATUS.RECEIPT:
            return false;
          default:
            return true;
        }
      case VISIT_TABLE_ACTION.VISIT_VIEW_AND_EDIT:
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return true;
          case VISIT_STATUS.NOT_CHECKED_IN:
            return true;
          case VISIT_STATUS.RECEIPT:
            return false;
          case VISIT_STATUS.TREATMENT:
            return true;
          case VISIT_STATUS.TEMP_KARUTE:
            return true;
          case VISIT_STATUS.CONFIRM_KARUTE:
            return true;
          default:
            return true;
        }
    }
  } else {
    // future day
    switch (key) {
      case VISIT_TABLE_ACTION.REGISTER_VISIT: {
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return true;
          default:
            return true;
        }
      }
      case VISIT_TABLE_ACTION.PATIENT_VIEW_AND_EDIT:
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return false;
          default:
            return true;
        }
      case VISIT_TABLE_ACTION.ADD_KARUTE:
        switch (status) {
          case VISIT_STATUS.RECEIPT:
            return false;
          default:
            return true;
        }
      case VISIT_TABLE_ACTION.VISIT_VIEW_AND_EDIT:
        switch (status) {
          case VISIT_STATUS.BOOKING_CANCEL:
            return true;
          case VISIT_STATUS.BOOKING:
            return true;
          default:
            return true;
        }
    }
  }
};

export { getDisabledAction, transformTreatersToOptions };
