$gap: 8px;
$tile: 80px;

.wrapper {
  gap: 12px;
  height: 100%;
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 12px;
  }

  .gridWrapper {
    height: 100%;
    overflow: auto;
    .blockTitle {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #205e78;
      margin-top: 12px;

      &::after {
        content: '';
        flex: 1;
        height: 1px;
        background-color: $gray-200;
      }
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(3, 80px);
      gap: 8px;
      padding: 4px 0;
    }
  }

  .uploadAction {
    display: flex;
    gap: 8px;
    flex-direction: column;

    :global(.ant-upload-wrapper),
    :global(.ant-upload.ant-upload-select) {
      width: 100%;
    }
  }

  .modeBar {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    color: $brand-600;
    align-self: flex-end;
  }

  .noData {
    color: $gray-500;
    width: 256px;
  }
}
