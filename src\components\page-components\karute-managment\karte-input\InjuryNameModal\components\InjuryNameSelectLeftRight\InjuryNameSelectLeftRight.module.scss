.leftRightWrapper {
  height: 100%;
  .block {
    width: 224px;
    display: flex;
    flex-direction: column;
    padding: 16px;
    min-height: calc(var(--diagram-max-height) + 60px);
    height: 100%;
    border-right: 1px solid $gray-200;

    .title {
      padding: 6px;
      border: 1px dashed $brand-600;
      border-radius: 4px;
      text-align: center;
      color: $gray-500;
      background-color: white;
      margin-bottom: 8px;
    }
    .list {
      padding-right: 4px;
      margin-right: -8px;
      flex: 1;
      overflow-y: auto;
      .listItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 40px;
        padding: 0 12px;
        width: 100%;
        background-color: #d7f9f6;
        cursor: pointer;
        position: relative;
        color: $gray-600;

        &:hover {
          background-color: $brand-300;
        }

        &.selected {
          background-color: $brand-800;
          color: white;
        }

        .checkMark {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: white;
        }
      }
    }
  }
}
