// Example:
// <Icon name="error" color="#609cad" height={30} width={30} />

import ErrorIcon from '@/assets/icons/error.svg?react';
import EyeIcon from '@/assets/icons/eyeOn.svg?react';
import EyeOffIcon from '@/assets/icons/eyeOff.svg?react';
import DownFilledIcon from '@/assets/icons/down-filled.svg?react';
import HomeIcon from '@/assets/icons/home.svg?react';
import FolderIcon from '@/assets/icons/folder.svg?react';
import PaperIcon from '@/assets/icons/paper.svg?react';
import GroupIcon from '@/assets/icons/group.svg?react';
import ArrowLeftIcon from '@/assets/icons/arrow-left.svg?react';
import ArrowRightIcon from '@/assets/icons/arrow-right.svg?react';
import ArrowExitIcon from '@/assets/icons/arrow-exit.svg?react';
import CheckIcon from '@/assets/icons/check.svg?react';
import CloseIcon from '@/assets/icons/close.svg?react';
import CloseFilledIcon from '@/assets/icons/close-filled.svg?react';
import BreadCrumbIcon from '@/assets/icons/breadcrumb.svg?react';
import UserFileIcon from '@/assets/icons/user-file.svg?react';
import UserInfoIcon from '@/assets/icons/user-info.svg?react';
import TreatmentIcon from '@/assets/icons/treatment.svg?react';
import RecordIcon from '@/assets/icons/record.svg?react';
import MedicalIcon from '@/assets/icons/medical.svg?react';
import BookingIcon from '@/assets/icons/booking.svg?react';
import RefreshIcon from '@/assets/icons/refresh.svg?react';
import AddPersonIcon from '@/assets/icons/add-person.svg?react';
import DownloadIcon from '@/assets/icons/download.svg?react';
import FilterIcon from '@/assets/icons/filter.svg?react';
import SortIcon from '@/assets/icons/sort.svg?react';
import EditIcon from '@/assets/icons/edit.svg?react';
import AddNewIcon from '@/assets/icons/add-new.svg?react';
import PlusIcon from '@/assets/icons/plus.svg?react';
import SearchIcon from '@/assets/icons/search.svg?react';
import RetryIcon from '@/assets/icons/retry.svg?react';
import SortAscIcon from '@/assets/icons/sort-asc.svg?react';
import SortDescIcon from '@/assets/icons/sort-desc.svg?react';
import DoctorIcon from '@/assets/icons/doctor.svg?react';
import AddSquareIcon from '@/assets/icons/add-square.svg?react';
import DoctorPlusIcon from '@/assets/icons/doctor-plus.svg?react';
import PersonInfoIcon from '@/assets/icons/person-info.svg?react';
import ArrowDownIcon from '@/assets/icons/arrow-down.svg?react';
import InsuranceInfoIcon from '@/assets/icons/insurance-info.svg?react';
import CalenderIcon from '@/assets/icons/calendar.svg?react';
import WarningIcon from '@/assets/icons/warning.svg?react';
import EmptyTableIcon from '@/assets/icons/empty-table.svg?react';
import TrashIcon from '@/assets/icons/trash.svg?react';
import UploadIcon from '@/assets/icons/upload.svg?react';

import styles from './Icon.module.scss';
import clsx from 'clsx';
import React from 'react';

export const iconMap = {
  error: ErrorIcon,
  eye: EyeIcon,
  eyeOff: EyeOffIcon,
  downFilled: DownFilledIcon,
  home: HomeIcon,
  folder: FolderIcon,
  paper: PaperIcon,
  group: GroupIcon,
  arrowLeft: ArrowLeftIcon,
  arrowRight: ArrowRightIcon,
  arrowExit: ArrowExitIcon,
  check: CheckIcon,
  close: CloseIcon,
  closeFilled: CloseFilledIcon,
  breadcrumb: BreadCrumbIcon,
  userFile: UserFileIcon,
  refresh: RefreshIcon,
  addPerson: AddPersonIcon,
  download: DownloadIcon,
  filter: FilterIcon,
  sort: SortIcon,
  edit: EditIcon,
  addNew: AddNewIcon,
  plus: PlusIcon,
  userInfo: UserInfoIcon,
  treatment: TreatmentIcon,
  record: RecordIcon,
  medical: MedicalIcon,
  booking: BookingIcon,
  search: SearchIcon,
  retry: RetryIcon,
  sortAsc: SortAscIcon,
  sortDesc: SortDescIcon,
  doctor: DoctorIcon,
  addSquare: AddSquareIcon,
  doctorPlus: DoctorPlusIcon,
  personInfo: PersonInfoIcon,
  arrowDown: ArrowDownIcon,
  insuranceInfo: InsuranceInfoIcon,
  calendar: CalenderIcon,
  warning: WarningIcon,
  emptyTable: EmptyTableIcon,
  trash: TrashIcon,
  upload: UploadIcon,
};
export type IconName = keyof typeof iconMap;
interface IconProps extends React.HTMLAttributes<HTMLSpanElement> {
  name: keyof typeof iconMap;
  width?: number;
  height?: number;
  color?: string;
  native?: boolean;
}

const Icon: React.FC<IconProps> = ({
  name,
  width = 16,
  height = 16,
  color = 'currentColor',
  native = false,
  className,
}) => {
  const Component = iconMap[name];
  if (!Component) {
    console.warn(`⚠️ Icon "${name}" not found.`);
    return null;
  }
  const resolveColor = (color: string): string => {
    if (color.startsWith('$')) {
      const variableName = color.slice(1);
      return `var(--${variableName})`;
    }
    return color;
  };

  return (
    <span
      className={clsx(styles.iconWrapper, className, native ? '' : styles.custom)}
      style={
        native
          ? {}
          : {
              width,
              height,
              ['--icon-color' as any]: resolveColor(color),
            }
      }
    >
      <Component />
    </span>
  );
};

export default React.memo(Icon);
