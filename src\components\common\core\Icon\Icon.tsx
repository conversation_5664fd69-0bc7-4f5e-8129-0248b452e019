// Example:
// <Icon name="error" color="#609cad" height={30} width={30} />

import React from 'react';
import styles from './Icon.module.scss';
import clsx from 'clsx';
import { iconMap, IconName } from './iconMap';

interface IconProps extends React.HTMLAttributes<HTMLSpanElement> {
  name: IconName;
  width?: number;
  height?: number;
  color?: string;
  native?: boolean;
  style?: React.CSSProperties;
}

const Icon: React.FC<IconProps> = ({
  name,
  width = 16,
  height = 16,
  color = 'currentColor',
  native = false,
  style,
  className,
}) => {
  const Component = iconMap[name];
  if (!Component) {
    console.warn(`⚠️ Icon "${name}" not found.`);
    return null;
  }
  const resolveColor = (color: string): string => {
    if (color.startsWith('$')) {
      const variableName = color.slice(1);
      return `var(--${variableName})`;
    }
    return color;
  };

  return (
    <span
      className={clsx(styles.iconWrapper, className, native ? '' : styles.custom)}
      style={
        native
          ? {}
          : {
              width,
              height,
              ['--icon-color' as any]: resolveColor(color),
              ...style,
            }
      }
    >
      <Component />
    </span>
  );
};

export default React.memo(Icon);
