import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppRadio } from '@/components/common/core/Radio';
import { AppRadioGroup } from '@/components/common/core/Radio/AppRadioGroup';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import { ZOKUGARA_RELATIONSHIP } from '@/types/constants';
import { Col, Flex, Row } from 'antd';
import { useState } from 'react';
import { AssistanceTab } from './AssistanceTab';
import { Certificate } from './Certificate';
import styles from './CustomTab.module.scss';
import { HokenCollapse } from './HokenCollapse';
import { Medical } from './MedicalAssitance';

export const Relationship = () => {
  const [tabActive, setTabActive] = useState<string>('1');
  const [isCollapsed, setIsCollapsed] = useState(false);
  return (
    <AppBlockHasHeader
      icon={<Icon name="arrowLeft" width={20} height={20} />}
      title="続柄・被保険者"
    >
      <AppFormItem label="続柄" name={'zokugara'} className="mb-4">
        <AppRadioGroup name="zokugara" disabled>
          {Object.entries(ZOKUGARA_RELATIONSHIP).map(([key, label]) => (
            <AppRadio value={Number(key)}>{label}</AppRadio>
          ))}
        </AppRadioGroup>
      </AppFormItem>
      <Row gutter={24}>
        <Col span={12}>
          <Flex flex={1} className="mb-4">
            <AppFormItem label="被保険者/組合人世帯主" name={'hihoken_name'}>
              <AppInput disabled />
            </AppFormItem>
          </Flex>
        </Col>
        <Col span={12}>
          <Flex flex={1} className="mb-4">
            <AppFormItem label="被保険者カタカナ" name={'hihoken_kana'}>
              <AppInput disabled />
            </AppFormItem>
          </Flex>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={12}>
          <Flex flex={1} className="mb-4">
            <AppFormItem label="郵便番号" name={'hihoken_post'}>
              <AppInput disabled />
            </AppFormItem>
          </Flex>
        </Col>
        <Col span={12}>
          <Flex flex={1} className="mb-4">
            <AppFormItem label="電話番号" name={'hihoken_tel'}>
              <AppInput disabled />
            </AppFormItem>
          </Flex>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24}>
          <Flex flex={1} className="mb-4">
            <AppFormItem label="住所" name={'ocr_data_hoken'}>
              <AppInput disabled />
            </AppFormItem>
          </Flex>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24}>
          <Flex flex={1} className="mb-4">
            <AppFormItem name={'ocr_data_kohi'}>
              <AppInput disabled />
            </AppFormItem>
          </Flex>
        </Col>
      </Row>
      <HokenCollapse onToggleCollapse={setIsCollapsed} />
      {isCollapsed && (
        <>
          <AssistanceTab
            className={styles.customTab}
            items={[
              {
                key: '1',
                label: '本証',
                children: <Certificate />,
              },
              {
                key: '2',
                label: '医療助成',
                children: <Medical />,
              },
            ]}
            onChange={val => setTabActive(val.toString())}
            defaultActiveKey={tabActive}
            activeKey={tabActive}
            centered
          />
        </>
      )}
    </AppBlockHasHeader>
  );
};
