import { createAsyncThunk } from '@reduxjs/toolkit';
import { setKaruteQueryParams, setLoading } from '.';
import { patientSummariesApi } from '../patient-summary/api';
import { KarutesParams } from './type';

export const prefetchKaruteDetails = createAsyncThunk<any, KarutesParams>(
  'karute/prefetchDetails',
  async (params: Partial<KarutesParams>, { dispatch }) => {
    dispatch(setLoading(true));
    dispatch(setKaruteQueryParams({ ...params }));
    try {
      const [patientSummaries] = await Promise.all([
        dispatch(
          patientSummariesApi.endpoints.getPatientSummaries.initiate(
            {
              ...params,
            },
            {
              forceRefetch: true,
            }
          )
        )?.unwrap(),
      ]);

      return { patient_summary: patientSummaries };
    } catch (_err: unknown) {
      console.log(_err);
    } finally {
      dispatch(setLoading(false));
    }
  }
);
