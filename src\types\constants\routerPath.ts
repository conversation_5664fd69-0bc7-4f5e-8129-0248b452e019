export const routerPaths = {
  main: '/',
  login: '/login',
  dashboard: '/:clinicCd/dashboard',
  resetPassword: '/:clinicCd/reset-password',
  visitManagement: {
    visitUpdate: (id?: number | string) =>
      id
        ? `/:clinicCd/dashboard/visit-management/${id}`
        : `/:clinicCd/dashboard/visit-management/:id`,
  },
  karuteManagement: {
    karuteList: '/:clinicCd/karute-management',
    karuteUpdate: '/:clinicCd/karute-management/:id',
    karuteUpdateByService: '/:clinicCd/karute-management/:id/:serviceId',
    karuteUpsertModal: '/:clinicCd/karute-management/:id/:serviceId/:upsertType/:upsertMode',
    karuteUpsertModalSegment: '/:upsertType/:upsertMode',
    karuteDbHistory: (id?: number | string) =>
      id
        ? `/:clinicCd/karute-management/${id}/db-history`
        : `/:clinicCd/karute-management/:id/:serviceId/db-history`,
    schemaSegment: '/schema/:schemaType/:disease/:diseaseBaseId',
  } as const,

  patientManagement: {
    patientList: '/:clinicCd/patient-management',
    patientCreate: '/:clinicCd/patient-management/create',
    patientUpdate: (id?: number | string) =>
      id ? `/:clinicCd/patient-management/${id}` : `/:clinicCd/patient-management/:id`,
  },

  userManagement: {
    userList: '/:clinicCd/user-management',
    userCreate: '/:clinicCd/user-management/create',
    userUpdate: (id?: number | string) =>
      id ? `/:clinicCd/user-management/${id}` : `/:clinicCd/user-management/:id`,
  },

  errors: {
    clinicNotFound: '/:clinicCd/404-clinic-not-found',
    clinicUnauthorized: '/:clinicCd/403-clinic-unauthorized',
    pageNotAuthorized: '/:clinicCd/403-page-not-authorized',
    dashboardNotFound: '/:clinicCd/404-dashboard-not-found',
  },
};
