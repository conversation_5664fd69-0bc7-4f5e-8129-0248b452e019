import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Checkbox, Flex, Popover, Tooltip, InputProps } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import styles from './MultiSelect.module.scss';
import Button from '../Button';
import { ButtonType } from '@/types/enum';
import clsx from 'clsx';
import { COMMA_SEPARATED } from '@/types/constants';

interface Option {
  value: string | number;
  label: string;
}

interface MultiSelectProps {
  options: Option[];
  onSelectChange?: (selectedValues: any[]) => void;
  onChange?: (value: any[], options: Option[]) => void;
  value?: any[] | null;
  dropdownStyle?: React.CSSProperties;
  inputProps?: InputProps;
}

const TAG_WIDTH = 54;
const TAG_GAP = 4;
const EXTRA_PADDING = 24;

export const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  value,
  onSelectChange,
  onChange,
  dropdownStyle,
  inputProps,
}) => {
  const inputRef = useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);
  const [tempSelected, setTempSelected] = useState<any[]>(Array.isArray(value) ? value : []);

  useEffect(() => {
    if (open) setTempSelected(Array.isArray(value) ? value : []);
  }, [open, value]);

  const handleCheckboxChange = (v: any, checked: boolean) =>
    setTempSelected(prev => (checked ? [...prev, v] : prev.filter(x => x !== v)));
  const handleSelectAll = () => setTempSelected(options.map(o => o.value));
  const clearSelectAll = () => setTempSelected([]);
  const handleApply = () => {
    onChange?.(
      tempSelected,
      options.filter(o => tempSelected.includes(o.value))
    );
    onSelectChange?.(tempSelected);
    setOpen(false);
  };
  const handleCancel = () => {
    const actual = Array.isArray(value) ? value : [];
    setTempSelected(actual);
    onSelectChange?.(actual);
    setOpen(false);
  };

  const { visibleLabels, hiddenLabels, plusCount, showPlus } = useMemo(() => {
    const selected = Array.isArray(value) ? value : [];
    const labels = options.filter(o => selected.includes(o.value)).map(o => o.label);

    let totalWidth = 200;
    const w = inputProps?.style?.width;
    if (typeof w === 'number') totalWidth = w;
    else if (typeof w === 'string' && w.endsWith('px')) totalWidth = parseInt(w, 10);

    const avail = totalWidth - EXTRA_PADDING;
    const maxSlots = Math.floor((avail + TAG_GAP) / (TAG_WIDTH + TAG_GAP));

    let vCount = maxSlots,
      pCount = 0,
      plus = false;
    if (labels.length > maxSlots) {
      plus = true;
      if (maxSlots <= 1) {
        vCount = 0;
        pCount = labels.length;
      } else {
        vCount = maxSlots - 1;
        pCount = labels.length - vCount;
      }
    }
    return {
      visibleLabels: labels.slice(0, vCount),
      hiddenLabels: labels.slice(vCount),
      plusCount: pCount,
      showPlus: plus,
    };
  }, [options, value, inputProps?.style?.width]);

  const dropdownContent = (
    <div className={styles.dropdown} style={dropdownStyle || { width: 200 }}>
      <Flex justify="end">
        <Button customType={ButtonType.TERTIARY_COLOR} onClick={handleSelectAll} customSize="xxs">
          すべて選択
        </Button>
        <Button customType={ButtonType.TERTIARY_COLOR} onClick={clearSelectAll} customSize="xxs">
          クリア
        </Button>
      </Flex>
      <div className={styles.options}>
        {options.map(opt => (
          <div key={opt.value} className={styles.optionItem}>
            <Checkbox
              checked={tempSelected.includes(opt.value)}
              onChange={e => handleCheckboxChange(opt.value, e.target.checked)}
            >
              {opt.label}
            </Checkbox>
          </div>
        ))}
      </div>
      <Flex justify="end" gap={8} className="mt-1">
        <Button customSize="xxs" customType={ButtonType.SECONDARY_COLOR} onClick={handleCancel}>
          キャンセル
        </Button>
        <Button
          customSize="xxs"
          customType={ButtonType.PRIMARY}
          onClick={handleApply}
          style={{ width: '86px' }}
        >
          適用
        </Button>
      </Flex>
    </div>
  );
  return (
    <div className={styles.container}>
      <Popover
        content={dropdownContent}
        trigger="click"
        open={open}
        onOpenChange={setOpen}
        placement="bottomLeft"
        arrow={false}
        getPopupContainer={() => document.body}
        overlayClassName={styles.customPopover}
      >
        <div ref={inputRef} className={styles.inputTagContainer} style={inputProps?.style}>
          {visibleLabels.length === 0 && !showPlus ? (
            <span className={styles.placeholder}>{inputProps?.placeholder}</span>
          ) : (
            <>
              {visibleLabels.map((lab, i) => (
                <Tooltip key={i} title={lab}>
                  <span className={clsx(styles.tag, 'fs12-medium')}>{lab}</span>
                </Tooltip>
              ))}
              {showPlus && (
                <Tooltip title={hiddenLabels.join(COMMA_SEPARATED)}>
                  <span className={clsx(styles.tag, styles.plusTag, 'fs12-medium')}>
                    +{plusCount}
                  </span>
                </Tooltip>
              )}
            </>
          )}
          <span className={styles.arrow}>{open ? <UpOutlined /> : <DownOutlined />}</span>
        </div>
      </Popover>
    </div>
  );
};
