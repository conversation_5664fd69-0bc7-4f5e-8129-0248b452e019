import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';
import { RootState } from '@/config/redux/store';
import { getAuth } from '@/store/auth/selectors';
import { APIResponse } from '@/types/interface';
import { Category, KeiKaImages, SchemaImage } from './type';

export const schemaApi = medixKaruteBaseQueryApi.injectEndpoints({
  overrideExisting: true,
  endpoints: build => ({
    getCategories: build.query<APIResponse<Category[]>, void>({
      query: () => ({ url: `/api/category-schemas/get-all`, method: 'GET' }),
      providesTags: ['schema'],
    }),
    getAllSchemaImages: build.query<APIResponse<SchemaImage[]>, { service_id?: number } | void>({
      query: (arg = {}) => {
        const { service_id } = arg as { service_id?: number };
        return {
          url: '/api/schema-images/all',
          method: 'GET',
          params: service_id ? { service_id } : undefined,
        };
      },
      providesTags: ['schema'],
    }),
    getSchemaImagesByCategory: build.query<
      APIResponse<SchemaImage[]>,
      { category_id: number; service_id?: number }
    >({
      query: ({ category_id, service_id }) => ({
        url: `/api/schema-images/all`,
        method: 'GET',
        params: {
          category_id,
          ...(service_id ? { service_id } : {}),
        },
      }),
      providesTags: ['schema'],
    }),
    favoriteSchemaImage: build.mutation<APIResponse<SchemaImage>, { schema_image_id: number }>({
      query: ({ schema_image_id }) => ({
        url: `/api/schema-images/${schema_image_id}/favorite`,
        method: 'POST',
      }),
    }),
    unFavoriteSchemaImage: build.mutation<APIResponse<SchemaImage>, { schema_image_id: number }>({
      query: ({ schema_image_id }) => ({
        url: `/api/schema-images/${schema_image_id}/unfavorite`,
        method: 'POST',
      }),
    }),
    postKeikaImage: build.mutation<APIResponse<SchemaImage>, KeiKaImages[]>({
      query: images => {
        const fd = new FormData();
        images.forEach((img, i) => {
          !img.isTemp && img.schema_image_id
            ? fd.append(`keika_images[${i}][schema_image_id]`, String(img.schema_image_id))
            : null;
          fd.append(`keika_images[${i}][type]`, String(img.type));
          fd.append(`keika_images[${i}][file]`, img.file);
        });

        return {
          url: '/api/keika-images/create-many',
          method: 'POST',
          body: fd,
        };
      },
    }),
    postDatabaseImage: build.mutation<APIResponse<SchemaImage>, KeiKaImages[]>({
      query: images => {
        const fd = new FormData();
        images.forEach((img, i) => {
          !img.isTemp && img.schema_image_id
            ? fd.append(`disease_base_images[${i}][schema_image_id]`, String(img.schema_image_id))
            : null;
          fd.append(`disease_base_images[${i}][type]`, String(img.type));
          fd.append(`disease_base_images[${i}][file]`, img.file);
        });

        return {
          url: '/api/disease-base-images/create-many',
          method: 'POST',
          body: fd,
        };
      },
    }),
    postSchemaImage: build.mutation<
      APIResponse<SchemaImage>,
      { image: SchemaImage; service_ids?: number[] }
    >({
      queryFn: async ({ image, service_ids }, api, _extra, fetchWithBQ) => {
        const clinicId = getAuth(api.getState() as RootState).currentClinicId;

        const fd = new FormData();
        fd.append('category_schema_id', String(image.category_schema_id));
        if (image.file) fd.append('file', image.file);
        fd.append('clinic_id', String(clinicId));
        service_ids?.forEach(id => fd.append('service_ids[]', String(id)));

        const result = await fetchWithBQ({
          url: '/api/schema-images',
          method: 'POST',
          body: fd,
        });

        if (result.error) {
          return { error: result.error, meta: undefined };
        }
        return {
          data: result.data as APIResponse<SchemaImage>,
          meta: undefined,
        };
      },
    }),
    deleteSchemaImage: build.mutation<APIResponse<SchemaImage>, { schema_image_id: number }>({
      query: ({ schema_image_id }) => ({
        url: `/api/schema-images/${schema_image_id}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const {
  useGetCategoriesQuery,
  useGetAllSchemaImagesQuery,
  useLazyGetSchemaImagesByCategoryQuery,
  useFavoriteSchemaImageMutation,
  useUnFavoriteSchemaImageMutation,
  // useLazyGetKeikaImageQuery,
  usePostKeikaImageMutation,
  usePostDatabaseImageMutation,
  usePostSchemaImageMutation,
  useDeleteSchemaImageMutation,
} = schemaApi;
