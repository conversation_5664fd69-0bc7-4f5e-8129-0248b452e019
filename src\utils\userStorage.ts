import { LOCAL_STORAGE_KEY } from '@/types/constants';
import { UserInfo } from '@/types/interface';

export function saveUser(user: UserInfo | null) {
  localStorage.setItem(LOCAL_STORAGE_KEY.USER_INFO, JSON.stringify(user));
  window.dispatchEvent(new Event('user-change'));
}

export function clearUser() {
  localStorage.removeItem(LOCAL_STORAGE_KEY.USER_INFO);
  window.dispatchEvent(new Event('user-change'));
}

export function loadUser(): UserInfo | null {
  const raw = localStorage.getItem(LOCAL_STORAGE_KEY.USER_INFO);
  return raw ? (JSON.parse(raw) as UserInfo) : null;
}
