// Screen: K105_参照
import { useEffect, useMemo, useState } from 'react';
import { Col, Empty, Flex, Form, Row, Spin } from 'antd';
import clsx from 'clsx';
import { useWatch } from 'antd/es/form/Form';

import styles from './ReferenceModal.module.scss';
import { AppModal } from '@/components/common/core/Modal';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import Button from '@/components/common/core/Button';
import { AppTextArea } from '@/components/common/core/Input';
import { AppFormItem } from '@/components/common/core/FormItem';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import Icon from '@/components/common/core/Icon';

import {
  useLazyGetReferenceCategoryQuery,
  useLazyGetReferenceMasterByCategoryQuery,
} from '@/store/master/api';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { ClinicReferenceCategory, ClinicReferenceSentence } from '@/store/master/type';
import { ButtonType, KaruteMasterField, KaruteMasterFieldCode } from '@/types/enum';
import {
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  SUBMIT_BTN,
} from '@/types/constants';
import { trimField } from '@/components/page-components/visit-management/dashboard/VisitDetail/helper';
import { showFlashNotice } from '@/utils';
import MasterModal from '../MasterModal';


const MAX_TEXT_LENGTH = 1000;

type Props = {
  label: string;
  field: KaruteMasterField;
  handleCloseModal: () => void;
  handleSubmitModal: (text: string) => void;
  code: KaruteMasterFieldCode;
  screen: 'database' | 'keika';
};
type ReferenceFormSchema = {
  reference: string;
};
const concatField = (current: string | undefined, newValue: string | undefined) =>
  current || newValue ? trimField(`${current ?? ''}${newValue ?? ''}`, MAX_TEXT_LENGTH) : '';
export function ReferenceModal({
  label,
  field,
  code,
  screen,
  handleCloseModal,
  handleSubmitModal,
}: Props) {
  // states
  const [activeCategory, setActiveCategory] = useState<number | null>(null);
  const [activeMaster, setActiveMaster] = useState<number | null>(null);
  const [isMasterModalOpen, setIsMasterModalOpen] = useState<boolean>(false);

  const [init] = useState<ReferenceFormSchema>({
    reference: '',
  });
  // Library Hooks
  const [form] = Form.useForm<ReferenceFormSchema>();
  // Custom Hooks
  const { handleValuesChange, confirmLeave, submitHandler } =
    useUnsavedWarning<ReferenceFormSchema>(init);
  // API
  const [getReferenceCategory, { data: referenceCategoryData, isFetching: isFetchingCategory }] =
    useLazyGetReferenceCategoryQuery();
  const [
    getReferenceMasterByCategory,
    { data: referenceMasterData, isFetching: isFetchingMaster },
  ] = useLazyGetReferenceMasterByCategoryQuery();

  // Functions

  const handleOpenMasterModal = () => {
    setIsMasterModalOpen(true);
  };
  const handleSubmit = (value: ReferenceFormSchema) => {
    handleSubmitModal(value.reference);
    showFlashNotice({ message: NOTICE_COMMON_MESSAGE.REFLECTED_REFERENCE_DATA, type: 'success' });
    submitHandler();
    handleCloseModal();
  };

  const handleSelectMaster = (item: ClinicReferenceSentence) => {
    setActiveMaster(item.clinic_reference_sentence_id);
    const currentValue = form.getFieldValue('reference');
    const updatedValue = concatField(currentValue, item.content);
    form.setFieldValue('reference', updatedValue);
    handleValuesChange(null, { reference: updatedValue });
  };
  const handleSelectCategory = (item: ClinicReferenceCategory) => {
    setActiveCategory(item.clinic_reference_category_id);
    getReferenceMasterByCategory({
      reference_cd: field,
      category_id: item.clinic_reference_category_id,
    })
      .unwrap()
      .then(res => {
        if (res.data?.length) {
          setActiveMaster(res?.data?.[0]?.clinic_reference_sentence_id);
        }
      });
  };
  const handleCancelModal = () => {
    const confirm = confirmLeave?.();
    if (confirm) {
      handleCloseModal();
    }
  };

  // Side effects
  useEffect(() => {
    if (referenceCategoryData?.data?.length) {
      const firstCategoryId = referenceCategoryData?.data?.[0]?.clinic_reference_category_id;
      setActiveCategory(firstCategoryId);

      getReferenceMasterByCategory({
        reference_cd: field,
        category_id: firstCategoryId,
      })
        .unwrap()
        .then(res => {
          if (res.data?.length) {
            setActiveMaster(res?.data?.[0]?.clinic_reference_sentence_id);
          }
        });
    }
  }, [referenceCategoryData, getReferenceMasterByCategory, field]);
  const isCategoryEmpty = useMemo(
    () => !referenceCategoryData?.data?.length,
    [referenceCategoryData]
  );
  const isMasterEmpty = useMemo(() => !referenceMasterData?.data?.length, [referenceMasterData]);
  const isReferenceEmpty = useWatch('reference', form)?.trim() === '';
  const renderModal = (
    <AppModal width="1000px" destroyOnClose open onCancel={handleCancelModal} zIndex={1500}>
      <Flex justify="space-between" align="center" className={clsx('pr-6', styles.reference_title)}>
        <ModalTitle title={'参照'} />
        <Button
          className="mr-2"
          customType={ButtonType.PRIMARY}
          onClick={handleOpenMasterModal}
          customSize="sm"
        >
          マスター編集
        </Button>
      </Flex>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        onValuesChange={(changedValues, allValues) => {
          handleValuesChange(null, allValues);
        }}
        initialValues={init}
      >
        <Flex vertical gap="middle" className="mt-6">
          <AppFormItem
            name="reference"
            label={label}
            rules={[
              {
                whitespace: true,
                required: true,
                message: ERROR_COMMON_MESSAGE.REQUIRED(label),
              },
            ]}
          >
            <AppTextArea
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
              maxLength={MAX_TEXT_LENGTH}
              style={{ minHeight: '90px' }}
              disabled={isFetchingCategory || isCategoryEmpty}
            />
          </AppFormItem>
          {/* 1/3 */}
          <Spin spinning={isFetchingCategory} className={styles.reference_master_tab}>
            <div className="mx-3">
              <Row gutter={24} className={styles.reference_master_tab}>
                {!isFetchingCategory && isCategoryEmpty && (
                  <Col span={24}>
                    <Empty
                      image={<Icon name="emptyTable" native width={90.27} height={80} />}
                      description={<p className="fs14-regular">{ERROR_COMMON_MESSAGE.NO_DATA}</p>}
                      className={styles.reference_emty_data}
                    />
                  </Col>
                )}
                {!isFetchingCategory && !isCategoryEmpty && (
                  <>
                    <Col span={6} className={styles.reference_master_tab_sidebar_container}>
                      <Flex vertical gap="0" className={styles.content}>
                        {referenceCategoryData?.data?.map(item => (
                          <div
                            key={item.clinic_reference_category_id}
                            className={clsx(styles.row_item, 'fs14-medium', {
                              [styles.active]: item.clinic_reference_category_id === activeCategory,
                            })}
                            onClick={() => handleSelectCategory(item)}
                          >
                            <RenderWithTooltip text={item.name} maxLines={1} />
                          </div>
                        ))}
                      </Flex>
                    </Col>
                    {/* <Spin spinning={isFetchingMaster}> */}
                    <Col span={18} className={styles.reference_master_tab_content_container}>
                      <Spin spinning={isFetchingMaster}>
                        {isMasterEmpty && (
                          <Empty
                            image={<Icon name="emptyTable" native width={90.27} height={80} />}
                            description={
                              <p className="fs14-regular">{ERROR_COMMON_MESSAGE.NO_DATA}</p>
                            }
                            className={styles.reference_emty_data}
                          />
                        )}
                        {!isMasterEmpty && (
                          <Flex vertical gap="0" className={styles.content}>
                            {referenceMasterData?.data?.map(item => (
                              <div
                                key={item.clinic_reference_sentence_id}
                                className={clsx(styles.row_item, 'fs14-medium', {
                                  [styles.active]:
                                    item.clinic_reference_sentence_id === activeMaster,
                                })}
                                onClick={() => {
                                  handleSelectMaster(item);
                                }}
                              >
                                <RenderWithTooltip text={item.content} maxLines={2} />
                              </div>
                            ))}
                          </Flex>
                        )}
                      </Spin>
                    </Col>
                  </>
                )}
              </Row>
            </div>
          </Spin>
        </Flex>
        <Flex gap="middle" justify="end" className="mt-6">
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            onClick={handleCancelModal}
            className="btn-modal-width"
            customSize="lg"
          >
            キャンセル
          </Button>
          <Button
            customType={ButtonType.PRIMARY}
            htmlType={'submit'}
            className="btn-modal-width"
            loading={isFetchingMaster || isFetchingCategory}
            customSize="lg"
            disabled={isReferenceEmpty}
          >
            {SUBMIT_BTN.CREATE}
          </Button>
        </Flex>
      </Form>
    </AppModal>
  );
  useEffect(() => {
    if (!isMasterModalOpen) {
      getReferenceCategory({ reference_cd: field });
    }
  }, [getReferenceCategory, field, isMasterModalOpen]);

  return (
    <>
      {!isMasterModalOpen && renderModal}

      {isMasterModalOpen && (
        <MasterModal
          screen={screen}
          field={field}
          code={code}
          handleCloseModal={() => setIsMasterModalOpen(false)}
        />
      )}
    </>
  );
}
