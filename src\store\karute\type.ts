import { InjuryNameIdentifier } from '@/components/page-components/karute-managment/karte-input/InjuryNameModal/type';
import {
  DiseaseImage,
  EStatus,
  MondaiItem,
} from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import { KeikaItem } from '@/types/constants/karute';
import { EKaruteStatus, EPaymentType } from '@/types/enum';
import { PatientSummary } from '../patient-summary/type';
import { ServiceInfo } from '../visit/type';
import { OrderItem, PatientItem } from './../patient/type';

export interface KaruteItem extends Partial<PatientItem> {
  karute_id: number;
  clinic_id: number;
  patient_id: number;
  patient_cd: string;
  patient_name: string;
  patient_kana: string;
  mondais_count: number;

  first_submit_date: string;
  latest_submit_date: string;
  latest_treatment_date: string;
  status: EKaruteStatus;
}

export interface KarutesParams extends OrderItem {
  limit?: number;
  page?: number;
  trauma_type?: number;
  status?: number;
  patient_id?: string | number;
  service_id?: number;
  karute_id?: number;
}
export interface KaruteDetailItem {
  age: number;
  birthday: string; // ISO format: "YYYY-MM-DD"
  clinic_id: number;
  first_submit_date: string | null;
  karute_id: number;
  latest_submit_date: string | null;
  latest_treatment_date: string; // Format: "DD/MM/YYYY HH:mm"
  mondais_count: number | null;
  patient_cd: number;
  patient_cmt: string | null;
  patient_id: number;
  patient_kana: string;
  patient_name: string;
  status: number;
  created_at?: string;
  updated_at?: string;
}
export interface KarutePatientMedicalHistory {
  family: string;
  patient_id: string;
  patient_medical_history_id: string;
  personal: string;
  social: string;
}

export interface OptionItem {
  option_id: number;
  clinic_id: number;
  service_id: number;
  name: string;
  status: number;
}

export interface GetKaruteHokenCourseItem {
  course_id: number;
  course_name: string;
  status: string;
  cost: number | null;
  category_id: number | null;
  category_name: string | null;
  category_cd: string | null;
  payment_course_type: number;
  payment_course_name: string | null;
  payment_type_name: string | null;
  duration: string;
  hide_type: number;
  services: ServiceInfo;
}
export interface RegisteredVisitItem {
  patient_id: number;
  visit_id: number;
  registration_date: string; // e.g., "2025/04/26 10:00"
  courses_name: string;
  payment_types: string; // ví dụ: "0" hoặc "3, 0"
  created_at: string; // e.g., "2025/05/12 06:56:34"
  payment_type_checked: number;
  addon_option_checked: boolean;
  course_ids: number[];

  doctor?: GetKaruteDoctorItem | null;
  doctors?: GetKaruteDoctorItem[];
  selected_jihi_courses: GetKaruteJihiCourseItem[];
  hoken_courses: GetKaruteHokenCourseItem[];
  selected_hoken_courses: GetKaruteHokenCourseItem[]; // -> mostly don't use
}

export interface DiseaseHistoryItem extends Omit<InjuryNameIdentifier, 'name'> {
  disease_base_id: number;
  version_uuid: string;
  karute_id: number;
  service_id: number;
  injury_name: string;
  subjective: string | null;
  present_illness: string | null;
  objective: string | null;
  assessment: string | null;
  plan: string | null;
  patient_name: string;
  treatment: string | null;
  payment_type_label: string;
  payment_type: number;
  remarks: string;
  doctor_name: string;
  doctor_status: EStatus;
  doctor_id: number;
  objective_images: DiseaseImage[];
  treatment_images: DiseaseImage[];
  bui_cd: number;
}
export type ClinicService = {
  service_id: number;
  name: string;
};

export type KaruteServiceDetail = {
  patient_summary: PatientSummary[];
  databases: MondaiItem[];
  keika_records: KeikaItem[];
  patientInfo?: Partial<PatientItem>;
};

export interface DiseaseHistoryParams extends OrderItem {
  limit: number;
  page: number;
  clinic_id: string | number;
  service_id: string | number;
  patient_id: string | number;
  injury_name?: string;
  search?: string;
}

export interface ValidateInjuryResponse {
  name: string;
  part_type: number;
  bui_cd: number;
  lr_type: number;
  matched_name: number;
  part_id: number;
}
export interface DatabaseSchema {
  disease_base_image_id: number;
  disease_base_id: number;
  schema_image_id: number;
  type: number;
  path: string;
  url: string;
  version_uuid: string;
}

export interface DatabaseResponse {
  disease_bases: Database[];
  has_history: boolean;
}

export interface Database {
  disease_base_id: number;
  trauma: string;
  visit_date: string;
  injury_date: string;
  subjective: string;
  present_illness: string;
  objective: string;
  assessment: string;
  plan: string;
  payment_type: EPaymentType;
  treatment: string;
  remarks: string | null;
  doctor_name: string;
  status: number;
  version_uuid: string;
  objective_images: DatabaseSchema[];
  treatment_images: DatabaseSchema[];
}

export interface GetKaruteJihiCourseItem {
  course_id: number;
  course_name: string;
  cost: number;
  duration: string;
  payment_course_type: number;
  service_id: number;
}
export interface KeikaOptionItem {
  keika_option_revision_id: number;
  keika_revision_id: number;
  option_id: number;
  name: string;
  treatment_date: string; // dạng "YYYY-MM-DD"
  quantity: number;
  registration_date: string; // dạng "YYYY-MM-DD"
}
export interface GetKaruteDoctorItem {
  user_id: number;
  kanji_name: string;
  kana_name: string;
}

export interface KeikaHistoryParams extends OrderItem {
  limit: number;
  page: number;
  service_id: number | string;
  injury_name?: string;
  search?: string;
}

export interface KeikaDetailHistoryItem {
  keika_detail_id: number;
  karute_id: number;
  disease_base_id: number;
  keika_id: number;
  subjective: string;
  subjective_note: string;
  injury_name: string;
  objective: string;
  assessment: string;
  plan: string;
  treatment: string;
  remarks: string;
  payment_type: number;
  payment_type_label: string;
  doctor_id: number;
  doctor_status: EStatus;
  doctor_name: string;
  created_at: string; // YYYY-MM-DD HH:mm:ss
  updated_at: string; // YYYY-MM-DD HH:mm:ss
  objective_images: KeikaImage[];
  treatment_images: KeikaImage[];
}

export interface KeikaDiseaseBaseItem
  extends Omit<MondaiItem, 'tmp_id' | 'objective_images' | 'treatment_images'> {
  trauma_counter: number;
  created_at: string | null;
  updated_at: string | null;
  deleted_at: string | null;
}

// UPSERT
export interface UpsertKeikaMondai {
  payment_type: number;
  subjective: string;
  disease_base_id: number;
  objective: string;
  assessment: string;
  plan: string;
  treatment: string;
  remarks: string;
  doctor_id: number;
  status?: number;
}
export interface UpsertKeikaOption {
  option_id: number;
  quantity: number;
  treatment_date: string; // e.g. "2025-12-12"
  registration_date: string; // e.g. "2025-12-12"
  name: string;
}

export interface UpertKeikaCourse {
  course_id: number;
  treatment_date: string;
  registration_date: string;
  name: string;
}

export interface UpsertKeikaPayload {
  patient_id: number | string;
  karute_id: number | string;
  visit_id: number;
  option_checked: number;
  visit_date: string;
  service_id: number | string;

  keika_details: UpsertKeikaMondai[];
  objective_images: number[];
  treatment_images: number[];
  keika_options: UpsertKeikaOption[];
  keika_courses: UpertKeikaCourse[];
}
export type KeikaImage = {
  keika_image_id: number;
  schema_image_id: number | null;
  keika_id: number;
  path: string;
  type: number;
  is_draft: number;
  url: string;
};

export interface GetKeikaOption {
  keika_option_id: number;
  keika_id: number;
  option_id: number;
  name: string;
  treatment_date: string;
  quantity: number;
  registration_date: string;
}

export interface GetKeikaCourse {
  keika_course_id: number;
  keika_id: number;
  course_id: number;
  name: string;
  treatment_date: string;
  registration_date: string;
  payment_course_type: number;
}

export interface DetailKeikaData {
  keika_id: number;
  karute_id: number;
  visit_id: number;
  visit_date: string;
  service_id: number;
  option_checked: boolean;
  keika_details: KeikaDetail[];
  objective_images: KeikaImage[];
  treatment_images: KeikaImage[];
  keika_options: GetKeikaOption[];
  keika_courses: GetKeikaCourse[];
}

export interface PreviousKeikaItem {
  keika_id: number;
  option_checked: number;
  karute_id: number;
  version: number;
  visit_id: number;
  visit_date: string;
  service_id: number;
  created_at: string;
  updated_at: string;
  keika_details: PreviousKeikaDetail[];
  objective_images: KeikaImage[];
  treatment_images: KeikaImage[];
  // keika_options: GetKeikaOption[];
  // keika_courses: GetKeikaCourse[];
}

export interface LatestKeikaDetail {
  keika_detail_id: number;
  disease_base_id: number;
  keika_id: number;
  subjective: string | null;
  subjective_note: string;
  injury_name: string;
  objective: string | null;
  assessment: string | null;
  plan: string | null;
  treatment: string | null;
  remarks: string | null;
  payment_type: number;
  payment_type_label: string;
  doctor_id: number;
  doctor_name: string | null;
  created_at: string;
  updated_at: string;
  disease_base: KeikaLatestDiseaseBase;
}

interface KeikaLatestDiseaseBase {
  disease_base_id: number;
  version_uuid: string;
  karute_id: number;
  visit_date: string;
  visit_id: number;
  injury_date: string | null;
  injury_name: string;
  lr_type: number | null;
  part_type: number | null;
  bui_cd: number;
  part_id: number | null;
  trauma_type: number;
  trauma_counter: number;
  trauma_name: string;
  subjective: string | null;
  present_illness: string | null;
  objective: string | null;
  assessment: string | null;
  plan: string | null;
  payment_type: number;
  option_checked: number;
  treatment: string | null;
  remarks: string | null;
  doctor_id: number;
  doctor_name: string | null;
  status: number;
}

export interface KeikaDetail {
  keika_detail_id: number;
  disease_base_id: number;
  keika_id: number;
  trauma_type?: number; // default 1
  trauma_counter?: number;
  trauma_name?: string;
  subjective: string;
  subjective_note: string;
  objective: string;
  assessment: string;
  plan: string;
  treatment: string;
  remarks: string;
  payment_type: number;
  doctor_id: number;
  doctor_name: string | null;
  doctor_status?: EStatus; // for getting latest keika only
  version: number;

  disease_base: KeikaDiseaseBaseItem & {
    mondai: {
      mondai_id: number;
      karute_id: number;
      patient_id: number;
      patient_name: string;
      patient_name_kana: string;
      trauma_type: number;
      trauma_counter: number;
      disease_base_id: number;
      subject: string;
      remarks: string;
      created_by: number;
      version: number;
      status: EStatus;
      created_at: string;
      updated_at: string;
    };
  };
}
export interface PreviousKeikaDetail
  extends Omit<KeikaDetail, 'doctor_status' | 'doctor_name' | 'doctor_id'> {
  doctor: {
    doctor_id: number;
    kanji_name: string | null;
    kana_name: string | null;
    status?: EStatus;
  };
}

//#region GET KEIKA
export interface MainKeika {
  keika_id: number;
  option_checked: number;
  karute_id: number;
  version: number;
  visit_id: number;
  visit_date: string;
  service_id: number;
  created_at: string;
  updated_at: string;
  keika_details: MainKeikaDetail[];
  objective_images: KeikaImage[];
  treatment_images: KeikaImage[];
  keika_revisions_count: number;
}

export interface MainKeikaDetail {
  keika_detail_id: number;
  disease_base_id: number;
  keika_id: number;
  subjective: string;
  subjective_note?: string;
  injury_name?: string;
  objective: string;
  assessment: string;
  plan: string;
  treatment: string;
  remarks?: string;
  payment_type: number;
  payment_type_label?: string;
  doctor_id: number;
  doctor_name?: string;
  created_at: string;
  updated_at: string;
  disease_base: KeikaDatabase;
}

export interface HistoryDetailKeika {
  keika_revision_id: number;
  keika_id: number;
  version: number;
  option_checked: number;
  karute_id: number;
  visit_id: number;
  visit_date: string;
  service_id: number;
  created_at: string;
  updated_at: string;
  keika_detail_revisions: KeikaDetailRevision[];
  objective_image_revisions: KeikaImageRevision[];
  treatment_image_revisions: KeikaImageRevision[];
  keika_options: KeikaOptionRevision[];
  keika_courses: KeikaCourseRevision[];
}

export interface KeikaDetailRevision {
  keika_detail_revision_id: number;
  disease_base_id: number;
  keika_revision_id: number;
  subjective: string;
  subjective_note: string;
  objective: string;
  assessment: string;
  plan: string;
  treatment: string;
  remarks: string;
  payment_type: number;
  doctor_id: number;
  version: number;
  disease_base: KeikaDatabase;
}

export interface KeikaOptionRevision {
  keika_option_revision_id: number;
  keika_revision_id: number;
  option_id: number;
  name: string;
  treatment_date: string;
  quantity: number;
  registration_date: string;
}

export interface KeikaCourseRevision {
  keika_course_revision_id: number;
  keika_revision_id: number;
  course_id: number;
  name: string;
  treatment_date: string;
  registration_date: string;
}

export interface KeikaImageRevision {
  keika_image_revision_id: number;
  schema_image_id: number;
  keika_revision_id: number;
  path: string;
  type: number;
  is_draft: number;
  url: string;
}

export interface KeikaDatabase {
  disease_base_id: number;
  karute_id: number;
  visit_date: string;
  visit_id: number;
  injury_date: string;
  injury_name: string;
  lr_type: number;
  part_type: number;
  bui_cd: number;
  part_id: number;
  trauma_type: number;
  trauma_counter: number;
  trauma_name: string;
  subjective: string;
  present_illness: string;
  objective: string;
  assessment: string;
  plan: string;
  payment_type: number;
  option_checked: number;
  treatment: string;
  remarks: string;
  doctor_id: number;
  doctor_name: string | null;
  status: number;
  version_uuid: string;
}

export interface KaruteSubmitPayload {
  karute_id: number;
}

//#endregion
export interface KaruteCourse {
  course_id: number;
  course_name: string;
  status: 'active' | 'inactive' | string;
  cost: number;
  category_id: number;
  category_name: string;
  category_cd: string;
  payment_course_type: number;
  payment_course_name: string | null;
  duration: string;
  hide_type: number;
  services: {
    service_id: number;
    name: string;
  };
}

export enum KaruteLatestUpdateStatus {
  NOT_SAVED = 1,
  SAVED = 2,
}
export interface KaruteLatestUpdate {
  latest_updated_date: string;
  status: KaruteLatestUpdateStatus;
}

export interface KeikaMondaiRegistered {
  patient_id: number;
  visit_id: number;
  registration_date: string;
  visit_date: string;
  payment_types: string;
  payment_type_checked: number;
  addon_option_checked: boolean;
  doctor: GetKaruteDoctorItem;
  doctors: GetKaruteDoctorItem[];
  selected_jihi_courses: GetKaruteJihiCourseItem[];
  selected_hoken_courses: GetKaruteHokenCourseItem[];
  courses: GetKaruteJihiCourseItem[];
  hoken_courses: GetKaruteHokenCourseItem[];
  disease_bases: RegisteredMondai[];
}
// TO-DO: there're a lot of duplicate type
export type RegisteredMondai = {
  disease_base_id: number;
  version_uuid: string;
  karute_id: number;
  trauma_type: number;
  trauma_counter: number;
  subjective: string;
  present_illness: string;
  objective: string;
  assessment: string;
  plan: string;
  payment_type: number;
  treatment: string;
  option_checked: number;
  remarks: string;
  doctor_id: number;
  visit_date: string;
  visit_id: number;
  injury_name: string;
  injury_name_display: string;
  lr_type: number;
  part_type: number;
  bui_cd: number;
  part_id: number;
  injury_date: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
};

export interface DuplicateInjuryPayload {
  karute_id: number;
  injuries: {
    disease_base_id: number | null;
    injury_name: string;
    status: EStatus;
  }[];
}

export interface DuplicateInjuryResponse {
  disease_base_id: number | null;
  injury_name: string;
  status: EStatus;
  matched: boolean;
}
