import { KaruteMondai } from '@/types/interface/KaruteMondai';
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { mondaiApi } from '../mondai/api';
import { OrderItem } from '../patient/type';
import { sharedApi } from '../shared/api';

export type MondaiQueryParams = {
  patient_id: number;
  status?: number;
  trauma_type?: number;
  search?: string;
  page?: number;
  limit?: number;
} & OrderItem;

const initQuery = {
  status: 1,
  trauma_type: undefined,
  search: undefined,
  patient_id: undefined,
  limit: import.meta.env.MODE !== 'production' ? 5 : 100,
  page: 1,
};

export const mondaiThunks = {
  fetchMondai: createAsyncThunk(
    'karute/fetchMondais',
    async (params: MondaiQueryParams, { dispatch }) => {
      const [result, servicesActive] = await Promise.all([
        await dispatch(
          mondaiApi.endpoints.getMondaiList.initiate(
            { ...params, status: 0 },
            { forceRefetch: true }
          )
        ).unwrap(),
        await dispatch(
          sharedApi.endpoints.getPatientServiceActive.initiate({ patient_id: params.patient_id })
        ).unwrap(),
      ]);
      return { mondais: result.data, servicesActive: servicesActive.data };
    }
  ),
};

const initialData: {
  mondais: KaruteMondai[];
  allMondais: KaruteMondai[];
  loading?: boolean;
  error?: boolean;
  queryParams: Partial<MondaiQueryParams>;
  modalQuery: Partial<MondaiQueryParams>;
  servicesActive: number[];
  selectedMondai?: Partial<KaruteMondai & { service_id?: number }>;
} = {
  mondais: [],
  loading: false,
  error: false,
  servicesActive: [],
  queryParams: {
    status: 1,
    trauma_type: undefined,
  },
  modalQuery: initQuery,
  allMondais: [],
};

export const mondaiSlice = createSlice({
  name: 'mondais',
  initialState: initialData,
  reducers: {
    resetMondais: state => {
      return initialData;
    },
    setMondaiQueryParams: (state, action: PayloadAction<Partial<MondaiQueryParams>>) => {
      state.queryParams = { ...state.queryParams, ...action.payload };
    },
    updateMondaiById: (state, action: PayloadAction<Partial<KaruteMondai>>) => {
      state.mondais = state.mondais.map(item =>
        item.mondai_id === action.payload.mondai_id
          ? {
              ...item,
              ...action.payload,
            }
          : item
      );
    },
    setMondaiModalQuery: (state, action: PayloadAction<Partial<MondaiQueryParams>>) => {
      state.modalQuery = { ...state.modalQuery, ...action.payload };
    },

    resetModalQuery: state => {
      state.modalQuery = initQuery;
    },
    setLoadingMondai: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setSelectedMondai: (
      state,
      action: PayloadAction<Partial<KaruteMondai & { service_id?: number }> | undefined>
    ) => {
      state.selectedMondai = action.payload;
    },
  },
  extraReducers: builder => {
    builder.addCase(mondaiThunks.fetchMondai.fulfilled, (state, action) => {
      if (action.payload) {
        state.allMondais = Array.isArray(action.payload.mondais) ? action.payload.mondais : [];
        state.mondais = Array.isArray(action.payload.mondais)
          ? action.payload.mondais.filter(mondai =>
              state.queryParams.status === 1 ? mondai.status === 1 : mondai
            )
          : [];
        state.servicesActive = Array.isArray(action.payload.servicesActive?.service_id)
          ? action.payload.servicesActive.service_id
          : [];
      }

      state.loading = false;
      state.error = false;
    });
    builder.addCase(mondaiThunks.fetchMondai.pending, state => {
      state.loading = true;
      state.error = false;
    });
    builder.addCase(mondaiThunks.fetchMondai.rejected, state => {
      state.loading = false;
      state.error = true;
    });
  },
});

export const {
  resetMondais,
  setMondaiQueryParams,
  updateMondaiById,
  setMondaiModalQuery,
  setLoadingMondai,
  resetModalQuery,
  setSelectedMondai,
} = mondaiSlice.actions;

export const mondaiReducer = mondaiSlice.reducer;
