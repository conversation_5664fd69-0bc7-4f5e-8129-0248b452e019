import { EGender } from '@/components/page-components/patient-management/create/FormCreate/type';
import { ActiveCourses, CourseItem } from '@/store/visit/type';
import dayjs from 'dayjs';
import 'dayjs/locale/ja';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';
import weekday from 'dayjs/plugin/weekday';
import { jwtDecode } from 'jwt-decode';

dayjs.extend(utc);
dayjs.extend(weekday);
dayjs.extend(updateLocale);
dayjs.locale('ja');

dayjs.updateLocale('ja', {
  weekdaysShort: ['日', '月', '火', '水', '木', '金', '土'],
});
export const isTokenExpired = (token: string) => {
  const jwtToken = jwtDecode(token);
  const currentTime = dayjs().unix();

  return jwtToken.exp && jwtToken.exp <= currentTime;
};

export const formatDate = {
  /**
   * Convert a date string in 'YYYY-MM-DD' format to Japanese format 'YYYY年MM月DD日'.
   *
   * @param {string} dateStr - A date string in 'YYYY-MM-DD' format.
   * @returns {string} A formatted string like '2024年04月11日'.
   *
   * @example
   * formatDate.toJapanDate('2024-04-11') // '2024年04月11日'
   */
  toJapanDate: (dateStr: string): string => {
    const date = dayjs(dateStr);
    return date.isValid() ? date.format('YYYY年MM月DD日') : '';
  },

  toJapanDayOfWeek: (date: string): string => {
    if (!date) return '';
    const dateObj = dayjs(date);
    const year = dateObj.year();
    const month = dateObj.month() + 1;
    const day = dateObj.date();
    const weekday = dateObj.format('ddd');
    return `${year}年${month}月${day}日（${weekday}）`;
  },

  /**
   * Convert a 'YYYY-MM-DD' date string into a Dayjs instance at the start of the day in UTC.
   * Useful to avoid timezone-related date shifting issues.
   *
   * @param {string} dateStr - A date string in 'YYYY-MM-DD' format.
   * @returns {dayjs.Dayjs} A Dayjs object set to 00:00 UTC of the given date.
   *
   * @example
   * formatDate.YYYYMMDDtoDayJs('2024-04-11') // Dayjs object at 2024-04-11T00:00:00Z
   */
  YYYYMMDDtoDayJs: (dateStr: string): dayjs.Dayjs => {
    return dayjs.utc(dateStr, 'YYYY-MM-DD').startOf('day') ?? '';
  },
};
export const formatGender = (gender: EGender) => {
  switch (gender) {
    case EGender.MALE:
      return '男性';
    case EGender.FEMALE:
      return '女性';
    default:
      return String(gender);
  }
};

export const downloadBlobFile = (data: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(data);

  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();

  a.remove();
  window.URL.revokeObjectURL(url);
};
/**
 * Converts a key-value object map into an array of options in the format { value, label }.
 *
 * @param {Object} map - The key-value object to convert.
 * @returns {Array} - An array of objects with { value, label } structure.
 */
export const convertMapToOptions = (map: object) => {
  return Object.entries(map).map(([value, label]) => ({
    value,
    label,
  }));
};

export const flattenCourses = (courses: ActiveCourses[]) => {
  const result: Array<CourseItem> = [];

  courses.forEach(item => {
    item.data.forEach(inner => {
      if (inner.data) {
        if (Array.isArray(inner.data)) {
          inner.data.forEach(course => {
            result.push({
              ...course,
              payment_course_name: inner.payment_course_name || '',
            });
          });
        }
      } else {
        result.push(inner as unknown as CourseItem);
      }
    });
  });

  return result;
};

export const isSameBasePath = (
  from: string,
  to: string,
  segments: string | string[] = 'schema'
): boolean => {
  const segList = Array.isArray(segments) ? segments : [segments];

  const normalize = (p: string) => {
    let path = p.split('?')[0].split('#')[0];

    for (const seg of segList) {
      if (!seg) continue;
      const token = `/${seg}/`;
      const idx = path.indexOf(token);
      if (idx !== -1) path = path.slice(0, idx);
    }

    return path.replace(/\/$/, '');
  };

  return normalize(from) === normalize(to);
};

export * from './dashboard';
export * from './formValues';
export * from './getRole';
export * from './kana';
export * from './schema';
export * from './karute';
