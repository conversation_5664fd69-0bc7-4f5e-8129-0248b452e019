import dayjs from 'dayjs';
import { jwtDecode } from 'jwt-decode';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
export const isTokenExpired = (token: string) => {
  const jwtToken = jwtDecode(token);
  const currentTime = dayjs().unix();

  return jwtToken.exp && jwtToken.exp <= currentTime;
};

export const formatDate = {
  /**
   * Convert a date string in 'YYYY-MM-DD' format to Japanese format 'YYYY年MM月DD日'.
   *
   * @param {string} dateStr - A date string in 'YYYY-MM-DD' format.
   * @returns {string} A formatted string like '2024年04月11日'.
   *
   * @example
   * formatDate.toJapanDate('2024-04-11') // '2024年04月11日'
   */
  toJapanDate: (dateStr: string): string => {
    const date = dayjs(dateStr);
    return date.isValid() ? date.format('YYYY年MM月DD日') : '';
  },

  /**
   * Convert a 'YYYY-MM-DD' date string into a Dayjs instance at the start of the day in UTC.
   * Useful to avoid timezone-related date shifting issues.
   *
   * @param {string} dateStr - A date string in 'YYYY-MM-DD' format.
   * @returns {dayjs.Dayjs} A Dayjs object set to 00:00 UTC of the given date.
   *
   * @example
   * formatDate.YYYYMMDDtoDayJs('2024-04-11') // Dayjs object at 2024-04-11T00:00:00Z
   */
  YYYYMMDDtoDayJs: (dateStr: string): dayjs.Dayjs => {
    return dayjs.utc(dateStr, 'YYYY-MM-DD').startOf('day') ?? '';
  },
};

export const downloadBlobFile = (data: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(data);

  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();

  a.remove();
  window.URL.revokeObjectURL(url);
};

export const makeLocationSnapshot = (loc: Location) => ({
  pathname: loc.pathname,
  search: loc.search,
  hash: loc.hash,
});
export * from './formValues';
export * from './fileToBase64';
export * from './fileToSrc';
export * from './patternToRegex';
export * from './getRole';
export * from './kana';
