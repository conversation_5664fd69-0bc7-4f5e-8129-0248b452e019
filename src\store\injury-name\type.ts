import { LeftRightOption } from '@/components/page-components/karute-managment/karte-input/InjuryNameModal/type';

export interface InjuryBlock {
  bruises_sprains_front: OptionsByBlock[];
  bruises_sprains_back: OptionsByBlock[];
  dislocation_fracture_front: OptionsByBlock[];
  dislocation_fracture_back: OptionsByBlock[];
}

export interface OptionsByBlock {
  block_name: string;
  block_no: number;
  lr_type: number;
  options: BlockOption[];
}

export interface BlockOption {
  part_type: number;
  part_id: number;
  name: string;
  disp_name: string;
  disp_full_name: string;
  m_fushobui_id: number;
  anatomical_injury_id: number;
  part_name_original: string;
  part_name: string;
  block_l_no: number;
  block_r_no: number;
  dbk_cd: number;
  nnz_cd: number;
  zsu_cd: number;
  dx_cd: number;
  fx_cd: number;
  ffx_cd: number;
  bui_cd: number;
  treatment_column: string;
}

export interface PartType {
  injury_type_id: number;
  part_type: number;
  name: string;
}

export interface PartTypeOption {
  anatomical_injury_id: number;
  m_fushobui_id: number;
  part_id: number;
  part_name_original: string;
  part_name: string;
  dbk_cd: number;
  nnz_cd: number;
  zsu_cd: number;
  dx_cd: number;
  fx_cd: number;
  ffx_cd: number;
  block_r_no: number;
  block_l_no: number;
  lr_type: number;
  dbk_sort: number;
  nnz_sort: number;
  zsu_sort: number;
  dx_sort: number;
  fx_sort: number;
  ffx_sort: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface OptionsByPartType {
  [key: number]: PartTypeOption[];
}

export interface OptionBySide {
  name: string;
  part_type: number;
  lr_type: number;
  part_id: number;
  bui_cd: number;
  kani_cd: number;
  value: string;
}
export type OptionsBySide = Record<LeftRightOption, OptionBySide[]>;
