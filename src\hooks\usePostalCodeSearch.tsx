import { useState } from 'react';
import { Flex, Radio } from 'antd';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { AppRadio } from '@/components/common/core/Radio';
import { ButtonType } from '@/types/enum';
import { showFlashNotice } from '@/utils';
import Button from '@/components/common/core/Button';

interface AddressResponse {
  postalCode: string;
  addresses: {
    ja: { prefecture: string; address1: string };
    kana: { prefecture: string; address1: string };
  }[];
}

export const usePostalCodeSearch = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { show, hide } = useModalConfirm();

  const searchPostalCode = async (postalCode: string): Promise<string | undefined> => {
    if (postalCode.length < 7) return;

    try {
      setIsLoading(true);
      const res = await fetch(`https://jp-postal-code-api.ttskch.com/api/v1/${postalCode}.json`);
      const data: AddressResponse = await res.json();
      if (data.addresses.length > 1) {
        return new Promise((resolve) => {
          let selectedIndex = -1;

          show({
            title: <>住所検索結果 (〒{postalCode})</>,
            content: (
              <Radio.Group style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {data.addresses.map((address, index) => (
                  <Flex align="start" key={index}>
                    <AppRadio
                      value={index}
                      onChange={(e) => {
                        if (e.target.checked) selectedIndex = e.target.value;
                      }}
                    />
                    <Flex vertical gap={8}>
                      <span className="fs14-medium">
                        {address.ja.prefecture}
                        {address.ja.address1}
                      </span>
                      <span className="fs12-regular">
                        {address.kana.prefecture}
                        {address.kana.address1}
                      </span>
                    </Flex>
                  </Flex>
                ))}
              </Radio.Group>
            ),
            buttons: [
              <Button
                key="cancel"
                customType={ButtonType.SECONDARY_COLOR}
                onClick={() => {
                  hide();
                  resolve(undefined); // User cancel
                }}
                customSize="lg"
                style={{ width: '120px' }}
              >
                キャンセル
              </Button>,
              <Button
                key="confirm"
                customSize="lg"
                customType={ButtonType.PRIMARY}
                loading={isLoading}
                onClick={() => {
                  hide();
                  if (selectedIndex >= 0) {
                    resolve(
                      data.addresses[selectedIndex].ja.prefecture +
                      data.addresses[selectedIndex].ja.address1
                    );
                  }
                }}
                style={{ width: '120px' }}
              >
                選択
              </Button>,
            ],
            width: '464px',
          });
        });
      } else {
        return data.addresses[0].ja.prefecture + data.addresses[0].ja.address1;
      }
    } catch {
      showFlashNotice({
        type: 'error',
        message: '入力した郵便番号に該当する住所がありません。',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    searchPostalCode,
    isLoading,
  };
};
