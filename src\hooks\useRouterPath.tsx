import { generatePath } from 'react-router-dom';
import { useAppSelector } from '@/config/redux/store';
import useAuth from '@/hooks/useAuth';

export function useRouterPath() {
  const clinicCd = useAppSelector(s => s.auth.currentClinicCd);
  const { isAuthenticated, expiredAt } = useAuth();
  const getPath = (pattern: string, params?: Record<string, string>) => {
    const hasAuth = isAuthenticated && !(expiredAt && expiredAt < Date.now());
    if (!hasAuth) {
      return '/login';
    }

    if (!clinicCd) {
      throw new Error('Chưa có clinicCd trong store');
    }

    if (pattern.includes(':')) {
      return generatePath(pattern, { clinicCd, ...(params || {}) } as any);
    } else {
      const normalized = pattern.startsWith('/') ? pattern : `/${pattern}`;
      return `/${clinicCd}${normalized}`;
    }
  };
  return { getPath };
}

