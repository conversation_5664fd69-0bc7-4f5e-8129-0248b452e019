.mondaiSectionTable {
  border-radius: 8px;
  overflow: hidden;
  margin-top: 24px;
  background-color: var(--white);
  height: 100%;
  > div:nth-child(2) {
    overflow-y: hidden;
    height: calc(100% - 50px);
  }

  .header {
    border-radius: 8px 8px 0 0;
    background-color: var(--white);
    padding: 12px;
    border-bottom: 1px solid $gray-200;
    height: 60px;
  }
  .selectMondai {
    width: 80px !important;
    height: 36px !important;
    min-width: unset;
    :global(.ant-select-selector) {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      :global(.ant-select-selection-wrap) {
        height: 100% !important;
      }
      :global(.ant-select-selection-item-remove) {
        display: none;
      }
      :global(.ant-select-selection-item) {
        background-color: unset;
        margin-inline-end: unset;
        margin-block: unset;
      }
    }
  }
  :global(.ant-table-cell) {
    height: 56px;
  }
  :global(.ant-table-expanded-row) {
    > td:first-child {
      padding: unset !important;
    }
  }

  .mainTable,
  .subTable {
    :global(.ant-table-cell) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      overflow-wrap: normal;
    }
  }
  .mainTable {
    :global(.ant-table-thead) {
      :global(.ant-table-cell) {
        height: 34px;
      }
    }
    :global(.ant-table-row) {
      text-align: center;
    }

    :global(.ant-table-cell .ant-table-row-expand-icon-cell) {
      width: 48px;
      height: 50px;
    }
    :global(.ant-table-row-expand-icon-cell) {
      > button {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        > span {
          width: unset !important;
          height: unset !important;
        }
      }
    }
  }
  .subTable {
    :global(.ant-table-content) {
      > table {
        table-layout: fixed;
      }
    }
    :global(.ant-table-row) {
      background-color: var(--gray-50);
    }
    :global(.ant-table-body) {
      height: auto;
    }
    :global(.ant-table-head) {
      height: 34px !important;
    }
    :global(.ant-table-row) {
      text-align: center;
    }

    :global(.ant-table-row-expand-icon-cell) {
      > button {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        > span {
          width: unset !important;
          height: unset !important;
        }
      }
    }
  }

  .highlightRow {
    background-color: $pink-100;
  }
}

.mondaiType {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  > p:nth-child(2) {
    padding-left: 16px;
    border-left: 1px solid var(--gray-200);
  }
}

.mondaiStatus {
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
}
.active {
  width: 72px;
  height: 23px;
  border-radius: 16px;
  color: $success-700;
  background-color: #eef4e2;
  padding: 3px 16px;
}
.inactive {
  width: 72px;
  height: 23px;
  border-radius: 16px;
  color: $error-600;
  padding: 3px 16px;
  background-color: $error-100;
}

.mondaiModal {
  .mondaiSectionTable {
    margin-top: unset;
  }
  :global(.ant-modal) {
    max-height: 678px;
    height: 678px;
  }
  :global(.ant-modal-content) {
    padding: 20px 0;
    height: 100%;
    padding-bottom: unset;
  }
  :global(.ant-table) {
    height: 594px;
    max-height: 594px;
  }
  .mainTable {
    :global(.ant-table-body) {
      height: unset;
    }
  }
  .subTable {
    :global(.ant-table) {
      height: auto !important;
    }
  }
  .mainTable,
  .subTable {
    :global(.ant-table-cell) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      overflow-wrap: normal;
    }
  }
  .mainTable {
    :global(.ant-table-head) {
      height: 34px !important;
    }
    :global(.ant-table-row) {
      text-align: center;
    }

    :global(.ant-table-cell .ant-table-row-expand-icon-cell) {
      width: 48px;
      height: 50px;
    }
    :global(.ant-table-row-expand-icon-cell) {
      > button {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        > span {
          width: unset !important;
          height: unset !important;
        }
      }
    }
  }
  .subTable {
    :global(.ant-table-content) {
      > table {
        table-layout: fixed;
      }
    }
    :global(.ant-table-row) {
      background-color: var(--gray-50);
    }
    :global(.ant-table-body) {
      height: auto;
    }
    :global(.ant-table-head) {
      height: 34px !important;
    }
    :global(.ant-table-row) {
      text-align: center;
    }

    :global(.ant-table-row-expand-icon-cell) {
      > button {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        > span {
          width: unset !important;
          height: unset !important;
        }
      }
    }
  }
  :global(.ant-table-cell) {
    height: 50px;
  }

  :global(.ant-pagination) {
    margin: 16px 0;
    row-gap: 8;
    align-items: baseline;
    :global(.ant-pagination-item) {
      width: 24px;
      height: 24px;
      font-size: 12px;
      line-height: 18px;
      min-width: 24px;
    }
    :global(.ant-pagination-item-active) {
      background-color: #2a5875;
      border: none;
      transition: none !important;
      border-radius: 4px !important;
      box-shadow: none !important;
      > a {
        color: white;
      }
    }
  }
}

.checkboxCustom {
  :global(.ant-checkbox) {
    width: 100% !important;
    height: 100% !important;
    :global(.ant-checkbox-inner) {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.headerModal {
  border-radius: 8px 8px 0 0;
  background-color: var(--white);
  padding: 0 24px;
  height: 60px;
  .title {
    color: $gray-800;
    font-weight: 700;
    text-wrap: nowrap;
    font-size: 18px;
    line-height: 20px;
  }
}
.mondaiInputModal {
  padding: 0 24px;
  > input {
    padding: unset;
  }
  :global(.ant-input-affix-wrapper) {
    padding: unset !important;
  }
  :global(.ant-input-suffix) {
    padding: 10px 12px 10px 0;
  }
}

.searchBtn {
  width: 100px !important;
}

.title {
  color: $gray-800;
  text-wrap: nowrap;
}
