import Button from '@/components/common/core/Button';
import Table, { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { AppModal } from '@/components/common/core/Modal';
import { useGetPatientListQuery } from '@/store/patient/api';
import { PatientItem } from '@/store/patient/type';
import { ButtonType } from '@/types/enum';
import { formatPhoneNumber } from '@/utils/helper';
import { Flex, ModalProps } from 'antd';
import { useEffect, useState } from 'react';
import ModalTitle from '../../layout/ModalTitle/ModalTitle';
import './PatientExistsSearchModal.modules.scss';
import { DATE_FORMATS, genderMap, PAGINATION_THRESHOLD } from '@/types/constants';
import { APIResponse, ListResponse } from '@/types/interface';
import dayjs from 'dayjs';
import { EGender } from '@/components/page-components/patient-management/create/FormCreate/type';
import clsx from 'clsx';

interface Props extends ModalProps {
  isModalOpen: boolean;
  searchValue?: string;
  onCancel?: () => void;
  onConfirm?: (patient: any) => void;
}

const columns: CustomColumnType<PatientItem>[] = [
  {
    title: '患者番号',
    dataIndex: 'patient_cd',
    key: 'patient_cd',
    align: 'right',
    width: 77,
  },
  {
    title: '氏名 (漢字)',
    dataIndex: 'name',
    key: 'name',
    align: 'left',
    width: 104,
  },
  {
    title: '氏名（カナ)',
    dataIndex: 'kana',
    key: 'kana',
    align: 'left',
    width: 115,
  },
  {
    title: '性別',
    dataIndex: 'gender',
    key: 'gender',
    align: 'left',
    width: 54,
    render: (gender: EGender) => genderMap[gender],
  },
  {
    title: '生年月日',
    dataIndex: '',
    key: 'birthday',
    align: 'right',
    render: (value: any) =>
      value.birthday ? dayjs(value.birthday).format(DATE_FORMATS.DATE) : '-',
    width: 113,
  },
  {
    title: '電話番号',
    dataIndex: '',
    key: 'phone_number',
    align: 'right',
    width: 94,
    render: (value: any) => (value.cellphone ? formatPhoneNumber(value.cellphone) : '-'),
  },
];

const AppPatientExistsSearchModal = ({
  isModalOpen,
  onCancel,
  onConfirm,
  searchValue,
  ...props
}: Props) => {
  const [patientSelect, setPatientSelect] = useState<PatientItem>();
  const [dataSource, setDataSource] = useState<
    APIResponse<ListResponse<PatientItem>> | undefined
  >();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const shouldFetch = isModalOpen;

  const { data: patients, isFetching } = useGetPatientListQuery(
    { type_search: searchValue?.trim(), page: currentPage, limit: PAGINATION_THRESHOLD },
    {
      skip: !shouldFetch,
    }
  );

  useEffect(() => {
    if (patients) {
      setDataSource(patients);
    }
  }, [patients]);
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const handleCancel = () => {
    onCancel?.();
    setDataSource(undefined);
  };
  useEffect(() => {
    setCurrentPage(1);
    setDataSource(undefined);
  }, [isModalOpen]);
  return (
    <AppModal
      className="patient-search-result-modal"
      destroyOnClose
      open={isModalOpen}
      onCancel={handleCancel}
      width={600}
      centered
      key={isModalOpen ? 'open' : 'closed'}
      isPadding={false}
      zIndex={1200}
      {...props}
    >
      <ModalTitle title="検索結果" className="pt-5 px-6" />
      <div className="tableWrapper">
        <Table
          rowClassName={record =>
            patientSelect && record.patient_id === patientSelect?.patient_id ? 'selected-row' : ''
          }
          style={{
            overflowX: 'auto',
            marginBottom: dataSource?.data?.total ? '0' : '20px',
          }}
          rowKey={record => record.patient_id}
          onRow={record => {
            return {
              onClick: () => {
                setPatientSelect(record);
              },
            };
          }}
          columns={columns}
          dataSource={dataSource?.data?.data || []}
          loading={isFetching}
          scroll={{
            y: 340,
          }}
          pagination={
            (dataSource?.data?.total ?? 0) > PAGINATION_THRESHOLD
              ? {
                  current: dataSource?.data?.current_page ?? 1,
                  pageSize: dataSource?.data?.per_page ?? PAGINATION_THRESHOLD,
                  showSizeChanger: false,
                  total: dataSource?.data?.total ?? 0,
                  onChange: handlePageChange,
                }
              : false
          }
        />
      </div>
      <div className={clsx({ 'pb-5': !dataSource?.data?.total })}>
        {!!dataSource?.data?.total && (
          <Flex
            gap="12px"
            justify="end"
            className={clsx('pb-5 px-6', {
              'mt-2': (dataSource?.data?.total ?? 0) > PAGINATION_THRESHOLD,
              'mt-6': (dataSource?.data?.total ?? 0) <= PAGINATION_THRESHOLD,
            })}
          >
            <Button
              customType={ButtonType.SECONDARY_COLOR}
              onClick={handleCancel}
              className="btn-modal-width"
              customSize="lg"
            >
              キャンセル
            </Button>
            <Button
              customType={ButtonType.PRIMARY}
              htmlType="submit"
              className="btn-modal-width"
              onClick={() => {
                onConfirm?.(patientSelect);
              }}
              disabled={!patientSelect}
              loading={isFetching}
              customSize="lg"
            >
              登録
            </Button>
          </Flex>
        )}
      </div>
    </AppModal>
  );
};

export default AppPatientExistsSearchModal;
