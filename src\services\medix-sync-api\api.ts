import axios from 'axios';
import { showFlashNotice } from '@/utils/flashNotice';
import { LOCAL_STORAGE_KEY } from '@/types/constants';
import { logout } from './logout';

const baseURL = import.meta.env.VITE_MEDIX_SYNC_API;

const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  config => {
    const token = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

api.interceptors.response.use(
  response => {
    if (response.status === 401 || response.data.status === 401) {
      logout();
    }
    return response;
  },
  error => {
     if (!window.navigator.onLine) {
       window.location.reload();
     }
    if (error.response) {
      if (error.response.status === 500) {
        showFlashNotice({
          type: 'error',
          message: '予期せぬエラーが発生しました。',
        });
      }
    } else if (!error.response) {
      showFlashNotice({
        type: 'error',
        message: '予期せぬエラーが発生しました。',
      });
    }
    return Promise.reject(error);
  }
);

export default api;
