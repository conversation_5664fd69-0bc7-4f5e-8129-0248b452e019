import {
  BlockResult,
  BlockSelect,
  LeftRightOption,
  LRType,
  PartIdOption,
} from '@/components/page-components/karute-managment/karte-input/InjuryNameModal/type';
import {
  InjuryBlock,
  OptionBySide,
  OptionsByBlock,
  OptionsByPartType,
  OptionsBySide,
  PartType,
} from '@/store/injury-name/type';
import { LOCAL_STORAGE_KEY } from '@/types/constants';
import { useMemo } from 'react';
import useAuth from './useAuth';

export interface InjuryNameStorage {
  optionsByBlock: InjuryBlock;
  partTypes: PartType[];
  optionsByPartType: OptionsByPartType;
  optionsBySide: OptionsBySide;
  isInitialized: boolean;
}

export const INITIAL_STORAGE: InjuryNameStorage = {
  optionsByBlock: {
    bruises_sprains_front: [],
    bruises_sprains_back: [],
    dislocation_fracture_front: [],
    dislocation_fracture_back: [],
  },
  partTypes: [],
  optionsByPartType: {},
  optionsBySide: {
    left: [],
    right: [],
  },
  isInitialized: false,
};

export default function useInjuryNameStore() {
  const { isAuthenticated, expiredAt } = useAuth();
  const isLoggedIn = isAuthenticated && !(expiredAt && expiredAt < Date.now());

  const stored = useMemo<InjuryNameStorage>(() => {
    if (!isLoggedIn) {
      return INITIAL_STORAGE;
    }
    const raw = window.localStorage.getItem(LOCAL_STORAGE_KEY.INJURY_NAME);
    if (!raw) {
      return INITIAL_STORAGE;
    }
    try {
      const parsed = JSON.parse(raw) as InjuryNameStorage;
      if (!parsed || typeof parsed !== 'object' || !parsed.optionsByBlock || !parsed.partTypes) {
        return INITIAL_STORAGE;
      }
      return parsed;
    } catch {
      return INITIAL_STORAGE;
    }
  }, [isLoggedIn]);

  const setInjuryNameStorage = (payload: InjuryNameStorage) => {
    window.localStorage.setItem(LOCAL_STORAGE_KEY.INJURY_NAME, JSON.stringify(payload));
  };

  const getOptionsByBlock = (key: string, blockNo: number): BlockSelect => {
    const block = stored.optionsByBlock[key as keyof InjuryBlock]?.find(
      b => b.block_no === blockNo
    );
    const options = block?.options.map(item => ({
      part_type: item.part_type,
      part_id: item.part_id,
      lr_type: block.lr_type,
      disp_name: item.disp_name,
    }));
    return { options: options || [], lr_type: block?.lr_type || LRType.None };
  };

  const getPartIdOptions = (partType: number): PartIdOption[] => {
    const options = stored.optionsByPartType[partType];
    if (!options) return [];
    return options.map(item => ({
      part_id: item.part_id,
      part_name: item.part_name,
      part_name_original: item.part_name_original,
      lr_type: item.lr_type,
    }));
  };

  const getOptionsBySide = (side: LeftRightOption): OptionBySide[] => {
    return stored.optionsBySide[side].map(item => ({
      ...item,
      value: `${item.part_type}_${item.part_id}_${item.lr_type}_${side}`,
    }));
  };

  const getBlocksByPart = (
    partType: number | null,
    partId: number | null,
    lrType: LRType | null
  ): BlockResult | null => {
    if (partId == null) return null;

    const blocksInfo: { no: number; lr: LRType }[] = [];

    (Object.values(stored.optionsByBlock) as OptionsByBlock[][]).forEach(arr =>
      arr.forEach(b => {
        if (b.options.some(o => o.part_id === partId && o.part_type === partType)) {
          blocksInfo.push({ no: b.block_no, lr: b.lr_type as LRType });
        }
      })
    );

    if (!blocksInfo.length) return null;

    const center = blocksInfo.filter(b => b.lr === LRType.None).map(b => b.no);
    const left = blocksInfo.filter(b => b.lr === LRType.Left).map(b => b.no);
    const right = blocksInfo.filter(b => b.lr === LRType.Right).map(b => b.no);

    const needSide = center.length === 0;

    if (needSide && (lrType == null || lrType === LRType.None)) return null;

    let result: number[] = [];

    if (lrType == null || lrType === LRType.None) {
      result = center.length ? center : [...left, ...right];
    } else if (lrType === LRType.Left) {
      result = left.length ? left : center;
    } else if (lrType === LRType.Right) {
      result = right.length ? right : center;
    }

    return result.length ? { blocks: [...new Set(result)] } : null;
  };

  const hasCenterBlock = (partType: number | null, partId: number): boolean => {
    const allBlocks = (Object.values(stored.optionsByBlock) as OptionsByBlock[][]).flat();
    return allBlocks.some(
      blk =>
        blk.lr_type === LRType.None &&
        blk.options.some(opt => opt.part_id === partId && opt.part_type === partType)
    );
  };

  return {
    optionsByBlock: stored.optionsByBlock,
    partTypes: stored.partTypes,
    optionsByPartType: stored.optionsByPartType,
    optionsBySide: stored.optionsBySide,
    isInitialized: stored.isInitialized,
    getOptionsByBlock,
    getPartIdOptions,
    getOptionsBySide,
    setInjuryNameStorage,
    getBlocksByPart,
    hasCenterBlock,
  };
}
