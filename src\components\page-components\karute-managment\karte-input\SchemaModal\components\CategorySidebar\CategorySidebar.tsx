import { useSchema } from '@/components/page-components/karute-managment/karte-input/SchemaModal/context/SchemaContext';
import { getCategories } from '@/store/schema/selectors';
import { Category } from '@/store/schema/type';
import clsx from 'clsx';
import React, { useEffect, useState, useTransition } from 'react';
import { useSelector } from 'react-redux';
import styles from './CategorySidebar.module.scss';
const CategorySidebar: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const { categoryId, setCategoryId, updateCategorySchemaImages, setIsPendingCategory } =
    useSchema();
  const [collapsed] = useState(false);
  const [isPending, startTransition] = useTransition();
  const storedCategories = useSelector(getCategories);

  useEffect(() => {
    setCategories(storedCategories.list);
  }, [storedCategories]);

  const handleClick = (nextId: number) => {
    if (nextId === categoryId) return;
    setCategoryId(nextId);
    setIsPendingCategory(true);

    startTransition(() => {
      updateCategorySchemaImages(nextId).finally(() => {
        setIsPendingCategory(false);
      });
    });
  };

  return (
    <div className={clsx(styles.sidebar, collapsed && styles.collapsed)}>
      <div className={styles.menu}>
        {[...categories]
          .sort((a, b) => {
            if (a.category_schema_id === 0 && b.category_schema_id !== 0) return -1;
            if (a.category_schema_id !== 0 && b.category_schema_id === 0) return 1;
            return a.category_schema_id - b.category_schema_id;
          })
          .map(cat => (
            <div
              key={cat.category_schema_id}
              className={clsx(
                styles.menuItem,
                cat.category_schema_id === categoryId && styles.active,
                isPending && styles.pending
              )}
              onClick={() => handleClick(cat.category_schema_id)}
              title={cat.name}
            >
              {cat.name}
            </div>
          ))}
      </div>
    </div>
  );
};

export default CategorySidebar;
