.container {
  display: inline-block;
  position: relative;
}

.inputTagContainer {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  padding: 0 8px;
  overflow: hidden;
  cursor: pointer;
  font-size: 14px;
  height: 32px;
}

.tag {
  width: 50px;
  margin-right: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: $brand-100;
  color: $brand-700;
  padding: 4px;
  height: 24px;
  border-radius: 12px;
  line-height: 16px;

  &.plusTag {
    width: 30px;
  }
}

.placeholder {
  color: rgba(0, 0, 0, 0.25);
}

.arrow {
  margin-left: auto;
  display: flex;
  align-items: center;
  color: #bfbfbf;
}

.options {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px 0;
}

.optionItem {
  padding: 4px 8px;
}

.dropdown {
  padding: 4px 8px;
  .dropdown_action {
    :global(> .ant-btn) {
      color: #257399;
      &:hover {
        color: $brand-800 !important;
      }
    }
  }
}

.customPopover {
  :global(.ant-popover-inner) {
    padding: 0;
  }
}
