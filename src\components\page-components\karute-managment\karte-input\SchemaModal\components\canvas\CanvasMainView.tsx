import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { SchemaImage } from '@/store/schema/type';
import { ButtonType } from '@/types/enum';
import { getImgKey } from '@/utils';
import clsx from 'clsx';
import { useEffect } from 'react';
import { Color, COLORS, STAGE_H, STAGE_W } from '../../type';
import styles from './Canvas.module.scss';
import DrawingCanvas, { DrawingCanvasHandle } from './DrawingCanvas';

interface Props {
  images: SchemaImage[];
  current: number;
  setCurrent: React.Dispatch<React.SetStateAction<number>>;
  confirmRemove: () => void;
  activeColor: Color;
  canvasRefs: Record<string, DrawingCanvasHandle | null>;
}

const CanvasMainView: React.FC<Props> = ({
  images,
  current,
  setCurrent,
  confirmRemove,
  activeColor,
  canvasRefs,
}) => {
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'z') {
        e.preventDefault();

        const uid = images[current] ? getImgKey(images[current]) : undefined;
        if (uid && canvasRefs[uid]) {
          canvasRefs[uid]!.undo();
        }
      }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [current, images, canvasRefs]);

  return (
    <div className={styles.mainView}>
      <div className={styles.actionBtns}>
        <Button
          shape="circle"
          className={clsx(styles.navBtn, styles.prev)}
          onClick={() => setCurrent(i => Math.max(0, i - 1))}
          disabled={current === 0}
          customType={current === 0 ? ButtonType.SECONDARY_COLOR : ButtonType.PRIMARY}
        >
          <Icon name="arrowLeft2" />
        </Button>

        <Button
          shape="circle"
          className={clsx(styles.navBtn, styles.next)}
          onClick={() => setCurrent(i => Math.min(images.length - 1, i + 1))}
          disabled={current === images.length - 1}
          customType={
            current === images.length - 1 ? ButtonType.SECONDARY_COLOR : ButtonType.PRIMARY
          }
        >
          <Icon name="arrowRight2" />
        </Button>

        <Button
          shape="circle"
          className={styles.removeBtn}
          customType={ButtonType.RED_SECONDARY}
          onClick={confirmRemove}
        >
          <Icon name="close" />
        </Button>
      </div>

      <div className={styles.canvasArea} style={{ height: STAGE_H }}>
        {images.map((img, idx) => {
          const key = getImgKey(img);
          return (
            <div key={key} style={{ display: idx === current ? 'block' : 'none' }}>
              <DrawingCanvas
                ref={h => (canvasRefs[key] = h)}
                imgId={key}
                imgUrl={img.url}
                width={STAGE_W}
                height={STAGE_H}
                color={activeColor || COLORS.COLOR_1}
              />
            </div>
          );
        })}
      </div>

      <div className={styles.dots}>
        {images.map((_, idx) => (
          <span
            key={idx}
            className={idx === current ? styles.dotActive : styles.dot}
            onClick={() => setCurrent(idx)}
          />
        ))}
      </div>
    </div>
  );
};

export default CanvasMainView;
