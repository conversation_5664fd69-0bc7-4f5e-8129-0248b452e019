.loginMain {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  max-width: 100%;
  .logoText {
    font-size: 24px;
    font-weight: 700;
    color: $brand-800;
  }

  .loginForm {
    background-color: white;
    padding: 32px 32px 40px 32px;
    border-radius: 24px;
    box-shadow: 3px 4px 20px 0px rgba(16, 24, 40, 0.05);
    width: 442px;
    max-width: 100%;

    .title {
      color: $brand-800;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
    }

    .loginInput {
      margin-top: 32px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    .emailRequestSuccessLine {
      margin-top: 16px;
      color: $gray-800;
    }
    .resetPasswordDescription {
      color: $gray-500;
    }
    .resetPasswordFailedDescription {
      color: $gray-800;
      margin-top: 16px;
      text-align: center;
    }
    .resetPasswordButton {
      margin-top: 6px;
    }
    .loginButton {
      margin-top: 32px;
    }
    .forgetPasswordButton {
      margin-top: 16px;
    }
  }

  .loginFail {
    color: $error-600;
    margin-top: 16px;
  }
  .forgetPasswordFail {
    color: $error-600;
    opacity: 0;
    transform: translateY(-4px);
    transition: margin-top 0.2s ease, opacity 0.2s ease, transform 0.2s ease;

    &.visible {
      opacity: 1;
      margin-top: 16px;
      transform: translateY(0);
    }
  }

  .loginFooter {
    color: $brand-800;
    cursor: pointer;
  }
}
.reset_password_success {
  .loginForm {
    width: 500px;
  }
}
