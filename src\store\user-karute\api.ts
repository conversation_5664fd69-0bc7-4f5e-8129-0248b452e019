import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse } from '@/types/interface';
import { DetailUserResponse, UserUpdateMetaPayload } from './type';

export const medixUserKaruteManagementApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,
    updateUserMeta: build.mutation<APIResponse<DetailUserResponse>, UserUpdateMetaPayload | any>({
      query: params => {
        return {
          url: `/api/users/update-meta`,
          body: params,
          method: 'POST',
        };
      },
    }),
  }),
});

export const { useUpdateUserMetaMutation } = medixUserKaruteManagementApi;
