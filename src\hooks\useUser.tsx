import { useState, useEffect } from 'react';
import { loadUser } from '@/utils/userStorage';
import { UserInfo } from '@/types/interface';

export default function useUser(): UserInfo | null {
  const [ver, setVer] = useState(0);

  useEffect(() => {
    const h = () => setVer(v => v + 1);
    window.addEventListener('user-change', h);
    return () => window.removeEventListener('user-change', h);
  }, []);

  return loadUser();
}
