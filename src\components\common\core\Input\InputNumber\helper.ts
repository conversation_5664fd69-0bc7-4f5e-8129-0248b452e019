const numberFormatter = new Intl.NumberFormat('en-US', {
  style: 'decimal',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

const numberFormatterWithDecimal = new Intl.NumberFormat('en-US', {
  style: 'decimal',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

export function formatNumber(value: number) {
  return numberFormatter.format(value);
}

export function formatNumberWithDecimal(value: number) {
  return numberFormatterWithDecimal.format(value);
}

export function unicodeToKanji(unicodeStr: string) {
  if (!unicodeStr) return '';
  return unicodeStr.replace(/\\u[\dA-F]{4}/gi, match => {
    return String.fromCharCode(parseInt(match.replace('\\u', ''), 16));
  });
}
