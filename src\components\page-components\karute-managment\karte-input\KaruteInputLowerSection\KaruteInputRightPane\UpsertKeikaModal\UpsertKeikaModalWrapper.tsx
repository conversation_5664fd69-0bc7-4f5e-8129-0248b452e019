import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import React, { useRef } from 'react';
import { useKaruteInput } from '../../../provider';
import UpsertKeikaModal from './UpsertKeikaModal';

type ModalProps = Omit<React.ComponentProps<typeof UpsertKeikaModal>, 'unsavedHandlers'>;

export default function UpsertKeikaModalWrapper(props: ModalProps) {
  const { ignoreSchemaNav } = useKaruteInput();
  const stableInit = useRef({}).current;

  const { handleValuesChange, submitHandler, resetInitial } = useUnsavedWarning(stableInit, {
    ignoreNavigation: ignoreSchemaNav,
  });

  const unsavedHandlers = useRef({
    unsavedChange: handleValuesChange,
    unsavedSubmit: submitHandler,
    resetInitial,
  });

  unsavedHandlers.current.unsavedChange = handleValuesChange;
  unsavedHandlers.current.unsavedSubmit = submitHandler;
  unsavedHandlers.current.resetInitial = resetInitial;

  return <UpsertKeikaModal {...props} unsavedHandlers={unsavedHandlers.current} />;
}
