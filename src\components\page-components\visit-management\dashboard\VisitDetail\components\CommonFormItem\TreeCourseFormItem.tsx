import Button from '@/components/common/core/Button';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import AppMenuSelectPaidModal from '@/components/common/modal/MenuSelectPaidModal/MenuSelectPaidModal';
import { useScreen } from '@/hooks/useScreen';
import { ButtonType, ECourse } from '@/types/enum';
import { Option } from '@/types/interface';
import { Tree, TreeDataNode, TreeProps } from 'antd';
import { DataNode } from 'antd/es/tree';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './CourseFormButton.module.scss';
function getAllKeys(tree: DataNode[]): React.Key[] {
  const keys: React.Key[] = [];

  function traverse(nodes: DataNode[]) {
    for (const node of nodes) {
      keys.push(node.key);
      if (node.children?.length) {
        traverse(node.children);
      }
    }
  }

  traverse(tree);
  return keys;
}

interface TreeCourseFormItemProps extends TreeProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  serviceCourseTreeData: TreeDataNode[];
  jihiCourseTreeData: TreeDataNode[];
}
// Note:
// This Course Form is created by 2 different Tree for scrolling-screen self-paid items
// - Cause: Ant Design Node is just indenting through class, there is no wrapper in each folder
// - Additionally: There are custom styling which which is not possible through default AntDom with checkbox
// - Solution:
//   + Create a custom title render to add scrollable wrapper for children of ECourse.SELF_PAID
//   + Create a custom Tree to handle check/uncheck of children of ECourse.SELF_PAID
//   + Create a custom checkbox to handle check/uncheck of ECourse.SELF_PAID

export const TreeCourseFormItem = ({
  value = [],
  onChange,
  serviceCourseTreeData,
  jihiCourseTreeData,
  ...rest
}: TreeCourseFormItemProps) => {
  const hasSetup = useRef(false);
  const [checkedKeys, setCheckedKeys] = useState<string[]>(value);
  const [selfPaidCheckedKeys, setSelfPaidCheckedKeys] = useState<string[]>(value);
  const [showModalPaidMenu, setShowModalPaidMenu] = useState<boolean>(false);
  const [selfPaidTree, setSelfPaidTree] = useState<TreeDataNode[]>([]);
  const { screen } = useScreen();
  const handleOnChange = (value: string[]) => {
    onChange?.(value);
  };
  const initialTreeData: TreeDataNode[] = useMemo(() => {
    return serviceCourseTreeData.map(node => {
      return {
        ...node,
        children: node.children?.map(child => {
          if (child.key === ECourse.SELF_PAID) {
            return {
              ...child,
              title: (
                <div className="paid-item">
                  <span>自費</span>
                  <Button
                    customType={ButtonType.SECONDARY_COLOR}
                    onClick={() => setShowModalPaidMenu(true)}
                    className={styles.treeCourseButton}
                  >
                    メニュー選択
                  </Button>
                </div>
              ),
            };
          }
          return child;
        }),
      };
    });
  }, [serviceCourseTreeData]);
  const serviceKey = useMemo(() => {
    return initialTreeData?.[0]?.key?.toString();
  }, [initialTreeData]);
  const initialChildrenTreeDataLength = useMemo(() => {
    return initialTreeData?.[0]?.children?.length ?? 0;
  }, [initialTreeData]);
  // Sync with parent value
  useEffect(() => {
    if (value && value.length > 0 && !hasSetup.current) {
      const updatedChildren = jihiCourseTreeData.filter(item => value.includes(String(item.key)));
      setSelfPaidTree(updatedChildren);
      const initSelfPaidKeys = jihiCourseTreeData
        .filter(item => value.includes(String(item.key)))
        .map(item => String(item.key));
      setSelfPaidCheckedKeys(initSelfPaidKeys);

      const nonSelfPaidKeys = (value ?? []).filter(
        item => !initSelfPaidKeys.includes(String(item))
      );

      const selfPaidKeys = initSelfPaidKeys.length > 0 ? [ECourse.SELF_PAID] : [];
      const initCheckedKeys = [...nonSelfPaidKeys, ...selfPaidKeys];
      setCheckedKeys(initCheckedKeys?.length > 0 ? [...initCheckedKeys, serviceKey] : []);

      hasSetup.current = true;
    }
  }, [value, jihiCourseTreeData, initialTreeData]);

  const handleSelfPaidCheck = (nodes: any) => {
    const cleanKeys = checkedKeys.filter(
      item => ![String(ECourse.SELF_PAID), serviceKey].includes(item)
    );
    handleOnChange?.([...cleanKeys, ...nodes]);
    setSelfPaidCheckedKeys(nodes);

    if (nodes?.length > 0) {
      setCheckedKeys([...cleanKeys, ECourse.SELF_PAID, serviceKey]);
    } else {
      if (checkedKeys.length === 2) {
        setCheckedKeys([...cleanKeys]);
      } else {
        setCheckedKeys([...cleanKeys, serviceKey]);
      }
    }
  };

  const handleConfirmModalPaidMenu = (paidMenuList: Option<string>[]) => {
    const updatedChildren: TreeDataNode[] = paidMenuList.map((item: Option<string>) => ({
      title: item.label,
      key: `${item.value}`,
    }));
    const cleanKeys = checkedKeys.filter(
      item => ![String(ECourse.SELF_PAID), serviceKey].includes(item)
    );
    setSelfPaidTree(updatedChildren);
    setSelfPaidCheckedKeys(paidMenuList.map((item: Option<string>) => `${item.value}`));
    handleOnChange?.([
      ...cleanKeys,
      ...paidMenuList.map((item: Option<string>) => `${item.value}`),
    ]);
    if (paidMenuList?.length > 0) {
      setCheckedKeys([...cleanKeys, ECourse.SELF_PAID, serviceKey]);
    } else {
      if (checkedKeys.length === 2) {
        setCheckedKeys([...cleanKeys]);
      } else {
        setCheckedKeys([...cleanKeys, serviceKey]);
      }
    }
    setShowModalPaidMenu(false);
  };

  const handleCancelModalPaidMenu = () => {
    setShowModalPaidMenu(false);
  };
  // Custom title render to add scrollable wrapper for children of ECourse.SELF_PAID
  const titleRender = useCallback(
    (node: TreeDataNode) => {
      if (node.key === ECourse.SELF_PAID) {
        return (
          <div
            className="ml-6"
            style={{
              padding:
                initialChildrenTreeDataLength <= 1 && selfPaidTree.length === 0
                  ? '10px  0'
                  : initialChildrenTreeDataLength > 1 && selfPaidTree.length === 0
                  ? '10px 0'
                  : undefined,
              borderBottom:
                initialChildrenTreeDataLength > 1 ? '1px solid var(--gray-200)' : undefined,
            }}
          >
            <div
              className="paid-item "
              style={
                selfPaidTree.length > 0
                  ? {
                      padding: '10px 0  10px 0',
                      borderBottom: '1px solid var(--gray-200)',
                    }
                  : {}
              }
            >
              <AppCheckbox
                className="mr-2"
                checked={checkedKeys?.includes(node.key)}
                onChange={e => {
                  e.stopPropagation();
                  e.stopPropagation();
                  if (checkedKeys.includes(String(node?.key))) {
                    const cleanKeys = checkedKeys.filter(
                      item => ![String(node?.key), initialTreeData?.[0]?.key].includes(item)
                    );
                    if (checkedKeys.length === 2) {
                      setCheckedKeys([...cleanKeys]);
                    } else {
                      setCheckedKeys([...cleanKeys, serviceKey]);
                    }
                    setSelfPaidCheckedKeys([]);
                    handleOnChange?.(cleanKeys);
                  } else {
                    setShowModalPaidMenu(true);
                  }
                }}
              />

              <span>自費</span>
              <Button
                customType={ButtonType.SECONDARY_COLOR}
                onClick={() => setShowModalPaidMenu(true)}
                className={styles.treeCourseButton}
              >
                メニュー選択
              </Button>
            </div>
            <div className={selfPaidTree.length > 0 ? 'pt-1' : ''}>
              <Tree
                checkable
                checkedKeys={selfPaidCheckedKeys}
                treeData={selfPaidTree}
                expandedKeys={getAllKeys(selfPaidTree)}
                className="custom-tree custom-child-tree ml-6"
                switcherIcon={null}
                showLine={false}
                onCheck={handleSelfPaidCheck}
                height={132}
              />
            </div>
          </div>
        );
      }
      const isLastNode =
        node.key === initialTreeData?.[0].children?.[initialChildrenTreeDataLength - 1]?.key &&
        initialChildrenTreeDataLength >= 1;
      return (
        <div
          className={`${node.key !== initialTreeData?.[0]?.key ? 'ml-6' : ''} custom-tree-node`}
          style={{
            padding: '10px 0',
            borderBottom: isLastNode ? undefined : '1px solid var(--gray-200)',
          }}
        >
          <AppCheckbox
            className="mr-2"
            checked={checkedKeys?.includes(String(node?.key))}
            onChange={e => {
              e.stopPropagation();
              e.stopPropagation();
              if (node?.key === initialTreeData?.[0]?.key) {
                if (checkedKeys.includes(String(node?.key))) {
                  handleOnChange?.([]);
                  setCheckedKeys([]);
                  setSelfPaidCheckedKeys([]);
                }
                return;
              }

              const cleanKeys = checkedKeys.filter(
                item => ![String(node?.key), initialTreeData?.[0]?.key].includes(item)
              );
              const submitKeys = checkedKeys.filter(
                item =>
                  ![String(node?.key), initialTreeData?.[0]?.key, ECourse.SELF_PAID].includes(item)
              );
              if (checkedKeys.includes(String(node?.key))) {
                if (checkedKeys.length === 2) {
                  setCheckedKeys([...cleanKeys]);
                } else {
                  setCheckedKeys([...cleanKeys, serviceKey]);
                }
                handleOnChange?.([...submitKeys, ...selfPaidCheckedKeys]);
              } else {
                handleOnChange?.([...submitKeys, ...selfPaidCheckedKeys, node?.key?.toString()]);
                setCheckedKeys([...cleanKeys, node?.key?.toString(), serviceKey]);
              }
            }}
          />

          <span>
            {typeof node.title === 'function' ? String(node.key) : node.title || String(node.key)}
          </span>
        </div>
      );
    },
    [
      selfPaidTree,
      selfPaidCheckedKeys,
      checkedKeys,
      initialChildrenTreeDataLength,
      serviceKey,
      initialTreeData,
    ]
  );

  return (
    <>
      <Tree
        treeData={initialTreeData}
        className="custom-tree"
        switcherIcon={null}
        expandedKeys={getAllKeys(initialTreeData)}
        showLine={false}
        titleRender={titleRender}
        {...rest}
      />
      <AppMenuSelectPaidModal
        mask={screen !== 'mobile'}
        isModalOpen={showModalPaidMenu}
        selectedListPaid={value}
        onCancel={handleCancelModalPaidMenu}
        onConfirm={handleConfirmModalPaidMenu}
        paidMenu={jihiCourseTreeData.map(item => ({
          value: String(item.key),
          label: item.title as string,
        }))}
      />
    </>
  );
};
