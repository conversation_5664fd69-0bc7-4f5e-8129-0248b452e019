import { AppDispatch, RootState } from '@/config/redux/store';
import { LOCAL_STORAGE_KEY } from '@/types/constants';
import { initialState as initialSchemaState, setCategory } from '../schema';
import { initSelectionState as initialSelectionState, setSelection } from '../visit';
import { authApi } from './api';
import { logout } from './index';

export const logoutThunk = () => (dispatch: AppDispatch, getState: () => RootState) => {
  try {
    const token = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
    if (token) {
      dispatch(authApi.endpoints.logout.initiate({}));
    }
    dispatch(logout());
    dispatch(setSelection(initialSelectionState));
    dispatch(setCategory({ categories: initialSchemaState.categories }));
    window.dispatchEvent(new Event('auth-change'));
  } catch (error) {
    console.error('Logout API failed:', error);
    dispatch(logout());
    dispatch(setSelection(initialSelectionState));
    dispatch(setCategory({ categories: initialSchemaState.categories }));
    window.dispatchEvent(new Event('auth-change'));
  }
  localStorage.removeItem(LOCAL_STORAGE_KEY.INJURY_NAME);
};
