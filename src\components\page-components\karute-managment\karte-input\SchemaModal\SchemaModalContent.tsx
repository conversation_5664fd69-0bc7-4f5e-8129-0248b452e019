import Button from '@/components/common/core/Button';
import CanvasContainer from '@/components/page-components/karute-managment/karte-input/SchemaModal/components/canvas';
import { useAppDispatch } from '@/config/redux/store';
import { setChanged } from '@/store/karute';
import { usePostDatabaseImageMutation, usePostKeikaImageMutation } from '@/store/schema/api';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import { NOTICE_COMMON_MESSAGE, STATUS_CODES } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { getImgKey, showFlashNotice } from '@/utils';
import { Flex, Modal, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useKaruteInput } from '../provider';
import CategorySidebar from './components/CategorySidebar';
import SchemaGrid from './components/SchemaGrid';
import { useSchema } from './context/SchemaContext';
import styles from './SchemaModal.module.scss';
import { CanvasContainerHandle } from './type';

interface SchemaModalContentProps {
  open: boolean;
  onCancel: () => void;
}

const SchemaModalContent: React.FC<SchemaModalContentProps> = ({ open, onCancel }) => {
  const { setSelectedImages, setDrawnImages, submitUnsaved, setDrawnIds, setSavedIds } =
    useSchema();
  const { handleSubmitSchema } = useKaruteInput();
  const [postKeikaImage] = usePostKeikaImageMutation();
  const [postDatabaseImage] = usePostDatabaseImageMutation();
  const canvasRef = useRef<CanvasContainerHandle>(null);
  const { schemaType, disease } = useParams<{
    schemaType?: SchemaType;
    disease?: DiseaseType;
  }>();
  const [submitLoading, setSubmitLoading] = useState(false);
  const dispatch = useAppDispatch();
  const handleOk = async () => {
    setSubmitLoading(true);
    const files = (await canvasRef.current?.exportAll()) || [];
    setDrawnImages(files);
    switch (schemaType) {
      case SchemaType.DATABASE:
        const databaseBody = files.map(({ id, blob, isTemp, schema_image_id }) => {
          const ext = blob.type.split('/')[1] || 'png';
          const file = new File([blob], `database_${id}_${id}.${ext}`, { type: blob.type });
          return {
            schema_image_id: schema_image_id,
            file,
            type: disease,
            isTemp,
          };
        });
        const databaseRes =
          files.length === 0
            ? {
                data: {
                  status: STATUS_CODES.OK,
                  message: NOTICE_COMMON_MESSAGE.SCHEMA_UPDATED,
                  data: [],
                },
              }
            : await postDatabaseImage(databaseBody);
        if (databaseRes.data?.status === STATUS_CODES.OK) {
          showFlashNotice({ type: 'success', message: databaseRes.data?.message || '' });
          handleSubmitSchema(SchemaType.DATABASE, disease!, databaseRes.data?.data as any);
          submitUnsaved();
          onCancel();
          dispatch(setChanged());
        } else {
          showFlashNotice({ type: 'error', message: databaseRes.data?.message || '' });
        }
        break;
      case SchemaType.KEIKA:
        const keikaBody = files.map(({ id, blob, isTemp, schema_image_id }) => {
          const ext = blob.type.split('/')[1] || 'png';
          const file = new File([blob], `keika_${id}_${id}.${ext}`, { type: blob.type });
          return {
            schema_image_id: schema_image_id,
            file,
            type: disease,
            isTemp,
          };
        });
        const keikaRes =
          files.length === 0
            ? {
                data: {
                  status: STATUS_CODES.OK,
                  message: NOTICE_COMMON_MESSAGE.SCHEMA_UPDATED,
                  data: [],
                },
              }
            : await postKeikaImage(keikaBody);
        if (keikaRes.data?.status === STATUS_CODES.OK) {
          showFlashNotice({ type: 'success', message: keikaRes.data?.message || '' });
          handleSubmitSchema(SchemaType.KEIKA, disease!, keikaRes.data?.data as any);
          submitUnsaved();
          onCancel();
          dispatch(setChanged());
        } else {
          showFlashNotice({ type: 'error', message: keikaRes.data?.message || '' });
        }
        break;
    }
    setSubmitLoading(false);
  };

  const didFetchHistory = useRef(false);

  useEffect(() => {
    if (!open) {
      didFetchHistory.current = false;
      return;
    }
    if (didFetchHistory.current) return;

    didFetchHistory.current = true;
  }, [open]);

  return (
    <Modal
      title="シェーマ"
      open={open}
      width="1085px"
      footer={null}
      onCancel={onCancel}
      centered
      destroyOnClose
      className={styles.modal}
      maskClosable={false}
      zIndex={2000}
    >
      <div className={styles.modalWrapper}>
        <div style={{ height: '100%', maxHeight: 'calc(100% - 30px)' }}>
          <Spin spinning={submitLoading}>
            <div className={styles.container}>
              <div className={styles.sidebar}>
                <CategorySidebar />
                <div className={styles.gridStub}>
                  <SchemaGrid />
                </div>
              </div>

              <div className={styles.content}>
                <CanvasContainer
                  ref={canvasRef}
                  onRemoveImage={id => {
                    setSelectedImages(prev => {
                      return prev.filter(img => getImgKey(img) !== id);
                    });
                    setDrawnIds(prev => prev.filter(x => x !== id));
                    setSavedIds(prev => prev.filter(x => x !== id));
                  }}
                />
              </div>
            </div>
          </Spin>
        </div>
        <Flex justify="end" gap="small">
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            onClick={onCancel}
            style={{ width: '120px' }}
            customSize="lg"
          >
            キャンセル
          </Button>
          <Button
            customType={ButtonType.PRIMARY}
            onClick={handleOk}
            style={{ width: '120px' }}
            customSize="lg"
          >
            登録
          </Button>
        </Flex>
      </div>
    </Modal>
  );
};

export default SchemaModalContent;
