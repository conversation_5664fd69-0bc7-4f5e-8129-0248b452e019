import React, { ReactNode } from 'react';
import Header from '@/components/common/layout/Header';
import styles from './OnlyHeaderLayout.module.scss';
import { Outlet } from 'react-router-dom';

interface OnlyHeaderLayoutProps {
  children?: ReactNode;
}

const OnlyHeaderLayout: React.FC<OnlyHeaderLayoutProps> = ({ children }) => (
  <div className={styles.onlyHeaderLayout}>
    <Header />
    <div className={styles.body}>
      <div className={styles.mainContent}>{children ?? <Outlet />}</div>
    </div>
  </div>
);

export default OnlyHeaderLayout;
