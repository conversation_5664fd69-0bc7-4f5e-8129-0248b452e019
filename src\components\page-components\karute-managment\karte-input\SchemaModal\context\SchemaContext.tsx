import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import {
  useGetAllSchemaImagesQuery,
  useLazyGetSchemaImagesByCategoryQuery,
} from '@/store/schema/api';
import { DiseaseType, SchemaImage, SchemaType } from '@/store/schema/type';
import { NOTICE_COMMON_MESSAGE, SCHEMA_COMMON } from '@/types/constants';
import { getImgKey, mergeList, showFlashNotice } from '@/utils';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useKaruteInput } from '../../provider';
import { DrawTool, FAVORITE_CATEGORY_ID, SchemaMode } from '../type';

interface SchemaCtx {
  gridLoading: boolean;
  setGridLoading: React.Dispatch<React.SetStateAction<boolean>>;
  allImages: SchemaImage[];
  setAllImages: React.Dispatch<React.SetStateAction<SchemaImage[]>>;
  categoryId: number;
  setCategoryId: (id: number) => void;
  selectedImages: SchemaImage[];
  setSelectedImages: React.Dispatch<React.SetStateAction<SchemaImage[]>>;
  mode: SchemaMode;
  setMode: (mode: SchemaMode) => void;
  drawnIds: string[];
  setDrawnIds: React.Dispatch<React.SetStateAction<string[]>>;
  savedIds: string[];
  setSavedIds: React.Dispatch<React.SetStateAction<string[]>>;
  markAsDrawn: (id: string) => void;
  unMarkAsDrawn: (id: string) => void;
  drawnImages: { id: string; blob: Blob }[];
  setDrawnImages: React.Dispatch<React.SetStateAction<{ id: string; blob: Blob }[]>>;

  tool: DrawTool;
  setTool: (tool: DrawTool) => void;
  size: Record<DrawTool, number>;
  setSize: React.Dispatch<React.SetStateAction<Record<DrawTool, number>>>;
  updateCategorySchemaImages: (
    targetId: number,
    deletedId?: number | string | null,
    fetchApi?: boolean,
    force?: boolean
  ) => Promise<void>;
  initLoading: boolean;
  isPendingCategory: boolean;
  setIsPendingCategory: React.Dispatch<React.SetStateAction<boolean>>;
  showConfirmRemoveImage: (proceed: () => void) => void;
  showConfirmRemoveMasterImage: (proceed: () => void) => void;
  submitUnsaved: () => void;
}
const SchemaContext = React.createContext<SchemaCtx>({
  gridLoading: false,
  setGridLoading: () => {},
  allImages: [],
  setAllImages: () => {},
  categoryId: FAVORITE_CATEGORY_ID,
  setCategoryId: () => {},
  selectedImages: [],
  setSelectedImages: () => {},
  mode: SchemaMode.SELECT,
  setMode: () => {},
  drawnIds: [],
  setDrawnIds: () => {},
  savedIds: [],
  setSavedIds: () => {},
  markAsDrawn: () => {},
  unMarkAsDrawn: () => {},
  drawnImages: [],
  setDrawnImages: () => {},

  tool: DrawTool.PENCIL,
  setTool: () => {},
  size: {
    [DrawTool.PENCIL]: 15,
    [DrawTool.CIRCLE]: 15,
    [DrawTool.CIRCLE_OUTLINE]: 15,
    [DrawTool.ERASER]: 15,
    [DrawTool.NONE]: 0,
  },
  setSize: () => {},
  updateCategorySchemaImages: () => Promise.resolve(),
  initLoading: false,
  isPendingCategory: false,
  setIsPendingCategory: () => {},
  showConfirmRemoveImage: () => {},
  showConfirmRemoveMasterImage: () => {},
  submitUnsaved: () => {},
});

export const SchemaProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();

  const { serviceId } = useParams<{ serviceId: string }>();
  const [gridLoading, setGridLoading] = useState<boolean>(false);
  const [allImages, setAllImages] = useState<SchemaImage[]>([]);
  const [categoryId, setCategoryId] = useState<number>(FAVORITE_CATEGORY_ID);
  const [selectedImages, setSelectedImages] = useState<SchemaImage[]>([]);
  const [mode, setMode] = useState<SchemaMode>(SchemaMode.SELECT);
  const [drawnIds, setDrawnIds] = useState<string[]>([]);
  const [savedIds, setSavedIds] = useState<string[]>([]);
  const [drawnImages, setDrawnImages] = useState<{ id: string; blob: Blob }[]>([]);
  const [tool, setTool] = useState<DrawTool>(DrawTool.NONE);
  const [size, setSize] = useState<Record<DrawTool, number>>({
    [DrawTool.PENCIL]: 15,
    [DrawTool.CIRCLE]: 15,
    [DrawTool.CIRCLE_OUTLINE]: 15,
    [DrawTool.ERASER]: 15,
    [DrawTool.NONE]: 0,
  });
  const [isPendingCategory, setIsPendingCategory] = useState<boolean>(false);
  const { drawnImages: drawnImagesState } = useKaruteInput();
  const [initLoading, setInitLoading] = useState<boolean>(false);
  const { schemaType, disease, diseaseBaseId } = useParams<{
    schemaType?: SchemaType;
    disease?: DiseaseType;
    diseaseBaseId: string;
  }>();
  const [unsavedInit, setUnsavedInit] = useState({
    selected: [] as string[],
    drawn: [] as string[],
  });

  const { data: allSchemaResp, isFetching: isFetchingAllImages } = useGetAllSchemaImagesQuery(
    { service_id: Number(serviceId) },
    {
      refetchOnMountOrArgChange: true,
    }
  );
  const { handleValuesChange, submitHandler } = useUnsavedWarning(unsavedInit);

  const categoryIdRef = useRef<number>(categoryId);
  useEffect(() => {
    categoryIdRef.current = categoryId;
  }, [categoryId]);

  const [fetchCategorySchemaImages] = useLazyGetSchemaImagesByCategoryQuery();

  const markAsDrawn = (id: string) => {
    setDrawnIds(prev => (prev.includes(id) ? prev : [...prev, id]));
  };

  const unMarkAsDrawn = (id: string) => setDrawnIds(prev => prev.filter(x => x !== id));

  const allImagesRef = useRef<SchemaImage[]>(allImages);
  useEffect(() => {
    allImagesRef.current = allImages;
  }, [allImages]);

  const updateCategorySchemaImages = useCallback(
    async (id: number, deletedId?: number | string | null) => {
      try {
        setGridLoading(true);

        const res = await fetchCategorySchemaImages({
          category_id: id,
          service_id: Number(serviceId),
        }).unwrap();
        const fetched = (res.data ?? []) as SchemaImage[];
        const updated =
          id === FAVORITE_CATEGORY_ID
            ? allImagesRef.current.filter(i => i.schema_image_id !== deletedId)
            : mergeList(allImagesRef.current, fetched, id, deletedId);

        setAllImages(updated);
      } catch (e) {
        console.error(e);
      } finally {
        setGridLoading(false);
      }
    },
    [fetchCategorySchemaImages]
  );

  const initSchema = useCallback(() => {
    if (!schemaType || !disease) return;
    setInitLoading(true);
    const stored =
      drawnImagesState[schemaType]?.[String(diseaseBaseId)]?.[disease] ?? ([] as SchemaImage[]);
    setSelectedImages(stored);
    setSavedIds(stored.map(getImgKey));
    setMode(stored.length > 0 ? SchemaMode.DRAW : SchemaMode.SELECT);
    setInitLoading(false);
    setUnsavedInit({
      selected: stored.map(getImgKey),
      drawn: [],
    });
  }, [schemaType, disease, drawnImagesState]);

  const showConfirmRemoveImage = (proceed: () => void) => {
    showWarningDialog({
      message: <>{SCHEMA_COMMON.WARNING_REMOVE_IMAGE}</>,
      buttons: [
        {
          type: 'cancel',
          label: 'キャンセル',
          onClick: () => {
            hideWarningDialog();
          },
        },
        {
          type: 'confirm',
          label: '続行',
          onClick: () => {
            proceed();
            hideWarningDialog();
          },
        },
      ],
    });
  };

  const showConfirmRemoveMasterImage = (proceed: () => void) => {
    showWarningDialog({
      title: SCHEMA_COMMON.DELETE_MASTER_IMAGE_TITLE,
      message: <>{SCHEMA_COMMON.DELETE_MASTER_IMAGE_DESC}</>,
      hideIcon: true,
      buttons: [
        {
          type: 'cancel',
          label: 'キャンセル',
          onClick: () => {
            hideWarningDialog();
          },
        },
        {
          type: 'delete',
          label: '削除',
          onClick: () => {
            proceed();
            hideWarningDialog();
          },
        },
      ],
    });
  };

  useEffect(() => {
    initSchema();
  }, [initSchema]);

  const unsavedPayload = useMemo(
    () => ({
      selected: selectedImages.map(getImgKey),
      drawn: drawnIds,
    }),
    [selectedImages, drawnIds]
  );

  useEffect(() => {
    handleValuesChange(null, unsavedPayload);
  }, [selectedImages, drawnIds, handleValuesChange]);

  useEffect(() => {
    if (allSchemaResp?.data) {
      setAllImages(allSchemaResp.data);
      updateCategorySchemaImages(categoryIdRef.current).catch(() =>
        showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL })
      );
    }
  }, [allSchemaResp]);
  useEffect(() => {
    setGridLoading(isFetchingAllImages);
  }, [isFetchingAllImages]);

  return (
    <SchemaContext.Provider
      value={{
        gridLoading,
        setGridLoading,
        allImages,
        setAllImages,
        categoryId,
        setCategoryId,
        selectedImages,
        setSelectedImages,
        mode,
        setMode,
        drawnIds,
        savedIds,
        setSavedIds,
        setDrawnIds,
        markAsDrawn,
        unMarkAsDrawn,
        drawnImages,
        setDrawnImages,
        tool,
        setTool,
        size,
        setSize,
        updateCategorySchemaImages,
        initLoading,
        isPendingCategory,
        setIsPendingCategory,
        showConfirmRemoveImage,
        showConfirmRemoveMasterImage,
        submitUnsaved: submitHandler,
      }}
    >
      {children}
    </SchemaContext.Provider>
  );
};

export const useSchema = () => React.useContext(SchemaContext);
