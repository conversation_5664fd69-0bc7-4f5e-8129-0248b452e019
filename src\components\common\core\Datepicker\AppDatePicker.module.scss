@mixin input-common-styles {
  font-weight: 400;
  line-height: 22px;
  font-size: 16px;
  color: var(--input-text);
  background-color: transparent;
  @content;
}

@mixin input-lg-styles {
  &:global(.ant-picker-large) {
    line-height: 24px;
    font-size: 18px;
    @content;
  }
}

@mixin input-sm-styles {
  &:global(.ant-picker-small) {
    line-height: 20px;
    font-size: 14px;
    @content;
  }
}

@mixin input-range-styles {
  &:global(.ant-picker-range) {
    line-height: 20px;
    font-size: 14px;
    @content;
  }
}

.appPicker {
  &:global(.ant-picker) {
    width: 100%;
    background-color: transparent;

    &:global(.ant-picker-focused) {
      border-color: var(--input-border-hover) !important;
    }

    &:hover {
      border-color: var(--input-border-hover) !important;
    }

    @include input-common-styles {
      border-color: var(--input-border);
      padding: 12px 12px 12px 16px;
    }

    @include input-lg-styles {
      padding: 16px 12px 16px 16px;
      height: 56px;
    }
    @include input-sm-styles {
      padding: 10px 12px 10px 16px;
      height: 40px;
    }
    @include input-range-styles {
      padding: 12px 12px 12px 16px;
    }

    &:global(.ant-picker-range) {
      line-height: 24px;
      font-size: 18px;
    }
    &:global(.ant-picker-disabled) {
      border-color: var(--input-border) !important;
      background-color: var(--input-bg-disabled) !important;
    }

    &:global(.ant-picker-focused) {
      box-shadow: none;
    }

    &:global(.ant-picker-status-error):not(:global(.ant-picker-disabled)):not([disabled]) {
      box-shadow: none !important;

      &:hover {
        border-color: var(--input-border-error) !important;
      }
    }

    &::placeholder {
      color: var(--input-text-placeholder);
    }

    & > :global(.ant-picker-input) > input[disabled]:hover {
      background-color: transparent;
    }
    // & > :global(.ant-picker-input) > input[:disabled]:hover {
    //   background-color: ;
    // })
  }
}
.rangePlaceHolder {
  position: absolute;
  top: 0;
  left: 0;
  margin: 1px;
  width: calc(100% - 32px);
  bottom: 0;
  display: flex;
  align-items: center;
  padding-left: 12px;
  color: #bfbfbf;
  pointer-events: none;
  font-size: 14px;
  // background-color: var(--white);
  border-radius: 8px;
}
