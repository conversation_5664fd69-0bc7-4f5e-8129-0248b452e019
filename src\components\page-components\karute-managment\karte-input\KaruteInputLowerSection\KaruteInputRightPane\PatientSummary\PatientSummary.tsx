import Button from '@/components/common/core/Button';
import EditableTable from '@/components/common/core/CommonTable/EditableTable';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import Icon from '@/components/common/core/Icon';
import styles from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLowerSection.module.scss';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { setChanged, setLoading, updatePatientSummaries } from '@/store/karute';
import { prefetchKaruteDetails } from '@/store/karute/karute-serviceThunk';
import {
  useCreatePatientSummaryMutation,
  useDeletePatientSummaryMutation,
  useUpdatePatientSummaryByIdMutation,
} from '@/store/patient-summary/api';
import type { PatientSummary } from '@/store/patient-summary/type';
import { DATE_FORMATS, NOTICE_COMMON_MESSAGE, STATUS_CODES } from '@/types/constants';
import { ButtonType, ESort } from '@/types/enum';
import { showFlashNotice } from '@/utils';
import { Flex, Form } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { Fragment, useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { useKaruteInput } from '../../../provider';

const initData: Partial<PatientSummary & { rowId?: number | string; mode?: 'add' | 'edit' }> = {
  updated_at: undefined,
  remark: undefined,
  mode: 'add',
};

const DELETE_PATIENT_SUMMARY_SUCCESS_MESSAGE = '患者サマリーを削除しました。';

export function PatientSummary() {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<
    Partial<PatientSummary & { rowId?: number | string; mode?: 'add' | 'edit' }>[]
  >([]);
  const { id: karuteId, serviceId } = useParams();

  const { patient_summary } = useAppSelector(state => state.karute.karute.details);
  const { queryParams } = useAppSelector(state => state.karute.karute);
  const { loading } = useAppSelector(state => state.karute.karute);

  const [updateMutation] = useUpdatePatientSummaryByIdMutation();
  const [createMutation] = useCreatePatientSummaryMutation();
  const [deleteMutation] = useDeletePatientSummaryMutation();

  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const dispatch = useAppDispatch();
  const tableRef = useRef<any>(null);
  const { setElemRef, height } = useResizeObserver();
  const { karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;
  useEffect(() => {
    if (!karuteId || !serviceId || !patientId) return;
    dispatch(
      prefetchKaruteDetails({
        service_id: Number(serviceId),
        patient_id: Number(patientId),
        karute_id: Number(karuteId),
      })
    );
  }, [karuteId, serviceId, patientId]);

  useEffect(() => {
    const newTableData = (!Array.isArray(patient_summary) ? [] : patient_summary).map(item => ({
      ...item,
      rowId: item.patient_summary_id,
    }));
    const initRow: Partial<PatientSummary & { rowId?: number | string; mode?: 'add' | 'edit' }> = {
      ...initData,
      rowId: uuidv4(),
      mode: 'add',
    };
    setDataSource([...newTableData, initRow]);
    const validRowIds = newTableData.map(item => String(item.rowId));
    const prevSelected = selectedKeys.filter(key => validRowIds.includes(key));
    setSelectedKeys([...prevSelected, initRow.rowId?.toString() || '']);
    form.setFieldValue(initRow.rowId, initData);
  }, [patient_summary]);

  const isEditable = useCallback(
    (data: Partial<PatientSummary & { rowId?: number | string }>) => {
      return selectedKeys.includes(data.rowId?.toString() || '');
    },
    [selectedKeys]
  );

  useEffect(() => {
    if (!loading) {
      tableRef.current?.scrollToFirstRow();
    }
  }, [loading]);

  const handleMutation = (
    payload: Partial<PatientSummary & { rowId?: number; mode?: 'add' | 'edit' }>
  ) => {
    const {
      patient_summary_id,
      created_at,
      deleted_at,
      updated_at,
      remark_date,
      rowId,
      mode,
      ...body
    } = payload;
    if (patient_summary_id !== undefined) {
      const { karute_id: _omitKaruteId, service_id: _omitServiceId, ...payload } = body;
      return updateMutation({
        patient_summary_id,
        karute_id: _omitKaruteId,
        ...payload,
      });
    }
    return createMutation({
      ...body,
      remark: body.remark ?? '',
      service_id: Number(queryParams?.service_id),
      karute_id: Number(queryParams?.karute_id),
    });
  };

  const handleSortChange = (sortData: { order_by: string; order_type: ESort }[]) => {
    dispatch(
      prefetchKaruteDetails({
        ...queryParams,
        order_by: sortData[0]?.order_by,
        order_type: sortData[0]?.order_type,
      })
    );
  };

  const handleDelete = ({
    patient_summary_id,
    ...rest
  }: Partial<PatientSummary & { rowId?: number | string }>) => {
    if (!patient_summary_id) return;
    return showWarningDialog({
      title: '患者サマリーを削除する',
      message: 'この操作は元に戻せません。患者サマリーを削除してもよろしいでしょうか？',
      height: '208px',
      buttons: [
        {
          label: 'キャンセル',
          type: 'cancel',
          onClick: hideWarningDialog,
        },
        {
          label: '削除',
          type: 'delete',

          onClick: async () => {
            const res = await deleteMutation({ patient_summary_id: patient_summary_id });
            if (
              res.data?.status === STATUS_CODES.OK ||
              res.data?.status === STATUS_CODES.NOT_FOUND
            ) {
              hideWarningDialog();
              showFlashNotice({
                type: 'success',
                message: DELETE_PATIENT_SUMMARY_SUCCESS_MESSAGE,
              });
              const updatedData = [
                ...patient_summary.filter(item => item.patient_summary_id !== patient_summary_id),
              ];
              dispatch(updatePatientSummaries(updatedData as PatientSummary[]));
              setSelectedKeys(selectedKeys.filter(item => item !== String(patient_summary_id)));
              form.resetFields([rest.rowId]);
              dispatch(setChanged());
            }
          },
        },
      ],
      hideIcon: true,
    });
  };
  const columns: CustomColumnType<Partial<PatientSummary & { rowId?: number | string }>>[] = [
    {
      title: '記載日',
      dataIndex: 'updated_at',
      key: 'updated_at',
      align: 'right',
      width: 162,
      render: (_: any, record: Partial<PatientSummary>) =>
        record.updated_at ? dayjs(record.updated_at).format(DATE_FORMATS.JAPAN_DATE) : '',
      editable: false,
      sortKey: 'remark_date',
      sortable: true,
    },
    {
      title: '患者の状態（要約した内容）',
      dataIndex: 'remark',
      key: 'remark',
      align: 'left',
      width: 570,
      render: (_: any, record: Partial<PatientSummary>) => (
        <div style={{ maxWidth: '100%', wordBreak: 'break-word' }}>
          <RenderWithTooltip text={record.remark || ''} maxLines={2} />
        </div>
      ),
      editable: {
        type: 'input',
        maxLength: 300,
        rules: [
          {
            whitespace: true,
            message: '',
          },
        ],
        style: {
          width: '546px',
          height: '30px',
        },
      },
    },

    {
      title: '',
      key: 'actions',
      align: 'center',
      render: (
        _: any,
        record: Partial<PatientSummary & { rowId?: number | string; mode?: 'add' | 'edit' }>
      ) => {
        return isEditable(record) ? (
          <Fragment key={record.rowId}>
            <Flex
              style={{
                flexFlow: 'row-reverse',
                justifyContent: 'flex-end',
              }}
              align="center"
              gap={6}
            >
              <div
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  dispatch(setLoading(true));
                  const payload = form.getFieldValue(record.rowId);
                  handleMutation(payload)
                    ?.unwrap()
                    .then(res => {
                      if (res.status === STATUS_CODES.OK) {
                        dispatch(
                          prefetchKaruteDetails({
                            ...queryParams,
                          })
                        );
                        setSelectedKeys(selectedKeys.filter(item => item !== String(record.rowId)));
                        form.resetFields([record.rowId]);
                        showFlashNotice({
                          type: 'success',
                          message: NOTICE_COMMON_MESSAGE.PATIENT_SUMMARY_UPDATED,
                        });
                        dispatch(setChanged());
                        return res;
                      }
                    })
                    .catch((err: any) => {
                      if (err instanceof Error) {
                        showFlashNotice({ type: 'error', message: err.message });
                      } else {
                        showFlashNotice({ type: 'error', message: err?.data?.message });
                      }
                    })
                    .finally(() => dispatch(setLoading(false)));
                }}
              >
                <Icon name="check" color="green" height={16} width={16} />
              </div>
              <div
                onClick={() => {
                  if (record.mode !== 'add') {
                    setSelectedKeys(keys =>
                      keys.filter(
                        k => k !== String(record.rowId) || k === dataSource.length.toString()
                      )
                    );
                  } else {
                    form?.setFieldValue(record.rowId, undefined);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                <Icon name="close" color="red" height={16} width={16} />
              </div>
            </Flex>
          </Fragment>
        ) : (
          <Fragment key={record.rowId}>
            <Flex align="center" gap={6}>
              <div
                onClick={() => {
                  setSelectedKeys(prevState => [...prevState, record?.rowId?.toString() || '']);
                  form.setFieldsValue({ [record.rowId!]: record });
                }}
                style={{ cursor: 'pointer' }}
              >
                <Icon name="edit" color="gray" height={16} width={16} />
              </div>
              <div onClick={() => handleDelete(record)} style={{ cursor: 'pointer' }}>
                <Icon name="trash" color="red" height={16} width={16} />
              </div>
            </Flex>
          </Fragment>
        );
      },
    },
  ];

  return (
    <div className={styles.patientSummary} ref={setElemRef}>
      <Flex justify="space-between" align="center" className={styles.sectionHeader}>
        <h2 className={clsx(styles.title, 'fs14-bold')}>患者サマリー</h2>
        <Button
          customSize="sm"
          style={{
            width: '112px',
          }}
          customType={ButtonType.PRIMARY}
          onClick={() => {
            if (!tableRef.current) return;
            tableRef.current?.scrollToAddRow();
          }}
        >
          <Icon name="plus" />
          新規作成
        </Button>
      </Flex>

      <div className={styles.sectionContent}>
        <div>
          <>
            <Form style={{ width: '100%' }} form={form} component={false}>
              <EditableTable
                ref={tableRef}
                className={styles.table}
                rowKey={record => record.rowId || ''}
                columns={columns}
                loading={loading}
                tableLayout="fixed"
                onSortChange={handleSortChange}
                editingKeys={selectedKeys}
                dataSource={dataSource}
                pagination={false}
                scroll={{ y: height! - 60 - 50 }}
              />
            </Form>
          </>
        </div>
      </div>
    </div>
  );
}
