import { Form } from 'antd';
import { AppInput, AppTextArea } from '@/components/common/core/Input';
import Button from '@/components/common/core/Button';
import { ButtonType } from '@/types/enum';
import { useEffect } from 'react';
import { useNav } from '@/hooks/useNav';
import {
  DATE_FORMATS,
  genderMap,
  PLACEHOLDER_MESSAGE_DEFAULT,
  routerPaths,
} from '@/types/constants';
import { PatientDetailSchema, FormField } from '@/types/interface';
import { useVisitDetail } from '../provider';
import { useRouterPath } from '@/hooks/useRouterPath';
import { AppFormItem } from '@/components/common/core/FormItem';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';

const { useForm } = Form;

export default function PatientSidebar() {
  const { patient, isFetching } = useVisitDetail();
  const navigate = useNavigate();
  const [form] = useForm<PatientDetailSchema>();
  const { getPath } = useRouterPath();
  const patientUpdatePath = (id: number | undefined) =>
    getPath(routerPaths.patientManagement.patientUpdate(id));

  useEffect(() => {
    if (!isFetching && patient) {
      const {
        patient_cd,
        name,
        kana,
        gender,
        birthday,
        post,
        address1,
        address2,
        cellphone,
        email,
        patient_cmt,
      } = patient;
      form.setFieldsValue({
        patient_cd: patient_cd,
        name: name,
        kana: kana,
        gender: genderMap[gender],
        birthday: birthday ? dayjs(birthday).format(DATE_FORMATS.DATE) : '',
        post: post,
        address: `${address1 ?? ''} ${address2 ?? ''}`,
        cellphone: cellphone,
        email: email,
        patient_cmt: patient_cmt || '',
      });
    }
  }, [patient, isFetching]);

  const formFields: FormField<PatientDetailSchema>[] = [
    {
      name: 'patient_cd',
      label: '患者番号',
      disabled: true,
    },
    {
      name: 'name',
      label: '氏名（漢字）',
      disabled: true,
    },
    {
      name: 'kana',
      label: '氏名（カタカナ）',
      disabled: true,
    },
    {
      name: 'gender',
      label: '性別',
      disabled: true,
    },
    {
      name: 'birthday',
      label: '生年月日',
      disabled: true,
    },
    {
      name: 'post',
      label: '郵便番号',
      disabled: true,
    },
    {
      name: 'address',
      label: '住所',
      disabled: true,
    },
    {
      name: 'cellphone',
      label: '電話番号',
      disabled: true,
    },
    {
      name: 'email',
      label: 'メールアドレス',
      disabled: true,
    },
    {
      name: 'patient_cmt',
      label: '患者のメモ',
      disabled: true,
    },
  ];

  return (
    <Form layout="vertical" form={form} initialValues={{}}>
      {formFields.map(item => (
        <AppFormItem
          key={item.name}
          label={item.label}
          name={item.name}
          required={item.required}
          rules={item.required ? [{ required: true }] : []}
          style={{ marginBottom: '16px' }}
        >
          {item.name == 'patient_cmt' ? (
            <AppTextArea
              placeholder={item.disabled ? undefined : PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
              autoSize={{ minRows: 1, maxRows: 5 }}
              disabled={item.disabled}
            />
          ) : (
            <AppInput
              placeholder={item.disabled ? undefined : PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
              disabled={item.disabled}
            />
          )}
        </AppFormItem>
      ))}

      <Button
        customType={ButtonType.PRIMARY}
        customSize="md"
        block
        loading={isFetching}
        onClick={() => {
          navigate(patientUpdatePath(patient?.patient_id));
        }}
      >
        患者を編集
      </Button>
    </Form>
  );
}
