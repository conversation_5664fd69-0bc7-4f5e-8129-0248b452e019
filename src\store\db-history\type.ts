export type ContentItem = {
  name: string | null;
  content: string | null;
  sub_content?: string;
  images?: string[];
};

export type RevisionEntry = {
  schemas: any[]; // Replace `any` with specific schema type if available
  injury_name: ContentItem[];
  subjective: ContentItem[];
  objective: ContentItem[];
  assessment: ContentItem[];
  plan: ContentItem[];
  doctor: ContentItem[];
  treatment: ContentItem[];
  remarks: ContentItem[];
  objective_images: string[];
  treatment_images: string[];
  treatment_content?: string;
  courses: string[];
  options: string[];
};

export type Revisions = {
  [date: string]: RevisionEntry;
};
export type DbHistoryDetail = {
  version: number;
  patient_id: number;
  patient_name: string;
  birthday: string;
  staff_id: number;
  staff_name: string;
  clinic_id: number;
  submitted_at: string;
  revisions: Revisions;
  med_history_personal: string;
  med_history_social: string;
  med_history_family: string;
};

export interface ParamGetListDbHistory {
  clinic_id?: number | null;
  patient_id?: number | null;
  service_id?: number;
  page?: number;
  limit?: number;
}
