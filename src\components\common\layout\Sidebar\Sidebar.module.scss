.sidebar {
  height: calc(100vh - $header-height);
  background-color: #fff;
  position: relative;
  border-top-right-radius: 1px;
  border-bottom-right-radius: 1px;
  border-right: 1px solid $gray-200;
  :global(.ant-menu-item-selected) {
    background-color: $brand-50 !important;

    .menuLabel {
      color: $brand-800;
    }
  }

  .menuLabel {
    color: $gray-500;
    margin-top: 2px;
  }
  .toggleBtn {
    position: absolute;
    top: 14px;
    right: -14px;
    z-index: 100;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: $brand-800;
    color: white;
    border: none;
    box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
    transition: background-color 0.2s ease;

    &:hover {
      background-color: $brand-900 !important;
      border: 1px solid $brand-900;
    }

    &:focus,
    &:focus-visible,
    &:focus-within {
      background-color: $brand-700 !important;
      border: 1px solid $brand-700;
      outline: none;
      box-shadow: none;
    }
  }
}
