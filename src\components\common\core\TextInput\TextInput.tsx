// example:
// <TextInput
//   label="Password"
//   placeholder="Password"
//   message="This field is required"
//   validationStatus={{ error: false }}
//   password={true}
//   disabled={false}
//   value={testPassword}
//   onChange={e => setTestPassword(e.target.value)}
// />

import React, { useState } from 'react';
import { Input } from 'antd';
import classNames from 'classnames';
import styles from './TextInput.module.scss';
import { TextInputProps } from '@/types/interface/TextInputProps';
import clsx from 'clsx';
import Icon from '@/components/common/core/Icon';

const error600 = '#DB4B30';

const TextInput: React.FC<TextInputProps> = ({
  label,
  message,
  validationStatus = { error: false, errorIcon: false },
  prefixIcon,
  suffixIcon,
  className,
  password = false,
  inputRef = React.useRef<import('antd').InputRef>(null),
  width = '320px',
  height = '44px',
  ...rest
}) => {
  const [focused, setFocused] = useState(false);
  const [hovered, setHovered] = useState(false);
  const hasValue = !!rest.value;

  const wrapperClass = classNames(styles.wrapper, className, {
    [styles.error]: validationStatus.error,
  });

  const inputWrapperClass = classNames(styles.inputWrapper, {
    [styles.focused]: focused,
    [styles.hovered]: hovered,
    [styles.filled]: hasValue,
    [styles.disabled]: rest.disabled,
  });
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className={wrapperClass}>
      {label && (
        <label
          className={clsx('fs14-medium', styles.label)}
          onClick={() => inputRef.current?.focus()}
        >
          {label}
        </label>
      )}

      <div className={inputWrapperClass}>
        <Input
          ref={inputRef}
          {...rest}
          type={password && !showPassword ? 'password' : 'text'}
          onFocus={e => {
            setFocused(true);
            rest.onFocus?.(e);
          }}
          onBlur={e => {
            setFocused(false);
            rest.onBlur?.(e);
          }}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
          prefix={prefixIcon && <span className={styles.icon}>{prefixIcon}</span>}
          suffix={
            <div className={styles.iconWrapper}>
              {suffixIcon && <span className={styles.icon}>{suffixIcon}</span>}
              {password && (
                <span
                  className={styles.passwordToggleWrapper}
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    setShowPassword(!showPassword);
                  }}
                >
                  <Icon
                    width={16}
                    height={16}
                    name={showPassword ? 'eye' : 'eyeOff'}
                    className={styles.passwordIcon}
                  />
                </span>
              )}
              {validationStatus.error && validationStatus.errorIcon && (
                <span className={styles.icon}>
                  <Icon name="error" color={error600} width={16} height={16} />
                </span>
              )}
            </div>
          }
          style={{
            ...rest.style,
            width,
            height,
            lineHeight: height,
          }}
        />
      </div>

      {message && <div className={clsx('fs14-regular', styles.message)}>{message}</div>}
    </div>
  );
};

export default TextInput;
