// import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import EditableTable from '@/components/common/core/CommonTable/EditableTable';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { useGetKaruteInputCoursesQuery } from '@/store/karute/api';
import { GetKeikaCourse } from '@/store/karute/type';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
} from '@/types/constants';
import { Option } from '@/types/interface';
import { Form } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useParams } from 'react-router-dom';
import {
  DatabaseSchema,
  EPaymentType,
  PatientCourse,
  RegisteredVisit,
} from '../UpsertDatabaseForm/types';
import { OptionItem } from './ListOptionTable';
import { OptionCourseActionBtn } from './OptionCourseActionBtn';
import styles from './UpsertOptionListForm.module.scss';

// const sampleCourseErrors = {
//   'patient_courses.0.course_ids': ['コース名は必須項目です。 0'],
//   'patient_courses.1.course_ids': ['コース名は必須項目です。'],
// };
export type CourseItem = {
  rowId: string;
  course_ids: string[];
  isInit?: boolean;
  names?: string[];
} & Omit<PatientCourse, 'patient_id' | 'course_id' | 'name'>;

export type CourseTableProps = {
  courseSelectedKeys: string[];
  setCourseSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
  courseDataSource: CourseItem[];
  setCourseDataSource: React.Dispatch<React.SetStateAction<CourseItem[]>>;
  service_id?: number;
  scrollToFirstSameDayRow?: (treatment_date: string) => void;
  isActionable: (record: CourseItem) => boolean;
  isCourseNullable: (treatmentDate: string) => boolean;
  allRegisteredVisit: RegisteredVisit[];
  initialCourseData?: PatientCourse[] | GetKeikaCourse[];
  handleUnsavedValueChange?: ({
    optionData,
    courseData,
    database,
  }: {
    optionData?: OptionItem[];
    courseData?: CourseItem[];
    database?: DatabaseSchema;
  }) => void;
};

export type CourseTableRef = {
  handleCourseErrors: (errorData: Record<string, string[]>) => void;
  handleSubmittingAllEditingRows: () => Promise<CourseItem[] | null>;
  getAllCurrentData: () => CourseItem[];
  scrollToRow: (rowId: string) => void;
};
export const ListCourseTable = forwardRef<CourseTableRef, CourseTableProps>(
  (
    {
      courseSelectedKeys,
      setCourseSelectedKeys,
      courseDataSource,
      setCourseDataSource,
      service_id,
      scrollToFirstSameDayRow,
      isActionable,
      allRegisteredVisit,
      initialCourseData,
      isCourseNullable,
      handleUnsavedValueChange,
    },
    ref
  ) => {
    const { serviceId: activeService } = useParams();
    const [, forceUpdate] = useState(0);
    const currentErrors = useRef<string[]>([]); // {rowId: errors[]}
    const triggerRerender = () => {
      forceUpdate(prev => prev + 1);
    };
    const { data: courseOptionsData } = useGetKaruteInputCoursesQuery(
      {
        service_id: Number(service_id || activeService),
      },
      {
        skip: !service_id && !activeService,
      }
    );
    const courseOptions: Option<string>[] = useMemo(() => {
      const initialCourseDataOptions: Option<string>[] =
        initialCourseData
          ?.filter(course => course.payment_course_type === Number(EPaymentType.SELF_PAID))
          ?.map(course => ({
            value: String(course.course_id),
            label: course.name ?? 'BE Error',
          })) ?? [];

      const registeredCourse: Option<string>[] = allRegisteredVisit.flatMap(visit =>
        visit.courses.map(course => ({
          value: String(course.id),
          label: course.name,
        }))
      );

      const apiCourses: Option<string>[] = (courseOptionsData?.data ?? []).map(item => ({
        value: String(item.course_id),
        label: item.course_name,
      }));

      const combined = [...apiCourses, ...registeredCourse, ...initialCourseDataOptions];

      const uniqueMap = new Map<string, Option<string>>();
      for (const course of combined) {
        if (!uniqueMap.has(course.value)) {
          uniqueMap.set(course.value, course);
        }
      }

      return Array.from(uniqueMap.values());
    }, [initialCourseData, courseOptionsData, allRegisteredVisit]);

    const [form] = Form.useForm<Record<string, CourseItem>>();

    const getCourse = useCallback(
      (record: CourseItem) => {
        let result = '';

        if (record.course_ids && record.course_ids.length > 0) {
          result = record.course_ids
            .map(item => courseOptions.find(course => course.value == item)?.label)
            .join(COMMA_SEPARATED);
        }
        return result;
      },
      [courseOptions]
    );

    const isFetching: boolean = false;

    const mapFormValuesFromRowIds = () => {
      // when changing datasource
      // -> form will be remap -> have to reset form value
      const updatedValues: Record<string, any> = {};
      courseDataSource.forEach(option => {
        if (!courseSelectedKeys.includes(option.rowId!)) return;
        const key = option.rowId;
        const formValue = form.getFieldValue(key);
        if (formValue) {
          updatedValues[key!] = formValue;
        }
      });
      setTimeout(() => {
        form.setFieldsValue({
          ...updatedValues,
        });
      }, 0);
    };

    useEffect(() => {
      const formValues = courseDataSource.reduce((acc, item) => {
        acc[item.rowId!] = item;
        return acc;
      }, {} as Record<string, object>);
      form.setFieldsValue(formValues);
      const errorFields = (currentErrors.current ?? []).map(item => ({
        name: [item, 'course_ids'],
        errors: [ERROR_COMMON_MESSAGE.REQUIRED('コース名')],
      }));

      setTimeout(() => {
        if (errorFields.length > 0) {
          form.setFields(errorFields as any);
          triggerRerender();
        }
      }, 0);
    }, [form, courseDataSource]);

    const getAllCurrentData = () => {
      const currentDataSource = courseDataSource.map(item => {
        const targetsData = form.getFieldValue(item.rowId);
        return {
          ...targetsData,
        };
      });
      return currentDataSource;
    };

    const handleValuesChange = (changedValues: Record<string, CourseItem>) => {
      Object.entries(changedValues).forEach(([rowId, changedFields]) => {
        Object.keys(changedFields).forEach(field => {
          form.setFields([{ name: [rowId, field as any], errors: [] }]);
        });
      });
    };

    const handleCourseErrors = (errorData: Record<string, string[]>) => {
      const courseErrors: { name: (string | number)[]; errors: string[] }[] = [];
      const rowsWithErrors: string[] = [];

      Object.entries(errorData).forEach(([fieldName, errors]) => {
        const fieldPath = fieldName
          .split('.')
          .map(part => (isNaN(Number(part)) ? part : Number(part)));

        if (
          ['patient_courses', 'keika_courses'].includes(fieldPath[0] as string) &&
          fieldPath.length === 3
        ) {
          const index = Number(fieldPath[1]);
          const field = fieldPath[2];

          const row = courseDataSource[index];

          if (row && row.rowId) {
            courseErrors.push({
              name: [row.rowId, field],
              errors,
            });

            if (!rowsWithErrors.includes(row.rowId.toString())) {
              rowsWithErrors.push(row.rowId.toString());
            }
          }
        }
      });

      if (courseErrors.length > 0) {
        setTimeout(() => {
          form.setFields(courseErrors as any);
          currentErrors.current = [...new Set([...currentErrors.current, ...rowsWithErrors])];
          triggerRerender();
        }, 1);
      }
      if (rowsWithErrors.length > 0) {
        setCourseSelectedKeys(rowsWithErrors);
        setTimeout(() => {
          form.scrollToField([rowsWithErrors[0], 'course_ids'], {
            behavior: 'smooth',
            block: 'center',
          });
        }, 1);
      }
    };
    const updateCourseData = (
      courseDataSource: CourseItem[],
      dataUpdated: CourseItem,
      rowId: string,
      courseOptions: Option<string>[]
    ): CourseItem[] => {
      const registration_date = dayjs().format(DATE_FORMATS.DATE);
      const names = courseOptions
        .filter(item => dataUpdated.course_ids?.includes(item.value))
        .map(item => item.label);

      const isUpdated = courseDataSource.some(it => it.rowId === rowId);

      if (isUpdated) {
        // Cập nhật record hiện có
        return courseDataSource.map(data =>
          data.rowId === rowId
            ? {
                ...dataUpdated,
                registration_date,
                isInit: false,
                names,
              }
            : data
        );
      } else {
        // Thêm record mới
        return courseDataSource
          .filter(item => item.rowId !== rowId)
          .concat({
            ...dataUpdated,
            registration_date,
            isInit: false,
            names,
          });
      }
    };
    const validateCourseIds = (rowId: string, dataUpdated: CourseItem) => {
      const isCourseNullableForDate = isCourseNullable(dataUpdated.treatment_date);
      if (
        !isCourseNullableForDate &&
        (!dataUpdated?.course_ids || dataUpdated.course_ids.length === 0)
      ) {
        form.setFields([
          {
            name: [rowId, 'course_ids'],
            errors: [ERROR_COMMON_MESSAGE.REQUIRED('コース名')],
          },
        ]);
        currentErrors.current = [...new Set([...currentErrors.current, rowId])];
        return false;
      }
      form.setFields([{ name: [rowId, 'course_ids'], errors: [] }]);
      currentErrors.current = currentErrors.current.filter(item => item !== rowId);
      return true;
    };
    const handleSubmittingAllEditingRows = async (): Promise<CourseItem[]> => {
      try {
        const rowsWithEmptyCourseIds: string[] = [];

        isEditableKeys.forEach(rowId => {
          const dataUpdated = form.getFieldValue(rowId);
          const record = courseDataSource.find(item => item.rowId === rowId);

          if (record) {
            const isValid = validateCourseIds(rowId, dataUpdated);
            if (!isValid) {
              rowsWithEmptyCourseIds.push(rowId);
            } else {
              let currentUpdatedDataSource = [...courseDataSource];
              currentUpdatedDataSource = updateCourseData(
                currentUpdatedDataSource,
                dataUpdated,
                rowId,
                courseOptions
              );
              setCourseDataSource(currentUpdatedDataSource);
              form.setFieldValue([rowId, 'course_ids'], dataUpdated.course_ids);
            }
          }
        });

        if (rowsWithEmptyCourseIds.length > 0) {
          currentErrors.current = [
            ...new Set([...currentErrors.current, ...rowsWithEmptyCourseIds]),
          ];
          setCourseSelectedKeys(rowsWithEmptyCourseIds);
          form.scrollToField([rowsWithEmptyCourseIds[0], 'course_ids'], {
            behavior: 'smooth',
            block: 'center',
          });

          triggerRerender();
          throw new Error('Course validation failed');
        }

        let updatedDataSource = [...courseDataSource];
        isEditableKeys.forEach(rowId => {
          const dataUpdated = form.getFieldValue(rowId);
          updatedDataSource = updateCourseData(
            updatedDataSource,
            dataUpdated,
            rowId,
            courseOptions
          );
        });
        setCourseDataSource(updatedDataSource);
        setCourseSelectedKeys([]);
        mapFormValuesFromRowIds();

        return updatedDataSource;
      } catch (error) {
        const errorFields = (error as any).errorFields || [];
        const rowsWithErrors = errorFields
          .map((err: any) => err.name[0].toString())
          .filter((rowId: string) => isEditableKeys.includes(rowId));

        if (rowsWithErrors.length > 0) {
          setCourseSelectedKeys(rowsWithErrors);
          form.scrollToField([rowsWithErrors[0], 'course_ids'], {
            behavior: 'smooth',
            block: 'center',
          });
        }

        throw error;
      }
    };

    // Expose handleCourseErrors thông qua useImperativeHandle
    useImperativeHandle(ref, () => ({
      handleCourseErrors,
      handleSubmittingAllEditingRows,
      getAllCurrentData,
      scrollToRow,
    }));
    const handleCancelRecord = (record: CourseItem) => {
      // validate old value
      const isValid = validateCourseIds(record.rowId, record);
      if (!isValid) {
        triggerRerender();
        return;
      }

      const originalRecord = courseDataSource.find(item => item.rowId === record.rowId);
      form.setFieldValue([record.rowId, 'course_ids'], originalRecord?.course_ids);
      setCourseSelectedKeys(courseSelectedKeys.filter(item => item !== record.rowId!));
    };

    const handleConfirmEdit = (record: CourseItem) => {
      const dataUpdated = form.getFieldValue(record.rowId);

      // Validate course_ids
      const isValid = validateCourseIds(record.rowId, dataUpdated);
      if (!isValid) {
        // mapFormValuesFromRowIds();
        triggerRerender();
        return;
      }

      const updatedDataSource = updateCourseData(
        courseDataSource,
        dataUpdated,
        record.rowId,
        courseOptions
      );

      setCourseDataSource(updatedDataSource);
      mapFormValuesFromRowIds();
      setCourseSelectedKeys(courseSelectedKeys.filter(item => item !== String(record.rowId)));
    };
    const filteredCourseDataSource = useMemo(() => {
      mapFormValuesFromRowIds();
      return courseDataSource.filter(record => isActionable(record));
    }, [courseDataSource, isActionable]);

    const isEditableKeys = useMemo(() => {
      return courseSelectedKeys.filter(key => {
        const record = filteredCourseDataSource.find(item => item.rowId === key);
        return record && isActionable(record);
      });
    }, [courseSelectedKeys, filteredCourseDataSource, isActionable]);
    const isEditable = useCallback(
      (data: CourseItem) => {
        return isEditableKeys.includes(data.rowId?.toString() || '');
      },
      [isEditableKeys]
    );
    const scrollToRow = useCallback((rowId: string) => {
      // setTimeOut to make sure course DataSource has been updated with the latest data
      setTimeout(() => {
        document
          .querySelector(`.scroll-row_course_${rowId}`)
          ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 1);
    }, []);
    const columns: CustomColumnType<CourseItem>[] = [
      {
        title: '初診日',
        dataIndex: 'treatment_date',
        key: 'treatment_date',
        sortKey: 'treatment_date',
        width: 144,
        align: 'right',
        onCell: (record: CourseItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
          // style: {
          //   maxHeight: '84px',
          //   minHeight: 'px',
          //   display: 'flex',
          //   alignItems: 'center',
          //   justifyContent: 'right',
          //   width: '100%',
          // },
        }),
        render: (_, record) => {
          const isDisabled = !isActionable(record);
          return <div style={isDisabled ? { opacity: 0.5 } : {}}>{record.treatment_date}</div>;
        },
      },
      {
        title: 'コース名',
        dataIndex: 'course_ids',
        key: 'course_ids',
        width: 220,
        sortKey: 'course_ids',
        editable: {
          type: 'select',
          options: courseOptions,
          mode: 'multiple',
          showSearch: true,
          placeholder: PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN,
          style: {
            // width: '316px',
            height: '38px',
          },
        },
        render: (_, record) => {
          const isDisabled = !isActionable(record);
          return (
            <div
              className={clsx(styles.cellContentWrapper, styles.cellContent)}
              style={isDisabled ? { opacity: 0.5 } : undefined}
            >
              <RenderWithTooltip text={getCourse(record)} />
            </div>
          );
        },

        onCell: (record: CourseItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
          // className: styles.cellContent,
        }),
      },
      {
        title: '登録日',
        dataIndex: 'registration_date',
        key: 'registration_date',
        sortKey: 'registration_date',
        width: 144,
        align: 'right',
        onCell: (record: CourseItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
          // className: styles.cellContent,
        }),
        render: (_: unknown, record: CourseItem) => {
          const isDisabled = !isActionable(record);
          return <div style={{ opacity: isDisabled ? 0.5 : 1 }}>{record.registration_date}</div>;
        },
      },
      {
        title: '',
        key: 'actions',
        align: 'center',
        width: 70,
        fixed: 'right',
        render: (_: any, record: CourseItem) => {
          return (
            <OptionCourseActionBtn
              key={record.rowId}
              mode="course"
              handleCancelRecord={() => {
                handleCancelRecord(record);
              }}
              handleConfirmEdit={() => {
                handleConfirmEdit(record);
              }}
              handleOpenEdit={() =>
                setCourseSelectedKeys(prevState => [...prevState, record?.rowId?.toString() || ''])
              }
              isEdit={isEditable(record)}
              record={record}
              canDelete={false}
              isDisabled={!isActionable(record)}
              isCourseRecordNullable={!!isCourseNullable(record.treatment_date)}
            />
          );
        },
      },
    ];

    return (
      <Form
        form={form}
        className={clsx(styles.hoken_table, styles.table)}
        onValuesChange={changedValues => {
          handleValuesChange(changedValues);
          handleUnsavedValueChange?.({ courseData: getAllCurrentData() });
        }}
      >
        <EditableTable
          rowKey={record => record.rowId!}
          columns={columns}
          editingKeys={isEditableKeys}
          pagination={false}
          dataSource={courseDataSource}
          scroll={{ y: 162 }}
          loading={isFetching}
          rowClassName={record => `scroll-row_course_${record.rowId}`}
          isMargin
        />
        <div className={styles.add_record_btn} />
      </Form>
    );
  }
);
