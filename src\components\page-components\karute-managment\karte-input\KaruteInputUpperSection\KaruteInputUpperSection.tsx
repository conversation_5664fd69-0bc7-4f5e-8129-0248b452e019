import Button from '@/components/common/core/Button';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import EditableTable, { EditableRecord } from '@/components/common/core/CommonTable/EditableTable';
import Table, { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import Icon from '@/components/common/core/Icon';
import { AppModal } from '@/components/common/core/Modal';
import { AppSelect } from '@/components/common/core/Select';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import {
  useCreateMondaiMutation,
  useGetMondaiListQuery,
  useLazyGetMondaiHistoryQuery,
  useUpdateMondaiByIdMutation,
} from '@/store/mondai/api';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  MONDAI_STATUS_OPTION,
  MONDAI_TYPE,
  MONDAI_TYPE_OPTIONS,
  PAGE_LIMIT_NUMBER,
} from '@/types/constants';
import { ButtonType, ESort } from '@/types/enum';
import { KaruteMondai } from '@/types/interface/KaruteMondai';
import { showFlashNotice } from '@/utils';
import { Flex, Form } from 'antd';
import dayjs from 'dayjs';
import { Fragment, useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import styles from './KaruteInputUpperSection.module.scss';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import { useKaruteInput } from '../provider';
import clsx from 'clsx';

type TreeDataKaruteMondai = Partial<KaruteMondai & { rowId?: number; mode: 'add' | 'edit' }>;

type KaruteInputUpperSectionWithModalProps = {
  isOpen?: boolean;
  karute_id: number;
  patient_id: number;
  onClose?: () => void;
};

const KaruteInputUpperSectionWithModal = ({
  isOpen,
  karute_id,
  patient_id,
  onClose,
}: KaruteInputUpperSectionWithModalProps) => {
  return (
    <AppModal
      className={styles.karuteInputModal}
      closable={true}
      destroyOnClose={true}
      onCancel={() => onClose && onClose()}
      open={isOpen}
      width={'1000px'}
      height={'853px'}
      style={{
        minHeight: '100%',
      }}
    >
      <KaruteInputUpperSection karute_id={karute_id} patient_id={patient_id} isModal={true} />
    </AppModal>
  );
};

type KaruteInputUpperSection = {
  isModal?: boolean;
  patient_id?: number;
  karute_id?: number;
};

const initialData: TreeDataKaruteMondai = {
  medical_record_id: undefined,
  karute_id: undefined,
  patient_id: 0,
  patient_name: '',
  patient_name_kana: '',
  trauma_type: undefined,
  trauma_counter: undefined,
  subject: '',
  remarks: '',
  create_by: undefined,
  version: undefined,
  status: undefined,
  mondai_related: [],
  updated_at: dayjs().format(DATE_FORMATS.DATE),
  mondai_id: undefined,
};

export function KaruteInputUpperSection({ isModal }: KaruteInputUpperSection) {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [tableData, setTableData] = useState<TreeDataKaruteMondai[]>([]);
  const [historyCache, setHistoryCache] = useState<Record<number, TreeDataKaruteMondai[]>>({});
  const [fetchHistory, { isLoading }] = useLazyGetMondaiHistoryQuery();
  const { karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;
  const { id: karute_id } = useParams<{ id: string }>();
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: PAGE_LIMIT_NUMBER,
    trauma_type: undefined,
    status: 1,
    patient_id: patientId ? Number(patientId) : undefined,
    service_id: 1,
  });

  const [openModal, setOpenModal] = useState<boolean>(false);
  const [expendedHistoryKeys, setExpendedHistoryKeys] = useState<number[]>([]);
  const { data: mondaiData, isFetching, refetch } = useGetMondaiListQuery(queryParams);

  const [mutate] = useUpdateMondaiByIdMutation();
  const [createMutation] = useCreateMondaiMutation();
  const { showWarningDialog } = useWarningDialog();
  const isEditable = useCallback(
    (data: Partial<KaruteMondai> & { rowId?: number }) => {
      return selectedKeys.includes(data.rowId?.toString() || '');
    },
    [selectedKeys]
  );
  const [form] = Form.useForm();

  useEffect(() => {
    setTableData(
      mondaiData?.data?.data
        ? [...mondaiData.data.data, { ...initialData, mode: 'add' }]
        : [initialData]
    );
  }, [mondaiData]);

  useEffect(() => {
    setSelectedKeys([tableData.length.toString()]);
    setExpendedHistoryKeys([]);

    form.resetFields();
  }, [tableData]);
  useEffect(() => {
    // Rest patient_id if patient_id change
    if (karuteDetailData?.patient_id) {
      setQueryParams(prev => ({
        ...prev,
        patient_id: karuteDetailData?.patient_id,
      }));
    }
  }, [karuteDetailData]);
  const handleSortChange = (sortData: { order_by: string; order_type: ESort }[]) => {
    setQueryParams(prev => ({
      ...prev,
      order_by: sortData[0]?.order_by,
      order_type: sortData[0]?.order_type,
    }));
  };

  const subcolumns: CustomColumnType<TreeDataKaruteMondai>[] = [
    {
      title: '記載日',
      dataIndex: 'updated_at',
      width: 120,
    },
    {
      title: '問題番号',
      dataIndex: 'trauma_type',
      width: 150,
      render(value, record) {
        const mondai_type_mapping =
          MONDAI_TYPE[Number(record.trauma_type) as keyof typeof MONDAI_TYPE];
        return (
          <div className={styles.mondaiType}>
            <p>{mondai_type_mapping}</p>
            <p>
              {mondai_type_mapping}
              {record.trauma_counter}
            </p>
          </div>
        );
      },
    },
    {
      title: '問題名',
      dataIndex: 'subject',
      width: 250,
    },
    {
      title: '備考',
      dataIndex: 'remarks',
      width: 250,
    },
    {
      title: '統合先',
      dataIndex: 'mondai_related',
      render: (val: unknown, record: TreeDataKaruteMondai) => {
        if (Array.isArray(record.mondai_related)) {
          if (record.mondai_related.length === 0) return '-';
          return (
            <div>
              {record.mondai_related.map((item, idx) => {
                const mondai_type_mapping =
                  MONDAI_TYPE[Number(item.trauma_type) as keyof typeof MONDAI_TYPE];

                if (!mondai_type_mapping) return null; // Skip if mondai_type_mapping is undefined
                return (
                  <span key={item.mondai_id || idx} className={styles.mondaiType}>
                    {mondai_type_mapping}
                    {item.trauma_counter}
                    {Array.isArray(record.mondai_related) && idx < record.mondai_related.length - 1
                      ? COMMA_SEPARATED
                      : ''}
                  </span>
                );
              })}
            </div>
          );
        }
        return record.mondai_related ? String(record.mondai_related) : '-';
      },
      width: 300,
    },
    {
      title: '活性状態',
      dataIndex: 'status',
      render: (_: unknown, record: Partial<KaruteMondai>) => (
        <div className={styles.mondaiStatus}>
          <div className={`${record.status === 1 ? styles.active : styles.inactive}`}>
            {MONDAI_STATUS_OPTION[record.status as keyof typeof MONDAI_STATUS_OPTION]}
          </div>
        </div>
      ),
      width: 300,
    },
    {
      title: '',
      dataIndex: 'actions',
      render: () => <></>,
    },
  ];

  const columns: CustomColumnType<TreeDataKaruteMondai>[] = [
    {
      title: '記載日',
      dataIndex: 'updated_at',
      key: 'updated_at',
      sortable: true,
      sortKey: 'date',
      width: 120,
    },
    {
      title: '問題番号',
      dataIndex: 'trauma_type',
      key: 'trauma_type',
      width: 150,
      editable: (record: TreeDataKaruteMondai): EditableRecord => {
        return {
          type: 'select',
          options: Object.entries(MONDAI_TYPE_OPTIONS)
            .filter(([key, val]) => val !== MONDAI_TYPE_OPTIONS[0])
            .map(([key, val]) => ({
              label: val,
              value: Number(key),
              disabled: key === '1' && record.mode === 'add', // Disable M type
            })),
          rules: [
            {
              message: '',
              required: true,
            },
          ],
          render: (record: TreeDataKaruteMondai) => {
            const mondai_type_mapping =
              MONDAI_TYPE[Number(record.trauma_type) as keyof typeof MONDAI_TYPE];

            return (
              <p>
                {mondai_type_mapping}
                {record.trauma_counter}
              </p>
            );
          },
        };
      },
      render(value, record) {
        const mondai_type_mapping =
          MONDAI_TYPE[Number(record.trauma_type) as keyof typeof MONDAI_TYPE];
        return (
          <div className={styles.mondaiType}>
            <p>{mondai_type_mapping}</p>
            <p>
              {mondai_type_mapping}
              {record.trauma_counter}
            </p>
          </div>
        );
      },
      sortable: true,
      sortKey: 'trauma_type',
    },
    {
      title: '問題名',
      dataIndex: 'subject',
      key: 'subject',
      sortable: true,
      sortKey: 'subject',
      width: 250,
      editable: {
        type: 'input',
        rules: [
          {
            message: '',
            required: true,
          },
        ],
        maxLength: 100,
        disabled: <T extends Partial<KaruteMondai>>(record: T) => record.trauma_type === 1,
      },
      render: (val: unknown, record) => {
        return record.subject ? <RenderWithTooltip text={record.subject} /> : '-';
      },
    },
    {
      title: '備考',
      dataIndex: 'remarks',
      key: 'remarks',
      sortable: true,
      sortKey: 'remarks',
      width: 250,
      editable: {
        type: 'input',
        maxLength: 1000,
      },
      render: (val: unknown, record) => {
        return record.remarks ? <RenderWithTooltip text={record.remarks} /> : '-';
      },
    },
    {
      title: '統合先',
      dataIndex: 'mondai_related',
      key: 'mondai_related',
      //TODO: mondai_related should be mapped to the string M1, M2, M3
      render: (val: unknown, record: TreeDataKaruteMondai) => {
        if (Array.isArray(record.mondai_related)) {
          if (record.mondai_related.length === 0) return '-';
          return (
            <div>
              {record.mondai_related.map((item, idx) => {
                const mondai_type_mapping =
                  MONDAI_TYPE[Number(item.trauma_type) as keyof typeof MONDAI_TYPE];

                if (!mondai_type_mapping) return null; // Skip if mondai_type_mapping is undefined
                return (
                  <span key={item.mondai_id || idx} className={styles.mondaiType}>
                    {mondai_type_mapping}
                    {item.trauma_counter}
                    {Array.isArray(record.mondai_related) && idx < record.mondai_related.length - 1
                      ? COMMA_SEPARATED
                      : ''}
                  </span>
                );
              })}
            </div>
          );
        }
        return record.mondai_related ? String(record.mondai_related) : '-';
      },

      width: 300,
      sortable: true,
      sortKey: 'mondai_related',
      editable: (record: TreeDataKaruteMondai) => ({
        type: 'select',
        options:
          mondaiData?.data?.data
            ?.filter(item => item.mondai_id !== record.mondai_id)
            .map(item => ({
              label: `${MONDAI_TYPE[item.trauma_type]}${item.trauma_counter}`,
              value: item.mondai_id,
              disabled: item.mondai_id === record.mondai_id, // Disable self reference
            })) || [],
        filterOption: (input, option) => {
          if (!option || !option.label) return false;
          return option.label.toString().toLowerCase().includes(input.toLowerCase());
        },
        showSearch: true,
      }),
    },
    {
      title: '活性状態',
      dataIndex: 'status',
      key: 'status',
      render: (_: unknown, record: Partial<KaruteMondai>) => (
        <div className={styles.mondaiStatus}>
          <div className={`${record.status === 1 ? styles.active : styles.inactive}`}>
            {MONDAI_STATUS_OPTION[record.status as keyof typeof MONDAI_STATUS_OPTION]}
          </div>
        </div>
      ),
      width: 300,
      editable: {
        type: 'select',
        options: Object.entries(MONDAI_STATUS_OPTION).map(([key, val]) => ({
          label: val,
          value: isNaN(parseInt(key)) ? key : parseInt(key),
        })),
      },
      sortable: true,
      sortKey: 'status',
    },
    {
      title: '',
      dataIndex: 'actions',
      render: (_: unknown, record: Partial<KaruteMondai & { rowId?: number }>) => {
        return !isEditable(record) ? (
          <Flex style={{ width: '100%' }} align="center" gap={6}>
            <div
              style={{
                cursor: 'pointer',
              }}
              onClick={() => {
                setSelectedKeys(prevState => [...prevState, record?.rowId?.toString() || '']);
                form.setFieldsValue({ [record.rowId!]: record });
              }}
            >
              <Icon name="edit" color="$gray-400" width={16} height={16} />
            </div>
          </Flex>
        ) : (
          <Fragment key={record.medical_record_id}>
            <Flex align="center" gap={6}>
              <div
                style={{ cursor: 'pointer' }}
                onClick={async () => {
                  const data = form.getFieldValue(record.rowId);
                  try {
                    const rowFields = Object.keys(data).map(field => [record.rowId, field]);
                    await form.validateFields(rowFields);

                    const cb = await handleSubmitMondai(record, data);
                    setSelectedKeys(selectedKeys.filter(item => item !== String(record.rowId)));
                    if (cb && typeof cb === 'object' && 'isChange' in cb && cb.isChange) {
                      setExpendedHistoryKeys(
                        expendedHistoryKeys.filter(key => key !== record.rowId)
                      );
                    }
                  } catch (_err: unknown) {
                    console.log('🚀 ~ onClick={ ~ _err:', _err);
                  }
                }}
              >
                <Icon name="check" color="green" height={16} width={16} />
              </div>
              <div
                onClick={() => {
                  setSelectedKeys(
                    selectedKeys.filter(
                      item => item !== String(record.rowId) || item == tableData.length.toString()
                    )
                  );
                }}
                style={{ cursor: 'pointer' }}
              >
                <Icon name="close" color="red" height={16} width={16} />
              </div>
            </Flex>
          </Fragment>
        );
      },
    },
  ];

  const handleSubmitMondai = async (
    oldData: Partial<TreeDataKaruteMondai>,
    newData: Partial<TreeDataKaruteMondai>
  ) => {
    if (JSON.stringify(oldData) === JSON.stringify(newData)) {
      return Promise.resolve({ isChange: false });
    }
    const isUpdated = !!newData.mondai_id;
    const oldType = Number(oldData?.trauma_type) as keyof typeof MONDAI_TYPE;
    const newType = Number(newData?.trauma_type) as keyof typeof MONDAI_TYPE;

    const isToMedical = oldType !== 1 && newType === 1;
    const isToSocial = oldType === 1 && newType !== 1;
    const handleMutation = async () => {
      const res = await mutate({
        ...newData,
        mondai_relations: Array.isArray(newData.mondai_related)
          ? newData.mondai_related.map(item => Number(item.mondai_id))
          : newData.mondai_related
          ? [Number(newData.mondai_related)]
          : [],
      });
      if (res) {
        showFlashNotice({
          type: 'success',
          message: `「${MONDAI_TYPE[oldType]}」 種別を 「${MONDAI_TYPE[newType]}」種別に変更しました。`,
        });
        refetch();
      }
    };

    if (isToMedical && isUpdated) {
      return showWarningDialog({
        onConfirm: () => console.log('open K100'),
        message:
          '「S」または「P」種別から「M」種別への変更は、問題のデータベースを登録する必要があります。本当に続行してもよろしいでしょうか？',
      });
    }

    if (isToSocial && isUpdated) {
      return showWarningDialog({
        onConfirm: handleMutation,
        message:
          '「M」種別から「S」または「P」種別への変更は、「M」に登録されたデータは使用できなくなります。本当に続行してもよろしいでしょうか？',
      });
    }

    if (!isUpdated) {
      const res = await createMutation({
        ...newData,
        patient_id: Number(patientId),
        karute_id: Number(karute_id),
        disease_base_id: null,
        mondai_relations: Array.isArray(newData.mondai_related)
          ? newData.mondai_related.map(item => Number(item.mondai_id))
          : newData.mondai_related
          ? [Number(newData.mondai_related)]
          : [],
      });
      if (res.data) {
        refetch();
      }
    } else {
      const res = await mutate({
        ...newData,
        mondai_relations: Array.isArray(newData.mondai_related)
          ? newData.mondai_related.map(item => Number(item.mondai_id))
          : newData.mondai_related
          ? [Number(newData.mondai_related)]
          : [],
      });
      if (res.data) {
        refetch();
      }
    }
  };

  const handleExpand = async (expanded: boolean, record: TreeDataKaruteMondai) => {
    if (expanded && !historyCache[record.mondai_id!]) {
      try {
        const { data } = await fetchHistory({ mondai_id: record.mondai_id! }).unwrap();
        setHistoryCache(prev => ({
          ...prev,
          [record.rowId!]: data || [],
        }));
      } catch (err) {
        console.error('Failed to fetch history', err);
      }
    }

    setExpendedHistoryKeys(keys =>
      expanded ? [...keys, record.rowId!] : keys.filter(key => key !== record.rowId!)
    );
  };

  const expandedRowRender = (record: TreeDataKaruteMondai) => {
    return (
      <Table<TreeDataKaruteMondai>
        columns={subcolumns}
        className={styles.subTable}
        pagination={false}
        showHeader={false}
        loading={isLoading}
        rowKey={record => record.medical_record_id!}
        dataSource={historyCache[record.rowId!]}
        expandable={{
          expandIcon: () => <></>,
          expandedRowRender: () => <></>,
        }}
      />
    );
  };

  return (
    <>
      <div className={styles.mondaiSectionTable}>
        <Flex justify="space-between" align="center" className={styles.header}>
          <h2 className={clsx(styles.title, 'fs14-bold')}>問題リスト</h2>
          <Flex style={{ width: '100%', marginRight: '24px' }} justify="end">
            <Flex
              align="center"
              gap={'small'}
              style={{
                paddingRight: '24px',
                borderRight: '1px solid var(--gray-200)',
                fontSize: '14px',
              }}
            >
              <Icon name={'filter'} color="$gray-500" width={20} height={20} />
              <span style={{ color: '#667085' }}>保険者種別で絞込</span>
              <AppSelect
                style={{
                  width: '80px',
                  height: '36px !important',
                }}
                defaultValue={Object.values(MONDAI_TYPE_OPTIONS)[0]}
                onChange={val => setQueryParams({ ...queryParams, trauma_type: val })}
                options={Object.entries(MONDAI_TYPE_OPTIONS).map(([key, val]) => ({
                  label: val,
                  value: !isNaN(Number(key)) && Number(key) === 0 ? '' : key,
                }))}
              />
            </Flex>
            <Flex align="center" gap={'small'} style={{ marginLeft: '24px', fontSize: '14px' }}>
              <AppCheckbox
                defaultChecked={true}
                checked={!!queryParams.status}
                onChange={e => setQueryParams({ ...queryParams, status: e.target.checked ? 1 : 0 })}
              />
              <span style={{ color: '#344054' }}>非活性を非表示にする</span>
            </Flex>
          </Flex>
          <Flex gap={'middle'}>
            <Button
              style={{
                display: isModal ? 'none' : 'block',
              }}
              customType={ButtonType.PRIMARY}
              onClick={() => {
                setOpenModal(true);
              }}
            >
              <Icon name="paper" />
              全問題
            </Button>
            <Button
              customType={ButtonType.PRIMARY}
              onClick={() => {
                // TODO: focus to empty row
              }}
            >
              <Icon name="plus" />
              新規作成
            </Button>
          </Flex>
        </Flex>
        <Form form={form} component={false}>
          <EditableTable<TreeDataKaruteMondai>
            rowKey="rowId"
            className={styles.mainTable}
            columns={columns}
            dataSource={tableData}
            loading={isFetching || isLoading}
            editingKeys={selectedKeys}
            pagination={
              isModal
                ? {
                    pageSize: PAGE_LIMIT_NUMBER,
                    total: mondaiData?.data?.total || 0,
                    current: queryParams.page,
                    onChange: (page, pageSize) => {
                      setQueryParams(prev => ({ ...prev, page, limit: pageSize }));
                    },
                  }
                : false
            }
            onSortChange={handleSortChange}
            scroll={{ x: 'max-content' }}
            expandable={{
              onExpand: handleExpand,
              expandedRowKeys: expendedHistoryKeys,
              expandIcon: ({ expanded, onExpand, record }) => {
                if (record.version === 1 || !record.version) return <></>;
                return expanded ? (
                  <button
                    style={{
                      transform: 'rotate(0deg)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={e => onExpand(record, e)}
                  >
                    <Icon color="$gray-400" name="arrowFilled" width={16} height={16} />
                  </button>
                ) : (
                  <button
                    style={{
                      transform: 'rotate(180deg)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={e => onExpand(record, e)}
                  >
                    <Icon color="$gray-400" name="arrowFilled" width={16} height={16} />
                  </button>
                );
              },
              expandedRowRender: expandedRowRender,
            }}
          />
        </Form>
      </div>
      {openModal && (
        <KaruteInputUpperSectionWithModal
          karute_id={Number(karute_id)}
          key={'modal'}
          patient_id={Number(patientId)}
          isOpen={openModal}
          onClose={() => setOpenModal(false)}
        />
      )}
    </>
  );
}
