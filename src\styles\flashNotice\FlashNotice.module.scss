.customNoticeWrapper {
  padding: 16px !important;
  border-radius: 8px;

  &.success {
    background-color: $success-200;
    border: 1px solid $success-500;
    box-shadow: 0px 4px 12px rgba($success-500, 0.1);
  }

  &.error {
    background-color: $error-200;
    border: 1px solid $error-500;
    box-shadow: 0px 4px 12px rgba($error-500, 0.1);
  }
  :global(.ant-notification-notice-message) {
    display: none;
  }
  .customNotice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: 0.5px;
    &.error {
      color: $error-600;
    }
    &.success {
      color: $success-600;
    }

    .messageWrapper {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
    }

    .closeIcon {
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }
}
