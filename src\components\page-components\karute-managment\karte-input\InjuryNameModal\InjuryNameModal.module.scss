.modal {
  width: unset !important;
  .modalWrapper {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    height: 100%;
    padding-top: 16px;
    .container {
      flex: 1;
      border: 1px solid $gray-200;
      border-radius: 8px;
      margin: 20px 0;
      display: flex;
      justify-content: space-between;
      max-height: calc(100vh - 260px) !important;
      overflow-y: auto;
    }
  }

  :global(.ant-spin-nested-loading),
  :global(.ant-spin-container) {
    height: 100%;
  }
  :global(.ant-modal-body) {
    height: 900px;
    max-height: calc(100vh - 80px) !important;
    overflow-y: auto;
  }

  .form {
    height: 100%;
  }
}
