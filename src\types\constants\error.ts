export const ERROR_COMMON_MESSAGE = {
  EMPTY_DATA: '데이터가 없습니다.', // Empty data,
  REQUIRED: (field: string) => `${field}は必須項目です。`, // E_0001 Input field Required
  USER_NOT_FOUND: '治療院ID、ユーザーID、パスワードが一致しません。', // E_0002 Login store_id , userid, password not match
  NO_DATA: 'データが登録されていません。', // I_0001 No data
  MAX_LENGTH_NUMBER: (length: number) =>
    `半角数字のみで${length}文字  ※ハイフン(－)は入力しないでください。`, // E_0006 Max length in case number
  REQUIRED_ONLY_CHARACTER: '「@」を含み、半角英数字および記号（@ . - _）のみ入力してください。', // E_0007 Enter only numbers, half-width Latin letters and the @ characters. - _, the "@" character is required.
  MIN_MAX_LENGTH: (min: number, max: number) => `${min}文字以上${max}文字以下で入力してください。`, // E_0008 min max length input
  USERID_ALREADY: `このユーザーIDが既に登録されています。`, // E_0009 user id already
  EMAIL_ALREADY: `このメールアドレスが既に使用されています。`, // E_0010  email already
  PASSWORD_NOT_MATCH: `確認用パスワードが一致していません。`, // E_0011  password not match confirm password
  PASSWORD_INVALID: `半角英数字混在させて6文字以上を入力してください。`, // E_0018  password doesn't meet the requirements
  REQUIRED_NUMBER_IN_ADDRESS: `住所に番地を入力してください。`, // E_0013  house number into address
  REQUIRED_DOCTOR_SELECT: `プルダウンリストから施術師を選択してください。`, // E_0005  choose doctor
  MAX_LENGTH_HYPHEN: `半角数字とハイフン（-）のみ入力してください。`, // E_0014 enter {maximum number of characters} characters for half-width and hyphen index
  ENTER_ONLY_KANA: `全角カナのみ入力してください。`, // E_0015  enter full-width Kana only
  ENTER_ONLY_FULL_WIDTH_CHARS_INCLUDING_NUMBERS_SYMBOLS: `全角文字（数字・記号を含む）のみを入力してください`, // only full-width characters (including numbers and symbols).
  HALF_WIDTH_ALPHANUMERIC_ONLY: '半角英数字のみ入力してください。', // E_0016
  ENTER_ONLY_KANJI: `半角英数字のみ入力してください。`, // E_0017  enter only half-width Latin letters and numbers (no special characters included)
  EMAIL_INVALID: `「@」を含み、半角英数字および記号（@ . - _）のみ入力してください。`, // 無効なメール。
  EMPTY_SERVICE_INSURANCE: 'サービス及び対応する保険種類を選択してください', //E_0021 please select service and insurance type
  DUPLICATE_INJURY_NAMES: 'この負傷名が既に登録されています。', // E_0040 This injury name has already been registered.
  INVALID_FREE_INJURY_NAME: '「負傷名」ボタンからマスターデータの負傷名を選択してください。', // E_0038 Please select a master data injury name from the "Injury Name" button.
  INJURY_DATE_AFTER_VISIT_DATE: '初診日と同日またはそれ以前の日付を選択してください。', // E_0039 Please choose injury date same of before visit date
  DELETE_CONDITION_NOT_MET: '削除条件を満たすデータが存在しません。', // E_0041 Delete condition not met
  INJURY_NAME_APPLIED: '負傷名を適用しました。', // I_0024 Injury name applied
};

export const NOTICE_COMMON_MESSAGE = {
  ERROR_UNUSUAL: '予期せぬエラーが発生しました。', // E_0004 I_0001 An unusual error occurred.
  CREATED_PATIENT: '患者を登録しました。', // I_0002 created patient
  SESSION_INVALID: 'セクションが無効です。', // E_0012 session invalid
  CREATED_USER: 'ユーザーを登録しました。', // I_0003 created user
  RECEIVED: '受付を登録しました。', // I_0004 received
  UPDATED_PATIENT: '患者情報を更新しました。', // I_0005 updated patient
  UPDATED_USER: 'ユーザー情報を更新しました。', // I_0006 updated user
  EMPTY_DATA: '該当する検索結果がありません。', // I_0011 no data
  MONDAI_UPDATED: '問題リストを更新しました。',
  PATIENT_SUMMARY_UPDATED: '患者サマリーを更新しました。',
  IMAGE_SELECT:
    '画像の形式が正しくありません。.jpg、.jpeg、または.png形式の画像を選択してください。',
  IMAGE_SIZE(size: number): string {
    return `画像ファイルの容量は${size}MB以下にしてください`;
  },
  UNSAVED_WARNING: '保存されていない変更があります。本当にこのページを離れますか？',
  WARNING_HOKEN_MSG: '保険情報がまだ登録されていませんが、このまま処理を続行しますか？',
  QR_SCAN_ERROR: 'QRコードを読み取れませんでした。もう一度お試しください。',
  WARNING_PATIENT_CD: '患者番号がまだ登録されておりません。登録してもよろしいでしょうか。',
  DATABASE_CREATED: 'データベースを登録しました。', // I_0014 Database created
  KEIKA_CREATED: '経過記録を登録しました。', // I_0017 Keika created
  SCHEMA_UPDATED: 'シェーマ画像を更新しました。', // I_0020 Schema updated
  DRAG_DROP: '点々のボタンを長押しして上下に動かすと並び替え', // I_0027: Press and hold the dot button and move it up and down to rearrange the order.
  UPDATE_MASTER_DATA: 'マスターデータを更新しました。', // I_0028: Master data has been updated.
  RESET_LAYOUT: '初期レイアウトにリセットしました。', // I_0025: Layout has been reset.
  REFLECTED_REFERENCE_DATA: '参照データを反映しました。', // I_0026 Reflected reference data
  COURSE_OPTION_UPDATED: '同日のコース・オプションを更新しました。', // I_0030 Course option of a day has been updated
};

export const PLACEHOLDER_MESSAGE_DEFAULT = {
  TEXT_INPUT: '入力してください',
  TEXT_AREA: '入力してください',
  DROP_DOWN: '選択してください',
  DATE_PICKER: '選択してください',
};

export const SCHEMA_COMMON = {
  WARNING_COMPRESS:
    'アップロードされた画像の容量が20MBを超えており、上限を超過しています。\n画像は20MBに圧縮した上でアップロードしてもよろしいでしょうか？',
  FAIL_COMPRESS: '画像の圧縮に失敗しました。',
  UPLOAD_LIMIT: (n: number) => `最大${n}枚まで選択できます。`,
  WARNING_REMOVE_IMAGE:
    '描画されている画像を削除しようとしてしますが続行します？削除するには【続行】をクリックし、データベースまたは経過記録の【登録】をクリックしてください。',
  EMPTY_IMAGE:
    '画像が選択されていません。左側のリストから画像を選んでから「確定」をクリックしてください。',
  SHORTCUT:
    '※キーボードショートカットのCtrl+Z（Windows）またはCommand+Z（Mac）で、直前の描画・削除操作を元に戻します。',
  NO_DATA: 'データがありません。',
  UPLOAD_SUCCESS: '画像をアップロードしました。',
  DELETE_MASTER_IMAGE_TITLE: 'アップロードしたマスター画像を削除する',
  DELETE_MASTER_IMAGE_DESC:
    'この操作は元に戻せません。アップロードしたマスター画像を削除してもよろしいでしょうか？',
};
