import EditableTable from '@/components/common/core/CommonTable/EditableTable';
import Icon from '@/components/common/core/Icon';
import { AppRadioGroup } from '@/components/common/core/Radio/AppRadioGroup';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { useMondaiColumns } from '@/hooks/useMondaiColumns';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { setChanged } from '@/store/karute';
import {
  MondaiQueryParams,
  mondaiThunks,
  setLoadingMondai,
  setMondaiModalQuery,
  setMondaiQueryParams,
  setSelectedMondai,
} from '@/store/karute/mondaiSlice';
import { keikaApi } from '@/store/keika/api';
import {
  useCreateMondaiMutation,
  useLazyGetMondaiHistoryQuery,
  useUpdateMondaiByIdMutation,
} from '@/store/mondai/api';
import {
  ERROR_COMMON_MESSAGE,
  MONDAI_TYPE,
  NOTICE_COMMON_MESSAGE,
  STATUS_CODES,
  WARNING_CHANGE_MONDAI_M_TO_SP,
  WARNING_CHANGE_MONDAI_SP_TO_M,
} from '@/types/constants';
import {
  EDatabaseUpsertMode,
  EKaruteService,
  EKaruteServicePriority,
  EKaruteUpsertType,
  ESort,
} from '@/types/enum';
import { APIResponse, FieldError } from '@/types/interface';
import { KaruteMondai } from '@/types/interface/KaruteMondai';
import { showFlashNotice } from '@/utils';
import { Flex, Form, Pagination, Skeleton } from 'antd';
import { AxiosError } from 'axios';
import { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import UpsertDatabaseModalWrapper from '../KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal';
import { useKaruteInput } from '../provider';
import styles from './Mondai.module.scss';
import { MondaiFilter } from './MondaiFilter';

export type TreeDataKaruteMondai = Partial<
  KaruteMondai & { rowId?: number | string; mode?: 'add' | 'edit' }
>;

interface IPagination {
  total?: number;
  pageSize?: number;
  limit?: number;
  currentPage?: number;
  total_filter?: number;
}

type KaruteInputUpperSection = {
  isModal?: boolean;
  patient_id?: number;
  karute_id?: number;
  mondaiData: KaruteMondai[];
  loadingData?: boolean;
  queryWithParams?: ({ ...args }: MondaiQueryParams) => void;
  pagination?: IPagination;
  handleCloseModal?: () => void;
  selectedKeys: string[];
  setSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
};

const initialData: TreeDataKaruteMondai = {
  medical_record_id: undefined,
  karute_id: undefined,
  patient_id: 0,
  patient_name: '',
  patient_name_kana: '',
  trauma_type: undefined,
  trauma_counter: undefined,
  subject: '',
  remarks: '',
  create_by: undefined,
  version: undefined,
  status: undefined,
  mondai_related: [],
  updated_at: undefined,
  mondai_id: undefined,
};

export function MondaiTable({
  isModal,
  loadingData,
  mondaiData,
  pagination,
  handleCloseModal,
  selectedKeys,
  setSelectedKeys,
}: KaruteInputUpperSection) {
  const navigate = useNavigate();
  const [tableData, setTableData] = useState<TreeDataKaruteMondai[]>([]);
  const [historyCache, setHistoryCache] = useState<Record<string, TreeDataKaruteMondai[]>>({});
  const [fetchHistory, { isFetching }] = useLazyGetMondaiHistoryQuery();
  const [openDatabaseModal, setOpenDatabaseModal] = useState(false);
  const {
    id: karuteId,
    upsertType,
    upsertMode,
  } = useParams<{
    id: string;
    upsertType?: EKaruteUpsertType;
    upsertMode?: string;
  }>();
  const { setElemRef, height } = useResizeObserver();

  const tableRef = useRef<any>(null);

  const { openUpsertModal } = useKaruteInput();

  const { queryParams, modalQuery, allMondais, selectedMondai } = useAppSelector(
    state => state.karute.mondais
  );
  const { loading: isMondaiLoading, servicesActive } = useAppSelector(
    state => state.karute.mondais
  );
  const dispatch = useAppDispatch();
  const { activeTrauma, karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;
  const [expendedHistoryKeys, setExpendedHistoryKeys] = useState<string[]>([]);
  const [mutate] = useUpdateMondaiByIdMutation();
  const [createMutation] = useCreateMondaiMutation();
  const { showWarningDialog } = useWarningDialog();
  const { services } = useAppSelector(state => state.sharedData);
  const selectedRef = useRef<number>();
  const servicesMemo = useMemo(
    () =>
      services
        .slice()
        .sort((a, b) => {
          return (
            EKaruteServicePriority[a.name as keyof typeof EKaruteServicePriority] -
            EKaruteServicePriority[b.name as keyof typeof EKaruteServicePriority]
          );
        })
        .map(service => {
          return {
            label: EKaruteService[service.name as keyof typeof EKaruteService],
            value: service.service_id,
            disabled: !servicesActive.includes(Number(service.service_id)),
          };
        }),
    [services, servicesActive]
  );

  const [form] = Form.useForm();

  useEffect(() => {
    const newTableData = mondaiData.map(item => ({
      ...item,
      rowId: item.mondai_id.toString(),
      related_id: item.mondai_related
        ? transformMondaiRelations(item.mondai_related)[0]
        : undefined,
    }));
    const initRow = {
      ...initialData,
      rowId: uuidv4(),
      mode: 'add',
    } as TreeDataKaruteMondai;
    setTableData([...newTableData, initRow]);
    const validRowIds = newTableData.map(item => String(item.rowId));
    const prevSelected = selectedKeys.filter(key => validRowIds.includes(key));
    setSelectedKeys([...prevSelected, initRow.rowId?.toString() || '']);
    setExpendedHistoryKeys([]);
    setHistoryCache({});
  }, [mondaiData]);

  useEffect(() => {
    return () => {
      dispatch(setSelectedMondai(undefined));
    };
  }, []);

  useEffect(() => {
    if (tableData.length) {
      const formData = [...tableData].reduce<Record<string, TreeDataKaruteMondai>>((acc, item) => {
        if (item.rowId) {
          acc[item.rowId.toString()] = item;
        }
        return acc;
      }, {});
      form.setFieldsValue(formData);
    }
  }, [tableData]);

  const isEditable = useCallback(
    (data: Partial<TreeDataKaruteMondai> & { rowId?: number | string }) => {
      return selectedKeys.includes(data.rowId?.toString() || '');
    },
    [selectedKeys]
  );

  const mapErrorsIntoFields = (errors: FieldError<KaruteMondai>, index?: number | string) => {
    return (
      typeof errors === 'object' &&
      Object.entries(errors)
        .filter(([field]) => !!field)
        .map(([field, messages]) => ({
          name: index ? [index, field] : field,
          errors: [''],
        }))
    );
  };

  const isFieldError = (obj: unknown): obj is FieldError<KaruteMondai> => {
    return (
      typeof obj === 'object' &&
      obj !== null &&
      Object.values(obj).some(
        v => typeof v === 'string' || (Array.isArray(v) && v.every(i => typeof i === 'string'))
      )
    );
  };

  const transformMondaiRelations = (mondai_related: any) => {
    if (Array.isArray(mondai_related)) {
      return mondai_related.map(item => Number(item.mondai_id));
    } else {
      if (mondai_related) {
        return [Number(mondai_related)];
      }
    }

    return [];
  };
  const handleSaveMondai = async (
    newData: Partial<
      TreeDataKaruteMondai & {
        related_id?: number[] | number;
      }
    >,
    isUpdate: boolean
  ) => {
    dispatch(setLoadingMondai(true));
    try {
      const relatedId =
        newData.related_id === undefined
          ? []
          : Array.isArray(newData.related_id)
          ? newData.related_id
          : [newData.related_id];

      const {
        subject: _omitSubject,
        mondai_related: _omitRelated,
        ...rest
      } = {
        ...newData,
        patient_id: patientId,
        karute_id: Number(karuteId),
        mondai_relations: relatedId,
      };

      const response = isUpdate
        ? await mutate(
            newData.trauma_type === 1
              ? { ...rest }
              : { ...rest, disease_base_id: undefined, subject: _omitSubject }
          )
        : await createMutation({
            ...rest,
            subject: _omitSubject,
          });
      dispatch(keikaApi.util.invalidateTags(['mainKeika']));
      dispatch(keikaApi.util.invalidateTags(['conditionCheckCreateKeika']));

      const data = response.data;

      if (data?.status === STATUS_CODES.OK && data.data) {
        setSelectedKeys(keys => keys.filter(k => k !== data.data?.mondai_id.toString()));
        dispatch(setChanged());
        tableRef.current?.scrollToFirstRow();
        dispatch(
          mondaiThunks.fetchMondai({
            ...queryParams,
            patient_id: patientId!,
          })
        );
        dispatch(
          setMondaiModalQuery({
            ...modalQuery,
          })
        );
        dispatch(setSelectedMondai(undefined));
        return data;
      }
      if (data?.status === STATUS_CODES.INVALID_FIELD && data.data) {
        if (isFieldError(data.data)) {
          const errors = mapErrorsIntoFields(data.data, newData.rowId);
          form.setFields(errors as any);
        }
      }
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        return showFlashNotice({ type: 'error', message: err.message });
      }
      return showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL });
    } finally {
      dispatch(setLoadingMondai(false));
    }
  };

  const handleSubmitMondai = async (
    oldData: Partial<TreeDataKaruteMondai>,
    newData: Partial<TreeDataKaruteMondai>
  ): Promise<void | APIResponse<KaruteMondai>> => {
    const isUpdated = !!newData.mondai_id;
    const oldType = Number(oldData?.trauma_type) as keyof typeof MONDAI_TYPE;
    const newType = Number(newData?.trauma_type) as keyof typeof MONDAI_TYPE;

    const isToMedical = oldType !== 1 && newType === 1;

    const isToSocial = oldType === 1 && newType !== 1;
    const message = `「${MONDAI_TYPE[oldType]}」 種別を 「${MONDAI_TYPE[newType]}」種別に変更しました。`;
    const mutationCallback = async () => {
      const result = await handleSaveMondai(newData, true);
      if (result?.status === STATUS_CODES.OK) {
        showFlashNotice({
          type: 'success',
          message: message,
        });
      }
    };

    if (isToMedical && isUpdated) {
      return showWarningDialog({
        customContent: ({ error, setError }) => {
          return (
            <>
              <AppRadioGroup
                options={servicesMemo}
                onChange={e => {
                  setError('');
                  selectedRef.current = e.target.value;
                }}
              />
            </>
          );
        },
        onConfirm: async ({ setError }) => {
          if (!selectedRef.current) {
            const requiredField = ERROR_COMMON_MESSAGE.REQUIRED('サービス');
            setError(requiredField);
            return Promise.reject();
          }
          if (isModal) {
            setOpenDatabaseModal(true);
          } else {
            openUpsertModal(EKaruteUpsertType.DATABASE, EDatabaseUpsertMode.CHANGE_TYPE);
          }
          dispatch(setSelectedMondai({ ...newData, service_id: selectedRef.current }));
          setSelectedKeys?.(keys => keys.filter(k => k !== newData.rowId));
          selectedRef.current = undefined;
          return Promise.resolve();
        },
        confirmLabel: '続行',
        message: WARNING_CHANGE_MONDAI_SP_TO_M,
      });
    }

    if (isToSocial && true) {
      return showWarningDialog({
        onConfirm: mutationCallback,
        confirmLabel: '続行',
        message: WARNING_CHANGE_MONDAI_M_TO_SP,
      });
    }

    handleSaveMondai(newData, isUpdated).then(res => {
      if (res) {
        showFlashNotice({
          type: 'success',
          message: NOTICE_COMMON_MESSAGE.MONDAI_UPDATED,
        });
      }
    });
  };

  const { columns } = useMondaiColumns({
    mondaiData: mondaiData,
    form,
    selectedKeys,
    setSelectedKeys,
    expendedHistoryKeys,
    setExpendedHistoryKeys,
    tableData: tableData,
    isEditable,
    handleSubmitMondai: handleSubmitMondai,
    isModal: isModal,
    allMondais,
  });
  const { columns: historyColumns } = useMondaiColumns({
    isHistory: true,
    isModal: isModal,
  });

  const handleSortChange = (sortData: { order_by: string; order_type: ESort }[]) => {
    if (isModal) {
      return dispatch(
        setMondaiModalQuery({
          order_by: sortData[0]?.order_by,
          order_type: sortData[0]?.order_type,
        })
      );
    }
    dispatch(
      setMondaiQueryParams({
        order_by: sortData[0]?.order_by,
        order_type: sortData[0]?.order_type,
      })
    );
  };

  const handleExpand = async (expanded: boolean, record: TreeDataKaruteMondai) => {
    if (expanded) {
      if (!historyCache[record.mondai_id!])
        try {
          const { data } = await fetchHistory({ mondai_id: record.mondai_id! }).unwrap();
          setHistoryCache(prev => ({
            ...prev,
            [record.mondai_id!]: Array.isArray(data) ? data : [],
          }));
        } catch (err) {
          return showFlashNotice({
            type: 'error',
            message: NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL,
          });
        }
      setExpendedHistoryKeys(keys => [...keys, String(record.rowId!)]);
    } else {
      setExpendedHistoryKeys(keys => keys.filter(key => key !== String(record.rowId!)));
    }
  };

  const makeTraumaLabel = (record: TreeDataKaruteMondai) =>
    record.trauma_type && record.trauma_counter
      ? `${MONDAI_TYPE[record.trauma_type]}${record.trauma_counter}`
      : '';

  const expandedRowRender = (record: TreeDataKaruteMondai) => {
    const isLoading = isFetching && !(record.rowId && historyCache[record.rowId!]);
    const data = historyCache[record.rowId!] || [];
    if (isLoading) {
      return (
        <div style={{ padding: 16 }}>
          <Skeleton active paragraph={{ rows: 4 }} />
        </div>
      );
    }
    return (
      <EditableTable<TreeDataKaruteMondai>
        columns={historyColumns}
        className={styles.subTable}
        pagination={false}
        tableLayout="fixed"
        showHeader={false}
        loading={false}
        scroll={{ x: 'max-content' }}
        rowKey={r => r.medical_record_id!}
        dataSource={data}
        expandable={{
          expandIcon: () => <></>,
          expandedRowRender: () => <></>,
        }}
      />
    );
  };
  const handleCloseUpsertDatabase = () => {
    const basePath = location.pathname.split(`/${EKaruteUpsertType.DATABASE}`)[0];
    navigate(basePath, { replace: true });
  };

  const handleClickAddMondai = () => {
    if (!tableRef.current) return;
    tableRef.current?.scrollToAddRow();
  };

  return (
    <Fragment>
      <div className={styles.mondaiSectionTable} ref={setElemRef}>
        <MondaiFilter
          handleCloseModal={handleCloseModal}
          isModal={isModal}
          handleAddRow={handleClickAddMondai}
        />
        {pagination && (
          <Flex
            justify="end"
            style={{ marginTop: '24px', marginBottom: '16px', marginRight: '12px' }}
          >
            <span
              style={{
                display: 'inline-block',
                color: '#667085',
                fontSize: '12px',
                lineHeight: '18px',
              }}
            >
              {`件数: ${pagination?.total_filter || 0}/${pagination?.total || 0}件`}
            </span>
          </Flex>
        )}

        <Form form={form} component={false}>
          <EditableTable<TreeDataKaruteMondai>
            rowKey={'rowId'}
            ref={tableRef}
            className={styles.mainTable}
            columns={columns}
            dataSource={tableData}
            loading={loadingData || isMondaiLoading || isFetching}
            editingKeys={selectedKeys}
            tableLayout="fixed"
            scroll={{ y: height! - 50 - 60, x: 'max-content' }}
            rowClassName={record =>
              makeTraumaLabel(record) === activeTrauma ? styles.highlightRow : ''
            }
            pagination={false}
            onSortChange={handleSortChange}
            expandable={{
              onExpand: handleExpand,
              expandedRowKeys: expendedHistoryKeys,
              expandIcon: ({ expanded, onExpand, record }) => {
                if (!record.has_history) return <></>;
                return expanded ? (
                  <button
                    style={{
                      transform: 'rotate(180deg)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={e => onExpand(record, e)}
                  >
                    <Icon color="$gray-400" name="arrowFilled" />
                  </button>
                ) : (
                  <button
                    style={{
                      transform: 'rotate(0deg)',
                      transition: 'transform 0.2s ease-in-out',
                    }}
                    onClick={e => onExpand(record, e)}
                  >
                    <Icon color="$gray-400" name="arrowFilled" />
                  </button>
                );
              },
              expandedRowRender: expandedRowRender,
            }}
          />
          {isModal && (
            <Pagination
              align="end"
              className={styles.customPagination}
              pageSize={modalQuery.limit}
              defaultCurrent={1}
              current={pagination?.currentPage}
              total={pagination?.total_filter}
              onChange={val => {
                dispatch(setMondaiModalQuery({ ...modalQuery, page: val }));
              }}
            />
          )}
        </Form>
      </div>
      {upsertType == EKaruteUpsertType.DATABASE &&
        upsertMode === EDatabaseUpsertMode.CHANGE_TYPE && (
          <UpsertDatabaseModalWrapper
            mode={upsertMode}
            handleClose={() => handleCloseUpsertDatabase()}
            mondai={{ ...selectedMondai }}
            onSubmitChangeType={mondai => handleSaveMondai(mondai, true)}
            setSelectedKeys={setSelectedKeys}
          />
        )}
      {openDatabaseModal && (
        <UpsertDatabaseModalWrapper
          mode={EDatabaseUpsertMode.CHANGE_TYPE}
          handleClose={() => setOpenDatabaseModal(false)}
          mondai={{ ...selectedMondai }}
          onSubmitChangeType={mondai => handleSaveMondai(mondai, true)}
          setSelectedKeys={setSelectedKeys}
        />
      )}
    </Fragment>
  );
}
