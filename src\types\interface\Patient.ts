import { EInsurance } from '../enum';

export type Insurance =
  | 'all'
  | 'self_paid'
  | 'automobile_accident'
  | 'work_accident'
  | 'health_insurance'
  | 'none';

export interface PatientFilterItem {
  key: Insurance;
  value: EInsurance;
  label: string;
}
export interface InsuranceCount {
  type: Insurance;
  value: string;
  count: number;
}

export interface InsuranceCountResponse {
  data: InsuranceCount[];
}
