import { useEffect, useState } from 'react';

export const useScreen = () => {
  const [screen, setScreen] = useState<'mobile' | 'desktop'>('desktop');

  const handleResize = () => {
    if (window.innerWidth < 800) {
      setScreen('mobile');
    } else {
      setScreen('desktop');
    }
  };
  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return { screen };
};
