// reference: src/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/UpsertDatabaseForm.module.scss
.upsert_keika_form {
  overflow-x: auto;
  // padding-bottom: 16px;
  
  &::-webkit-scrollbar {
    width: 17px;
    height: 17px;
    background-color: $gray-50;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: $gray-200;
    border-radius: 10px;

    border: 5px solid $gray-50;
  }
  .keika_form_group_wrapper {
    .keika_form_group {
      width: 652px;
      //  calc(
      //   (94vw - 48px - 16px) / 2
      // );
       // make sure there will alway be 2 columns in big screen
      background-color: $gray-50;
      padding: 16px;
      border-radius: 8px;
      // @media (max-width: 600px) {
      //   width: calc(
      //     94vw - 48px - 16px
      //   ); // make sure there will alway be 1 column in vertical mobile screen
      // }
      .keika_form_item {
        :global(.ant-checkbox-label) {
          text-wrap: nowrap;
        }
        :global(.ant-form-item-label > label) {
          display: block;
          &::after {
            display: none;
          }
        }

        .disable_form_item {
          display: none;
        }
        .treatment_header_row {

          .hoken_type {
            flex: 0;
            :global(.ant-form-item) {
              margin-bottom: auto;
            }
          }
          .option {
            flex: 0;
            :global(.ant-form-item) {
              margin-bottom: auto;
            }
            :global(.ant-checkbox-label) {
              text-wrap: nowrap;
              // fs14-medium
              font-size: 14px !important;
              font-weight: 500 !important;
              line-height: 20px !important;
              letter-spacing: 0 !important;
            }
            :global(.ant-checkbox) {
              :global(.ant-checkbox-inner) {
                width: 16px !important;
                height: 16px !important;

                border-width: 1px;
              }
            }
          }
        }
        .date_picker_input {
          background-color: var(--white);
        }
      }
    }
  }
}

.schema_form_item {
  background-color: var(--white);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  position: relative;
  overflow-y: auto;
  textarea {
    overflow-y: hidden !important;
  }

  resize: vertical;
  display: flex;
  flex-direction: column;
}
.schema_form_item_disabled {
  background-color: var(--input-bg-disabled);
  cursor: not-allowed;
}
.schema_form_item_active:hover {
  border-color: var(--input-border-hover);
}

.schema_form_item:focus-within {
  box-shadow: none;
  border-color: var(--input-border-hover);
}

.imagePreview {
  flex: 1;
  figure {
    display: flex;
    padding: 0 8px 8px 8px;
    align-items: center;
    gap: 8px;
    max-width: calc(72px * 6 + 8px * 5); // for max 6 images on one row
    flex-wrap: wrap;
    img {
      width: 69px;
      height: 60px;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}
.is_selected_mondai {
  border: 3px solid $error-500;
}

.first_same_day_active_item {
  background-color: $gray-100 !important;
}
.is_form_disabled {
  background-color: $gray-100 !important;
}
