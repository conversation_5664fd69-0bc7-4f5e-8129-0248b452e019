import { RootState } from '@/config/redux/store';
import { createSelector } from '@reduxjs/toolkit';

export const getToken = createSelector(
  (state: RootState) => state.auth.accessToken,
  accessToken => accessToken
);
export const getUserInfo = createSelector(
  (state: RootState) => state.auth.user,
  user => user
);

export const getAuth = createSelector(
  (state: RootState) => state.auth,
  auth => auth
);

export const getAccessToken = createSelector(
  (state: RootState) => state.auth.accessToken,
  accessToken => accessToken
);

export const getClinicCd = createSelector(
  (state: RootState) => state.auth.currentClinicCd,
  clinicCd => clinicCd
);
