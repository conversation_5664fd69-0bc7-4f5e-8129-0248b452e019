import Button from '@/components/common/core/Button';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppInput } from '@/components/common/core/Input';
import { AppSelect } from '@/components/common/core/Select';
import useInjuryNameStore from '@/hooks/useInjuryNameStore';
import { ERROR_COMMON_MESSAGE } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex, Form, Modal } from 'antd';
import { Option } from 'antd/es/mentions';
import { useEffect } from 'react';
import { BlockOptionSelect, InjuryNameIdentifier, LRType, SideLabelOption } from '../../type';
import styles from './InjurySelectionModal.module.scss';

interface InjuryNameModalProps {
  open: boolean;
  onCancel: () => void;
  options: BlockOptionSelect[];
  onConfirm?: (payload: InjuryNameIdentifier) => void;
  lrType?: LRType | null;
}

const InjurySelectionModal: React.FC<InjuryNameModalProps> = ({
  open,
  onCancel,
  options,
  onConfirm,
  lrType,
}) => {
  const [form] = Form.useForm();

  const { partTypes } = useInjuryNameStore();
  const injuryName = Form.useWatch('selected', form);
  const handleConfirm = async () => {
    try {
      await form.validateFields();

      const [_pt, _pid, _lr] = form.getFieldValue('selected').split('_');
      const partType = Number(_pt);
      const partId = _pid === '0' ? null : Number(_pid);
      const lrType = _lr === '0' ? null : Number(_lr);

      const opt = options.find(
        o =>
          o.part_type === partType &&
          (o.part_id ?? null) === partId &&
          (o.lr_type ?? null) === lrType
      );

      const name = partId
        ? opt?.disp_name ?? ''
        : partTypes.find(p => p.part_type === partType)?.name ?? '';

      onConfirm?.({
        part_type: partType,
        part_id: partId,
        lr_type: lrType,
        name,
        name_other: null,
      });
      onCancel();
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  useEffect(() => {
    if (injuryName) {
      const parts = injuryName.split('_');
      const lrType = Number(parts[2]);
      form.setFieldsValue({
        lr_type: SideLabelOption.find(option => option.value === lrType)?.label || '',
      });
    }
  }, [injuryName, form]);

  useEffect(() => {
    if (lrType) {
      form.setFieldsValue({
        lr_type: SideLabelOption.find(option => option.value === lrType)?.label || '',
      });
    }
  }, [lrType, form]);

  return (
    <Modal
      title="負傷部位選択"
      open={open}
      width="464px"
      footer={null}
      onCancel={onCancel}
      centered
      destroyOnClose
      maskClosable={false}
      className={styles.modal}
    >
      <Form form={form} layout="vertical" onFinish={handleConfirm}>
        <div style={{ padding: '16px 0 8px 0' }}>
          <Flex gap="small" align="center" justify="space-between">
            <AppFormItem name="lr_type">
              <AppInput placeholder="" size="small" style={{ width: '100px' }} disabled />
            </AppFormItem>

            <AppFormItem
              name="selected"
              rules={[
                {
                  required: true,
                  message: ERROR_COMMON_MESSAGE.REQUIRED('負傷名'),
                },
              ]}
            >
              <AppSelect placeholder="---" size="small" style={{ width: '308px' }}>
                {options.map(opt => {
                  const key = `${opt.part_type}_${opt.part_id || '0'}_${opt.lr_type || '0'}`;
                  return (
                    <Option key={key} value={key}>
                      {opt.disp_name}
                    </Option>
                  );
                })}
              </AppSelect>
            </AppFormItem>
          </Flex>

          <Flex justify="end" gap="small" className="mt-6">
            <Button
              customType={ButtonType.SECONDARY_COLOR}
              customSize="lg"
              style={{ width: '120px' }}
              onClick={onCancel}
            >
              キャンセル
            </Button>
            <Button
              customType={ButtonType.PRIMARY}
              customSize="lg"
              style={{ width: '120px' }}
              onClick={form.submit}
            >
              適用
            </Button>
          </Flex>
        </div>
      </Form>
    </Modal>
  );
};

export default InjurySelectionModal;
