import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchingUserInfo, prefetchSharedData } from './thunk';
import { SharedData } from './type';

export const initialSharedState: SharedData & {
  isLoading?: boolean;
} = {
  services: [],
  doctors: [],
  courses: [],
  userInfo: null,
  isLoading: false,
  hasContractBooking: false,
};

// slice
export const sharedSlice = createSlice({
  name: 'sharedStore',
  initialState: initialSharedState,
  reducers: {
    setUserInfo: (state, action: PayloadAction<SharedData['userInfo']>) => {
      state.userInfo = action.payload;
    },
    updateUserMeta: (state, action: PayloadAction<Partial<SharedData['userInfo']>>) => {
      // console.log('action.payload.meta', action?.payload?.meta);
      state.userInfo = {
        ...state.userInfo,
        meta: {
          ...action.payload?.meta,
        },
      } as SharedData['userInfo'];
    },
  },
  extraReducers: builder => {
    builder.addCase(prefetchSharedData.fulfilled, (state, { payload }) => {
      state.services = payload.services || [];
      state.doctors = payload.doctors || [];
      state.courses = payload.courses || [];
      state.hasContractBooking = payload.hasContractBooking || false;
      state.isLoading = false;
    });
    builder.addCase(prefetchSharedData.pending, state => {
      state.isLoading = true;
    });

    builder.addCase(fetchingUserInfo.fulfilled, (state, { payload }) => {
      state.userInfo = payload || null;
      state.isLoading = false;
    });
    builder.addCase(fetchingUserInfo.pending, state => {
      state.isLoading = true;
    });
    builder.addCase(fetchingUserInfo.rejected, state => {
      state.isLoading = false;
    });
  },
});

// actions
export const { setUserInfo, updateUserMeta } = sharedSlice.actions;
// reducer
export const sharedReducer = sharedSlice.reducer;
