import { User } from '@/types/interface/User';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export type userStates = {
  user: User[];
};

const initialState: userStates = {
  user: [],
};

// slice
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, actions: PayloadAction<userStates>) => {
      state.user = actions.payload.user;
    },
  },
});

// actions
export const { setUser } = userSlice.actions;

// reducer
export const userReducer = userSlice.reducer;
