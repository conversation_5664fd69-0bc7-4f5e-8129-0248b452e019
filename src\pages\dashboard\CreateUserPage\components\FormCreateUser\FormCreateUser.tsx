import Button from '@/components/common/core/Button';
import { AppDatePicker } from '@/components/common/core/Datepicker';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import CustomImage from '@/components/common/core/Image';
import { AppInput, AppTextArea } from '@/components/common/core/Input';
import { AppInputNumber, AppInputPhoneNumber } from '@/components/common/core/Input/InputNumber';
import { AppRadio } from '@/components/common/core/Radio';
import { AppSelect } from '@/components/common/core/Select';
import { useKanaAutoFill } from '@/hooks/useKanaAutoFill';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import {
  createUser,
  fetchAllCompany,
  fetchClinics,
  fetchUserById,
  updateUser,
  userDegrees,
} from '@/services/medix-sync-api/userApi';
import {
  ALLOWED_IMAGE_EXTENSIONS,
  ALLOWED_IMAGE_TYPES,
  ERROR_COMMON_MESSAGE,
  LOCAL_STORAGE_KEY,
  MAX_IMAGE_SIZE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  REGEX_DIGIT,
  REGEX_EMAIL_CHAR,
  REGEX_HALF_WIDTH_ALNUM,
  REGEX_HAS_DIGIT,
  REGEX_KATAKANA,
  REGEX_PHONE,
  REGEX_PW,
  STATUS_CODES,
} from '@/types/constants';
import { ButtonType, ROLE } from '@/types/enum';
import { Clinic, Company, User, UserCreatePayload } from '@/types/interface';
import { deepCompareEmpty } from '@/utils/deepCompare';
import { showFlashNotice } from '@/utils/flashNotice';
import {
  fileToSrc,
  getManagerId,
  getRoleOptionsCreate,
  getUserRole,
  toBoolean,
  toFlag,
} from '@/utils/helper';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Col, Flex, Form, Radio, Row, Space, Spin, Tooltip, Upload } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './FormCreateUser.module.scss';

interface FormCreateUserProps {
  mode?: 'create' | 'edit';
  userId?: string;
}

const initialValues: UserCreatePayload = {
  username: '',
  email: '',
  password: '',
  password_confirmation: '',
  kanji_name: '',
  kana_name: '',
  manageable_type: null,
  provider: '',
  gender: null,
  avatar: null,
  birthday: '',
  postal_code: '',
  address1: '',
  address2: '',
  experience: null,
  introduce: null,
  degrees: undefined,
  is_doctor: null,
  can_access_karute: null,
  can_access_booking: null,
  phone: '',
  remove_avatar: 0,
  manageable_id: null,
  clinic_id: null,
  company_id: null,
};

const FormCreateUser = forwardRef<any, FormCreateUserProps>(({ mode = 'create', userId }, ref) => {
  const navigate = useNavigate();
  const [initial, setInitial] = useState<UserCreatePayload>(initialValues);
  const [removeAvatar, setRemoveAvatar] = useState(false);
  const [loadingInit, setLoadingInit] = useState(mode === 'edit');
  const [notFound, setNotFound] = useState(false);
  const currentUserRole = getUserRole();
  const currentUserManageableId = getManagerId();
  const [companies, setCompanies] = useState<Company[] | null>([]);
  const [clinics, setClinics] = useState<Clinic[] | null>([]);
  const [allClinics, setAllClinics] = useState<Clinic[] | null>([]);

  const [form] = Form.useForm<UserCreatePayload>();
  const avatarVal = Form.useWatch('avatar', form);
  const isDoctor = Form.useWatch('is_doctor', form);
  const manageableType = Form.useWatch('manageable_type', form);
  useKanaAutoFill(form, 'kanji_name', 'kana_name');
  const clinicId = Form.useWatch('clinic_id', form);
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const prevManageableRef = useRef<string | null>(null);

  useEffect(() => {
    prevManageableRef.current = form.getFieldValue('manageable_type');
  }, []);

  const activeKaruteContract = allClinics?.find(clinic => clinic.clinic_id === clinicId)
    ?.sub_contract_active?.karute;

  const activeBookingContract = allClinics?.find(clinic => clinic.clinic_id === clinicId)
    ?.sub_contract_active?.booking;

  const isMedixAdmin = manageableType === ROLE.MEDIX_ADMIN;
  const isCompanyAdmin = manageableType === ROLE.COMPANY_ADMIN;
  const isStoreAdmin = manageableType === ROLE.STORE_ADMIN;
  const isStaff = manageableType === ROLE.STAFF;

  const { handleValuesChange, submitHandler } = useUnsavedWarning(initial);

  const avatarSrc = fileToSrc(avatarVal);
  const handleChoose = (file: File) => {
    form.setFieldValue('avatar', file);
    const url = URL.createObjectURL(file);
    const img = new Image();
    img.src = url;
    handleValuesChange({ avatar: file }, form.getFieldsValue());
  };
  const handleRemove = () => {
    if (!avatarVal) return;
    const val = form.getFieldValue('avatar') as File | string | null;
    if (val instanceof File) {
      const tmp = URL.createObjectURL(val);
      URL.revokeObjectURL(tmp);
    } else if (typeof val === 'string' && val.startsWith('blob:')) {
      URL.revokeObjectURL(val);
    }
    form.setFieldValue('avatar', null);
    handleValuesChange({ avatar: null }, form.getFieldsValue());
    setRemoveAvatar(true);
  };

  const validateConfirmPw = (_: any, confirm: string) => {
    const pw = form.getFieldValue('password');
    if (!pw && !confirm) return Promise.resolve();
    if (!pw || !confirm) {
      return Promise.reject(new Error(ERROR_COMMON_MESSAGE.PASSWORD_NOT_MATCH));
    }
    if (pw !== confirm) {
      return Promise.reject(new Error(ERROR_COMMON_MESSAGE.PASSWORD_NOT_MATCH));
    }
    return Promise.resolve();
  };

  const validateAddress = (field: 'address1' | 'address2') => {
    return ({ getFieldValue }: any) => ({
      validator(_: any, v?: string) {
        const self = v ?? '';
        const other = getFieldValue(field === 'address1' ? 'address2' : 'address1') ?? '';

        const bothEmpty = self === '' || other === '';

        if (bothEmpty) {
          if (field === 'address1') {
            return Promise.reject(new Error(ERROR_COMMON_MESSAGE.REQUIRED('住所')));
          }
          return Promise.reject('');
        }

        const selfHasDigit = REGEX_HAS_DIGIT.test(self);
        const otherHasDigit = REGEX_HAS_DIGIT.test(other);
        if (self && !selfHasDigit && !otherHasDigit) {
          return Promise.reject(new Error(ERROR_COMMON_MESSAGE.REQUIRED_NUMBER_IN_ADDRESS));
        }
        return Promise.resolve();
      },
    });
  };

  const doctorRequiredRule = (label: string, whitespace = false) =>
    isDoctor ? [{ required: true, whitespace, message: ERROR_COMMON_MESSAGE.REQUIRED(label) }] : [];

  const handleSubmit = async (value: UserCreatePayload) => {
    const payload = {
      ...value,
      birthday: value.birthday ? dayjs(value.birthday).format('YYYY-MM-DD') : '',
      can_access_karute: toFlag(value.can_access_karute),
      can_access_booking: toFlag(value.can_access_booking),
      is_doctor: toFlag(value.is_doctor),
      provider: localStorage.getItem(LOCAL_STORAGE_KEY.FROM) ?? '',
      avatar: typeof value.avatar === 'string' ? null : value.avatar,
      password: value.password?.toString().trim() === '' ? null : value.password,
      password_confirmation:
        value.password_confirmation?.toString().trim() === '' ? null : value.password_confirmation,
      remove_avatar: removeAvatar ? 1 : 0,
      manageable_id: getManageableId(value),
    };

    const res =
      mode === 'create' ? await createUser(payload) : await updateUser(Number(userId), payload);

    if (res.status === STATUS_CODES.INVALID_FIELD) {
      const fields = Object.entries<string[]>(res.data).map(([name, msgs]) => ({
        name,
        errors: msgs,
      }));
      form.setFields(fields as any);
      return;
    }

    showFlashNotice({
      type: res.status === STATUS_CODES.OK ? 'success' : 'error',
      message: res.message,
    });
    if (res.status === STATUS_CODES.OK) {
      navigate('/dashboard');
    }
  };

  const getManageableId = (form: UserCreatePayload) => {
    switch (form.manageable_type) {
      case ROLE.MEDIX_ADMIN:
        return null;
      case ROLE.COMPANY_ADMIN:
        return form.company_id;
      case ROLE.STORE_ADMIN:
      case ROLE.STAFF:
        return form.clinic_id;
      default:
        return form.clinic_id;
    }
  };
  const mapUserToForm = (user: User | null | undefined): UserCreatePayload => {
    if (user)
      return {
        username: user.username,
        email: user.email,
        password: '',
        password_confirmation: '',
        kanji_name: user.kanji_name,
        kana_name: user.kana_name,
        manageable_type: user.manageable_type,
        provider: '',
        gender: user.gender,
        avatar: user.avatar,
        birthday: user.birthday ? dayjs(user.birthday) : '',
        postal_code: user.postal_code || '',
        address1: user.address1,
        address2: user.address2,
        experience: user.practitioner_info?.experience || '',
        introduce: user.practitioner_info?.introduce || '',
        degrees: user.practitioner_info?.degrees || undefined,
        is_doctor: user.is_doctor,
        can_access_karute: user.can_access_karute,
        can_access_booking: user.can_access_booking,
        phone: user.phone || '',
        remove_avatar: 0,
        manageable_id: user.manageable_id,
        clinic_id: user.clinic.clinic_id,
        company_id: user.company.company_id,
      };
    return initialValues;
  };

  useEffect(() => {
    if (!isDoctor) {
      form.setFieldsValue({
        degrees: undefined,
        experience: null,
        introduce: null,
      });

      form.setFields([
        { name: 'degrees', errors: [] },
        { name: 'experience', errors: [] },
        { name: 'introduce', errors: [] },
      ]);
    }
  }, [isDoctor, form]);

  useEffect(() => {
    (async () => {
      const companyRes = await fetchAllCompany();
      const clinicRes = await fetchClinics();
      setCompanies(companyRes?.data || null);
      setAllClinics(clinicRes?.data || null);
      setClinics(clinicRes?.data || null);
    })();
  }, []);

  useEffect(() => {
    if (mode === 'edit' && userId) {
      (async () => {
        setLoadingInit(true);
        const res = await fetchUserById(Number(userId));
        if (!res?.data || res?.status !== 200) {
          setNotFound(true);
          setLoadingInit(false);
          return;
        }
        const userPayload = mapUserToForm(res?.data);
        setInitial(userPayload);
        form.setFieldsValue(userPayload);
        setLoadingInit(false);
      })();
    }
  }, [mode, userId]);

  useEffect(() => {}, []);
  if (!loadingInit && notFound && mode === 'edit') {
    return (
      <div
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CustomImage
          name="notFound"
          style={{
            width: '100%',
            height: 'var(--page-content-height)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      </div>
    );
  }

  useEffect(() => {
    if (!clinics?.length) return;

    if (currentUserRole === ROLE.COMPANY_ADMIN) {
      form.setFieldValue('company_id', currentUserManageableId);
    }

    if (currentUserRole === ROLE.STORE_ADMIN) {
      form.setFieldValue('clinic_id', currentUserManageableId);

      const compId = clinics.find(c => c.clinic_id === currentUserManageableId)?.company_id;
      if (compId) form.setFieldValue('company_id', compId);
    }
    handleValuesChange({}, form.getFieldsValue());
  }, [currentUserRole, currentUserManageableId, clinics]);

  return loadingInit ? (
    <Spin
      style={{
        width: '100%',
        height: 'var(--page-content-height)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    />
  ) : (
    <Form
      ref={ref}
      form={form}
      initialValues={initial}
      layout="vertical"
      onFinish={values => {
        submitHandler();
        handleSubmit(values);
      }}
      onValuesChange={handleValuesChange}
    >
      <div className={styles.flexContainer}>
        <Row gutter={32}>
          <Form.Item name="avatar" hidden noStyle />

          <Col span={6}>
            <Space direction="vertical" align="center" size={12} style={{ width: '100%' }}>
              <Avatar
                size={200}
                src={avatarSrc}
                icon={!avatarVal && <UserOutlined />}
                shape="circle"
              />

              <div className={styles.avatarMeta}>
                <div className={clsx('fs18-bold', styles.avatarMetaTitle, 'text-primary2')}>
                  プロフィール画像
                </div>
                <div className="fs14-regular text-tertiary">推奨画像サイズ: 512*512</div>
                <div className="fs14-regular text-tertiary">
                  推奨画像容量: {MAX_IMAGE_SIZE}MB以下
                </div>
              </div>

              <Flex align="center" gap={8}>
                <Upload
                  accept={ALLOWED_IMAGE_EXTENSIONS.join(',')}
                  showUploadList={false}
                  beforeUpload={file => {
                    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
                      showFlashNotice({
                        type: 'error',
                        message: NOTICE_COMMON_MESSAGE.IMAGE_SELECT,
                      });
                      return;
                    }
                    if (file.size / 1024 / 1024 >= MAX_IMAGE_SIZE) {
                      showFlashNotice({
                        type: 'error',
                        message: NOTICE_COMMON_MESSAGE.IMAGE_SIZE,
                      });
                      return;
                    }
                    handleChoose(file);
                    return false;
                  }}
                >
                  <Button customType={ButtonType.SECONDARY_BRAND} style={{ width: '158px' }}>
                    <Icon name="upload" /> アップロード
                  </Button>
                </Upload>

                <div onClick={handleRemove} style={{ cursor: 'pointer' }}>
                  <Icon name="trash" width={20} height={20} color="var(--error-600)" />
                </div>
              </Flex>
            </Space>
          </Col>

          <Col span={18}>
            <Row gutter={24} className="mb-3">
              <Col span={12}>
                <AppFormItem
                  name="manageable_type"
                  label="権限タイプ"
                  required
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('権限タイプ'),
                    },
                  ]}
                >
                  <Radio.Group
                    value={form.getFieldValue('manageable_type')}
                    options={getRoleOptionsCreate(currentUserRole)}
                    disabled={mode === 'edit'}
                    style={{ display: 'flex', alignItems: 'center', height: 40 }}
                    onChange={e => {
                      const next = e.target.value as string;
                      const prev = prevManageableRef.current!;

                      const dirty = (
                        Object.keys(initialValues) as (keyof UserCreatePayload)[]
                      ).filter(key => {
                        if (key === 'manageable_type') return false;
                        if (!form.isFieldTouched(key)) return false;
                        return !deepCompareEmpty(initialValues[key], form.getFieldValue(key));
                      });

                      if (prev && dirty.length > 0) {
                        showWarningDialog({
                          message: '入力された情報が保存されていません。この操作を続行しますか？',
                          buttons: [
                            {
                              label: 'キャンセル',
                              type: 'cancel',
                              onClick: () => {
                                form.setFieldValue('manageable_type', prev);
                                hideWarningDialog();
                              },
                            },
                            {
                              label: '続行する',
                              type: 'confirm',
                              onClick: () => {
                                const resetObj: Record<string, any> = {};
                                dirty.forEach(key => {
                                  resetObj[key] = initialValues[key];
                                });
                                form.setFieldsValue({
                                  manageable_type: next,
                                  ...resetObj,
                                });
                                prevManageableRef.current = next;
                                hideWarningDialog();
                              },
                            },
                          ],
                        });
                      } else {
                        form.setFieldValue('manageable_type', next);
                        prevManageableRef.current = next;
                      }
                    }}
                  />
                </AppFormItem>
              </Col>
            </Row>
            <Row gutter={24} className="mb-3">
              {!isMedixAdmin && (
                <Col span={12}>
                  <AppFormItem
                    name="company_id"
                    label="企業"
                    required
                    rules={[
                      {
                        required: true,
                        message: ERROR_COMMON_MESSAGE.REQUIRED('企業'),
                      },
                    ]}
                  >
                    <AppSelect
                      showSearch
                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                      allowClear
                      style={{ display: 'flex', alignItems: 'center', height: 40 }}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toString().startsWith(input)
                      }
                      notFoundContent={NOTICE_COMMON_MESSAGE.NOT_FOUND_DATA}
                      options={companies?.map(c => ({ value: c.company_id, label: c.name })) || []}
                      onChange={value => {
                        form.setFieldValue('clinic_id', null);
                        if (value) {
                          setClinics(companies?.find(c => c.company_id === value)?.clinics || []);
                        } else {
                          setClinics(allClinics);
                        }
                      }}
                      disabled={currentUserRole !== ROLE.MEDIX_ADMIN}
                    />
                  </AppFormItem>
                </Col>
              )}
            </Row>
            <Row gutter={[24, 12]} className="mb-3">
              {(isStoreAdmin || isStaff) && (
                <Col span={12}>
                  <AppFormItem
                    name="clinic_id"
                    label="治療院"
                    required
                    rules={[
                      {
                        required: true,
                        message: ERROR_COMMON_MESSAGE.REQUIRED('治療院'),
                      },
                    ]}
                  >
                    <AppSelect
                      showSearch
                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                      style={{ display: 'flex', alignItems: 'center', height: 40 }}
                      allowClear
                      filterOption={(input, option) =>
                        (option?.label ?? '').toString().startsWith(input)
                      }
                      notFoundContent={NOTICE_COMMON_MESSAGE.NOT_FOUND_DATA}
                      options={
                        clinics?.map(clinic => ({
                          value: clinic.clinic_id,
                          label: clinic.clinic_name,
                        })) || []
                      }
                      onChange={value => {
                        if (value) {
                          form.setFieldValue(
                            'company_id',
                            clinics?.find(clinic => clinic.clinic_id === value)?.company_id
                          );
                        }
                      }}
                      disabled={
                        currentUserRole === ROLE.STORE_ADMIN || currentUserRole === ROLE.STAFF
                      }
                    />
                  </AppFormItem>
                </Col>
              )}

              <Col span={12}>
                <AppFormItem
                  name="username"
                  label="ユーザーID"
                  required
                  validateFirst
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('ユーザーID'),
                    },
                    { max: 128, message: ERROR_COMMON_MESSAGE.MAX_LENGTH_NUMBER(128) },
                    {
                      pattern: REGEX_HALF_WIDTH_ALNUM,
                      message: ERROR_COMMON_MESSAGE.HALF_WIDTH_ALPHANUMERIC_ONLY,
                    },
                  ]}
                >
                  <AppInput
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    maxLength={128}
                    disabled={mode === 'edit'}
                  />
                </AppFormItem>
              </Col>
            </Row>
            <Row gutter={[24, 12]} className="mb-3">
              <Col span={12}>
                <AppFormItem
                  name="password"
                  label="パスワード"
                  required={mode === 'create'}
                  subLabel={
                    mode === 'create' ? (
                      <span className={styles['text-error-600']}>
                        半角英数字を組み合わせて6文字以上で設定してください
                      </span>
                    ) : (
                      <span className={styles['text-gray-500']}>
                        (※未入力ならパスワードリセットなし)
                      </span>
                    )
                  }
                  validateFirst
                  rules={[
                    ...(mode === 'create'
                      ? [
                          {
                            required: true,
                            whitespace: true,
                            message: ERROR_COMMON_MESSAGE.REQUIRED('パスワード'),
                          },
                        ]
                      : []),
                    {
                      validator: (_, v: string) =>
                        !v || REGEX_PW.test(v)
                          ? Promise.resolve()
                          : Promise.reject(new Error(ERROR_COMMON_MESSAGE.PASSWORD_INVALID)),
                    },
                  ]}
                >
                  <AppInput
                    type="password"
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    maxLength={255}
                    autoComplete="new-password"
                  />
                </AppFormItem>
              </Col>

              <Col span={12}>
                <AppFormItem
                  name="password_confirmation"
                  label="確認用パスワード"
                  dependencies={['password']}
                  required={mode === 'create'}
                  validateFirst
                  rules={[
                    ...(mode === 'create'
                      ? [
                          {
                            required: true,
                            whitespace: true,
                            message: ERROR_COMMON_MESSAGE.REQUIRED('確認用パスワード'),
                          },
                        ]
                      : []),
                    { validator: validateConfirmPw },
                  ]}
                >
                  <AppInput
                    type="password"
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    maxLength={255}
                  />
                </AppFormItem>
              </Col>
            </Row>
            <Row gutter={[24, 12]} className="mb-3">
              <Col span={12}>
                <AppFormItem
                  name="kanji_name"
                  label="氏名 (漢字)"
                  required
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (漢字)'),
                    },
                    { max: 64, message: ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(0, 64) },
                  ]}
                >
                  <AppInput placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} maxLength={64} />
                </AppFormItem>
              </Col>
              <Col span={12}>
                <AppFormItem
                  name="kana_name"
                  label="氏名 (カナ)"
                  required
                  validateFirst
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (カナ)'),
                    },
                    { max: 64, message: ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(0, 64) },
                    {
                      pattern: REGEX_KATAKANA,
                      message: ERROR_COMMON_MESSAGE.ENTER_ONLY_KANA,
                    },
                  ]}
                >
                  <AppInput placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} maxLength={64} />
                </AppFormItem>
              </Col>
              <Col span={12}>
                <AppFormItem
                  name="birthday"
                  label="生年月日"
                  required
                  validateFirst
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) {
                          return Promise.reject(
                            new Error(ERROR_COMMON_MESSAGE.REQUIRED('生年月日'))
                          );
                        }
                        const selected = dayjs(value);
                        if (selected.isAfter(dayjs(), 'day')) {
                          return Promise.reject(
                            new Error('生年月日は本日以前の日付を入力してください。')
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <AppDatePicker
                    format="YYYY/MM/DD"
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                    disabledDate={current => current && current.isAfter(dayjs(), 'day')}
                    allowClear
                    style={{ height: '44px' }}
                    inputReadOnly={false}
                  />
                </AppFormItem>
              </Col>
              <Col span={12}>
                <AppFormItem
                  name="email"
                  label="メールアドレス"
                  required
                  validateFirst
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('メールアドレス'),
                    },
                    {
                      validator: (_, value) => {
                        if (!value) return Promise.resolve();
                        if (value.length < 6 || value.length > 255) {
                          return Promise.reject(
                            new Error(ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(6, 255))
                          );
                        }
                        if (!REGEX_EMAIL_CHAR.test(value) || !value.includes('@')) {
                          return Promise.reject(
                            new Error(ERROR_COMMON_MESSAGE.REQUIRED_ONLY_CHARACTER)
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <AppInput placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} maxLength={255} />
                </AppFormItem>
              </Col>
              <Col span={12}>
                <AppFormItem
                  name="phone"
                  label="電話番号"
                  required
                  validateFirst
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('電話番号'),
                    },
                    {
                      validator: (_, v: string) => {
                        if (!v) return Promise.resolve();
                        if (v.length > 20) {
                          return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_HYPHEN));
                        }
                        if (!REGEX_PHONE.test(v)) {
                          return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_HYPHEN));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <AppInputPhoneNumber
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    maxLength={20}
                  />
                </AppFormItem>
              </Col>
              <Col span={12}>
                <AppFormItem
                  name="gender"
                  label="性別"
                  required
                  rules={[
                    {
                      required: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('性別'),
                    },
                  ]}
                >
                  <Radio.Group style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                    <AppRadio value={1}>男性</AppRadio>
                    <AppRadio value={2}>女性</AppRadio>
                  </Radio.Group>
                </AppFormItem>
              </Col>
            </Row>

            <Row gutter={24} className="mb-3">
              <Col span={12}>
                <AppFormItem
                  name="postal_code"
                  label="郵便番号"
                  required
                  validateFirst
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('郵便番号'),
                    },
                    {
                      validator: (_, v: string) => {
                        if (!v) return Promise.resolve();
                        if (!REGEX_DIGIT.test(v) || v.length !== 7) {
                          return Promise.reject(
                            new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_NUMBER(7))
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <AppInputNumber
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    maxLength={7}
                    thousandSeparator={false}
                  />
                </AppFormItem>
              </Col>
              <Col span={12}>
                <AppFormItem label="住所" required>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="address1"
                        dependencies={['address2']}
                        validateFirst
                        rules={[
                          {
                            max: 100,
                            whitespace: true,
                            message: ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(0, 100),
                          },
                          validateAddress('address1'),
                        ]}
                        noStyle
                      >
                        <AppInput
                          placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                          maxLength={100}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        name="address2"
                        dependencies={['address1']}
                        validateFirst
                        rules={[
                          {
                            max: 100,
                            whitespace: true,
                            message: ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(0, 100),
                          },
                          validateAddress('address2'),
                        ]}
                        noStyle
                      >
                        <AppInput
                          placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                          maxLength={100}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </AppFormItem>
              </Col>
            </Row>

            <Row gutter={24} className="mb-3">
              {activeBookingContract && (
                <Col span={12}>
                  <AppFormItem name="can_access_booking" label="予約システムのステータス" required>
                    <Radio.Group style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                      <AppRadio value={true}>有効</AppRadio>
                      <AppRadio value={false}>無効</AppRadio>
                    </Radio.Group>
                  </AppFormItem>
                </Col>
              )}
              {!isMedixAdmin && activeKaruteContract && (
                <Col span={12}>
                  <AppFormItem name="can_access_karute" label="電子カルテのステータス" required>
                    <Radio.Group style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                      <AppRadio value={true}>有効</AppRadio>
                      <AppRadio value={false}>無効</AppRadio>
                    </Radio.Group>
                  </AppFormItem>
                </Col>
              )}
            </Row>
            {isStaff && (
              <>
                <Row gutter={24} className="mb-3">
                  <Col span={12}>
                    <AppFormItem
                      name="is_doctor"
                      label="施術師マスターのステータス"
                      required
                      rules={[
                        {
                          required: true,
                          message: ERROR_COMMON_MESSAGE.REQUIRED('施術師マスターのステータス'),
                        },
                      ]}
                    >
                      <Radio.Group style={{ display: 'flex', alignItems: 'center', height: 40 }}>
                        <AppRadio value={true}>有効</AppRadio>
                        <AppRadio value={false}>無効</AppRadio>
                      </Radio.Group>
                    </AppFormItem>
                  </Col>
                  <Col span={12}>
                    <AppFormItem
                      name="degrees"
                      label="資格"
                      required={toBoolean(isDoctor)}
                      rules={[
                        {
                          required: toBoolean(isDoctor),
                          type: 'array' as const,
                          min: 1,
                          message: ERROR_COMMON_MESSAGE.REQUIRED('資格'),
                        },
                      ]}
                    >
                      <AppSelect
                        disabled={!isDoctor}
                        placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                        allowClear
                        mode="multiple"
                        maxTagCount={'responsive'}
                        maxTagPlaceholder={omittedValues => (
                          <Tooltip
                            styles={{ root: { pointerEvents: 'none' } }}
                            title={omittedValues.map(({ label }) => label).join(', ')}
                          >
                            <span>+{omittedValues.length}</span>
                          </Tooltip>
                        )}
                        options={userDegrees}
                      />
                    </AppFormItem>
                  </Col>
                </Row>
                <Row gutter={24} className="mb-3">
                  <Col span={12}>
                    <AppFormItem
                      name="experience"
                      label="業務経験"
                      required={toBoolean(isDoctor)}
                      rules={[
                        ...doctorRequiredRule('業務経験', true),
                        { max: 1000, message: ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(0, 1000) },
                      ]}
                    >
                      <AppTextArea
                        rows={4}
                        maxLength={1000}
                        disabled={!isDoctor}
                        placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                      />
                    </AppFormItem>
                  </Col>

                  <Col span={12}>
                    <AppFormItem
                      name="introduce"
                      label="自己紹介"
                      required={toBoolean(isDoctor)}
                      rules={[
                        ...doctorRequiredRule('自己紹介', true),
                        { max: 1000, message: ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(0, 1000) },
                      ]}
                    >
                      <AppTextArea
                        rows={4}
                        maxLength={1000}
                        disabled={!isDoctor}
                        placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                      />
                    </AppFormItem>
                  </Col>
                </Row>
              </>
            )}
          </Col>
        </Row>
      </div>
    </Form>
  );
});

export default FormCreateUser;
