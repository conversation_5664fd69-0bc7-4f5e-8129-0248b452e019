import React, { createContext, useContext, useState, useC<PERSON>back, ReactNode, useMemo } from 'react';
import { IsInRange, Order, PatientItem } from '@/store/patient/type';
import dayjs, { Dayjs } from 'dayjs';
import { DATE_FORMATS, DEFAULT_INSURANCE_FILTER, PAGINATION_THRESHOLD } from '@/types/constants';
import { useGetPatientListQuery, useGetTotalPatientByInsuredTypeQuery } from '@/store/patient/api';
import { InsuranceCount, ListResponse } from '@/types/interface';
import { useSelector } from 'react-redux';
import { RootState } from '@/config/redux/store';
import { EInsurance, ESort } from '@/types/enum';
import { clearSpace } from '@/utils';
import { skipToken } from '@reduxjs/toolkit/query';

export interface PatientSearchParams {
  search: string;
  intake_date_1?: [Dayjs | null, Dayjs | null];
  intake_date_2?: [Dayjs | null, Dayjs | null];
  intake_case_1: boolean;
  intake_case_2: boolean;
  is_and: boolean;
  hoken_type?: string;
}

export interface PatientQueryParams {
  search: string;
  limit: number;
  page: number;
  order_by?: PatientSortBy;
  order_type?: Order;
  intake_case_1?: IsInRange;
  intake_date_from_1?: string;
  intake_date_to_1?: string;
  intake_case_2?: IsInRange;
  intake_date_from_2?: string;
  intake_date_to_2?: string;
  hoken_type?: string;
}

export type PatientSortBy =
  | 'clinic_id'
  | 'name'
  | 'kana'
  | 'gender'
  | 'birthday'
  | 'address'
  | 'cellphone'
  | 'latest_intake_date'
  | 'hoken_type';
// Define the context type
interface ListPatientContextType {
  queryParams: PatientQueryParams;

  handleSearch: (values: PatientSearchParams) => void;

  handlePageChange: (page: number) => void;

  handleSortChange: (sortData: { order_by: string; order_type: ESort }[]) => void;

  handleClear: () => void;

  patients?: ListResponse<PatientItem> | null;
  isFetching: boolean;
  insuranceCount: InsuranceCount[];
  formValues: PatientSearchParams;
  setFormValues: React.Dispatch<React.SetStateAction<PatientSearchParams>>;
  activeFilterValues: Record<EInsurance, boolean>;
  setActiveFilterValues: React.Dispatch<React.SetStateAction<Record<EInsurance, boolean>>>;
  handleChangeInsurance: (hokenTypes: string) => void;
}

const ListPatientContext = createContext<ListPatientContextType | undefined>(undefined);

const defaultFormValues: PatientSearchParams = {
  search: '',
  intake_date_1: [null, null],
  intake_date_2: [null, null],
  intake_case_1: true,
  intake_case_2: true,
  is_and: false,
  hoken_type: '',
};

interface ListPatientProviderProps {
  children: ReactNode;
}

export const ListPatientProvider: React.FC<ListPatientProviderProps> = ({ children }) => {
  const { user } = useSelector((state: RootState) => state.auth);

  const [formValues, setFormValues] = useState<PatientSearchParams>(defaultFormValues);

  const [page, setPage] = useState<number>(1);
  const [sortBy, setSortBy] = useState<PatientSortBy | null>(null);
  const [orderBy, setOrderBy] = useState<Order | null>(null);
  const [activeFilterValues, setActiveFilterValues] =
    useState<Record<EInsurance, boolean>>(DEFAULT_INSURANCE_FILTER);

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);

  const {
    data: insuranceCount,
    refetch: refetchInsuranceCount,
    isFetching: isFetchingInsuranceCount,
  } = useGetTotalPatientByInsuredTypeQuery(
    shouldFetch ? (user?.clinic?.clinic_id as number) : skipToken
  );

  const queryParams = useMemo<PatientQueryParams>(() => {
    const params: PatientQueryParams = {
      search: clearSpace(formValues.search) || '',
      limit: PAGINATION_THRESHOLD,
      page: page,
    };
    if (formValues.hoken_type) {
      params.hoken_type = formValues.hoken_type;
    }
    if (sortBy && orderBy) {
      params.order_by = sortBy;
      params.order_type = orderBy;
    }

    if (formValues.intake_date_1 && formValues.intake_date_1[0] && formValues.intake_date_1[1]) {
      params.intake_date_from_1 = dayjs(formValues.intake_date_1[0]).format(
        DATE_FORMATS.SUBMIT_DATE
      );
      params.intake_date_to_1 = dayjs(formValues.intake_date_1[1]).format(DATE_FORMATS.SUBMIT_DATE);
      params.intake_case_1 = formValues.intake_case_1 ? IsInRange.TRUE : IsInRange.FALSE;
    }

    if (
      formValues.is_and &&
      formValues.intake_date_2 &&
      formValues.intake_date_2[0] &&
      formValues.intake_date_2[1]
    ) {
      params.intake_date_from_2 = dayjs(formValues.intake_date_2[0]).format(
        DATE_FORMATS.SUBMIT_DATE
      );
      params.intake_date_to_2 = dayjs(formValues.intake_date_2[1]).format(DATE_FORMATS.SUBMIT_DATE);
      params.intake_case_2 = formValues.intake_case_2 ? IsInRange.TRUE : IsInRange.FALSE;
    }
    if (!isFetchingInsuranceCount) {
      refetchInsuranceCount();
    }
    return params;
  }, [formValues, page, sortBy, orderBy]);

  const handleSearch = useCallback((values: PatientSearchParams) => {
    setFormValues(values);
    setPage(1);
    setShouldFetch(true);
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleSortChange = useCallback((sortData: { order_by: string; order_type: ESort }[]) => {
    const newSort = sortData.length > 0 ? sortData[0] : null;

    if (!newSort || !newSort.order_type) {
      setSortBy(null);
      setOrderBy(null);
    } else {
      setSortBy(newSort.order_by as PatientSortBy);
      setOrderBy(newSort.order_type);
    }
  }, []);

  const handleClear = useCallback(() => {
    setFormValues(defaultFormValues);
    setPage(1);
    setSortBy(null);
    setOrderBy(null);
    // setActiveFilterValues(DEFAULT_INSURANCE_FILTER);
    setShouldFetch(false);
  }, []);

  const { data: patients, isFetching: isFetchingPatientList } = useGetPatientListQuery(
    queryParams,
    {
      skip: !shouldFetch,
    }
  );
  const handleChangeInsurance = useCallback(
    (hokenTypes: string) => {
      setFormValues({
        ...formValues,
        hoken_type: hokenTypes,
      });
      setPage(1);
      setShouldFetch(true);
    },
    [formValues]
  );

  const contextValue = useMemo(
    () => ({
      queryParams,
      handleSearch,
      handlePageChange,
      handleSortChange,
      handleClear,
      patients: patients?.data,
      isFetching: isFetchingPatientList || isFetchingInsuranceCount,
      insuranceCount: insuranceCount?.data || [],
      formValues,
      setFormValues,
      activeFilterValues,
      setActiveFilterValues,
      handleChangeInsurance,
    }),
    [
      queryParams,
      handleSearch,
      handlePageChange,
      handleSortChange,
      handleClear,
      formValues,
      isFetchingPatientList,
      isFetchingInsuranceCount,
      patients,
      insuranceCount,
      activeFilterValues,
      handleChangeInsurance,
    ]
  );

  return <ListPatientContext.Provider value={contextValue}>{children}</ListPatientContext.Provider>;
};

export const useListPatient = (): ListPatientContextType => {
  const context = useContext(ListPatientContext);
  if (context === undefined) {
    throw new Error('useListPatient must be used within a ListPatientProvider');
  }
  return context;
};
