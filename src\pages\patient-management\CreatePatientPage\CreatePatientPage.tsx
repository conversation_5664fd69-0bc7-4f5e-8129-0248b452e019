import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import PageLayout from '@/components/layouts/PageLayout';
import { routerPaths } from '@/types/constants/routerPath';
import { ESubmitType } from '@/types/enum';
import { useRef, useState } from 'react';
import FormCreatePatient from '../../../components/page-components/patient-management/create/FormCreate/FormCreatePatient';
import PatientCreateAction from '../../../components/page-components/patient-management/create/PatientCreateAction';
import { useRouterPath } from '@/hooks/useRouterPath';

export default function CreatePatientPage() {
  const formRef = useRef<any>(null);
  const [submitType, setSubmitType] = useState<ESubmitType>(ESubmitType.SAVE);
  const { getPath } = useRouterPath();
  return (
    <PageLayout
      title="患者登録"
      breadcrumb={
        <AppBreadcrumb
          items={[
            {
              title: <a href={getPath(routerPaths.patientManagement.patientList)}>患者一覧</a>,
            },
            { title: '患者登録' },
          ]}
        />
      }
      headerRight={
        <PatientCreateAction
          formRef={formRef}
          onClick={(type: ESubmitType) => setSubmitType(type)}
        />
      }
    >
      <FormCreatePatient ref={formRef} submitType={submitType} />
    </PageLayout>
  );
}
