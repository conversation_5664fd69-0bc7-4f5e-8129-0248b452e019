import { AppCheckbox } from '@/components/common/core/Checkbox';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppSelect } from '@/components/common/core/Select';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import { Col, Divider, Flex, Form, Row } from 'antd';
import './MedicalBenefits.modules.scss';
import { initialValuesMedicalBenefits, MedicalBenefitsSchema } from './type';
import { useEffect, useState } from 'react';
import { AppDatePicker } from '@/components/common/core/Datepicker';
import { DATE_FORMATS, kohiMap, PLACEHOLDER_MESSAGE_DEFAULT } from '@/types/constants';
import { convertMapToOptions } from '@/utils';
import { HokenCollapse } from '@/components/page-components/visit-management/dashboard/VisitDetail/components/HokenCollapse';

const { useForm } = Form;

interface IMedicalBenefitsProps {
  initialData?: any;
}
export default function MedicalBenefits({ initialData }: IMedicalBenefitsProps) {
  const [form] = useForm<MedicalBenefitsSchema>();
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }
  }, [initialData]);
  return (
    <Form
      form={form}
      initialValues={initialValuesMedicalBenefits}
      layout="vertical"
      className="form-medical-benefits"
    >
      <AppBlockHasHeader
        icon={<Icon name="doctorPlus" width={20} height={20} color="white" />}
        title="医療助成"
      >
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="kohi_type" label="種類選択" layout="vertical">
                <AppSelect
                  size="small"
                  options={convertMapToOptions(kohiMap)}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                />
              </AppFormItem>
              <AppFormItem name="kohi_no" label="公費負担番号" layout="vertical">
                <AppInput
                  maxLength={64}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                />
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <div style={{ flex: 1 }}>
                <AppFormItem name="kohi_jukyusya_no" label="公費有給者番号" layout="vertical">
                  <AppInput
                    maxLength={64}
                    disabled
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                  />
                </AppFormItem>
              </div>

              <div style={{ flex: 1, position: 'relative' }}>
                <AppFormItem name="kohi_ritsu" label="医療助成負担" layout="vertical">
                  <AppInput
                    maxLength={64}
                    disabled
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                  />
                </AppFormItem>
                <span className="unit_test fs16-regular">割</span>
              </div>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="kohi_start_date" label="適用開始日" layout="vertical">
                <AppDatePicker
                  format={DATE_FORMATS.DATE}
                  disabled
                  style={{ height: '44px' }}
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                />
              </AppFormItem>
              <AppFormItem name="kohi_end_date" label="適用終了日" layout="vertical">
                <AppDatePicker
                  format={DATE_FORMATS.DATE}
                  disabled
                  style={{ height: '44px' }}
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                />
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="kohi_jyosei_flg" valuePropName="checked" layout="vertical">
                <AppCheckbox disabled>一部助成</AppCheckbox>
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Divider
          style={{
            margin: '16px 0',
          }}
        />
        <Row gutter={24}>
          <Col span={24}>
            <Flex flex={1} className="mb-4">
              <AppFormItem label={'備考欄'} name="remarks" layout="vertical">
                <AppInput width={'100%'} disabled />
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <HokenCollapse onToggleCollapse={setIsCollapsed} />
        </Row>
        {isCollapsed && (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Flex gap={24} flex={1} className="mb-4">
                  <AppFormItem name="office_name" label="事業所名称" layout="vertical">
                    <AppInput disabled />
                  </AppFormItem>
                </Flex>
              </Col>
              <Col span={12}>
                <Flex gap={24} flex={1} className="mb-4">
                  <AppFormItem name="office_post" label="郵便番号" layout="vertical">
                    <AppInput disabled />
                  </AppFormItem>
                </Flex>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Flex gap={24} flex={1} className="mb-4">
                  <AppFormItem name="office_address" label="住所" layout="vertical">
                    <AppInput disabled />
                  </AppFormItem>
                </Flex>
              </Col>
            </Row>
          </>
        )}
      </AppBlockHasHeader>
    </Form>
  );
}
