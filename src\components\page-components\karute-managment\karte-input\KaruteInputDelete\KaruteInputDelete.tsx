import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';
import { useAppDispatch } from '@/config/redux/store';
import { useDeleteKaruteMutation, useLazyGetLastestUpdateQuery } from '@/store/karute/api';
import { KaruteLatestUpdateStatus } from '@/store/karute/type';
import { ERROR_COMMON_MESSAGE, STATUS_CODES } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { showFlashNotice } from '@/utils';
import { Flex } from 'antd';
import React, { memo, useRef } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import styles from './KaruteInputDelete.module.scss';

const KaruteInputDelete: React.FC = () => {
  const deletingRef = useRef(false);
  const location = useLocation();
  const { show, hide } = useModalConfirm();
  const navigate = useNavigate();
  const karuteId = useParams().id;
  const dispatch = useAppDispatch();

  const [deleteKarute, { isLoading }] = useDeleteKaruteMutation();
  const [getLatestUpdate] = useLazyGetLastestUpdateQuery();

  const deleteKaruteInput = async (successCallback: () => void) => {
    if (deletingRef.current) return;
    deletingRef.current = true;
    try {
      const res = await deleteKarute(Number(karuteId));
      showFlashNotice({
        message: res.data?.message || '',
        type: res.data?.status === STATUS_CODES.OK ? 'success' : 'error',
      });
      if (res.data?.status === STATUS_CODES.OK) {
        dispatch(medixKaruteBaseQueryApi.util.resetApiState());
        navigate(
          {
            pathname: location.pathname,
            search: `?refresh=${Date.now()}`,
          },
          { replace: true }
        );
        successCallback();
      }
    } finally {
      deletingRef.current = false;
    }
  };

  const showPopup = (savedFinal: boolean, jpDate: string) => {
    if (!savedFinal) {
      show({
        title: <>カルテの最新データを削除する</>,
        content: (
          <>
            この操作は元に戻せません。’{jpDate}
            ’に作成または更新されたカルテの全データを削除してもよろしいでしょうか？
          </>
        ),
        buttons: [
          <Button
            key="cancel"
            customType={ButtonType.SECONDARY_COLOR}
            onClick={hide}
            customSize="lg"
            style={{
              width: '120px',
            }}
          >
            キャンセル
          </Button>,
          <Button
            key="confirm"
            customSize="lg"
            customType={ButtonType.RED_PRIMARY}
            loading={isLoading}
            disabled={deletingRef.current}
            onClick={() => deleteKaruteInput(hide)}
            style={{
              width: '120px',
            }}
          >
            削除
          </Button>,
        ],
        width: '464px',
      });
    } else {
      show({
        title: <>確定済みデータのため削除不可</>,
        content: (
          <>{jpDate}に作成または更新されたカルテのデータは既に確定済みのため、削除できません。</>
        ),
        buttons: [
          <Button
            key="cancel"
            customType={ButtonType.PRIMARY}
            onClick={hide}
            customSize="lg"
            style={{
              width: '120px',
            }}
          >
            閉じる
          </Button>,
        ],
        width: '464px',
      });
    }
  };

  const handleDelete = async () => {
    try {
      const response = await getLatestUpdate({ karute_id: Number(karuteId) }).unwrap();
      const dateStr = response.data?.latest_updated_date || '';
      const parts = dateStr.split('-');
      if (!dateStr || !parts || parts.length < 3 || parts.some(p => !p)) {
        showFlashNotice({
          message: ERROR_COMMON_MESSAGE.DELETE_CONDITION_NOT_MET,
          type: 'error',
        });
        return;
      }
      const [y, m, d] = parts;
      const jpDate = `${y || ''}年${m || ''}月${d || ''}日`;
      const savedFinal = response.data?.status === KaruteLatestUpdateStatus.SAVED;
      showPopup(savedFinal, jpDate);
    } catch (error) {
      console.error('Failed to fetch latest update', error);
      showFlashNotice({
        message: ERROR_COMMON_MESSAGE.DELETE_CONDITION_NOT_MET,
        type: 'error',
      });
    }
  };

  return (
    <Flex justify="end">
      <div className={styles.container} onClick={handleDelete}>
        <Icon name="noAccess" color="$gray-500" height={20} width={20} />
      </div>
    </Flex>
  );
};

export default memo(KaruteInputDelete);
