import { AppCheckbox } from '@/components/common/core/Checkbox';
import { Flex } from 'antd/lib';
import styles from './DbHistory.module.scss';
import { clsx } from 'clsx';
import { HistoryRevision } from './HistoryRevision';
import { DbHistoryDetail } from '@/store/db-history/type';
import dayjs from 'dayjs';
import { Spin } from 'antd';

interface IDbHistoryProps {
  dbHistorys: DbHistoryDetail[];
  checkedList: string[];
  setCheckedList: React.Dispatch<React.SetStateAction<string[]>>;
  containerRef: React.RefObject<HTMLDivElement>;
  setIsSelectAll: React.Dispatch<React.SetStateAction<boolean>>;
  isFetching: boolean;
  total?: number;
}

const getPatientAge = (birthday: string) => {
  const patientYOB = dayjs(birthday).year();
  const currentYear = dayjs().year();

  return `${dayjs(birthday).format('YYYY/MM/DD')} (${currentYear - patientYOB} 歳)`;
};

export function DbHistory({
  dbHistorys,
  checkedList,
  setCheckedList,
  containerRef,
  setIsSelectAll,
  isFetching,
  total,
}: IDbHistoryProps) {
  return (
    <div className={styles.container}>
      <Flex vertical gap={'16px'} className={styles.dbHistoryList} ref={containerRef}>
        {total &&
          dbHistorys.map((dbHistory, index) => {
            const {
              version,
              submitted_at,
              patient_name,
              birthday,
              staff_name,
              revisions,
              med_history_family,
              med_history_personal,
              med_history_social,
            } = dbHistory;

            return (
              <Flex gap={'10px'} align="start" key={index} style={{ width: '100%' }}>
                <AppCheckbox
                  checked={checkedList.includes(version.toString())}
                  onChange={e => {
                    if (e.target.checked) {
                      const updateCheckedList = [...checkedList, version.toString()];

                      setCheckedList(updateCheckedList);

                      if (updateCheckedList.length === total) {
                        setIsSelectAll(true);
                      }
                    } else {
                      setCheckedList(checkedList.filter(item => item != version.toString()));
                      setIsSelectAll(false);
                    }
                  }}
                  className={styles.checkBox}
                />

                <Flex
                  vertical
                  className={clsx(styles.dbHistory, {
                    [styles.hidden]: !checkedList.includes(version.toString()),
                  })}
                >
                  {/* header */}
                  <Flex
                    align="start"
                    justify="space-between"
                    style={{ padding: '8px 12px', borderBottom: '1px solid var(--gray-200)' }}
                  >
                    <Flex
                      className="fs14-medium"
                      align="start"
                      justify="space-between"
                      gap={'20px'}
                    >
                      <span>No{version}</span>
                      <span className={styles.patientNamesInfo}>{patient_name}</span>

                      <span>{getPatientAge(birthday)}</span>
                    </Flex>

                    <Flex gap={'16px'}>
                      <Flex style={{ maxWidth: '300px' }}>
                        <span className={clsx('fs14-regular', styles.updatedDateLabel)}>
                          更新者：
                        </span>
                        <span
                          className={clsx(
                            'fs14-regular',
                            styles.updatedDateValue,
                            styles.patientNamesInfo
                          )}
                        >
                          {staff_name}
                        </span>
                      </Flex>
                      <Flex>
                        <span className={clsx('fs14-regular', styles.updatedDateLabel)}>
                          更新日時：
                        </span>
                        <span className={clsx('fs14-regular', styles.updatedDateValue)}>
                          {dayjs(submitted_at).format('YYYY/MM/DD HH:mm')}
                        </span>
                      </Flex>
                    </Flex>
                  </Flex>

                  <Flex className={clsx('fs12-regular', styles.historySection)} vertical>
                    <div className={styles.historySectionDetail}>
                      <span className={styles.sectionLabel}> 既往歴 :</span>
                      <span className={styles.sectionText}>{med_history_personal}</span>
                    </div>

                    <div className={styles.historySectionDetail}>
                      <span className={styles.sectionLabel}> 家族歴 :</span>
                      <span className={styles.sectionText}>{med_history_family}</span>
                    </div>

                    <div className={styles.historySectionDetail}>
                      <span className={styles.sectionLabel}> 社会歴 :</span>
                      <span className={styles.sectionText}>{med_history_social}</span>
                    </div>
                  </Flex>

                  {/* content */}

                  {Object.entries(revisions).map((item, index) => {
                    return (
                      <HistoryRevision
                        key={`${item[0]}-${index}`}
                        revisionDate={item[0]}
                        revision={item[1]}
                      />
                    );
                  })}
                </Flex>
              </Flex>
            );
          })}
        {isFetching && (
          <div
            style={{
              minHeight: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Spin />
          </div>
        )}
      </Flex>
    </div>
  );
}
