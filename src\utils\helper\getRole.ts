import { LOCAL_STORAGE_KEY, ROLE_OPTIONS } from '@/types/constants';
import { ROLE } from '@/types/enum';
import { UserInfo } from '@/types/interface';

export const getUserRole = () => {
  const userString = localStorage.getItem(LOCAL_STORAGE_KEY.USER_INFO);
  const user: UserInfo | null = userString ? JSON.parse(userString) : null;
  return user ? user.manageable_type : null;
};

export const getManagerId = () => {
  const userString = localStorage.getItem(LOCAL_STORAGE_KEY.USER_INFO);
  const user: UserInfo | null = userString ? JSON.parse(userString) : null;
  return user ? user.manageable_id : null;
};
export const getRoleName = (role: ROLE | null) => {
  switch (role) {
    case ROLE.MEDIX_ADMIN:
      return 'メディックス担当者';
    case ROLE.COMPANY_ADMIN:
      return '企業担当者';
    case ROLE.STORE_ADMIN:
      return '店舗担当者';
    case ROLE.STAFF:
      return 'スタッフ';
    default:
      return '';
  }
};

export const getRoleOptionsView = (role: ROLE | null) => {
  switch (role) {
    case ROLE.MEDIX_ADMIN:
      return ROLE_OPTIONS;
    case ROLE.COMPANY_ADMIN:
      return ROLE_OPTIONS.filter(option => option.value !== ROLE.MEDIX_ADMIN);
    case ROLE.STORE_ADMIN:
      return ROLE_OPTIONS.filter(
        option => option.value !== ROLE.MEDIX_ADMIN && option.value !== ROLE.COMPANY_ADMIN
      );
    case ROLE.STAFF:
      return [];
    default:
      return [];
  }
};

export const getRoleOptionsCreate = (role: ROLE | null) => {
  switch (role) {
    case ROLE.MEDIX_ADMIN:
      return ROLE_OPTIONS;
    case ROLE.COMPANY_ADMIN:
      return ROLE_OPTIONS.filter(
        option => option.value !== ROLE.MEDIX_ADMIN && option.value !== ROLE.COMPANY_ADMIN
      );
    case ROLE.STORE_ADMIN:
      return ROLE_OPTIONS.filter(
        option =>
          option.value !== ROLE.MEDIX_ADMIN &&
          option.value !== ROLE.COMPANY_ADMIN &&
          option.value !== ROLE.STORE_ADMIN
      );
    case ROLE.STAFF:
      return [];
    default:
      return [];
  }
};

export function getViewOnlyOptions(role: ROLE | null) {
  const viewOpts = getRoleOptionsView(role);
  const createOpts = getRoleOptionsCreate(role);
  const createValues = new Set(createOpts.map(o => o.value));
  return viewOpts.filter(o => !createValues.has(o.value));
}
