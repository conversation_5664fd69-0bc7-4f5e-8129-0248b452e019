import { NOTICE_COMMON_MESSAGE } from '@/types/constants';
import { deepCompare } from '@/utils/deepCompare';
import pick from 'lodash.pick';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { UNSAFE_NavigationContext } from 'react-router-dom';

interface UseUnsavedOptions {
  /** Message confirm when leave page */
  message?: string;
  ignoreNavigation?: (from: string, to: string) => boolean;
}

export function useUnsavedWarning<T extends object>(initialValues: T, opts?: UseUnsavedOptions) {
  const { message = NOTICE_COMMON_MESSAGE.UNSAVED_WARNING, ignoreNavigation } = opts ?? {};
  const initialRef = useRef(initialValues);
  const [current, setCurrent] = useState(initialValues);
  const skipRef = useRef(false);

  // Get initial values subset
  const initialSubset = useMemo(() => {
    const keys = Object.keys(current) as (keyof T)[];
    return pick(initialRef.current, keys);
  }, [current]);

  // Handle values change
  const handleValuesChange = useCallback((_: any, all: T) => {
    setCurrent(all);
  }, []);

  // Confirm leave function
  const confirmLeave = useCallback((): boolean => {
    const isDirty = !deepCompare(initialSubset, current);
    if (!isDirty) return true;
    return window.confirm(message);
  }, [message, current, initialSubset]);

  const disableWarning = useCallback(() => {
    skipRef.current = true;
    unblockRef.current?.();
    unblockRef.current = null;
  }, []);

  const resetInitial = useCallback(
    (init?: T) => {
      initialRef.current = init ?? current;
    },
    [current]
  );

  const resetCurrent = useCallback(() => {
    const clean = initialRef.current;
    setCurrent(clean);
  }, []);

  const resetAll = useCallback(
    (init?: T) => {
      initialRef.current = init ?? current;
      setCurrent(init ?? current);
    },
    [current]
  );
  const submitHandler = useCallback(() => {
    disableWarning();
    resetInitial();
    resetCurrent();
  }, []);

  // Handle beforeunload (tab close/reload)
  const beforeUnloadHandler = useCallback(
    (e: BeforeUnloadEvent) => {
      const isDirty = !deepCompare(initialSubset, current);
      if (!isDirty) return;
      e.preventDefault();
      e.returnValue = '';
    },
    [current, initialSubset]
  );

  useEffect(() => {
    window.addEventListener('beforeunload', beforeUnloadHandler);
    return () => window.removeEventListener('beforeunload', beforeUnloadHandler);
  }, [beforeUnloadHandler]);

  const { navigator } = useContext(UNSAFE_NavigationContext) as any;
  const unblockRef = useRef<null | (() => void)>(null);

  useEffect(() => {
    const isDirty = !deepCompare(initialSubset, current);
    if (!isDirty) return;
    unblockRef.current = navigator.block((tx: any) => {
      const from = window.location.pathname;
      const to = tx.location.pathname;

      if (ignoreNavigation?.(from, to)) {
        unblockRef.current?.();
        unblockRef.current = null;
        tx.retry();
        return;
      }
      if (window.confirm(message)) {
        unblockRef.current?.();
        unblockRef.current = null;
        tx.retry();
      }
    });
    return () => {
      unblockRef.current?.();
      unblockRef.current = null;
    };
  }, [message, navigator, current, initialSubset]);

  useEffect(() => {
    initialRef.current = initialValues;
    setCurrent(initialValues);
    skipRef.current = false;
  }, [initialValues]);

  return {
    handleValuesChange,
    disableWarning,
    submitHandler,
    confirmLeave,
    resetInitial,
    resetCurrent,
    //
    setCurrent,
    initialRef,
    resetAll,
  };
}
