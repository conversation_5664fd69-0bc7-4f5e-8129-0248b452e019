import { DATE_FORMATS } from '@/types/constants';
import { Flex } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import styles from './ProgressRecords.module.scss';

interface IRecordInfoProps {
  visitDate?: string;
  updatedAt?: string;
  className?: string;
}

export function RecordInfo({ visitDate, updatedAt, className }: IRecordInfoProps) {
  return (
    <Flex
      gap="16px"
      align="center"
      className={clsx(styles.recordInfo, className)}
      style={{ marginRight: 16 }}
    >
      <span className={clsx(styles.textGray800, 'fs12-medium')}>
        施術日：{visitDate && dayjs(visitDate).format(DATE_FORMATS.DATE)}
      </span>
      <span className={clsx(styles.textGray500, 'fs12-medium')}>
        更新日時：{updatedAt && dayjs(updatedAt).format(DATE_FORMATS.DATE_HOUR_MINUTE)}
      </span>
    </Flex>
  );
}
