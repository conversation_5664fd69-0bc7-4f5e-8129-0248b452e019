export const createFormValues = <T>(
  name: (keyof T)[],
  data: T,
  emptyValue: string | ((value: any) => string) = ''
): { name: string; value: any }[] => {
  return name.map(item => {
    return {
      name: item as string,
      value: data[item] || (typeof emptyValue === 'string' ? emptyValue : emptyValue(data[item])),
    };
  });
};

export const clearSpace = (value?: string | null) => {
  if (!value) {
    return '';
  } else {
    const values = value?.trim().split(' ');
    const newValue = values?.filter(item => item !== '')?.join(' ');
    return newValue;
  }
};

export const formatPhoneNumber = (phone: string) => {
  if (!phone) return '';
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
};

export const cleanParams = (params: Record<string, any>, convertArrayToComma = true) => {
  const cleaned: Record<string, any> = {};

  Object.entries(params).forEach(([key, value]) => {
    const isEmptyArray = Array.isArray(value) && value.length === 0;

    if (value !== null && value !== undefined && !isEmptyArray) {
      cleaned[key] = convertArrayToComma && Array.isArray(value) ? value.join(',') : value;
    }
  });

  return cleaned;
};

export const buildQueryString = (
  params: Record<string, any>,
  convertArrayToComma = false
): string => {
  const queryParts: string[] = [];

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && !(Array.isArray(value) && value.length === 0)) {
      if (Array.isArray(value)) {
        if (convertArrayToComma) {
          queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value.join(','))}`);
        } else {
          value.forEach(item => {
            queryParts.push(`${encodeURIComponent(key)}[]=${encodeURIComponent(item)}`);
          });
        }
      } else {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
  });

  return queryParts.length > 0 ? `?${queryParts.join('&')}` : '';
};
