.findingsCell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  white-space: normal;
  text-wrap: nowrap;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

.addImageButton {
  position: absolute;
  right: 5px;
  bottom: 5px;

  width: 18px;
  height: 18px;
  min-width: 18px;
  padding: 4px;
  border: none;
  background-color: $blue-700;
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.dividerRow {
  padding: 12px 4px;
  width: 100%;
  background-color: #d0d5dd;
}

.divider {
  width: 2px;
  height: 12px;
  background-color: #101828;
}

.container {
  .highlightRow {
    background-color: $pink-100;
  }
  .emptyTable {
    :global(.ant-table-body) {
      max-width: 0;
      overflow: auto;
    }
  }
  .keikaTable {
    :global(.ant-table-tbody .ant-table-cell) {
      vertical-align: top;
      height: 76px;

      > div {
        height: 100%;
      }
    }
    .imageGroup {
      padding-right: 5px;
      max-height: 160px;
      overflow-y: auto;
    }
  }
}

.textGray800 {
  color: $gray-800;
}

.textGray500 {
  color: $gray-500;
}

.historyRecordInfo {
  background-color: $gray-200;
  padding: 4px 14px 4px 14px;
}

.customButton {
  height: 30px !important;
  width: 52px !important;
}
