import { CourseService } from '@/store/visit/type';
import { TreeDataNode } from 'antd';

export function mapCoursesToCourseGroups(
  treeServices: CourseService[],
  course_ids: string[] // Use string for easier compare with treeDataNode key
): Record<string, string[]> {
  // Use a set for faster lookup
  // Cause: treeServices has hundreds of items
  const activeCourseIds = new Set(course_ids);

  const result: Record<string, string[]> = {};

  for (const service of treeServices) {
    const matchedIds: string[] = [];

    const extractKeys = (nodes: TreeDataNode[] = []) => {
      for (const node of nodes) {
        if (node.children) extractKeys(node.children);
        else if (activeCourseIds.has(String(node.key))) matchedIds.push(String(node.key));
      }
    };

    extractKeys(service.treeData);
    extractKeys(service.jihiCourse);

    if (matchedIds.length > 0) {
      result[service.service_id] = matchedIds;
    }
  }

  return result;
}

export const trimField = (value: string, maxLength: number): string => {
  if (value?.length > maxLength) {
    return value.substring(0, maxLength);
  }
  return value;
};

export const concatField = (
  current: string | undefined,
  newValue: string | undefined,
  maxLength: number
) => {
  return current || newValue
    ? trimField(
        `${current ?? ''}${current && newValue ? '\n' : ''}${newValue ?? ''}`,
        maxLength ?? 10000
      )
    : '';
};
