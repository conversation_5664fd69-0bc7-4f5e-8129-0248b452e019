import { Tooltip } from 'antd';
import { AppSelect } from '@/components/common/core/Select';
import { Doctor } from '@/store/visit/type';
import { transformTreatersToOptions } from '@/utils/helper';
import { SelectProps } from 'antd';
import { COMMA_SEPARATED, PLACEHOLDER_MESSAGE_DEFAULT } from '@/types/constants';

interface MultiTreaterSelectFormItemProps extends SelectProps {
  treaterList?: Doctor[];
}

export const MultiTreaterSelectFormItem = ({
  treaterList = [],
  ...rest
}: MultiTreaterSelectFormItemProps) => {
  return (
    <AppSelect
      className="treater-select"
      mode="multiple"
      // placeholder="選択してください"
      labelInValue
      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
      options={transformTreatersToOptions(treaterList)}
      maxTagCount="responsive"
      filterOption={(input, option) =>
        String(option?.label).toLowerCase().trim().startsWith(input.toLowerCase().trim())
      }
      maxTagPlaceholder={omittedValues => (
        <Tooltip
          styles={{
            root: {
              pointerEvents: 'none',
            },
          }}
          title={omittedValues.map(({ label }) => label).join(COMMA_SEPARATED)}
        >
          <span>+ {omittedValues?.length}</span>
        </Tooltip>
      )}
      {...rest}
    />
  );
};
