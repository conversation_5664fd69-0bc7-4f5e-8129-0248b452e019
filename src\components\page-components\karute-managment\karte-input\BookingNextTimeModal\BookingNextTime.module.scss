.create_booking {
  max-height: 748px;
  :global(.ant-modal-content) {
    padding: 20px 0px !important;
    :global(.title-modal) {
      padding: 0px 24px !important;
    }
  }
  :global(.ant-table-thead) {
    :global(.ant-table-cell) {
      height: 34px !important;
    }
  }

  :global(.ant-table-wrapper) {
    max-height: 170px;
    margin-bottom: 24px;
  }
  :global(.ant-table) {
    height: 170px;
    :global(.ant-table-thead) {
      height: 34px !important;
      :global(.ant-table-cell) {
        font-size: 12px;
        color: $gray-500;
        background-color: $brand-50;
        line-height: 18px;
        font-weight: 500;
        height: 34px !important;
        vertical-align: middle;
        padding: 8px 12px !important;
      }
    }
  }
  .booking_container {
    max-height: auto;
    gap: 16px;
    .booking_calendar {
      flex: 1;
      height: 100%;
      background-color: $gray-50;
      max-width: 360px;
      max-height: 316px;
      :global(.ant-picker-panel) {
        background-color: $gray-50 !important;
        width: 100%;
        justify-content: center !important;
        align-items: center !important;
        padding: 0 24px !important;
      }

      :global(.ant-picker-content) {
        > tbody > tr > td {
          width: 100%;
        }
      }

      .dayInCalendar {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: auto;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: $gray-800;
      }

      .isToday {
        border-radius: 20px;
        border: 1px solid #2a5875;
      }
      .isBooking {
        background-color: $brand-600;
        color: white;
      }
      .isBookedAndCurrent {
        background-color: $brand-600;
        color: white;
        border-radius: 20px;
        border: 1px solid #2a5875;
      }
      .notInMonth {
        color: $gray-300;
      }
    }
    .custom_date {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  :global(.ant-form-item-row) {
    height: 66px;
    flex-flow: column;
    width: 100%;
    :global(.ant-form-item-label) {
      padding: 0 0 6px 0 !important;
    }
  }

  :global(.ant-table-wrapper) {
    overflow-y: hidden;
  }

  :global(.ant-select) {
    height: 40px !important;
  }
  .noData {
    :global(.ant-table-cell) {
      border-bottom: unset;
    }
  }
  .booking_table {
    :global {
      .ant-table-tbody {
        .ant-table-placeholder {
          .ant-table-cell {
            height: 170px !important;
            padding: 0 !important;
            border-bottom: none !important;
          }
        }
      }
    }
  }
}

.calendarHeader {
  height: 24px;
  margin: 16px 24px 12px 24px;
  max-width: 360px;
  max-height: 316px;
  > div {
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .calendarHeaderLabel {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0;
    color: $gray-800;
  }
}

.selectCustom {
  width: 100%;
  height: 40px;
  :global(.ant-select) {
    border-radius: 8px;
  }
  :global(.ant-select-selector) {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    padding: 10px 12px !important;
    border-radius: 8px !important;
    :global(.ant-select-selection-item) {
      background-color: unset;
    }
    :global(.ant-select-selection-item-remove) {
      display: none;
    }
    :global(.ant-select-selection-item) {
      background-color: unset;
      margin-inline-end: unset;
      margin-block: unset;
    }
  }
  &:global(.ant-select:not(.ant-select-disabled):hover) {
    :global(.ant-select-selector) {
      border-color: $brand-800 !important;
    }
  }

  &:global(.ant-select-disabled) {
    :global(.ant-select-selector) {
      background-color: var(--input-bg-disabled) !important;
    }
    :global(.ant-select-selection-item) {
      color: var(--input-text-disabled) !important;
    }
  }
  span {
    :global(.ant-select-selection-item) {
      color: $gray-800;
    }
  }
  &:global(.ant-select.ant-select-focused) {
    :global(.ant-select-selector) {
      border-color: $brand-800 !important;
    }
  }

  &:global(.ant-select:not(.ant-select-disabled):hover) {
    :global(.ant-select-selector) {
      border-color: $brand-800 !important;
    }
  }

  &:global(.ant-select.ant-select-status-error),
  &:global(.ant-select.ant-select-status-error:hover) {
    :global(.ant-select-selector) {
      border-color: $error-600 !important;
    }
  }
}

.noSelect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
