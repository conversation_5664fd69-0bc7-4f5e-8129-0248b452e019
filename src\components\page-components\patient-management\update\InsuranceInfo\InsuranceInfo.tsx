import { AppCheckbox } from '@/components/common/core/Checkbox';
import { AppDatePicker } from '@/components/common/core/Datepicker';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import { Col, Flex, Form, Row } from 'antd';
import './InsuranceInfo.modules.scss';
import { initialValuesInsuranceInfo, InsuranceInfoSchema } from './type';

import { useEffect, useMemo } from 'react';
import { burdenMap, DATE_FORMATS, PLACEHOLDER_MESSAGE_DEFAULT } from '@/types/constants';
import { AppSelect } from '@/components/common/core/Select';
import { convertMapToOptions } from '@/utils';
import { useSelector } from 'react-redux';
import { getSelection } from '@/store/visit/selector';
const { useForm } = Form;

interface IInsuranceInfoProps {
  initialData?: InsuranceInfoSchema;
}
export default function InsuranceInfo({ initialData }: IInsuranceInfoProps) {
  const storedSelectionState = useSelector(getSelection);
  const selectionOptions = useMemo(
    () => ({
      status: storedSelectionState.selection.status || [],
      insurance_types: storedSelectionState.selection.insurance_types || [],
      treaters: [],
      courses: storedSelectionState.selection.courses || [],
    }),
    [storedSelectionState.selection]
  );

  const [form] = useForm<InsuranceInfoSchema>();
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }
  }, [initialData]);
  return (
    <Form
      form={form}
      initialValues={initialValuesInsuranceInfo}
      layout="vertical"
      className="form-insurance-info"
    >
      <AppBlockHasHeader
        icon={<Icon name="insuranceInfo" width={20} height={20} color="white" />}
        title="被保険情報"
      >
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="hoken_type" label="保険種別" layout="vertical">
                <AppSelect
                  size="small"
                  options={selectionOptions.insurance_types.flatMap(item =>
                    item.value
                      .toString()
                      .split(',')
                      .map(value => ({
                        label: item.label,
                        value: value,
                      }))
                  )}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                />
              </AppFormItem>
              <AppFormItem
                name="shoukan_flg"
                valuePropName="checked"
                layout="vertical"
                className="h-74 pb-4"
              >
                <AppCheckbox disabled>債還払い</AppCheckbox>
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="hoken_no" label="保険者番号" layout="vertical">
                <AppInput
                  maxLength={64}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                />
              </AppFormItem>
              <AppFormItem name="hokensya_type" layout="vertical" className="h-74">
                <AppInput
                  maxLength={64}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                />
              </AppFormItem>
            </Flex>
          </Col>
          <Col span={10}>
            <AppFormItem name="hoken_name" layout="vertical" className="h-74">
              <AppInput
                maxLength={64}
                disabled
                placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
              />
            </AppFormItem>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="futan_ritsu" label="受診者負担" layout="vertical">
                <AppInput
                  maxLength={64}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                />
              </AppFormItem>
              <AppFormItem name="futan_shubetsu" layout="vertical" className="h-74">
                <AppSelect
                  size="small"
                  options={convertMapToOptions(burdenMap)}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                />
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="hoken_kigo" label="記号" layout="vertical">
                <AppInput
                  maxLength={64}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                />
              </AppFormItem>
              <AppFormItem name="hoken_bango" label="番号" layout="vertical">
                <AppInput
                  maxLength={64}
                  disabled
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                />
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={14}>
            <Flex gap={24} flex={1} className="mb-4">
              <AppFormItem name="hoken_shikaku_date" label="資格取得日" layout="vertical">
                <AppDatePicker
                  format={DATE_FORMATS.DATE}
                  disabled
                  style={{ height: '44px' }}
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                />
              </AppFormItem>
              <AppFormItem name="hoken_yuko_date" label="有効期限" layout="vertical">
                <AppDatePicker
                  format={DATE_FORMATS.DATE}
                  disabled
                  style={{ height: '44px' }}
                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                />
              </AppFormItem>
            </Flex>
          </Col>
        </Row>
      </AppBlockHasHeader>
    </Form>
  );
}
