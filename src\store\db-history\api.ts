import { medixKaruteBaseQueryApi, medixSyncBaseQueryApi } from '@/config/redux/base-query-api';
import { DbHistoryDetail, ParamGetListDbHistory } from './type';
import { APIResponse, ListResponse } from '@/types/interface';

export const dbHistoryApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixSyncBaseQueryApi.endpoints,
    getDbHistory: build.query<APIResponse<ListResponse<DbHistoryDetail>>, ParamGetListDbHistory>({
      query: params => {
        return {
          url: '/api/disease-base-submit-versions/group-by-submit-date',
          method: 'GET',
          params,
        };
      },
    }),
  }),
});

export const { useGetDbHistoryQuery } = dbHistoryApi;
