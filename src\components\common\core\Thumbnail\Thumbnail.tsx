import clsx from 'clsx';
import { Image, Checkbox } from 'antd';
import Icon from '@/components/common/core/Icon';
import styles from './Thumbnail.module.scss';
import { SchemaImage } from '@/store/schema/type';
import React from 'react';

interface ThumbProps {
  data: SchemaImage;
  selected: boolean;
  selectable: boolean;
  onSelect: () => void;
  onToggleFav: () => void;
  deletable?: boolean;
  onDelete?: () => void;
}

const Thumbnail: React.FC<ThumbProps> = ({ data, selected, selectable, onSelect, onToggleFav, deletable, onDelete }) => {
  return (
    <div className={clsx(styles.thumb, selectable && selected && styles.chosen)}>
      <Image
        src={data.url}
        width={80}
        height={80}
        fallback="/images/fallback.png"
        preview={false}
        style={{ objectFit: 'cover' }}
        onClick={onSelect}
      />

      {selectable && (
        <Checkbox
          style={{ width: '20px', height: '20px' }}
          checked={selected}
          className={styles.checkbox}
          onChange={e => {
            e.stopPropagation();
            onSelect();
          }}
        />
      )}

      {!selectable && (
        <div
          className={clsx(styles.star, data.favorite && styles.favActive)}
          onClick={e => {
            e.stopPropagation();
            onToggleFav();
          }}
        >
          <Icon name={data.favorite ? 'starFill' : 'star'} width={16} height={16} />
        </div>
      )}
      {deletable && (
        <div
          className={styles.trash}
          onClick={e => {
            e.stopPropagation();
            onDelete?.();
          }}
        >
          <Icon name="trash" width={8} height={8} />
        </div>
      )}
    </div>
  );
};

export default React.memo(Thumbnail);
