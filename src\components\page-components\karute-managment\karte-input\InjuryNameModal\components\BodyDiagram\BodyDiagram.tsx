// // diagram A (Bruises/Sprains/Contusions)
// const ref = useRef<BodyDiagramHandle>(null);
// <BodyDiagramBruisesSprainsContusions ref={ref} onSelect={id => console.log(id)} />;

// // To clear from outside:
// ref.current?.clearActive();

import bruiseSvg from '@/assets/diagrams/BruisesSprainsContusions.svg?raw';
import dislocationSvg from '@/assets/diagrams/DislocationFractureIncompleteFracture.svg?raw';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { DiagramType } from '../../type';

export interface BodyDiagramProps {
  svg: string;
  defaultColor?: string;
  hoverColor?: string;
  activeColor?: string;
  onSelect?: (id: string | null, side: DiagramType | undefined) => void;
  className?: string;
  onReady?: () => void;
}

export interface BodyDiagramHandle {
  clearActive: () => void;
  selectActive: (id: string, side?: DiagramType) => void;
}

function detectDiagramType(el: SVGGraphicsElement): DiagramType {
  if (el.closest('#born')) return DiagramType.Back;

  const svg = el.ownerSVGElement!;
  const mid = (svg.viewBox.baseVal?.width ?? 600) / 2;
  const cx = el.getBBox().x + el.getBBox().width / 2;
  return cx < mid ? DiagramType.Front : DiagramType.Back;
}

export const BodyDiagramBruisesSprainsContusions = forwardRef<
  BodyDiagramHandle,
  Omit<BodyDiagramProps, 'svg'>
>((props, ref) => <BodyDiagram svg={bruiseSvg} ref={ref} {...props} />);

export const BodyDiagramDislocationFractureIncompleteFracture = forwardRef<
  BodyDiagramHandle,
  Omit<BodyDiagramProps, 'svg'>
>((props, ref) => <BodyDiagram svg={dislocationSvg} ref={ref} {...props} />);

const BodyDiagram = forwardRef<BodyDiagramHandle, BodyDiagramProps>(
  (
    {
      svg,
      defaultColor = 'rgba(177, 223, 221, 0.5)',
      hoverColor = 'var(--brand-500)',
      activeColor = 'var(--pink-200)',
      onSelect,
      className,
      onReady,
    },
    ref
  ) => {
    const container = useRef<HTMLDivElement>(null);

    const activeElsRef = useRef<SVGGraphicsElement[]>([]);
    const [, forceRerender] = useState(0);

    const getRegionElements = (id: string, side?: DiagramType): SVGGraphicsElement[] => {
      const svgEl = container.current?.querySelector('svg');
      if (!svgEl) return [];

      const all = [...svgEl.querySelectorAll<SVGGraphicsElement>(`[id="${id}"]`)].filter(el =>
        /^\d+$/.test(el.id)
      );

      if (!side) return all;
      return all.filter(el => detectDiagramType(el) === side);
    };

    const paint = (el: SVGGraphicsElement, fill: string) => {
      const colourize = (node: SVGGraphicsElement) => {
        node.style.fill = fill;
        node.setAttribute('fill', fill);
      };
      colourize(el);
      el.querySelectorAll<SVGGraphicsElement>(
        'path, polygon, rect, circle, ellipse, line, polyline'
      ).forEach(colourize);
      el.style.cursor = 'pointer';
    };

    const resetColors = () => {
      const svgEl = container.current?.querySelector('svg');
      if (!svgEl) return;
      svgEl
        .querySelectorAll<SVGGraphicsElement>('[id]')
        .forEach(el => /^\d+$/.test(el.id) && paint(el, defaultColor));

      activeElsRef.current = [];
    };

    useImperativeHandle(ref, () => ({
      clearActive: () => {
        resetColors();
        activeElsRef.current = [];
        forceRerender(x => x + 1);
      },

      selectActive: (id: string, side?: DiagramType) => {
        resetColors();
        let targets = side ? getRegionElements(id, side) : [];
        if (!targets.length) targets = getRegionElements(id);
        if (!targets.length) return;

        targets.forEach(el => paint(el, activeColor));
        activeElsRef.current = targets;
        forceRerender(x => x + 1);
      },
    }));

    useEffect(() => {
      if (!container.current) return;

      container.current.innerHTML = svg;
      const svgEl = container.current.querySelector('svg');
      if (!svgEl) return;

      const regions = [...svgEl.querySelectorAll<SVGGraphicsElement>('[id]')].filter(el =>
        /^\d+$/.test(el.id)
      );

      regions.forEach(el => paint(el, defaultColor));

      const handleEnter = (e: Event) => {
        const el = e.currentTarget as SVGGraphicsElement;
        if (!activeElsRef.current.includes(el)) paint(el, hoverColor);
      };

      const handleLeave = (e: Event) => {
        const el = e.currentTarget as SVGGraphicsElement;
        if (!activeElsRef.current.includes(el)) paint(el, defaultColor);
      };

      const handleClick = (e: Event) => {
        const el = e.currentTarget as SVGGraphicsElement;
        const id = el.id;
        const side = detectDiagramType(el);
        onSelect?.(id, side);
      };

      regions.forEach(el => {
        el.addEventListener('mouseenter', handleEnter);
        el.addEventListener('mouseleave', handleLeave);
        el.addEventListener('click', handleClick);
      });

      return () => {
        regions.forEach(el => {
          el.removeEventListener('mouseenter', handleEnter);
          el.removeEventListener('mouseleave', handleLeave);
          el.removeEventListener('click', handleClick);
        });
      };
    }, [svg, defaultColor, hoverColor, activeColor]);

    onReady?.();

    return <div ref={container} className={className} />;
  }
);

export default BodyDiagram;
