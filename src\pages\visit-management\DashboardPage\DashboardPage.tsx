import PageLayout from '@/components/layouts/PageLayout';
import DashboardAction from '../../../components/page-components/visit-management/dashboard/DashboardAction';
import DashboardContent from '../../../components/page-components/visit-management/dashboard/DashboardContent';
import { useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';

export default function DashboardPage() {
  // To avoid using provider
  const [filteredDate, setFilteredDate] = useState<Dayjs>(dayjs());

  return (
    <PageLayout
      title="ダッシュボード"
      headerRight={<DashboardAction filteredDate={filteredDate} />}
    >
      <DashboardContent filteredDate={filteredDate} setFilteredDate={setFilteredDate} />
    </PageLayout>
  );
}
