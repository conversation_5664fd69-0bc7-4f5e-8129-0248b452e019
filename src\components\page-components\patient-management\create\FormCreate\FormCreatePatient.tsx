import { AppDatePicker } from '@/components/common/core/Datepicker';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput, AppTextArea } from '@/components/common/core/Input';
import { AppRadio } from '@/components/common/core/Radio';
import AppSaveReceiveModal from '@/components/common/modal/SaveReceiveModal/SaveReceiveModal';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import { useAppSelector } from '@/config/redux/store';
import { useInsuranceWarning } from '@/hooks/useInsuranceWarning';
import { useKanaAutoFill } from '@/hooks/useKanaAutoFill';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { getUserInfo } from '@/store/auth/selectors';
import { useCheckDuplicatePatientMutation, useCreatePatientMutation } from '@/store/patient/api';
import {
  CheckDuplicatePatientPayload,
  CreatePatientPayload,
  PatientItem,
} from '@/store/patient/type';
import {
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  REGEX_KANA_NAME,
  REGEX_PHONE,
  STATUS_CODES,
} from '@/types/constants';
import { routerPaths } from '@/types/constants/routerPath';
import { ButtonType, ESubmitType } from '@/types/enum';
import { showFlashNotice } from '@/utils/flashNotice';
import { clearSpace, formatDate, formatGender } from '@/utils/helper';
import { Flex, Form, Radio } from 'antd';
import dayjs from 'dayjs';
import { forwardRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './FormCreatePatient.modules.scss';
import { AddressResponse, CreatePatientSchema, EGender, ERegisterType, initialValuesCreatePatient } from './type';
import Button from '@/components/common/core/Button';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { usePostalCodeSearch } from '@/hooks/usePostalCodeSearch';

const { useForm } = Form;

interface IFormCreatePatient {
  submitType: ESubmitType;
}

const FormCreatePatient = forwardRef<any, IFormCreatePatient>(
  ({ submitType }: IFormCreatePatient, ref) => {
    const [form] = useForm<CreatePatientSchema>();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const navigate = useNavigate();
    const [createPatientMutation] = useCreatePatientMutation();
    const [checkDuplicatePatient, { isLoading }] = useCheckDuplicatePatientMutation();
    const { showWarningDialog } = useWarningDialog();
    const { searchPostalCode, isLoading: loadingPostal } = usePostalCodeSearch();

    const { doctors: doctorsData, isLoading: isFetchingDoctorData } = useAppSelector(
      state => state.sharedData
    );

    const [patientInitData, setPatientInitData] = useState<PatientItem>();
    const user = useAppSelector(getUserInfo);
    const { getPath } = useRouterPath();
    const patientListPath = getPath(routerPaths.patientManagement.patientList);
    const dashboardPath = getPath(routerPaths.dashboard);
    const { showInsuranceWarning } = useInsuranceWarning();
    const { handleValuesChange, submitHandler } = useUnsavedWarning(initialValuesCreatePatient);
    useKanaAutoFill(form, 'name', 'kana');

    const handleClickSubmit = async (value: CreatePatientSchema) => {
      if (isLoading) return;
      try {
        const payload: CheckDuplicatePatientPayload = {
          name: clearSpace(value.name),
          birthday: dayjs(value.birthday).format(DATE_FORMATS.SUBMIT_DATE),
          gender: Number(value.gender),
          clinic_id: user?.clinic?.clinic_id || null,
        };
        const res = await checkDuplicatePatient(payload).unwrap();
        if (!res.data) {
          handleFinish();
        } else {
          const content = `${clearSpace(value.name)}(${clearSpace(
            value.kana
          )})_${formatDate.toJapanDate(value.birthday)}_${formatGender(Number(value.gender))}`;
          showWarningDialog({
            message: `${content}と同じ患者が登録済みです。本当に登録してもよろしいでしょうか？`,
            onConfirm: handleFinish,
            confirmLabel: '登録',
          });
        }
      } catch (error) {
        console.error(error);
      }
    };
    const handleOpenVisitRegistration = (data: PatientItem) => {
      setIsModalOpen(true);
      setPatientInitData(data);
    };
    const handleFinish = async () => {
      try {
        const newPatientEmail = form.getFieldValue('email')?.trim();
        const newPatientCmt = form.getFieldValue('patient_cmt')?.trim();
        const payload: CreatePatientPayload = {
          kana: clearSpace(form.getFieldValue('kana')),
          name: clearSpace(form.getFieldValue('name')),
          birthday: dayjs(form.getFieldValue('birthday')).format(DATE_FORMATS.SUBMIT_DATE),
          gender: Number(form.getFieldValue('gender')),
          address1: clearSpace(form.getFieldValue('address1')),
          cellphone: clearSpace(form.getFieldValue('cellphone')),
          post: form.getFieldValue('post')?.trim(),
          patient_cmt: newPatientCmt?.length ? newPatientCmt : undefined,
          email: newPatientEmail?.length ? newPatientEmail : undefined,
          address2: clearSpace(form.getFieldValue('address2')),
        };

        await createPatientMutation(payload)
          .unwrap()
          .then(res => {
            if (res.status === STATUS_CODES.INVALID_FIELD) {
              const fields = Object.entries<string[]>(res.data).map(([name, msgs]) => ({
                name,
                errors: msgs,
              }));
              form.setFields(fields as any);
              return;
            }
            if (res.status === STATUS_CODES.OK) {
              if (submitType === ESubmitType.SAVE) {
                navigate(patientListPath);
                showFlashNotice({
                  type: 'success',
                  message: NOTICE_COMMON_MESSAGE.CREATED_PATIENT,
                });
              }
              if (submitType === ESubmitType.SAVE_AND_RECEIVE) {
                showFlashNotice({
                  type: 'success',
                  message: NOTICE_COMMON_MESSAGE.CREATED_PATIENT,
                });
                showInsuranceWarning(() => {
                  handleOpenVisitRegistration(res.data);
                });
              }
            } else {
              showFlashNotice({
                type: 'error',
                message: res.message,
              });
            }
          });
      } catch (error) {
        console.error(error);
      }
    };
    const handlePostalCodeSearch = async (postalCode: string) => {
      if (postalCode.length < 7) return;
      const address = await searchPostalCode(postalCode)
      form.setFieldValue('address1', (address));
    }
    return (
      <>
        <Form
          ref={ref}
          form={form}
          layout="vertical"
          initialValues={initialValuesCreatePatient}
          onValuesChange={handleValuesChange}
          onFinish={values => {
            submitHandler();
            handleClickSubmit(values);
          }}
          className="form-create-patient"
        >

          <div className="register-type mb-4">
            <Flex flex={1}>
              <AppFormItem name="register_type" layout="vertical">
                <Radio.Group>
                  <AppRadio value={ERegisterType.MANUAL}>手動登録</AppRadio>
                </Radio.Group>
              </AppFormItem>
            </Flex>
          </div>
          <AppBlockHasHeader icon={<Icon name="userFile" color="white" />} title="基本情報">
            <div className="content-info">
              <Flex gap={24} flex={1} className="mb-4">
                <AppFormItem
                  name="name"
                  label="氏名 (漢字)"
                  layout="vertical"
                  required
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (漢字)'),
                    },
                  ]}
                >
                  <AppInput
                    name="name"
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    maxLength={64}
                  />
                </AppFormItem>
                <AppFormItem
                  name="kana"
                  label="氏名 (カナ)"
                  layout="vertical"
                  required
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (カナ)'),
                    },
                    {
                      pattern: new RegExp(REGEX_KANA_NAME),
                      message: ERROR_COMMON_MESSAGE.ENTER_ONLY_KANA,
                    },
                  ]}
                >
                  <AppInput maxLength={64} placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} />
                </AppFormItem>
              </Flex>
              <Flex gap={24} className="mb-4">
                <AppFormItem
                  name="birthday"
                  label="生年月日"
                  layout="vertical"
                  required
                  rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('生年月日') }]}
                >
                  <AppDatePicker
                    disabledDate={current => current && current.isAfter(dayjs(), 'day')}
                    size="small"
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                    style={{ height: '44px' }}
                    format={DATE_FORMATS.DATE}
                    inputReadOnly={false}
                  />
                </AppFormItem>
                <AppFormItem
                  name="gender"
                  label="性別"
                  layout="vertical"
                  required
                  rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('性別') }]}
                >
                  <Radio.Group className="radio-button">
                    <AppRadio value={EGender.MALE}>男性</AppRadio>
                    <AppRadio value={EGender.FEMALE}>女性</AppRadio>
                  </Radio.Group>
                </AppFormItem>
              </Flex>
              <Flex gap={24} className="mb-4">
                <Flex gap={12} className='w-full'>
                  <AppFormItem
                    name="post"
                    label="郵便番号（ハイフンなし）"
                    layout="vertical"
                    required
                    rules={[
                      { required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('郵便番号') },
                      { min: 7, max: 7, message: ERROR_COMMON_MESSAGE.MAX_LENGTH_NUMBER(7) },
                    ]}
                  >
                    <Flex gap={12}>
                      <AppInput maxLength={7} placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} />
                      <Button disabled={loadingPostal} customType={ButtonType.SECONDARY_COLOR} customSize='lg' onClick={() => handlePostalCodeSearch(form.getFieldValue('post'))}>検索</Button>
                    </Flex>
                  </AppFormItem>
                </Flex>

                <Flex gap={24} className="w-full">
                  <AppFormItem
                    name="address1"
                    label="住所"
                    layout="vertical"
                    validateFirst
                    required
                    rules={[
                      { required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('住所') },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!getFieldValue('address2')) {
                            return Promise.reject(new Error(ERROR_COMMON_MESSAGE.REQUIRED('住所')));
                          }
                          if (
                            !/\d/.test(getFieldValue('address2')?.toString()) &&
                            !/\d/.test(value?.toString())
                          ) {
                            return Promise.reject(
                              new Error(ERROR_COMMON_MESSAGE.REQUIRED_NUMBER_IN_ADDRESS)
                            );
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <AppInput
                      maxLength={100}
                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    />
                  </AppFormItem>
                  <AppFormItem
                    name="address2"
                    className="address2"
                    layout="vertical"
                    rules={[{ required: true, message: '' }]}
                  >
                    <AppInput
                      onChange={() => form.validateFields(['address1'])}
                      maxLength={100}
                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                    />
                  </AppFormItem>
                </Flex>
              </Flex>
              <Flex gap={24} className="mb-4">
                <AppFormItem
                  name="cellphone"
                  label="電話番号"
                  layout="vertical"
                  validateFirst
                  required
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('電話番号'),
                    },
                    {
                      validator: (_, v: string) => {
                        if (!v) return Promise.resolve();
                        if (v.length > 20) {
                          return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_HYPHEN));
                        }
                        if (!REGEX_PHONE.test(v)) {
                          return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_HYPHEN));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <AppInput maxLength={20} />
                </AppFormItem>
                <AppFormItem
                  name="email"
                  label="メールアドレス"
                  layout="vertical"
                  validateFirst
                  rules={[
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (
                          value &&
                          (getFieldValue('email')?.length < 6 ||
                            getFieldValue('email')?.length > 255)
                        ) {
                          return Promise.reject(
                            new Error(ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(6, 254))
                          );
                        }
                        return Promise.resolve();
                      },
                    }),
                    { type: 'email', message: ERROR_COMMON_MESSAGE.EMAIL_INVALID },
                  ]}
                >
                  <AppInput
                    minLength={6}
                    maxLength={255}
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                  />
                </AppFormItem>
              </Flex>
              <Flex className="mb-4">
                <AppFormItem name="patient_cmt" label="患者のメモ" layout="vertical">
                  <AppTextArea
                    rows={4}
                    maxLength={1000}
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                  />
                </AppFormItem>
              </Flex>
            </div>
          </AppBlockHasHeader>
        </Form>
        {isModalOpen && (
          <AppSaveReceiveModal
            initialData={patientInitData}
            isModalOpen
            onCancel={() => setIsModalOpen(false)}
            onSubmitVisit={() => navigate(dashboardPath)}
            doctorList={doctorsData || []}
            isLoadingInitialData={isFetchingDoctorData}
          />
        )}
      </>
    );
  }
);

export default FormCreatePatient;
