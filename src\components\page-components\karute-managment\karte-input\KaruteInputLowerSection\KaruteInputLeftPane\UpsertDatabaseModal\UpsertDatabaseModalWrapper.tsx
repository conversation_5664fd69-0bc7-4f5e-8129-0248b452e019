import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import React, { createContext, useRef } from 'react';
import { useKaruteInput } from '../../../provider';
import UpsertDatabaseModal from './UpsertDatabaseModal';
import { GetDiseaseBase } from './UpsertDatabaseForm/types/database_form_get';

export const DatabaseMapContext = createContext<{
  virtualDatabaseMap: React.MutableRefObject<Partial<GetDiseaseBase>[]>;
}>({
  virtualDatabaseMap: { current: [] },
});

type ModalProps = Omit<React.ComponentProps<typeof UpsertDatabaseModal>, 'unsavedHandlers'>;

function UpsertDatabaseModalContent(props: ModalProps) {
  const { ignoreSchemaNav } = useKaruteInput();
  const stableInit = useRef({}).current;

  const { handleValuesChange, submitHandler, resetInitial } = useUnsavedWarning(stableInit, {
    ignoreNavigation: ignoreSchemaNav,
  });

  const unsavedHandlers = useRef({
    unsavedChange: handleValuesChange,
    unsavedSubmit: submitHandler,
    resetInitial,
  });

  unsavedHandlers.current.unsavedChange = handleValuesChange;
  unsavedHandlers.current.unsavedSubmit = submitHandler;
  unsavedHandlers.current.resetInitial = resetInitial;
  return <UpsertDatabaseModal {...props} unsavedHandlers={unsavedHandlers.current} />;
}
export default function UpsertDatabaseModalWrapper(props: ModalProps) {
  const virtualDatabaseMap = useRef<Partial<GetDiseaseBase>[]>([]);

  return (
    <DatabaseMapContext.Provider value={{ virtualDatabaseMap }}>
      <UpsertDatabaseModalContent {...props} />
    </DatabaseMapContext.Provider>
  );
}

