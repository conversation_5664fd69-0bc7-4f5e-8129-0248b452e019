import { Dayjs } from 'dayjs';
import { DiseaseImage, EStatus } from '.';
import { InjuryNameIdentifier } from '@/components/page-components/karute-managment/karte-input/InjuryNameModal/type';
export enum EPaymentType {
  HEALTH_INSURANCE = '1',
  TRANSPORT_ACCIDENT = '2',
  WORK_ACCIDENT = '3',
  SELF_PAID = '0',
}
export const paymentMap = {
  [EPaymentType.HEALTH_INSURANCE]: '保険',
  [EPaymentType.TRANSPORT_ACCIDENT]: '自賠責',
  [EPaymentType.WORK_ACCIDENT]: '労災',
  [EPaymentType.SELF_PAID]: '自費',
};
const paymentOrder: EPaymentType[] = [
  EPaymentType.HEALTH_INSURANCE,
  EPaymentType.TRANSPORT_ACCIDENT,
  EPaymentType.WORK_ACCIDENT,
  EPaymentType.SELF_PAID,
];

export const paymentOptions = paymentOrder.map(value => ({
  value,
  label: paymentMap[value],
}));
export interface MondaiItem extends Omit<InjuryNameIdentifier, 'name'> {
  tmp_id?: string; // for navigating in UI only
  disease_base_id?: string | number | null;
  trauma_type?: number; // default 1 - Mondai - set at handleSubmit
  trauma_counter?: number;
  trauma_name?: string;
  visit_date: string | null; // 初診日 - ISO date string, e.g., "2025-04-15"
  visit_id: number; // id of visit date
  // injury_name
  injury_name: string; // #${index} 問題名又は負傷名
  injury_name_other: string; // #${index} 問題名又は負傷名
  bui_cd?: number | null;
  injury_date: Dayjs | string | null; // 負傷日
  subjective: string; // 主訴 (S)
  present_illness: string; // 現病歴 (S)
  objective: string; // 現症 (O)
  assessment: string; // 病態把握 (A)
  plan: string; // 施術計画 (P)
  payment_type: EPaymentType | null; // 施術 (T) - 保険, 自賠責, 労災, 自費
  option_checked?: boolean | null | 1 | 0; // オプション checkbox
  treatment?: string | null; // treatment
  remarks?: string | null; // 備考 (R) - optional
  doctor_id: number | null; // 施術師
  status?: EStatus;
  objective_images: DiseaseImage[];
  treatment_images: DiseaseImage[];

  // image
  first_keika_date?: string; // use only for FE to filter visit_date
}
export interface DatabaseSchema {
  database: MondaiItem[];
  patient_medical_history: {
    personal: string | null;
    social: string | null;
    family: string | null;
  };
}
