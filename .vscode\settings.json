{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.eol": "\n", "editor.tabSize": 2, "prettier.semi": true, "prettier.singleQuote": true, "scss.validate": true, "css.validate": true, "editor.quickSuggestions": {"strings": true}, "editor.suggest.showWords": true, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact", "scss": "css"}}