import { GetKaruteDoctorItem, GetKaruteHokenCourseItem } from '@/store/karute/type';

export * from './database_form_upsert';
export * from './list_database';
export * from './database_schema';
export enum EStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}

export interface RegisteredVisit {
  value: string;
  label: string;
  visit_id: number;
  courses: {
    id: number;
    name: string;
  }[];
  doctor?: GetKaruteDoctorItem | null;
  payment_type_checked: string | null;
  addon_option_checked: boolean;
  hoken_courses?: GetKaruteHokenCourseItem[];
}
