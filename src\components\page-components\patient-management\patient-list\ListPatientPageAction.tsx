import { useNav } from '@/hooks/useNav';
import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { routerPaths } from '@/types/constants/routerPath';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import { useLazyGetCsvPatientListQuery } from '@/store/patient/api';
import { downloadBlobFile } from '@/utils/helper';
import { useListPatient } from './provider/ListPatientProvider';
import { useState } from 'react';
import { useRouterPath } from '@/hooks/useRouterPath';
import dayjs from 'dayjs';
import { DATE_FORMATS } from '@/types/constants';

export default function ListPatientPageAction() {
  const navigate = useNav();
  const { queryParams } = useListPatient();
  const [isDownloading, setIsDownloading] = useState(false);
  const [fetchCsvPatientList] = useLazyGetCsvPatientListQuery();
  const { getPath } = useRouterPath();
  const patientCreatePath = getPath(routerPaths.patientManagement.patientCreate);
  const getCsvPatientList = async () => {
    setIsDownloading(true);
    (await fetchCsvPatientList(queryParams)
      .then(res =>
        downloadBlobFile(
          res.data as Blob,
          `patientlist_${dayjs().format(DATE_FORMATS.DATE_TIME_CSV)}.csv`
        )
      )
      .finally(() => {
        setIsDownloading(false);
      })) as any;
  };
  return (
    <Flex gap="small">
      <Button
        customType={ButtonType.SECONDARY_COLOR}
        customSize="md"
        onClick={getCsvPatientList}
        loading={isDownloading}
      >
        <Icon name="download" width={20} height={20} />
        CSV出力
      </Button>
      <Button
        customType={ButtonType.SECONDARY_COLOR}
        customSize="md"
        onClick={() => navigate(patientCreatePath)}
        disabled={isDownloading}
      >
        <Icon name="addPerson" width={20} height={20} />
        患者登録
      </Button>
    </Flex>
  );
}
