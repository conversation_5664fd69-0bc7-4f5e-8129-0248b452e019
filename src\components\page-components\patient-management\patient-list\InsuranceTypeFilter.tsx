import Icon from '@/components/common/core/Icon';
import { HuggedTabs } from '@/components/common/core/Tabs';
import { DEFAULT_INSURANCE_FILTER, patientListFilterItems } from '@/types/constants/patient';
import { EInsurance } from '@/types/enum';
import { Insurance } from '@/types/interface';
import { FC, useMemo } from 'react';
import styles from './ListPatientPage.module.scss';
import { useListPatient } from './provider/ListPatientProvider';

const InsuranceTypeFilter: FC = () => {
  const {
    isFetching,
    activeFilterValues,
    setActiveFilterValues,
    insuranceCount,
    handleChangeInsurance,
  } = useListPatient();

  const mapInsuranceType = useMemo(() => {
    if (!Array.isArray(insuranceCount)) return {};

    return insuranceCount.reduce((acc, curr) => {
      acc[curr.type] = curr.count;
      return acc;
    }, {} as Record<Insurance, number>);
  }, [insuranceCount]);

  const onFilterChange = (key: EInsurance) => {
    if (isFetching) {
      return;
    }

    let newActiveFilters = { ...activeFilterValues };

    setActiveFilterValues(prev => {
      // If "ALL" is selected, reset to the default filter state
      if (key === EInsurance.ALL) {
        newActiveFilters = { ...DEFAULT_INSURANCE_FILTER };
        return newActiveFilters;
      }

      // Toggle the selected filter
      const updatedFilters = { ...prev, [key]: !prev[key] };

      // Deselect "ALL" if a specific filter is selected
      updatedFilters[EInsurance.ALL] = false;

      // Get all filters keys except "ALL"
      const filterKeys = patientListFilterItems
        .map(item => item.value)
        .filter(k => k !== EInsurance.ALL);

      // Count how many specific filters are currently selected
      const activeCount = filterKeys.filter(k => updatedFilters[k]).length;

      // If none or all are selected, reset to the default state
      if (activeCount === 0 || activeCount === filterKeys.length) {
        newActiveFilters = { ...DEFAULT_INSURANCE_FILTER };
        return newActiveFilters;
      }

      // Otherwise, return the updated filter state
      newActiveFilters = updatedFilters;
      return updatedFilters;
    });

    // Update form values based on the active filters

    handleChangeInsurance(
      Object.entries(newActiveFilters)
        .filter(([, value]) => value)
        .map(([key]) => String(key))
        .join(',')
    );
  };

  const tabItems = patientListFilterItems.map(item => ({
    key: item.key,
    label: item.label,
    value: item.value,
    count: (mapInsuranceType as Record<Insurance, number>)?.[item.key] ?? 0,
  }));

  return (
    <div className={styles.insurance_type_filter}>
      <p className={styles.filter_icon}>
        <Icon name={'filter'} width={20} height={20} />
        <span>保険者種別で絞込</span>
      </p>
      <HuggedTabs
        items={tabItems}
        activeValues={activeFilterValues}
        onChange={key => {
          onFilterChange(key as EInsurance);
        }}
      />
    </div>
  );
};

export default InsuranceTypeFilter;
