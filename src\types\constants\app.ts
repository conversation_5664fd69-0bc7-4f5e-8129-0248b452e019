import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';

export const LOCAL_STORAGE_KEY = {
  ACCESS_TOKEN: 'accessToken',

  INJURY_NAME: 'injuryName',
} as const;

export const FE_DEFINED_META_KEYS = {
  KARUTE_INPUT_PANES: {
    MAIN: 'karuteMainContentVerticalSizes',
    LOWER_SECTION: {
      MAIN: 'karuteLowerSectionHorizontalSizes',
      RIGHT: 'karuteLowerSectionVerticalSizes',
    },
  },
} as const;
export const SPLIT_SCREEN_DEFAULT_RATIO = {
  LOWER_HORIZONTAL_SIZES: [30, 70],
  LOWER_VERTICAL_SIZES: [22.186955743281736, 77.81304425671826],
  MAIN_VERTICAL_SIZES: [22, 78],
};
dayjs.extend(isoWeek);
const now = dayjs();
export const initDateRangeReport = [
  now.startOf('isoWeek').format('YYYY-MM-DD'),
  now.endOf('isoWeek').format('YYYY-MM-DD'),
];

export const PHONE_NUMBER_TOTAL_DIGITS = 11 as const;
