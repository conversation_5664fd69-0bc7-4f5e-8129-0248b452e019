// Screen: K100_デース登録

import { Col, Flex, Form, Row, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';

// Components
import Button from '@/components/common/core/Button';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppTextArea } from '@/components/common/core/Input';
import { AppModal } from '@/components/common/core/Modal';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import { SetDatabaseFromHistoryModal } from './SetDatabaseFromHistoryModal';
import {
  UpsertDatabaseForm,
  UpsertDatabaseFormRef,
  UpsertDatabaseFormSchema,
} from './UpsertDatabaseForm';
import {
  CourseItem,
  CourseTableRef,
  ListCourseTable,
} from './UpsertOptionListForm/ListCourseTable';
import {
  ListOptionTable,
  OptionItem,
  OptionTableRef,
  initOptionData,
} from './UpsertOptionListForm/ListOptionTable';

// Hooks
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import {
  useFindDuplicateInjuryMutation,
  useGetDatabaseVersionQuery,
  useGetPatientVisitRegisteredQuery,
  useUpsertDatabaseMutation,
} from '@/store/karute/api';
import { useKaruteInput } from '../../../provider';

// Types
import {
  ButtonType,
  EDatabaseMedicalField,
  EKaruteServieId,
  IsReferenceModalOpen,
} from '@/types/enum';
import { KaruteMondai } from '@/types/interface/KaruteMondai';
import {
  DatabaseSchema,
  DatabaseSubmitSchema,
  EPaymentType,
  EStatus,
  MondaiItem,
  RegisteredVisit,
  SubmitDiseaseBase,
} from './UpsertDatabaseForm/types';
import { DoctorByVisitDate, GetDiseaseBase } from './UpsertDatabaseForm/types/database_form_get';

import { getAuth } from '@/store/auth/selectors';
// COMMON CONSTANTS
import { AppDivider } from '@/components/common/core/Divider/AppDivider';
import { trimField } from '@/components/page-components/visit-management/dashboard/VisitDetail/helper';
import { setChanged } from '@/store/karute';
import { mondaiThunks, setSelectedMondai } from '@/store/karute/mondaiSlice';
import { DiseaseHistoryItem } from '@/store/karute/type';
import { keikaApi } from '@/store/keika/api';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  DEFAULT_TRAUMA_TYPE,
  ERROR_COMMON_MESSAGE,
  K101_SUFFIX_WARNING_DUPLICATE_INJURY_NAME,
  K107_SUFFIX_WARNING_FREE_INJURY_NAME,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  SPACE,
  STATUS_CODES,
  SUBMIT_BTN,
  WARNING_DATABASE_MSG,
  databaseEmptyWarningField,
} from '@/types/constants';
import { clearSpace, isInjuryNameEditable, showFlashNotice } from '@/utils';
import LabelRender from './UpsertDatabaseForm/LabelRender';
import { UnsavedHandlers } from '@/types/interface';
import styles from './UpsertDatabaseModal.module.scss';
import { ReferenceModal } from '../../ReferenceModal';
import { DatabaseMapContext } from './UpsertDatabaseModalWrapper';
const MAX_TEXT_LENGTH = 1000;

type Props = {
  mode: 'upsert' | 'change_type';
  handleClose: () => void;
  onSubmitChangeType?: ({ ...args }: Partial<KaruteMondai>) => void;
  mondai?: Partial<KaruteMondai & { service_id: number; rowId?: string }>;
  initialValues?: UpsertDatabaseFormSchema;
  disease_base_id?: number;
  karuteId?: number; //-> use for select from history
  setSelectedKeys?: React.Dispatch<React.SetStateAction<string[]>>;
};
const concatField = (current: string | undefined, newValue: string | undefined) =>
  current || newValue
    ? trimField(
        `${current ?? ''}${current && newValue ? '\n' : ''}${newValue ?? ''}`,
        MAX_TEXT_LENGTH
      )
    : '';
function UpsertDatabaseModal({
  mode,
  handleClose,
  initialValues,
  disease_base_id, // for show history
  mondai, // for change type
  onSubmitChangeType,
  unsavedHandlers,
  setSelectedKeys,
}: Props & { unsavedHandlers: UnsavedHandlers }) {
  const hasSetUp = useRef(false);
  const hasOpenSchemaSetUp = useRef(false);

  const courseTableRef = useRef<CourseTableRef>(null);
  const optionTableRef = useRef<OptionTableRef>(null);
  const formRef = useRef<UpsertDatabaseFormRef>(null);
  const { id, serviceId, schemaType, disease, diseaseBaseId } = useParams<{
    id: string; // number
    serviceId: string; // number
    schemaType: SchemaType;
    disease: DiseaseType;
    diseaseBaseId: string; // number
  }>();

  // States
  const [isSetFromHistoryModalOpen, setIsSetFromHistoryModalOpen] = useState<number | null>(null);
  const [isFirstActiveMondai, setIsFirstActiveMondai] = useState<Record<number, MondaiItem | null>>(
    {}
  );
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState<IsReferenceModalOpen>(null);

  // Store
  const auth = useAppSelector(getAuth);
  const { queryParams } = useAppSelector(state => state.karute.mondais);
  const dispatch = useAppDispatch();
  // Course
  const [courseDataSource, setCourseDataSource] = useState<CourseItem[]>([]);
  const [courseSelectedKeys, setCourseSelectedKeys] = useState<string[]>([]);
  // Options
  const [optionDataSource, setOptionDataSource] = useState<OptionItem[]>([]);
  const [optionSelectedKeys, setOptionSelectedKeys] = useState<string[]>([]);

  // Custom hooks
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const {
    doctorList,
    isInitalLoading: isFetchingKarute,
    setDrawnImages,
    karuteDetailData,
  } = useKaruteInput();
  const { unsavedChange, unsavedSubmit, resetInitial } = unsavedHandlers;

  const patientId = karuteDetailData?.patient_id;
  const { data: patientVisitRegisteredData, isFetching: isFetchingPatientVisitRegisteredData } =
    useGetPatientVisitRegisteredQuery(
      {
        service_id: mondai?.service_id || (Number(serviceId) as EKaruteServieId),
        patient_id: patientId!,
      },
      {
        skip: !patientId || !serviceId,
      }
    );

  const { data: databaseLatestVersion, isFetching: isFetchingDatabaseVersion } =
    useGetDatabaseVersionQuery(
      {
        service_id: mondai?.service_id || (Number(serviceId) as EKaruteServieId),
        karute_id: id!,
      },
      {
        skip: !serviceId,
      }
    );
  const [handleCreateKaruteMutation, { isLoading: isSubmittingDatabase }] =
    useUpsertDatabaseMutation();
  const [handleFindDuplicateInjuryMutation, { isLoading: isCheckingDuplicateInjury }] =
    useFindDuplicateInjuryMutation();
  const [form] = Form.useForm<DatabaseSchema>();

  const allRegisteredVisit: RegisteredVisit[] = useMemo(() => {
    return (patientVisitRegisteredData?.data ?? []).map(item => {
      return {
        value: dayjs(item.registration_date).format(DATE_FORMATS.DATE),
        label: dayjs(item.registration_date).format(DATE_FORMATS.DATE),
        visit_id: item.visit_id,
        courses: (item?.selected_jihi_courses ?? []).map(item => ({
          id: item.course_id,
          name: item.course_name,
        })),
        doctor: item.doctor,
        payment_type_checked:
          item?.payment_type_checked !== undefined && item?.payment_type_checked != null
            ? String(item.payment_type_checked)
            : null,
        addon_option_checked: item.addon_option_checked,
        hoken_courses: item?.hoken_courses ?? [],
      };
    });
  }, [patientVisitRegisteredData]);
  const allRegisteredMap = useMemo(
    () => new Map<string, RegisteredVisit>(allRegisteredVisit.map(item => [item.value, item])),
    [allRegisteredVisit]
  );
  const submittingAction = useMemo(() => {
    return isSubmittingDatabase || isCheckingDuplicateInjury;
  }, [isSubmittingDatabase, isCheckingDuplicateInjury]);
  const loadingAction = useMemo(() => {
    return isFetchingPatientVisitRegisteredData || isFetchingDatabaseVersion || isFetchingKarute;
  }, [isFetchingPatientVisitRegisteredData, isFetchingDatabaseVersion, isFetchingKarute]);
  const initialValuesMemo: UpsertDatabaseFormSchema & { karute_id?: number } = useMemo(() => {
    if (!databaseLatestVersion?.data) {
      return {};
    }
    return {
      karute_disease_base_id: databaseLatestVersion?.data?.karute_disease_base_id,
      database: databaseLatestVersion?.data?.disease_bases,
      patient_medical_history: databaseLatestVersion?.data?.patient_medical_history,
      patient_options: databaseLatestVersion?.data?.patient_options,
      patient_courses: databaseLatestVersion?.data?.patient_courses,
      ...initialValues,
      karute_id: databaseLatestVersion?.data?.karute_id,
    };
  }, [databaseLatestVersion]);

  const initialDoctorByVisitDate = useMemo(() => {
    // Get registered doctor from visit and previous mondai -> avoid deleted doctor
    const diseaseDoctors = (databaseLatestVersion?.data?.disease_bases ?? []).map(item => ({
      disease_base_id: item.disease_base_id as number,
      visit_date: item.visit_date,
      user_id: item.doctor_id,
      kanji_name: item.doctor_name,
    }));

    const allDoctors = [...diseaseDoctors];
    const result = allDoctors.reduce<DoctorByVisitDate>((acc, item) => {
      const { disease_base_id, user_id, kanji_name } = item;

      if (!disease_base_id || !user_id || !kanji_name) return acc;

      if (acc[disease_base_id]) {
        if (
          !acc[disease_base_id].doctors.some(
            doctor => doctor.user_id === user_id && doctor.kanji_name === kanji_name
          )
        ) {
          acc[disease_base_id].doctors.push({ user_id, kanji_name });
        }
      } else {
        acc[disease_base_id] = {
          doctors: [{ user_id, kanji_name }],
          visit_date: dayjs(item.visit_date).format(DATE_FORMATS.DATE),
        };
      }

      return acc;
    }, {});
    return result;
  }, [databaseLatestVersion]);
  const availableRegisteredVisit = useMemo(() => {
    const databaseList = initialValuesMemo?.database ?? [];
    const latestVisitDate = databaseList[databaseList.length - 1]?.visit_date;

    if (!latestVisitDate) return allRegisteredVisit ?? [];

    return (allRegisteredVisit ?? []).filter(
      item =>
        dayjs(item.value).isSame(dayjs(latestVisitDate)) ||
        dayjs(item.value).isAfter(dayjs(latestVisitDate))
    );
  }, [allRegisteredVisit, initialValuesMemo]);

  // functions
  const calculateFirstActiveMondai = useCallback((database: MondaiItem[]): void => {
    const visitDateMap: Record<string, number> = {};
    const firstActiveMap: Record<number, MondaiItem | null> = {};
    const visitDateGroups: Record<string, { index: number; mondai: MondaiItem }[]> = {};

    // Group by visit_date
    database.forEach((mondai, index) => {
      const visitDate = mondai?.visit_date as string;
      if (visitDate) {
        if (!visitDateGroups[visitDate]) {
          visitDateGroups[visitDate] = [];
        }
        visitDateGroups[visitDate].push({ index, mondai });
      } else {
        firstActiveMap[index] = null;
      }
    });

    for (const [visitDate, mondaiList] of Object.entries(visitDateGroups)) {
      let firstActive: { index: number; mondai: MondaiItem } | undefined = undefined;

      for (const item of mondaiList) {
        if (item.mondai.status === EStatus.ACTIVE) {
          firstActive = item;
          break;
        }
      }

      if (firstActive) {
        visitDateMap[visitDate] = firstActive.index;
        firstActiveMap[firstActive.index] = firstActive.mondai;
        // Set all other mondai to null -> for consistency

        for (const { index } of mondaiList) {
          if (index !== firstActive.index) {
            firstActiveMap[index] = null;
          }
        }
      } else {
        // show full form for first mondai by default : if there's no active mondai in the same day
        const first = mondaiList[0];
        visitDateMap[visitDate] = first.index;
        firstActiveMap[first.index] = { ...first.mondai, status: EStatus.ACTIVE };
        // Set all other mondai to null -> for consistency
        for (let i = 1; i < mondaiList.length; i++) {
          firstActiveMap[mondaiList[i].index] = null;
        }
      }
    }

    setIsFirstActiveMondai(firstActiveMap);
  }, []);

  const handleUnsavedValueChange = useCallback(
    ({
      optionData,
      courseData,
      database,
    }: {
      optionData?: OptionItem[];
      courseData?: CourseItem[];
      database?: DatabaseSchema;
    }) => {
      const currentOptionData = optionData ?? optionTableRef.current?.getAllCurrentData();
      const currentCourseData = courseData ?? courseTableRef.current?.getAllCurrentData();
      const currentDatabase: DatabaseSchema = database ?? form.getFieldsValue();
      unsavedChange(null, {
        database: currentDatabase.database,
        patient_medical_history: currentDatabase.patient_medical_history,
        patient_options: currentOptionData,
        patient_courses: currentCourseData,
      });
    },
    [form, unsavedChange]
  );
  const handleAddCourse = useCallback(
    ({
      visitDate,
      visitRegistered,
      isInit,
    }: {
      visitDate: string;
      visitRegistered?: RegisteredVisit;
      isInit?: boolean;
    }) => {
      if (!setCourseDataSource || !setCourseSelectedKeys) return;
      const currentVisitRegistered =
        visitRegistered ?? allRegisteredVisit.find(item => item.value === visitDate);
      let newCourseData: CourseItem | null = null;
      const rowId = uuidv4();
      setCourseDataSource(prev => {
        const exists = prev.some(item => item.treatment_date === visitDate);
        const courseIds = (currentVisitRegistered?.courses ?? []).map(course => String(course.id));

        if (exists) {
          const existingItem = prev.find(item => item.treatment_date === visitDate);
          if (existingItem) {
            const existId = existingItem.rowId;
            setCourseSelectedKeys(prevKeys => [...new Set([...prevKeys, existId])]);
            courseTableRef.current?.scrollToRow(existId);
          }
          // return prev.map(item =>
          //   item.treatment_date === visitDate
          //     ? { ...item, course_ids: [...new Set([...item.course_ids, ...courseIds])] }
          //     : item
          // );
          return prev;
        }

        newCourseData = {
          rowId,
          isInit: true,
          treatment_date: visitDate,
          course_ids: courseIds,
          registration_date: courseIds.length ? dayjs().format(DATE_FORMATS.DATE) : '',
        };

        setCourseSelectedKeys(prevKeys => [...new Set([...prevKeys, rowId])]);

        return [...prev, newCourseData].sort((a, b) =>
          dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
        );
      });
      const currentCourseData = courseTableRef.current?.getAllCurrentData() ?? [];
      if (!isInit) {
        handleUnsavedValueChange({
          courseData: newCourseData
            ? [newCourseData, ...currentCourseData].sort((a, b) =>
                dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
              )
            : currentCourseData,
        });
      }
      if (rowId) {
        courseTableRef.current?.scrollToRow(rowId);
      }
    },
    [
      setCourseDataSource,
      setCourseSelectedKeys,
      allRegisteredVisit,
      courseTableRef,
      handleUnsavedValueChange,
    ]
  );

  const handleAddOption = useCallback(
    ({
      value,
      name,
      visitDate,
      isInit,
    }: {
      value: boolean;
      name: number;
      visitDate?: string;
      isInit?: boolean;
    }) => {
      if (!value || !setOptionDataSource || !setOptionSelectedKeys) return;
      const currentFirstVisitDate =
        visitDate ?? form.getFieldValue(['database', name, 'visit_date']);
      const rowId = uuidv4();
      let newOption: OptionItem | null = null;
      setOptionDataSource(prev => {
        const exists = prev.some(item => item.treatment_date === currentFirstVisitDate);
        if (exists) {
          const existingItem = prev.find(item => item.treatment_date === currentFirstVisitDate);
          const existId = existingItem?.rowId;
          if (existingItem) {
            setOptionSelectedKeys(prevKeys => [...new Set([...prevKeys, existId!])]);
            optionTableRef.current?.scrollToRow(existId!);
          }
          return prev;
        }
        newOption = {
          ...initOptionData,
          rowId,
          isInit: true,
          treatment_date: currentFirstVisitDate
            ? dayjs(currentFirstVisitDate).format(DATE_FORMATS.DATE)
            : dayjs().format(DATE_FORMATS.DATE),
          registration_date: '',
        };
        return [...prev, newOption].sort((a, b) =>
          dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
        );
      });
      setOptionSelectedKeys(prevKeys => [...new Set([...prevKeys, rowId])]);
      const currentOptionData = optionTableRef.current?.getAllCurrentData() ?? [];
      if (!isInit) {
        handleUnsavedValueChange({
          optionData: newOption
            ? [newOption, ...currentOptionData].sort((a, b) =>
                dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
              )
            : currentOptionData,
        });
      }
      if (rowId) {
        optionTableRef.current?.scrollToRow(rowId);
      }
    },
    [form, setOptionDataSource, setOptionSelectedKeys, handleUnsavedValueChange]
  );
  const handleAddNewDatabase = useCallback(
    (isInit?: boolean) => {
      const currentDatabases = form.getFieldValue('database') || [];

      const lastVisit = availableRegisteredVisit?.[0];
      const defaultMondais = getDefaultMondais();
      const newEntry: Partial<MondaiItem> = {
        tmp_id: uuidv4(),
        visit_id: lastVisit?.visit_id,
        visit_date: lastVisit?.value,
        status: EStatus.ACTIVE,
        objective: defaultMondais?.[lastVisit?.value as string]?.objective,
        plan: defaultMondais?.[lastVisit?.value as string]?.plan,
        treatment: defaultMondais?.[lastVisit?.value as string]?.treatment,
        remarks: defaultMondais?.[lastVisit?.value as string]?.remarks,
        treatment_images: defaultMondais?.[lastVisit?.value as string]?.treatment_images,
        objective_images: defaultMondais?.[lastVisit?.value as string]?.objective_images,
        doctor_id: lastVisit?.doctor?.user_id,
        option_checked: lastVisit?.addon_option_checked,
        payment_type: lastVisit?.payment_type_checked as EPaymentType,
      };
      const updatedDatabase = [...currentDatabases, newEntry];
      form.setFieldsValue({
        database: updatedDatabase,
      });

      if (
        lastVisit?.payment_type_checked === EPaymentType.SELF_PAID ||
        lastVisit?.addon_option_checked
      ) {
        handleAddCourse({ visitDate: lastVisit?.value as string, isInit: isInit });
      }
      if (lastVisit?.addon_option_checked) {
        handleAddOption({
          value: true,
          name: currentDatabases.length - 1,
          visitDate: lastVisit?.value as string,
          isInit: isInit,
        });
      }

      if (formRef.current) {
        calculateFirstActiveMondai(updatedDatabase);
        formRef.current.scrollToMondai();
      }
      const currentDatabase = form.getFieldsValue();
      if (!isInit) {
        handleUnsavedValueChange({
          database: { ...currentDatabase, database: updatedDatabase },
        });
      }
    },
    [form, availableRegisteredVisit]
  );

  const mapBackendErrorsToForm = useCallback(
    (errorData: Record<string, string[]>) => {
      const databaseErrors: { name: (string | number)[]; errors: string[] }[] = [];
      const patientOptionsErrors: Record<string, string[]> = {};
      const patientCoursesErrors: Record<string, string[]> = {};

      Object.entries(errorData).forEach(([fieldName, errors]) => {
        const fieldPath = fieldName.split('.').map(part => {
          return isNaN(Number(part)) ? part : Number(part);
        });

        if (fieldPath[0] === 'disease_bases' && fieldPath.length >= 2) {
          fieldPath[0] = 'database';
          databaseErrors.push({
            name: fieldPath,
            errors,
          });
        } else if (fieldPath[0] === 'patient_options' && fieldPath.length === 3) {
          patientOptionsErrors[fieldName] = errors;
        } else if (fieldPath[0] === 'patient_courses' && fieldPath.length === 3) {
          patientCoursesErrors[fieldName] = errors;
        }
      });
      if (Object.keys(patientOptionsErrors).length > 0 && optionTableRef.current) {
        optionTableRef.current.handleOptionErrors(patientOptionsErrors);
      }
      if (Object.keys(patientCoursesErrors).length > 0 && courseTableRef.current) {
        courseTableRef.current.handleCourseErrors(patientCoursesErrors);
      }

      return databaseErrors;
    },
    [form]
  );

  const clearFieldErrors = useCallback(
    (fieldPath: (string | number)[]) => {
      form.setFields([{ name: fieldPath as any, errors: [] }]);
    },
    [form]
  );

  const handleValuesChange = useCallback(
    (changedValues: any) => {
      if (!clearFieldErrors) return;
      if (changedValues.database) {
        Object.entries(changedValues.database).forEach(([index, changedFields]: [string, any]) => {
          const idx = Number(index);
          Object.keys(changedFields).forEach(field => {
            clearFieldErrors(['database', idx, field]);
          });
        });
      }

      if (changedValues.patient_medical_history) {
        Object.keys(changedValues.patient_medical_history).forEach(field => {
          clearFieldErrors(['patient_medical_history', field]);
        });
      }
    },
    [clearFieldErrors]
  );

  const handleOpenReference = (isReferenceModalOpen: IsReferenceModalOpen) => {
    setIsReferenceModalOpen(isReferenceModalOpen);
  };
  const handleSubmitReference = (text: string) => {
    if (!isReferenceModalOpen || !isReferenceModalOpen?.name || !isReferenceModalOpen?.field)
      return;
    const currentValue = form.getFieldValue([
      'database',
      isReferenceModalOpen?.name,
      isReferenceModalOpen?.field as any,
    ]);
    form.setFieldValue(
      ['database', isReferenceModalOpen?.name, isReferenceModalOpen?.field as any],
      concatField(currentValue, text)
    );
  };
  const handleCloseReference = () => {
    setIsReferenceModalOpen(null);
  };
  const closeCleanUpModal = useCallback(() => {
    setDrawnImages({});
    handleClose();
  }, [setDrawnImages, handleClose]);

  const handleValidateInjuryNames = useCallback(
    async (updatedDatabase: SubmitDiseaseBase[]) => {
      const k101Indexes: number[] = [];
      const k107Indexes: number[] = [];

      // const injuryNameMap: Record<string, number[]> = {};
      const statusMap: Record<number, EStatus> = {};
      let k107ApiSuccess = true;
      try {
        const res = await handleFindDuplicateInjuryMutation({
          karute_id: Number(id),
          injuries: updatedDatabase.map(
            ({ disease_base_id, injury_name, status, payment_type, injury_name_other }) => {
              return {
                disease_base_id: disease_base_id ? (disease_base_id as number) : null,
                injury_name: isInjuryNameEditable(payment_type as EPaymentType)
                  ? injury_name_other
                  : injury_name,
                status: status as EStatus,
              };
            }
          ),
        }).unwrap();

        if (res.status === STATUS_CODES.OK) {
          res.data?.forEach((item, index) => {
            if (item.matched) k107Indexes.push(index);
          });
        } else {
          k107ApiSuccess = false;
        }
      } catch (error) {
        console.error('BE injury validation failed', error);
        return;
      }
      updatedDatabase.forEach((item, index) => {
        const status = item?.status ?? EStatus.INACTIVE;
        statusMap[index] = status;

        if (
          item?.payment_type !== EPaymentType.SELF_PAID &&
          serviceId === String(EKaruteServieId.Judo) &&
          !item?.part_type
        ) {
          k101Indexes.push(index);
        }
      });

      const fields: { name: (string | number)[]; errors: string[] }[] = [];
      const filterActive = (arr: number[]) => arr.filter(i => statusMap[i] === EStatus.ACTIVE);

      const activeK101 = filterActive(k101Indexes);
      const activeK107 = filterActive(k107Indexes);

      activeK101.forEach(index =>
        fields.push({
          name: ['database', index, 'injury_name'],
          errors: [ERROR_COMMON_MESSAGE.INVALID_FREE_INJURY_NAME],
        })
      );

      activeK107.forEach(index => {
        fields.push({
          name: [
            'database',
            index,
            isInjuryNameEditable(updatedDatabase[index].payment_type as EPaymentType)
              ? 'injury_name_other'
              : 'injury_name',
          ],
          errors: [ERROR_COMMON_MESSAGE.DUPLICATE_INJURY_NAMES],
        });
      });

      form.setFields(fields as any);

      const prefix = (list: number[]) =>
        list
          .sort((a, b) => a - b)
          .map(i => `#${i + 1}`)
          .join(COMMA_SEPARATED);

      const prefixK101 = prefix(activeK101);
      const prefixK107 = prefix(activeK107);

      if (activeK101.length > 0) {
        showWarningDialog({
          message: (
            <div>
              <p>
                {prefixK101}
                {K101_SUFFIX_WARNING_DUPLICATE_INJURY_NAME.line1}
              </p>
              <p>{K101_SUFFIX_WARNING_DUPLICATE_INJURY_NAME.line2}</p>
            </div>
          ),
          buttons: [
            {
              type: 'confirm',
              label: '閉じる',
              onClick: async () => {
                hideWarningDialog();
                if (activeK107.length > 0) {
                  showWarningDialog({
                    message: `${prefixK107}${K107_SUFFIX_WARNING_FREE_INJURY_NAME}`,
                    buttons: [{ type: 'confirm', label: '閉じる', onClick: hideWarningDialog }],
                  });
                }
              },
            },
          ],
        });
      } else if (activeK107.length > 0) {
        showWarningDialog({
          message: `${prefixK107}${K107_SUFFIX_WARNING_FREE_INJURY_NAME}`,
          buttons: [{ type: 'confirm', label: '閉じる', onClick: hideWarningDialog }],
        });
      }
      return activeK101.length === 0 && activeK107.length === 0 && k107ApiSuccess;
    },
    [serviceId, showWarningDialog, hideWarningDialog, form, id]
  );

  const getDefaultMondais = useCallback((): Record<string, MondaiItem> => {
    const database: MondaiItem[] = form.getFieldValue('database') ?? [];
    // At this point, getDefaultMondais is being called
    // The `visit_date` in the database may have been changed due to a `visit_date` update `handleChangeVisitDate` -> `handleSortMondaiItem`
    // Therefore, we need to remap `visit_date` to this filtered database to ensure correct data mapping
    // `isFirstActiveMondai` ensures that the data is updated correctly after `visit_date` changes
    const filteredDatabase = database
      .map((item, index) => ({
        ...item,
        visit_date: isFirstActiveMondai[index]
          ? isFirstActiveMondai[index].visit_date
          : item.visit_date,
      }))
      .filter((item, index) => {
        const result = !!isFirstActiveMondai[index];
        return result;
      });

    const remapDefaultDatabase = filteredDatabase.reduce(
      (acc: Record<string, MondaiItem>, item) => {
        acc[item.visit_date!] = item;
        return acc;
      },
      {}
    );
    return remapDefaultDatabase;
  }, [isFirstActiveMondai, form]);
  const handleSubmit = useCallback(
    async (
      value: DatabaseSchema,
      updatedDatabase: SubmitDiseaseBase[],
      optionData: OptionItem[],
      courseData: CourseItem[]
    ) => {
      if (!initialValuesMemo?.karute_id) return;

      const validateResult = await handleValidateInjuryNames(updatedDatabase);
      if (!validateResult) {
        return;
      }
      const updateDatabase: DatabaseSubmitSchema = {
        karute_disease_base_id: initialValuesMemo?.karute_disease_base_id,
        karute_id: id!,
        patient_id: patientId!,
        service_id: mondai?.service_id || (Number(serviceId) as EKaruteServieId),
        clinic_id: auth.currentClinicId!,
        disease_bases: updatedDatabase,
        patient_medical_history: {
          ...value?.patient_medical_history,
          patient_id: patientId!,
          service_id: mondai?.service_id || (Number(serviceId) as EKaruteServieId),
        },

        patient_options: optionData.map(item => {
          return {
            patient_id: patientId!,
            ...item,
            registration_date: dayjs(item.registration_date).format(DATE_FORMATS.SUBMIT_DATE),
            treatment_date: dayjs(item.treatment_date).format(DATE_FORMATS.SUBMIT_DATE),
            option_id: Number(item.option_id),
          };
        }),
        patient_courses: courseData.flatMap(item =>
          item.course_ids.length > 0
            ? item.course_ids.map(course_id => ({
                // ...item,
                patient_id: patientId!,
                registration_date: dayjs(item.registration_date).format(DATE_FORMATS.SUBMIT_DATE),
                treatment_date: dayjs(item.treatment_date).format(DATE_FORMATS.SUBMIT_DATE),
                course_id: Number(course_id),
                payment_course_type: item.payment_course_type,
              }))
            : [
                {
                  // ...item,
                  patient_id: patientId!,
                  registration_date: dayjs(item.registration_date).format(DATE_FORMATS.SUBMIT_DATE),
                  treatment_date: dayjs(item.treatment_date).format(DATE_FORMATS.SUBMIT_DATE),
                  course_id: NaN,
                  payment_course_type: item.payment_course_type,
                },
              ]
        ),
      };

      handleCreateKaruteMutation(updateDatabase)
        .unwrap()
        .then(res => {
          if (res.status === STATUS_CODES.INVALID_FIELD) {
            const fields = mapBackendErrorsToForm(res.data as any);
            form.setFields(fields as any);
            return;
          }
          if (mondai && onSubmitChangeType && (res.data?.new_ids ?? []).length > 0) {
            onSubmitChangeType({
              ...mondai,
              disease_base_id: res.data?.new_ids?.[0],
            });
            setSelectedKeys?.(keys => keys.filter(k => k !== mondai.rowId));
            dispatch(setSelectedMondai(undefined));
          }
          if (!mondai) {
            dispatch(
              mondaiThunks.fetchMondai({
                patient_id: patientId!,
                ...queryParams,
              })
            );
          }
          if (res.status === STATUS_CODES.OK) {
            showFlashNotice({
              type: 'success',
              message: NOTICE_COMMON_MESSAGE.DATABASE_CREATED,
            });
            dispatch(keikaApi.util.invalidateTags(['mainKeika']));
            dispatch(keikaApi.util.invalidateTags(['conditionCheckCreateKeika']));
            unsavedSubmit();
            closeCleanUpModal();
          }
          dispatch(setChanged());
        });
    },
    [
      serviceId,
      form,
      handleCreateKaruteMutation,
      initialValuesMemo,
      patientId,
      mapBackendErrorsToForm,
      closeCleanUpModal,
      onSubmitChangeType,
      mondai,
      handleValidateInjuryNames,
      id,
      auth.currentClinicId,
      dispatch,
      queryParams,
      unsavedSubmit,
    ]
  );

  const handleGetWarningRequiredValue = useCallback(
    (value: SubmitDiseaseBase[]) => {
      // K106: show warning empty fields
      const indices = new Set<number>();
      const emptyFields: { label: string; priority: number }[] = [];
      const seenLabels = new Set<string>();

      value.forEach((item, index) => {
        if (item.status === EStatus.INACTIVE) return;

        databaseEmptyWarningField.forEach(field => {
          const fieldValue = item[field.key as keyof SubmitDiseaseBase];
          if (!fieldValue && !seenLabels.has(field.label)) {
            if (field.isCommon && isFirstActiveMondai[index]) {
              indices.add(index + 1);
            } else if (!field.isCommon) {
              indices.add(index + 1);
            }
            if (!seenLabels.has(field.label)) {
              emptyFields.push({ label: field.label, priority: field.priority });
              seenLabels.add(field.label);
            }
          }
        });
      });
      if (indices.size > 0) {
        const sortedIndices = Array.from(indices)
          .sort((a, b) => a - b)
          .map(item => `#${item}`)
          .join(COMMA_SEPARATED);
        const fields = emptyFields
          .sort((a, b) => a.priority - b.priority)
          .map(item => item.label)
          .join(COMMA_SEPARATED);
        return `${sortedIndices}${SPACE.repeat(3)}${fields}${WARNING_DATABASE_MSG}`;
      }

      return '';
    },
    [isFirstActiveMondai]
  );

  const handleFinish = useCallback(
    async (value: DatabaseSchema) => {
      const courseOptionValidate = await Promise.all([
        optionTableRef.current?.handleSubmittingAllEditingRows(),
        courseTableRef.current?.handleSubmittingAllEditingRows(),
      ]);
      if (!courseOptionValidate) return;
      const defaultMondais = getDefaultMondais();
      const hokenCourseDataSourceByVisitDate: Map<string, CourseItem> = new Map();

      const updatedDatabase: SubmitDiseaseBase[] = value.database.map(item => {
        const hokenId = allRegisteredMap
          ?.get(item?.visit_date as string)
          ?.hoken_courses?.find(
            course => String(course.payment_course_type) === item.payment_type
          )?.course_id;
        if (hokenId) {
          const currentHoken = hokenCourseDataSourceByVisitDate.get(item?.visit_date as string);

          hokenCourseDataSourceByVisitDate.set(item?.visit_date as string, {
            rowId: String(hokenId),
            course_ids: [...new Set([...(currentHoken?.course_ids ?? []), String(hokenId)])],
            isInit: true,
            names: [],
            treatment_date: item.visit_date!,
            registration_date: dayjs().format(DATE_FORMATS.DATE),
          });
        }

        const curentVisitDate = item?.visit_date as string;
        const defaultMondaiValue = {
          objective: clearSpace(defaultMondais?.[curentVisitDate]?.objective),
          plan: clearSpace(defaultMondais?.[curentVisitDate]?.plan),
          treatment: clearSpace(defaultMondais?.[curentVisitDate]?.treatment),
          remarks: clearSpace(defaultMondais?.[curentVisitDate]?.remarks),
          objective_images: (defaultMondais?.[curentVisitDate]?.objective_images ?? []).map(
            item => item.disease_base_image_id
          ),
          treatment_images: (defaultMondais?.[curentVisitDate]?.treatment_images ?? []).map(
            item => item.disease_base_image_id
          ),
        };
        const isInjuryNameOther = isInjuryNameEditable(item?.payment_type as EPaymentType);
        return {
          ...item,
          karute_id: id!,
          injury_name: clearSpace(!isInjuryNameOther ? item?.injury_name : ''),
          injury_name_other: clearSpace(isInjuryNameOther ? item?.injury_name_other : ''),
          trauma_type: DEFAULT_TRAUMA_TYPE,
          visit_date: curentVisitDate
            ? dayjs(curentVisitDate).isValid()
              ? dayjs(curentVisitDate).format(DATE_FORMATS.SUBMIT_DATE)
              : null
            : null,
          visit_id: allRegisteredMap?.get(curentVisitDate)?.visit_id as number,
          injury_date: item?.injury_date
            ? dayjs(item?.injury_date).isValid()
              ? dayjs(item.injury_date).format(DATE_FORMATS.SUBMIT_DATE)
              : null
            : null,
          option_checked: item?.option_checked ? 1 : 0,
          // objective: defaultValue?.objective,
          subjective: clearSpace(item?.subjective),
          assessment: clearSpace(item?.assessment),
          // plan: defaultValue?.plan,
          // treatment: defaultValue?.treatment,
          // remarks: defaultValue?.remarks,
          present_illness: clearSpace(item?.present_illness),
          // objective_images: defaultValue?.objective_images.map(item => item.disease_base_image_id),
          // treatment_images: defaultValue?.treatment_images.map(item => item.disease_base_image_id),
          ...(mondai?.mondai_id && { mondai_id: mondai.mondai_id }),
          doctor_id: item?.doctor_id ?? null, // Assert null to prevent undefined

          ...defaultMondaiValue,
        };
      });

      const mergedCourseData = [
        ...(courseOptionValidate[1] ?? []),
        ...hokenCourseDataSourceByVisitDate.values(),
      ];
      const fullWarningMessage = handleGetWarningRequiredValue(updatedDatabase);
      if (fullWarningMessage?.length > 0) {
        // K106: Show warning empty fields
        showWarningDialog({
          message: fullWarningMessage,
          buttons: [
            {
              type: 'cancel',
              label: 'キャンセル',
              onClick: () => {
                hideWarningDialog();
                return;
              },
            },
            {
              type: 'confirm',
              label: '登録する',
              onClick: async () => {
                hideWarningDialog();
                await handleSubmit(
                  value,
                  updatedDatabase,
                  courseOptionValidate[0] ?? [],
                  mergedCourseData
                );
                return;
              },
            },
          ],
        });
        return;
      }

      await handleSubmit(value, updatedDatabase, courseOptionValidate[0] ?? [], mergedCourseData);
      return;
    },
    [handleSubmit, showWarningDialog, hideWarningDialog, handleGetWarningRequiredValue]
  );

  const handleSetFromHistory = useCallback(
    (historyItem: DiseaseHistoryItem) => {
      if (isSetFromHistoryModalOpen === null) return;

      const current = form.getFieldsValue();
      const newDatabase = [...(current.database || [])];

      const currentDatabase = newDatabase[isSetFromHistoryModalOpen as number];

      if (!currentDatabase) {
        return;
      }

      const isInFirstActiveMondai = !!isFirstActiveMondai[isSetFromHistoryModalOpen];

      // Common fields: If not in isFirstActiveMondai, retain values from currentDatabase
      const commonFields = isInFirstActiveMondai
        ? {
            treatment: concatField(currentDatabase?.treatment ?? '', historyItem?.treatment ?? ''),
            objective: concatField(currentDatabase?.objective ?? '', historyItem?.objective ?? ''),
            objective_images: historyItem?.objective_images,
            treatment_images: historyItem?.treatment_images,
            plan: concatField(currentDatabase?.plan ?? '', historyItem?.plan ?? ''),
            remarks: concatField(currentDatabase?.remarks ?? '', historyItem?.remarks ?? ''),
          }
        : {
            treatment: currentDatabase?.treatment ?? '',
            objective: currentDatabase?.objective ?? '',
            objective_images: currentDatabase?.objective_images,
            treatment_images: currentDatabase?.treatment_images,
            plan: currentDatabase?.plan ?? '',
            remarks: currentDatabase?.remarks ?? '',
          };

      // Individual fields applied to the current mondai
      const updatedDatabaseItem = {
        visit_date: currentDatabase?.visit_date,
        visit_id: currentDatabase?.visit_id,
        injury_date: currentDatabase?.injury_date ? dayjs(currentDatabase?.injury_date) : null,
        injury_name: historyItem?.injury_name,
        part_type: historyItem?.part_type,
        part_id: historyItem?.part_id,
        lr_type: historyItem?.lr_type,
        bui_cd: historyItem?.bui_cd,
        subjective: concatField(currentDatabase?.subjective, historyItem?.subjective ?? ''),
        present_illness: concatField(
          currentDatabase?.present_illness,
          historyItem?.present_illness ?? ''
        ),
        assessment: concatField(currentDatabase?.assessment, historyItem?.assessment ?? ''),
        payment_type: currentDatabase?.payment_type,
        option_checked: currentDatabase?.option_checked,
        doctor_id:
          historyItem?.doctor_status === EStatus.ACTIVE
            ? historyItem?.doctor_id
            : currentDatabase?.doctor_id,
        ...commonFields, // Include common fields
      };

      if (isInFirstActiveMondai) {
        // If apply history to active Mondai
        // -> Apply common fields to all databases with the same visit_date
        newDatabase.forEach((db, index) => {
          if (db?.visit_date === currentDatabase?.visit_date) {
            newDatabase[index] = {
              ...newDatabase[index],
              ...commonFields,
            };
          }
        });
      }

      // Update the database at isSetFromHistoryModalOpen with individual and common fields
      newDatabase[isSetFromHistoryModalOpen as number] = {
        ...newDatabase[isSetFromHistoryModalOpen as number],
        ...updatedDatabaseItem,
      };

      // Update the form and close the modal
      form.setFieldsValue({ database: newDatabase });
      const patient_medical_history = form.getFieldValue(['patient_medical_history']);
      handleUnsavedValueChange({ database: { database: newDatabase, patient_medical_history } });
      setIsSetFromHistoryModalOpen(null);
    },
    [form, isSetFromHistoryModalOpen, isFirstActiveMondai, handleUnsavedValueChange]
  );
  const { virtualDatabaseMap } = useContext(DatabaseMapContext);
  useEffect(() => {
    if (
      hasSetUp.current ||
      !initialValuesMemo.karute_id ||
      isFetchingPatientVisitRegisteredData ||
      isFetchingDatabaseVersion ||
      !patientVisitRegisteredData
    )
      return;

    const hokenDataSource: CourseItem[] = [];
    const courseDataSource: CourseItem[] = [];

    (initialValuesMemo?.patient_courses ?? []).forEach(item => {
      const date = item.treatment_date;
      const isJihi =
        String(item?.payment_course_type ?? EPaymentType.SELF_PAID) === EPaymentType.SELF_PAID;
      const targetDataSource = isJihi ? courseDataSource : hokenDataSource;

      const existing = targetDataSource.find(entry => entry.treatment_date === date);
      if (existing) {
        if (!item.course_id) return;
        existing.course_ids.push(String(item.course_id));
        if (new Date(item.registration_date) > new Date(existing.registration_date)) {
          existing.registration_date = item.registration_date;
        }
      } else {
        targetDataSource.push({
          rowId: uuidv4(),
          treatment_date: dayjs(date).format(DATE_FORMATS.DATE),
          course_ids: item.course_id ? [String(item.course_id)] : [],
          registration_date: dayjs(item.registration_date).format(DATE_FORMATS.DATE),
        });
      }
    });
    const optionDataSource: OptionItem[] = (initialValuesMemo?.patient_options ?? []).map(item => ({
      rowId: uuidv4(),
      registration_date: dayjs(item?.registration_date).format(DATE_FORMATS.DATE),
      option_id: item?.option_id ? String(item?.option_id) : undefined,
      quantity: item?.quantity,
      treatment_date: dayjs(item?.treatment_date).format(DATE_FORMATS.DATE),
    }));
    const initialMondai = [
      ...(initialValuesMemo?.database ?? [])
        .map(item => ({
          ...item,
          visit_date: item?.visit_date ? dayjs(item?.visit_date).format(DATE_FORMATS.DATE) : null,
          injury_date: item?.injury_date ? dayjs(item?.injury_date) : null,
          payment_type: String(item?.payment_type) as EPaymentType,
          treatment_images: item?.treatment_images ?? [],
          objective_images: item?.objective_images ?? [],
          first_keika_date: item?.first_keika?.visit_date,
        }))
        .sort((a, b) => {
          // Put null visit_date to end
          if (!a.visit_date && !b.visit_date) return 0;
          if (!a.visit_date) return 1;
          if (!b.visit_date) return -1;
          return dayjs(a.visit_date).diff(dayjs(b.visit_date));
        }),
    ];
    //
    const initialFormValue = {
      database: initialMondai,
      patient_medical_history: {
        personal: clearSpace(initialValuesMemo?.patient_medical_history?.personal),
        social: clearSpace(initialValuesMemo?.patient_medical_history?.social),
        family: clearSpace(initialValuesMemo?.patient_medical_history?.family),
      },
      patient_options: optionDataSource,
      patient_courses: courseDataSource,
    };
    form.setFieldsValue({
      database: initialFormValue.database,
      patient_medical_history: initialFormValue?.patient_medical_history,
    });
    virtualDatabaseMap.current = (initialFormValue.database ?? []).map(item => ({
      payment_type: item.payment_type,
    }));
    resetInitial(initialFormValue);
    setCourseDataSource(courseDataSource);
    setOptionDataSource(optionDataSource);
    if ((initialValuesMemo?.database ?? []).length == 0 || mode === 'change_type') {
      handleAddNewDatabase(true);
    }
    hasSetUp.current = true;
  }, [form, initialValuesMemo, mode, handleAddNewDatabase, availableRegisteredVisit]);

  useEffect(() => {
    // To: set Draw Image when open schema for database list
    // use hasOpenSchemaSetUp to avoid re-run when open in DatabaseForm
    if (
      (initialValuesMemo?.database ?? []).length > 0 &&
      schemaType === SchemaType.DATABASE &&
      (DiseaseType.O === disease || DiseaseType.T === disease) &&
      diseaseBaseId &&
      !hasOpenSchemaSetUp.current
    ) {
      const allMondai = initialValuesMemo.database;
      const currentMondai = (allMondai ?? []).find(item => {
        if (String(item?.disease_base_id) && diseaseBaseId == item?.disease_base_id) {
          return true;
        }
        if (!item?.disease_base_id && item.tmp_id && item.tmp_id == diseaseBaseId) {
          return true;
        }
        return false;
      });
      if (currentMondai) {
        const edittedImage = {
          [SchemaType.DATABASE]: {
            [String(currentMondai?.disease_base_id ?? currentMondai?.tmp_id)]: {
              [DiseaseType.O]: (currentMondai?.objective_images ?? []).map(item => ({
                id: `${item.disease_base_id}_${item.disease_base_image_id}`,
                disease_base_image_id: item.disease_base_image_id,
                url: item.url,
                schema_image_id: item?.schema_image_id,
              })),
              [DiseaseType.T]: (currentMondai?.treatment_images ?? []).map(item => ({
                id: `${item.disease_base_id}_${item.disease_base_image_id}`,
                disease_base_image_id: item.disease_base_image_id,
                url: item.url,
                schema_image_id: item?.schema_image_id,
              })),
            },
          },
        };
        setDrawnImages(edittedImage);
      }
      hasOpenSchemaSetUp.current = true;
    }
  }, [schemaType, disease, diseaseBaseId, initialValuesMemo]);
  const initialDatabaseMap = useMemo(() => {
    const databaseList = initialValuesMemo?.database ?? [];
    const databaseMap = databaseList.reduce((acc, item) => {
      if (typeof item.disease_base_id === 'number') {
        acc[item.disease_base_id] = item;
      }
      return acc;
    }, {} as Record<number, Partial<GetDiseaseBase>>);
    return databaseMap;
  }, [initialValuesMemo]);

  const findMondaiToScroll = useCallback(
    (treatment_date: string) => {
      const mondai = (form.getFieldValue(['database']) ?? []).find(
        (item: MondaiItem, index: number) => {
          if (!isFirstActiveMondai[index]) return false;
          const isSameDate = dayjs(item.visit_date).isSame(dayjs(treatment_date), 'day');
          return isSameDate;
        }
      );

      if (mondai) {
        formRef.current?.scrollToMondai(mondai);
      }
    },
    [form, isFirstActiveMondai]
  );
  const activeMondaiByDate = useMemo(() => {
    if (!isFirstActiveMondai) return {};

    const currentDatabase: MondaiItem[] = form.getFieldValue(['database']) ?? [];
    if (!currentDatabase.length) return {};

    return currentDatabase.reduce((acc, item) => {
      const isJihi = item.payment_type === EPaymentType.SELF_PAID;
      const isOptionChecked = item.option_checked;

      if (item.visit_date && item.status === EStatus.ACTIVE && (isJihi || isOptionChecked)) {
        acc[item.visit_date] = {
          ...item,
          isJihi: acc[item.visit_date]?.isJihi || !!isJihi,
          isOptionChecked: acc[item.visit_date]?.isOptionChecked || !!isOptionChecked,
        };
      }
      return acc;
    }, {} as Record<string, MondaiItem & { isJihi: boolean; isOptionChecked: boolean }>);
  }, [form, isFirstActiveMondai]);

  const isCourseNullableByDate = useMemo(() => {
    const courseObj: { [key: string]: { isJihi: boolean; isOptionChecked: boolean } } = {};

    const currentMondais: MondaiItem[] = form.getFieldValue(['database']) ?? [];

    currentMondais.forEach(item => {
      if (item.status === EStatus.INACTIVE || !item.visit_date) return;

      const isJihi = item.payment_type === EPaymentType.SELF_PAID;
      const isOptionChecked = item.option_checked;

      const key = item.visit_date;

      courseObj[key] = {
        isJihi: courseObj[key]?.isJihi || isJihi,
        isOptionChecked: courseObj[key]?.isOptionChecked || !!isOptionChecked,
      };
    });
    return courseObj;
  }, [isFirstActiveMondai, form]);

  const isCourseNullable = useCallback(
    (treatmentDate: string) => {
      return (
        isCourseNullableByDate[treatmentDate]?.isOptionChecked &&
        !isCourseNullableByDate[treatmentDate]?.isJihi
      );
    },
    [isCourseNullableByDate]
  );

  const isActionable = useCallback(
    (record: OptionItem | CourseItem, mode: 'course' | 'option') => {
      const activeMondai = activeMondaiByDate[record.treatment_date];
      if (!activeMondai) return false;

      if (mode === 'course') {
        const isActionable = activeMondai.isJihi || activeMondai.isOptionChecked;

        return isActionable;
      } else {
        const isActionable = activeMondai.isOptionChecked;

        return isActionable;
      }
    },
    [activeMondaiByDate]
  );

  return (
    <>
      <AppModal
        width="1376px"
        destroyOnClose
        open
        onCancel={closeCleanUpModal}
        isPadding={false}
        zIndex={1500}
      >
        <Spin spinning={submittingAction || loadingAction}>
          <Flex
            justify="space-between"
            align="center"
            className=" px-6"
            style={{
              paddingTop: '15px',
            }}
          >
            <ModalTitle title={'データベース登録'} />
            <Button
              className="mr-6"
              customType={ButtonType.PRIMARY}
              onClick={() => {
                handleAddNewDatabase();
              }}
              disabled={mode === 'change_type'}
              style={{
                width: '120px',
              }}
            >
              <Icon name="plus" />
              新規作成
            </Button>
          </Flex>
          <Flex
            vertical
            gap="middle"
            className={styles.create_database_modal_content}
            // style={{ minHeight: loadingAction ? undefined : 'min-height: calc(100vh - 83px)' }}
          >
            <>
              <Form
                form={form}
                name="upsert_database_form"
                layout="vertical"
                onFinish={value => {
                  handleFinish(value);
                }}
                className="pl-6"
                style={{
                  paddingRight: '18px',
                }}
                onValuesChange={(changedValues, allValues) => {
                  handleValuesChange(changedValues);
                  handleUnsavedValueChange({ database: allValues });
                }}
                scrollToFirstError

                // ={{ behavior: 'instant', block: 'end', focus: true }}
              >
                <UpsertDatabaseForm
                  mondaiServiceId={mondai?.service_id}
                  allRegisteredVisit={allRegisteredVisit}
                  availableRegisteredVisit={availableRegisteredVisit}
                  doctorList={doctorList.map(item => ({
                    kanji_name: item.kanji_name,
                    user_id: item.user_id,
                  }))}
                  initialDoctorByVisitDate={initialDoctorByVisitDate}
                  // Course
                  courseDataSource={courseDataSource}
                  setCourseDataSource={setCourseDataSource}
                  courseSelectedKeys={courseSelectedKeys}
                  setCourseSelectedKeys={setCourseSelectedKeys}
                  // Options
                  optionDataSource={optionDataSource}
                  setOptionDataSource={setOptionDataSource}
                  optionSelectedKeys={optionSelectedKeys}
                  setOptionSelectedKeys={setOptionSelectedKeys}
                  // set from hisotry
                  setIsSetFromHistoryModalOpen={setIsSetFromHistoryModalOpen}
                  disease_base_id={disease_base_id}
                  ref={formRef}
                  hasSetUp={hasSetUp.current}
                  isFirstActiveMondai={isFirstActiveMondai}
                  calculateFirstActiveMondai={calculateFirstActiveMondai}
                  getDefaultMondais={getDefaultMondais}
                  handleAddCourse={handleAddCourse}
                  handleAddOption={handleAddOption}
                  initialDatabaseMap={initialDatabaseMap}
                  isCourseNullable={isCourseNullable}
                  handleOpenReference={handleOpenReference}
                />
                <AppDivider />
                <Flex gap="large" vertical>
                  <Flex gap="large" justify="space-between">
                    <div style={{ width: '100%' }}>
                      <LabelRender
                        label="既往歴"
                        secondBtn={{
                          label: '参照',
                          onClick: () => {
                            handleOpenReference({
                              label: '既往歴',
                              field: EDatabaseMedicalField.Personal,
                              code: EDatabaseMedicalField.Personal,
                            });
                          },
                        }}
                      />
                      <AppFormItem
                        className={styles.lower_section_form_item}
                        name={['patient_medical_history', 'personal']}
                      >
                        <AppTextArea
                          style={{ minHeight: '82px' }}
                          placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                          maxLength={MAX_TEXT_LENGTH}
                        />
                      </AppFormItem>
                    </div>
                    <div style={{ width: '100%' }}>
                      <LabelRender
                        label="家族歴"
                        secondBtn={{
                          label: '参照',
                          onClick: () => {
                            handleOpenReference({
                              label: '家族歴',
                              field: EDatabaseMedicalField.Family,
                              code: EDatabaseMedicalField.Family,
                            });
                          },
                        }}
                      />
                      <AppFormItem
                        className={styles.lower_section_form_item}
                        name={['patient_medical_history', 'family']}
                      >
                        <AppTextArea
                          style={{ minHeight: '82px' }}
                          placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                          maxLength={MAX_TEXT_LENGTH}
                        />
                      </AppFormItem>
                    </div>
                    <div style={{ width: '100%' }}>
                      <LabelRender
                        label="社会歴"
                        secondBtn={{
                          label: '参照',
                          onClick: () => {
                            handleOpenReference({
                              label: '社会歴',
                              field: EDatabaseMedicalField.Social,
                              code: EDatabaseMedicalField.Social,
                            });
                          },
                        }}
                      />
                      <AppFormItem
                        className={styles.lower_section_form_item}
                        name={['patient_medical_history', 'social']}
                      >
                        <AppTextArea
                          style={{ minHeight: '82px' }}
                          placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                          maxLength={MAX_TEXT_LENGTH}
                        />
                      </AppFormItem>
                    </div>
                  </Flex>

                  <Row gutter={24} className={styles.lower_section_tables}>
                    <Col span={12}>
                      <ListCourseTable
                        ref={courseTableRef}
                        courseDataSource={courseDataSource}
                        setCourseDataSource={setCourseDataSource}
                        courseSelectedKeys={courseSelectedKeys}
                        setCourseSelectedKeys={setCourseSelectedKeys}
                        scrollToFirstSameDayRow={(treatment_date: string) => {
                          findMondaiToScroll(treatment_date);
                        }}
                        isActionable={(record: CourseItem) => isActionable(record, 'course')}
                        allRegisteredVisit={allRegisteredVisit}
                        initialCourseData={initialValuesMemo?.patient_courses}
                        isCourseNullable={isCourseNullable}
                        handleUnsavedValueChange={handleUnsavedValueChange}
                      />
                    </Col>
                    <Col span={12}>
                      <ListOptionTable
                        ref={optionTableRef}
                        service_id={mondai?.service_id}
                        optionDataSource={optionDataSource}
                        setOptionDataSource={setOptionDataSource}
                        optionSelectedKeys={optionSelectedKeys}
                        setOptionSelectedKeys={setOptionSelectedKeys}
                        scrollToFirstSameDayRow={(treatment_date: string) => {
                          findMondaiToScroll(treatment_date);
                        }}
                        isActionable={(record: OptionItem) => isActionable(record, 'option')}
                        initialOptionData={initialValuesMemo?.patient_options}
                        handleUnsavedValueChange={handleUnsavedValueChange}
                      />
                    </Col>
                  </Row>
                </Flex>
                <Flex gap="middle" justify="end" className="mt-6 mb-6 ">
                  <Button
                    customType={ButtonType.SECONDARY_COLOR}
                    onClick={closeCleanUpModal}
                    className="btn-modal-width"
                    disabled={submittingAction}
                    customSize="lg"
                  >
                    キャンセル
                  </Button>
                  <Button
                    customType={ButtonType.PRIMARY}
                    htmlType={'submit'}
                    className="btn-modal-width"
                    loading={loadingAction}
                    disabled={submittingAction}
                    customSize="lg"
                  >
                    {SUBMIT_BTN.CREATE}
                    {/* Spec required usign the same btn */}
                  </Button>
                </Flex>
              </Form>
            </>
          </Flex>
        </Spin>
      </AppModal>
      {isSetFromHistoryModalOpen !== null && (
        <SetDatabaseFromHistoryModal
          handleClose={() => {
            setIsSetFromHistoryModalOpen(null);
          }}
          handleSubmit={handleSetFromHistory}
        />
      )}
      {!!isReferenceModalOpen && (
        <ReferenceModal
          handleCloseModal={handleCloseReference}
          handleSubmitModal={handleSubmitReference}
          label={isReferenceModalOpen?.label}
          screen="database"
          field={isReferenceModalOpen.field}
          code={isReferenceModalOpen.code}
        />
      )}
    </>
  );
}

export default memo(UpsertDatabaseModal);
