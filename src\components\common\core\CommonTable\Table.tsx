// example:
// <AppTable
//   rowKey={record => record.id}
//   columns={fakeColumns}
//   dataSource={fakeData}
//   pagination={{
//     current: params.page || 1,
//     pageSize: params.limit || 100,
//     showSizeChanger: false,
//     total: fakeData.length,
//     onChange: (page: number, pageSize: number) =>
//       setParams(prev => ({ ...prev, page, limit: pageSize })),
//   }}
//   onSortChange={handleSortChange}
//   scroll={{ x: 'max-content', y: 400 }}
// />

import { isValidElement, useState } from 'react';
import { Table as AntdTable, Empty, TableProps } from 'antd';
import clsx from 'clsx';
import styles from './Table.module.scss';
import { ColumnType } from 'antd/es/table';
import Icon from '@/components/common/core/Icon';
import { RenderWithTooltip } from './RenderWithTooltip';
import { Order } from '@/types/enum';
import { ESort } from '@/types/enum';
import { EditableRecord } from './EditableTable';
import { NOTICE_COMMON_MESSAGE } from '@/types/constants';

export interface CustomTableProps<T> extends TableProps<T> {
  onSortChange?: (sortData: { order_by: string; order_type: ESort.ASC | ESort.DESC }[]) => void;
  total?: number;
}

export interface CustomColumnType<T> extends ColumnType<T> {
  sortable?: boolean;
  sortKey?: string;
  editable?: EditableRecord | boolean;
}

function Table<T extends object>({ columns, onSortChange, total, ...rest }: CustomTableProps<T>) {
  const [sortState, setSortState] = useState<{
    order_by: string | null;
    order_type: Order;
  }>({
    order_by: null,
    order_type: null,
  });

  const handleSortClick = (key?: string) => {
    setSortState(prev => {
      let newOrderType: Order = null;

      if (prev.order_by !== key) {
        newOrderType = ESort.ASC;
      } else {
        if (prev.order_type === ESort.ASC) newOrderType = ESort.DESC;
        else if (prev.order_type === ESort.DESC) newOrderType = null;
        else newOrderType = ESort.ASC;
      }

      const newState = {
        order_by: newOrderType ? key ?? null : null,
        order_type: newOrderType,
      };

      if (onSortChange) {
        const payload =
          newState.order_by && newState.order_type
            ? [{ order_by: newState.order_by, order_type: newState.order_type }]
            : [];
        onSortChange(payload);
      }

      return newState;
    });
  };

  const modifiedColumns = columns?.map(column => {
    const customColumn = column as CustomColumnType<T>;

    if (customColumn.sortable && !customColumn.sortKey) {
      console.warn(
        `[CustomTable] Column with title "${customColumn.title}" is marked as sortable but missing sortKey.`
      );
    }

    const isSorted = sortState.order_by === customColumn.sortKey;
    const isSortable = !!customColumn.sortable;

    return {
      ...customColumn,
      title: (
        <div
          className={clsx(styles.headerCell, 'fs12-medium', {
            [styles.sorted]: isSorted,
          })}
          onClick={() => {
            if (isSortable) handleSortClick(customColumn.sortKey);
          }}
          style={{
            cursor: isSortable ? 'pointer' : 'default',
            userSelect: isSortable ? 'none' : 'auto',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 4,
          }}
        >
          {typeof customColumn.title === 'string'
            ? customColumn.title
            : typeof customColumn.title === 'function'
            ? customColumn.title({
                sortOrder: isSorted
                  ? sortState.order_type === ESort.ASC
                    ? 'ascend'
                    : sortState.order_type === ESort.DESC
                    ? 'descend'
                    : undefined
                  : undefined,
                sortColumn: customColumn,
                filters: {},
              })
            : customColumn.title}
          {isSortable && (
            <div className="mt-1">
              <Icon
                name={
                  isSorted
                    ? sortState.order_type === ESort.ASC
                      ? 'sortAsc'
                      : sortState.order_type === ESort.DESC
                      ? 'sortDesc'
                      : 'sort'
                    : 'sort'
                }
                height={12}
                width={12}
              />
            </div>
          )}
        </div>
      ),
      render: (text: any, record: T, index: number) => {
        const renderedContent = column.render ? column.render(text, record, index) : text;
        if (isValidElement(renderedContent)) {
          return <div className={clsx(styles.cellContentWrapper)}>{renderedContent}</div>;
        }
        return (
          <div className={styles.cellContentWrapper}>
            {typeof text === 'string' ? (
              <RenderWithTooltip text={text} />
            ) : (
              <RenderWithTooltip text={renderedContent} />
            )}
          </div>
        );
      },
    };
  });

  return (
    <div className={styles.tableWrapper}>
      <AntdTable
        columns={modifiedColumns}
        {...rest}
        locale={{
          emptyText: (
            <Empty
              image={<Icon name="emptyTable" native />}
              description={
                !rest.loading && rest.dataSource?.length === 0 && total! > 0
                  ? NOTICE_COMMON_MESSAGE.EMPTY_DATA
                  : 'データが登録されていません。'
              }
            />
          ),
        }}
      />
    </div>
  );
}

export default Table;
