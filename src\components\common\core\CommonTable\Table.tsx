// example:
// <AppTable
//   rowKey={record => record.id}
//   columns={fakeColumns}
//   dataSource={fakeData}
//   pagination={{
//     current: params.page || 1,
//     pageSize: params.limit || 100,
//     showSizeChanger: false,
//     total: fakeData.length,
//     onChange: (page: number, pageSize: number) =>
//       setParams(prev => ({ ...prev, page, limit: pageSize })),
//   }}
//   onSortChange={handleSortChange}
//   scroll={{ x: 'max-content', y: 400 }}
// />

import { isValidElement, useEffect, useMemo, useState } from 'react';
import { Table as AntdTable, Empty, TableProps, Tooltip } from 'antd';
import clsx from 'clsx';
import styles from './Table.module.scss';
import { ColumnType } from 'antd/es/table';
import Icon from '@/components/common/core/Icon';
import { RenderWithTooltip } from './RenderWithTooltip';
import { Order } from '@/store/patient/type';
import { ESort } from '@/types/enum';
import { EditableRecord } from './EditableTable';
import { ERROR_COMMON_MESSAGE, NOTICE_COMMON_MESSAGE } from '@/types/constants';

// DnD
import { DragHandle, Row } from './TableDnDKit';
import { DndContext, DragEndEvent, UniqueIdentifier } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
export interface CustomTableProps<T> extends TableProps<T> {
  onSortChange?: (sortData: { order_by: string; order_type: ESort.ASC | ESort.DESC }[]) => void;
  total?: number;
  maxLines?: number;
  isDnD?: boolean;
  onRowOrderChange?: (payload: {
    positionChange: {
      olderPositionData: T;
      olderPositionIndex: number;
      newPositionData: T;
      newPositionIndex: number;
    };
    newData: T[];
  }) => void;
  canDragToEnd?: boolean;
}

export interface CustomColumnType<T> extends ColumnType<T> {
  sortable?: boolean;
  sortKey?: string;
  editable?: EditableRecord | boolean | ((record: T) => EditableRecord);
}

function Table<T extends object>({
  columns,
  onSortChange,
  total,
  maxLines,
  isDnD = false,
  onRowOrderChange,
  canDragToEnd = true,
  ...rest
}: CustomTableProps<T>) {
  const [tooltipOpen, setTooltipOpen] = useState(false);
  const [tableData, setTableData] = useState<T[]>([]);
  const [sortState, setSortState] = useState<{
    order_by: string | null;
    order_type: Order;
  }>({
    order_by: null,
    order_type: null,
  });

  useEffect(() => {
    setTableData([...(rest?.dataSource ?? [])]);
  }, [rest?.dataSource]);

  const handleSortClick = (key?: string) => {
    setSortState(prev => {
      let newOrderType: Order = null;

      if (prev.order_by !== key) {
        newOrderType = ESort.ASC;
      } else {
        if (prev.order_type === ESort.ASC) newOrderType = ESort.DESC;
        else if (prev.order_type === ESort.DESC) newOrderType = null;
        else newOrderType = ESort.ASC;
      }

      const newState = {
        order_by: newOrderType ? key ?? null : null,
        order_type: newOrderType,
      };

      if (onSortChange) {
        const payload =
          newState.order_by && newState.order_type
            ? [{ order_by: newState.order_by, order_type: newState.order_type }]
            : [];
        onSortChange(payload);
      }

      return newState;
    });
  };

  const modifiedColumns = (columns ?? []).map(column => {
    const customColumn = column as CustomColumnType<T>;

    if (customColumn.sortable && !customColumn.sortKey) {
      console.warn(
        `[CustomTable] Column with title "${customColumn.title}" is marked as sortable but missing sortKey.`
      );
    }

    const isSorted = sortState.order_by === customColumn.sortKey;
    const isSortable = !!customColumn.sortable;

    return {
      ...customColumn,
      title: (
        <div
          className={clsx(styles.headerCell, 'fs12-medium', {
            [styles.sorted]: isSorted,
          })}
          onClick={() => {
            if (isSortable) handleSortClick(customColumn.sortKey);
          }}
          style={{
            cursor: isSortable ? 'pointer' : 'default',
            userSelect: isSortable ? 'none' : 'auto',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 4,
          }}
        >
          {typeof customColumn.title === 'string'
            ? customColumn.title
            : typeof customColumn.title === 'function'
            ? customColumn.title({
                sortOrder: isSorted
                  ? sortState.order_type === ESort.ASC
                    ? 'ascend'
                    : sortState.order_type === ESort.DESC
                    ? 'descend'
                    : undefined
                  : undefined,
                sortColumn: customColumn,
                filters: {},
              })
            : customColumn.title}
          {isSortable && (
            <div className="mt-1">
              <Icon
                name={
                  isSorted
                    ? sortState.order_type === ESort.ASC
                      ? 'sortAsc'
                      : sortState.order_type === ESort.DESC
                      ? 'sortDesc'
                      : 'sort'
                    : 'sort'
                }
                height={12}
                width={12}
              />
            </div>
          )}
        </div>
      ),
      render: (text: any, record: T, index: number) => {
        const renderedContent = column?.render ? column.render(text, record, index) : text;
        if (isValidElement(renderedContent)) {
          return (
            <div className={clsx(styles.cellContentWrapper, styles.tooltip_content)}>
              {renderedContent}
            </div>
          );
        }
        return (
          <div className={clsx(styles.cellContentWrapper, styles.tooltip_content)}>
            {typeof text === 'string' ? (
              <RenderWithTooltip text={text} maxLines={maxLines} />
            ) : (
              <RenderWithTooltip text={renderedContent} maxLines={maxLines} />
            )}
          </div>
        );
      },
    };
  });

  const getRowKey = useMemo(() => {
    if (typeof rest.rowKey === 'function') {
      return rest.rowKey;
    }
    return (record: T) => record[rest.rowKey as keyof T] as string | number;
  }, [rest.rowKey]);
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setTableData(prevState => {
        const activeIndex = prevState.findIndex(record => getRowKey(record) === active.id);
        const overIndex = prevState.findIndex(record => getRowKey(record) === over?.id);
        if (overIndex === prevState.length - 1 && !canDragToEnd) {
          return prevState;
        }
        const updatedData = arrayMove(prevState, activeIndex, overIndex);
        onRowOrderChange?.({
          positionChange: {
            olderPositionData: prevState[activeIndex],
            olderPositionIndex: activeIndex,
            newPositionData: prevState[overIndex],
            newPositionIndex: overIndex,
          },
          newData: updatedData,
        });
        return updatedData;
      });
    }
  };

  const TableRender = (
    <div className={styles.tableWrapper}>
      <AntdTable
        columns={
          isDnD
            ? [
                {
                  title: (
                    <Tooltip title={NOTICE_COMMON_MESSAGE.DRAG_DROP} open={tooltipOpen}>
                      <Icon
                        name="infoFilled"
                        width={16.21}
                        height={16.21}
                        color={'var(--brand-600)'}
                        style={{ verticalAlign: 'middle' }}
                      />
                    </Tooltip>
                  ),
                  key: 'sort',
                  align: 'center' as const,
                  width: 48,
                  render: record => (record?.isDraggable === false ? null : <DragHandle />),
                  onHeaderCell: () => ({
                    style: { cursor: 'pointer' },
                    onMouseEnter: () => setTooltipOpen(true),
                    onMouseLeave: () => setTooltipOpen(false),
                    onClick: () => setTooltipOpen(!tooltipOpen),
                  }),
                  onCell: () => {
                    return {
                      style: {
                        verticalAlign: 'middle',
                      },
                    };
                  },
                },
                ...modifiedColumns,
              ]
            : modifiedColumns
        }
        {...rest}
        components={isDnD ? { body: { row: Row } } : undefined}
        dataSource={isDnD ? tableData : rest.dataSource}
        locale={{
          emptyText: (
            <Empty
              image={<Icon name="emptyTable" native width={90.27} height={80} />}
              description={
                <p className={styles.emptyText}>
                  {!rest.loading && rest.dataSource?.length === 0 && total! > 0
                    ? NOTICE_COMMON_MESSAGE.EMPTY_DATA
                    : ERROR_COMMON_MESSAGE.NO_DATA}
                </p>
              }
            />
          ),
        }}
      />
    </div>
  );

  if (!isDnD) return TableRender;

  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext
        items={tableData.map(item => getRowKey(item) as UniqueIdentifier)}
        strategy={verticalListSortingStrategy}
      >
        {TableRender}
      </SortableContext>
    </DndContext>
  );
}

export default Table;
