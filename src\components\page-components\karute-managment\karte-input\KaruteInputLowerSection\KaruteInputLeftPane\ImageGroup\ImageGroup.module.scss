$thumbnail-size: 80px;

.image_grid {
  display: grid;
  grid-template-columns: repeat(var(--number-row, 3), $thumbnail-size);
  gap: 8px;
  width: 100%;
}

.image_group {
  .image_item {
    :global(.ant-image-img) {
      border-radius: 4px;
      object-fit: cover;
      width: $thumbnail-size;
      height: 70px;
    }
    :global(.ant-image-mask) {
      border-radius: 4px;
      backdrop-filter: blur(2px);
    }
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .remaining_item {
    position: relative;
    .remaining_image {
      :global(.ant-image-img) {
        border-radius: 4px;
        opacity: 0; // Hidden by default
        transition: opacity 0.3s ease;
      }
    }
    &:hover {
      .remaining_image {
        :global(.ant-image-img) {
          opacity: 1; // Show the next image on hover
        }
      }
      .remaining_overlay {
        background-color: rgba(0, 0, 0, 0.2); // Black overlay on hover
      }
    }
    .remaining_overlay {
      cursor: pointer;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      // background-color: #000; // Black background by default
       background-color: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;
    }
    .remaining_text {
      font-size: 16px;
      font-weight: bold;
      color: white;
      z-index: 1; // Ensure text stays on top
    }
  }
}

.myPreview {
  :global(.ant-image-preview-img) {
    border-radius: 8px !important;
    overflow: hidden;
  }
  :global(.ant-image-preview-progress) {
    color: white;
  }
  :global(.ant-image-preview-close),
  :global(.ant-image-preview-switch-left),
  :global(.ant-image-preview-switch-right) {
    background-color: $gray-900;
    &:hover {
      opacity: 0.7;
    }

    &:global(.ant-image-preview-switch-right-disabled),
    &:global(.ant-image-preview-switch-left-disabled) {
      background-color: rgba(0, 0, 0, 0.3);
      cursor: not-allowed;
      &:hover {
        background-color: rgba(0, 0, 0, 0.3);
        opacity: 1;
      }
    }
  }
  .toolbarWrapper {
    background-color: $gray-900;
    height: 42px;
    display: flex;
    gap: 12px;
    justify-content: space-between;
    align-items: center;
    padding-left: 24px;
    padding-right: 24px;
    border-radius: 24px;
    .action {
      display: flex;
      align-items: center;
      padding: 12px;
      cursor: pointer;
      overflow: hidden;
      &:hover {
        opacity: 0.85;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.85;
      }
      svg {
        height: 18px;
        width: 18px;
        color: $gray-300;
      }
    }
  }

  &.noProgress {
    :global(.ant-image-preview-progress) {
      display: none;
    }
  }
}