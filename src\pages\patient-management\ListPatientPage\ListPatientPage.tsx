import ListPatientPageTable from '@/components/page-components/patient-management/patient-list/ListPatientPageTable';
import ListPatientPageTableHeader from '@/components/page-components/patient-management/patient-list/ListPatientPageTableHeader';
import PageLayout from '@/components/layouts/PageLayout';
import ListPatientPageAction from '@/components/page-components/patient-management/patient-list/ListPatientPageAction';
import { ListPatientProvider } from '@/components/page-components/patient-management/patient-list/provider/ListPatientProvider';
export default function ListPatientPage() {
  return (
    <ListPatientProvider>
      <PageLayout title="患者一覧" headerRight={<ListPatientPageAction />}>
        <>
          <ListPatientPageTableHeader />
          <ListPatientPageTable />
        </>
      </PageLayout>
    </ListPatientProvider>
  );
}
