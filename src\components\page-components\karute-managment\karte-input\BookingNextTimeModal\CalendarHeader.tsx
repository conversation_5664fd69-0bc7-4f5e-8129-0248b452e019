import Icon from '@/components/common/core/Icon';
import { Flex } from 'antd';
import { ComponentPropsWithRef, PropsWithChildren } from 'react';
import styles from './BookingNextTime.module.scss';

type ICalendarHeaderProps = {
  className?: string;
  style?: ComponentPropsWithRef<'div'>['style'];
  onNext?: () => void;
  onLast?: () => void;
  onPrev?: () => void;
  onFirst?: () => void;
};

export const CalendarHeader = ({
  children,
  onPrev,
  onFirst,
  onLast,
  onNext,
}: PropsWithChildren<ICalendarHeaderProps>) => {
  return (
    <Flex dir="column" gap={8} justify="space-between" className={styles.noSelect}>
      <Flex dir="column">
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
          onClick={onFirst}
        >
          <Icon name="arrowLeft" width={20} height={20} color="$gray-500" />
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
          onClick={onPrev}
        >
          <Icon name="arrowLeft2" color="$gray-500" width={20} height={20} />
        </div>
      </Flex>
      {children ?? ''}
      <Flex dir="column" align="center" gap={4}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
          onClick={onNext}
        >
          <Icon name="arrowRight2" width={20} height={20} color="$gray-500" />
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
          onClick={onLast}
        >
          <Icon name="arrowRight" width={20} height={20} color="$gray-500" />
        </div>
      </Flex>
    </Flex>
  );
};
