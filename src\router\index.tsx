import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';

import AuthLayout from '@/components/layouts/AuthLayout/AuthLayout';
// import OnlyHeaderLayout from '@/components/layouts/OnlyHeaderLayout/OnlyHeaderLayout';
import DashboardLayout from '@/components/layouts/DashboardLayout';

import RoleBasedRoute from './RoleBasedRoute';
import ClinicInit from './ClinicInit';
import ClinicGuard from './ClinicGuard';

import { publicRouterMenu, privateRouterMenu } from './routerMenu';
import { routerPaths } from '@/types/constants/routerPath';
import {
  AuthNotFoundPage,
  DashboardNotFoundPage,
  PageNotAuthorizedPage,
} from '@/pages/error-pages/ErrorPages';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { selectClinic } from '@/store/auth';
import { Spin } from 'antd';
import { REGEX_PUBLIC_PATH } from '@/types/constants';

function AutoClinicRedirect() {
  const { clinics, currentClinicCd } = useAppSelector(s => s.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    if (clinics.length > 0) {
      const cd = currentClinicCd || clinics[0].clinic_cd;
      dispatch(selectClinic(cd));
      navigate(`/${cd}/dashboard`, { replace: true });
    }
  }, [clinics, currentClinicCd, dispatch, navigate]);

  if (clinics.length === 0) {
    return <Spin />;
  }
  return null;
}

export default function AppRouter() {
  const { isAuthenticated, expiredAt } = useAuth();
  const hasAuth = isAuthenticated && !(expiredAt && expiredAt < Date.now());
  const navigate = useNavigate();

  useEffect(() => {
    const path = window.location.pathname;
    const publicPathPatterns = Object.values(REGEX_PUBLIC_PATH);
    const isPublicPath = publicPathPatterns.some(pattern => pattern.test(path));

    if (!hasAuth && !isPublicPath) {
      navigate(routerPaths.login, { replace: true });
    }
  }, [hasAuth]);
  return (
    <Routes>
      {!hasAuth && (
        <Route element={<AuthLayout />}>
          {publicRouterMenu.map(r => (
            <Route key={r.path} path={r.path} element={r.element} />
          ))}
          <Route path="/" element={<Navigate to={routerPaths.login} replace />} />
          {/* <Route path="*" element={<AuthNotFoundPage />} /> */}
        </Route>
      )}

      {hasAuth && (
        <>
          <Route path="/" element={<AutoClinicRedirect />} />
          <Route path="login/*" element={<Navigate to="/" replace />} />
          <Route path="/:clinicCd/*" element={<ClinicInit />}>
            <Route element={<ClinicGuard />}>
              <Route element={<DashboardLayout />}>
                {privateRouterMenu.map(r => (
                  <Route
                    key={r.path}
                    path={r.path.replace(/^\/:clinicCd\//, '')}
                    element={<RoleBasedRoute roles={(r as any).roles}>{r.element}</RoleBasedRoute>}
                  />
                ))}

                <Route path="403-page-not-authorized" element={<PageNotAuthorizedPage />} />
                <Route path="404-dashboard-not-found" element={<DashboardNotFoundPage />} />
                <Route path="*" element={<DashboardNotFoundPage />} />
              </Route>
            </Route>
          </Route>
        </>
      )}
    </Routes>
  );
}
