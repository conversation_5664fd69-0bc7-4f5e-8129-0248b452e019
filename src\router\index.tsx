import DashboardLayout from '@/components/layouts/DashboardLayout';
import useAuth from '@/hooks/useAuth';
import { Fragment, useEffect, useRef } from 'react';
import { matchPath, Navigate, Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import { privateModalMenu, privateRouterMenu, publicRouterMenu } from './routerMenu';
import { routerPaths } from '@/types/constants';
import NotAuthenPage from '@/pages/authen/NotAuthenPage';
import { fetchCurrentUser } from '@/services/medix-sync-api/userApi';
import { saveUser } from '@/utils/userStorage';
import { logout } from '@/services/medix-sync-api/logout';
import { ROLE } from '@/types/enum';

const snap = (l: Location) => ({ pathname: l.pathname, search: l.search, hash: l.hash });
const DASHBOARD_SNAPSHOT = snap({ pathname: routerPaths.dashboard, search: '', hash: '' } as any);

export default function AppRouter() {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const hasAuth = isAuthenticated;
  const navigate = useNavigate();
  const didFetch = useRef(false);
  const tokenBridge = window.location.pathname === routerPaths.tokenBridge;

  const isModalPath = privateModalMenu.some(r =>
    matchPath({ path: r.path, end: true }, location.pathname)
  );

  const background =
    (location.state as any)?.background || (isModalPath ? DASHBOARD_SNAPSHOT : null);

  useEffect(() => {
    if (
      !isAuthenticated &&
      location.pathname !== routerPaths.notAuthen &&
      location.pathname !== routerPaths.tokenBridge
    ) {
      navigate(routerPaths.notAuthen, { replace: true });
    }
  }, [isAuthenticated, location.pathname, navigate]);

  useEffect(() => {
    if (!isAuthenticated) {
      didFetch.current = false;
    }

    if (tokenBridge) return;

    if (isAuthenticated && !didFetch.current) {
      didFetch.current = true;

      fetchCurrentUser()
        .then(res => {
          if (res.status === 200 && res.data?.manageable_type !== ROLE.STAFF) {
            saveUser(res.data || null);
          } else {
            logout();
          }
        })
        .catch(() => logout());
    }
  }, [isAuthenticated, tokenBridge]);
  return (
    <Fragment>
      <Routes location={background || location}>
        {/* public */}
        <Route
          path={routerPaths.notAuthen}
          element={hasAuth ? <Navigate to={routerPaths.dashboard} replace /> : <NotAuthenPage />}
        />
        {publicRouterMenu
          .filter(r => r.path !== routerPaths.notAuthen)
          .map(r => (
            <Route key={r.path} path={r.path} element={r.element} />
          ))}

        {/* private */}
        {hasAuth && (
          <Route element={<DashboardLayout />}>
            <Route path="/" element={<Navigate to={routerPaths.dashboard} replace />} />
            {privateRouterMenu.map(r => (
              <Route key={r.path} path={r.path} element={r.element} />
            ))}
          </Route>
        )}

        {/* catch-all */}
        <Route
          path="*"
          element={
            <Navigate
              to={hasAuth && !tokenBridge ? routerPaths.dashboard : routerPaths.notAuthen}
              replace
            />
          }
        />
      </Routes>

      {/* modal */}
      {isModalPath && (
        <Routes>
          {privateModalMenu.map(r => (
            <Route key={r.path} path={r.path} element={r.element} />
          ))}
        </Routes>
      )}
    </Fragment>
  );
}
