import { InputProps } from 'antd/es/input';

export interface ValidationStatus {
  error: boolean;
  errorIcon?: boolean;
}

export interface TextInputProps extends InputProps {
  label?: string;
  message?: string;
  password?: boolean;
  validationStatus?: ValidationStatus;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  defaultStyle?: boolean;
  inputRef?: React.RefObject<import('antd').InputRef>;
}
