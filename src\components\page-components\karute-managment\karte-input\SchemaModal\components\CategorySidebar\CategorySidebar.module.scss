$header-height: 56px;
$sidebar-width: 160px;
$sidebar-collapsed: 80px;

.sidebar {
  width: $sidebar-width;
  height: 100%;
  background-color: #fff;
  position: relative;
  border-right: 1px solid $gray-200;
  transition: width 0.25s;
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
  padding: 16px;

  &.collapsed {
    width: $sidebar-collapsed;

    .menuItem {
      padding-left: 12px;
      text-indent: -9999px;
    }
  }
}

.menu {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  max-height: 100%;
  overflow-y: auto;
}

.menuItem {
  min-height: 40px;
  padding: 8px 20px;
  font-size: 14px;
  line-height: 22px;
  color: $gray-500;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  border-radius: 8px;

  &:hover {
    background-color: $gray-50;
  }
}

.active {
  background-color: $brand-50;
  color: $brand-800;
  font-weight: 600;
}

.toggleBtn {
  position: absolute;
  top: 14px;
  right: -14px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: $brand-800;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
  transition: background-color 0.2s;

  &:hover {
    background-color: $brand-900;
  }

  &:focus,
  &:focus-visible,
  &:focus-within {
    background-color: $brand-700;
    outline: none;
  }
}
