import { MondaiItem } from './database_schema';

export interface ListDatabaseImageItem {
  disease_base_id?: number;
  schema_image_id: number;
  img_path: string;
  version?: number;
}
export interface ListDatabaseItem extends Omit<MondaiItem, 'objective_images' | 'treatment_images'> {
  disease_base_id?: number | null;
  karute_id?: number | string;
  objective_images: ListDatabaseImageItem[];
  treatment_images: ListDatabaseImageItem[];
  version?: number | null;
}
