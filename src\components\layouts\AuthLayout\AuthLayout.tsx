import React, { ReactNode } from 'react';
import styles from './AuthLayout.module.scss';
import Footer from '@/components/common/layout/Footer';
import { Outlet } from 'react-router-dom';

interface AuthLayoutProps {
  children?: ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className={styles.authLayout}>
      <div className={styles.logoArea}>
        {/* <img src="" alt="" /> */}
        <span className="fs24-bold">LOGO</span>
      </div>
      <div className={styles.contentArea}>{children ?? <Outlet />}</div>
      <Footer />
    </div>
  );
};

export default AuthLayout;
