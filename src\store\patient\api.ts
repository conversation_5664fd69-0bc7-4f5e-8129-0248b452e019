import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse, InsuranceCount, ListResponse } from '@/types/interface';
import {
  CheckDuplicatePatientPayload,
  CreatePatientPayload,
  PatientItem,
  PatientsParams,
  UpdatePatientPayload,
} from './type';

export const patientManagementApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,

    getPatientList: build.query<APIResponse<ListResponse<PatientItem>>, Partial<PatientsParams>>({
      query: params => {
        return {
          url: `/api/patients`,
          method: 'get',
          params,
        };
      },
      providesTags: ['patient-list'],
    }),
    getCsvPatientList: build.query<Blob, Omit<PatientsParams, 'limit' | 'page'>>({
      query: params => {
        return {
          url: `/api/patients/export`,
          method: 'get',
          params,
          responseHandler: response => response.blob(),
        };
      },
      providesTags: ['patient-list'],
    }),

    createPatient: build.mutation<APIResponse<any>, CreatePatientPayload>({
      query: payload => {
        return {
          url: `/api/patients`,
          body: payload,
          method: 'post',
        };
      },
      invalidatesTags: ['patient-list'],
    }),

    updatePatient: build.mutation<APIResponse<any>, { id: number; body: UpdatePatientPayload }>({
      query: payload => {
        return {
          url: `/api/patients/${payload.id}`,
          body: payload.body,
          method: 'put',
        };
      },
      invalidatesTags: ['patient-list'],
    }),

    getDetailsPatient: build.query<APIResponse<PatientItem>, number>({
      query: params => {
        return {
          url: `/api/patients/${params}`,
          method: 'get',
        };
      },
      providesTags: ['patient-list'],
    }),

    getTotalPatientByInsuredType: build.query<APIResponse<InsuranceCount[]>, number>({
      query: params => {
        return {
          url: `/api/patient-insurances/count-by-type?store_id=${params}`,
          method: 'get',
        };
      },
      providesTags: ['insurance-list'],
    }),

    checkDuplicatePatient: build.mutation<APIResponse<PatientItem>, CheckDuplicatePatientPayload>({
      query: payload => {
        return {
          url: '/api/patients/get-duplicate-by-clinic',
          body: payload,
          method: 'post',
        };
      },
    }),
    getInsuranceInfo: build.query<APIResponse<Record<'has_insurance', boolean>>, number>({
      query: params => ({
        url: `/api/patients/${params}/has-insurance`,
        method: 'get',
      }),
    }),
    checkPatientCd: build.query<
      APIResponse<{
        has_patient_code: boolean;
        patient_cd: boolean | null;
      }>,
      number
    >({
      query: params => ({
        url: `/api/patients/${params}/has-patient-cd`,
        method: 'get',
      }),
    }),
    createPatientCd: build.mutation<APIResponse<PatientItem>, number>({
      query: params => ({
        url: `/api/patients/${params}/patient-cd`,
        body: {},
        method: 'post',
      }),
    }),
  }),
});

export const {
  useGetPatientListQuery,
  useLazyGetCsvPatientListQuery,
  useCreatePatientMutation,
  useGetDetailsPatientQuery,
  useLazyGetDetailsPatientQuery,
  useUpdatePatientMutation,
  useGetTotalPatientByInsuredTypeQuery,
  useCheckDuplicatePatientMutation,
  useLazyGetInsuranceInfoQuery,
  useLazyCheckPatientCdQuery,
  useCreatePatientCdMutation,
} = patientManagementApi;
