import { User } from '@/types/interface/User';
export interface LoginResponse {
  token_type: string;
  expires_in: number;
  access_token: string;
  refresh_token: string;
  user: User;
}

export interface LoginSchema {
  username: string;
  password: string;
  clinic_cd: string;
}
export interface ForgotPasswordParams {
  username: string;
  email: string;
  clinic_cd: string;
  domain: string;
}

export interface ResetPasswordParams {
  token: string;
  clinic_cd: string;
  password: string;
  password_confirmation: string;
}

export type { User };
