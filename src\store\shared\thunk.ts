import { createAsyncThunk } from '@reduxjs/toolkit';
import { sharedApi } from './api';
import { visitApi } from '../visit/api';

export const prefetchSharedData = createAsyncThunk(
  'prefetch/sharedData',
  async (_, { dispatch }) => {
    const [doctors, services, courses, hasContractBooking] = await Promise.all([
      await dispatch(
        sharedApi.endpoints.getDoctors.initiate(null, {
          forceRefetch: true,
        })
      ).unwrap(),
      await dispatch(
        sharedApi.endpoints.getServicesByClinicId.initiate(null, {
          forceRefetch: true,
        })
      ).unwrap(),
      await dispatch(
        visitApi.endpoints.getActiveCourse.initiate(null, {
          forceRefetch: true,
        })
      ).unwrap(),
      await dispatch(
        sharedApi.endpoints.checkHasContractBooking.initiate(null, {
          forceRefetch: true,
        })
      ).unwrap(),
    ]);

    return {
      doctors: doctors.data,
      services: services.data,
      courses: courses.data,
      hasContractBooking: hasContractBooking.data?.has_contract || false,
    };
  }
);

export const fetchingUserInfo = createAsyncThunk(
  'shared/fetchingUserInfo',
  async (_, { dispatch }) => {
    const userInfo = await dispatch(sharedApi.endpoints.getUserInfo.initiate(null)).unwrap();
    return userInfo.data;
  }
);
