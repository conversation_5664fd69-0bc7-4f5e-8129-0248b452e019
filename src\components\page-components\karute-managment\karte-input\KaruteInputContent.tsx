import { AppSplit } from '@/components/common/core/Split';
import { FE_DEFINED_META_KEYS } from '@/types/constants';
import { Fragment, useCallback, useEffect, useState } from 'react';
import { KaruteInputLowerSection } from './KaruteInputLowerSection';
import styles from './KaruteInputLowerSection/KaruteInputLowerSection.module.scss';
import { KaruteInputHeader } from './KaruteInputUpperSection';
import { MondaiTable } from './Mondai/MondaiTable';
import { KaruteInputLowerSectionProvider, useKaruteInput } from './provider';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { mondaiThunks } from '@/store/karute/mondaiSlice';
import { useUpdateUserMetaMutation } from '@/store/user-karute/api';

export function KaruteInputContent() {
  // providers
  const {
    karuteDetailData,
    mainVerticalSizes,
    lowerHorizontalSizes,
    lowerVerticalSizes,
    setMainVerticalSizes,
  } = useKaruteInput();

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const [updateUserMetaMutation] = useUpdateUserMetaMutation();

  const handleVerticalDragEnd = useCallback(
    (newSizes: number[]) => {
      const updatedMeta = {
        meta: {
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.MAIN]: newSizes,
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.MAIN]: lowerHorizontalSizes,
          [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.RIGHT]: lowerVerticalSizes,
        },
      };
      setMainVerticalSizes(newSizes);
      // Don't need to handle success | error here
      updateUserMetaMutation(updatedMeta);
    },
    [lowerHorizontalSizes, lowerVerticalSizes, updateUserMetaMutation, setMainVerticalSizes]
  );
  const patientId = karuteDetailData?.patient_id;
  const dispatch = useAppDispatch();

  const { mondais, loading, queryParams } = useAppSelector(state => state.karute.mondais);
  useEffect(() => {
    if (!patientId) return;
    dispatch(
      mondaiThunks.fetchMondai({
        patient_id: patientId,
        ...queryParams,
      })
    );
  }, [queryParams, patientId]);

  return (
    <>
      <KaruteInputLowerSectionProvider>
        <KaruteInputHeader />
        <AppSplit
          direction="vertical"
          sizes={mainVerticalSizes}
          onDragEnd={handleVerticalDragEnd}
          className={styles.karute_input}
          gutterSize={6}
          maxSize={[Infinity, 1130]}
        >
          <Fragment key="mondai-table">
            <MondaiTable
              selectedKeys={selectedKeys}
              setSelectedKeys={setSelectedKeys}
              mondaiData={mondais}
              loadingData={loading}
            />
          </Fragment>
          <KaruteInputLowerSection />
        </AppSplit>
      </KaruteInputLowerSectionProvider>
    </>
  );
}
