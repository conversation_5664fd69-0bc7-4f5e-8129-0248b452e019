import { GetKaruteHokenCourseItem } from '@/store/karute/type';
import {
  DiseaseImage,
  PatientCourse,
  PatientMedicalHistory,
  PatientOption,
} from './database_form_upsert';
import { MondaiItem } from './database_schema';

// Get Database Form
type FirstKeika = {
  keika_id: number;
  option_checked: number;
  karute_id: number;
  version: number;
  visit_id: number;
  visit_date: string; // ISO date string, e.g. "2025-06-05"
  service_id: number;
  created_at: string; // ISO datetime string, e.g. "2025-06-12 14:51:24"
  updated_at: string; // ISO datetime string
};

export interface GetDiseaseBase extends MondaiItem {
  trauma_type: number;
  trauma_counter: number;
  objective_images: DiseaseImage[];
  treatment_images: DiseaseImage[];
  version: number | null;
  karute_id?: number;
  first_keika?: FirstKeika;
  doctor_name: string;
  hoken_course?: GetKaruteHokenCourseItem[];
}
export interface DatabaseGetSchema {
  karute_disease_base_id: number;
  latest_uuid: string; // uuid;
  version: number;
  karute_id: number;
  service_id: number;
  disease_bases: GetDiseaseBase[]; // mondai list
  patient_medical_history: PatientMedicalHistory; //
  patient_options: PatientOption[];
  patient_courses: PatientCourse[];
}

export type DoctorByVisitDate = Record<
  string,
  {
    doctors: {
      user_id: number;
      kanji_name: string;
    }[];

    visit_date: string;
  }
>;
