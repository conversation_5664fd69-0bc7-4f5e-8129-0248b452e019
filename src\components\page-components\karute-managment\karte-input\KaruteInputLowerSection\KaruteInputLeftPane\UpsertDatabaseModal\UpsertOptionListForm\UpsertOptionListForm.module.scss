.table {
  border-radius: 12px !important;
  border: 1px solid $gray-200;

  .add_record_btn {
    border-radius: 0 0 12px 12px;
    color: $brand-900;
    border-top: 1px solid $gray-200;
    height: 54px;
  }
  :global {
    .ant-table-row:hover {
      cursor: pointer;
    }

    .ant-spin-container {
      border-radius: 12px !important;
    }

    .ant-table {
      border-radius: 12px !important;

      .ant-table-header {
        border-top-left-radius: 12px !important;
        border-top-right-radius: 12px !important;
      }

      .ant-table-container table > thead > tr:first-child th:first-child {
        border-top-left-radius: 12px !important;
      }

      .ant-table-container table > thead > tr:first-child th:last-child {
        border-top-right-radius: 12px !important;
      }

      .ant-table-tbody {
        .ant-table-cell {
          .ant-form-item {
            .ant-form-item-explain {
              .ant-form-item-explain-error {
                font-size: 12px !important;
                font-weight: 500 !important;
                line-height: 18px !important;
                letter-spacing: 0 !important;
              }
            }
            .ant-form-item-control-input-content,
            input {
              font-size: 12px;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0;
            }
            input::placeholder {
              font-size: 12px;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0;
            }
            .ant-select {
              .ant-select-selection-placeholder,
              .ant-select-selection-item-content,
              .ant-select-selection-item,
              input {
                // fs12-medium
                font-size: 12px;
                font-weight: 500;
                line-height: 18px;
                letter-spacing: 0;
              }
            }
          }
        }
        .ant-table-placeholder {
          .ant-table-cell {
            border-bottom-left-radius: 12px !important;
            border-bottom-right-radius: 12px !important;
            height: 161px !important;
            padding: 0 !important;
            border-bottom: none !important;

            // .ant-empty {
            //   .ant-empty-image {
            //     height: 87px !important;
            //   }
            // }
            .ant-empty-description {
              font-size: 12px ;
              font-weight: 400 ;
              line-height: 18px ;
              letter-spacing: 0 ;
            }
          }
        }
      }
    }
  }
}

.hoken_table {
  .tableWrapper {
    overflow: hidden;
    background-color: var(--white);
    height: 100%;
    > div:nth-child(2) {
      overflow-y: hidden;
      height: calc(100% - 52px);
    }
  }
  :global(.ant-select-selection-wrap) {
    height: 100% !important;
  }
  :global(.ant-table-row) {
    min-height: 34px;
    max-height: 54px;
    height: 54px;
    :global(.ant-table-cell) {
      vertical-align: unset;
    }
  }
}
.option_table {
  .active_add_record_btn {
    cursor: pointer;
    &:hover {
      background-color: $brand-100 !important;
      box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
    }

    &:focus,
    &:focus-visible,
    &:focus-within {
      background-color: $brand-200 !important;
      color: white;
      outline: none;
      box-shadow: none;
    }
  }
  .disabled_add_record_btn {
    cursor: not-allowed;
    .disabled_add_record_btn_text {
      opacity: 30%;
    }
  }
  .tableWrapper {
    overflow: hidden;
    background-color: var(--white);
    height: 100%;
    > div:nth-child(2) {
      overflow-y: hidden;
      height: calc(100% - 52px);
    }
    :global {
      .ant-spin-container {
        border-radius: 12px !important;
      }

      .ant-table {
        border-radius: 12px !important;
        .ant-table-header {
          border-top-left-radius: 12px !important;
          border-top-right-radius: 12px !important;
        }
        .ant-table-container table > thead > tr:first-child th:first-child {
          border-top-left-radius: 12px !important;
        }

        .ant-table-container table > thead > tr:first-child th:last-child {
          border-top-right-radius: 12px !important;
        }
        .ant-table-tbody {
          .ant-table-row:last-child > td {
            border-bottom: none !important;
          }
        }
      }
    }
  }
  :global(.ant-select-selection-wrap) {
    height: 100% !important;
  }
  :global(.ant-table-row) {
    min-height: 34px;
    max-height: 54px;
    height: 54px;
    :global(.ant-table-cell) {
      vertical-align: unset;
    }
  }
}

.action_btn {
  // cursor: pointer;
  padding: 3px;
  // background-color: #f5f5f5;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: $gray-200;
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;

    &:hover {
      background-color: var(---white);
    }
  }
}
.cellContentWrapper {
  color: $gray-800;
  // overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  // -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  // padding: 0 4px;
  word-break: break-all;

  // fs12-medium
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  letter-spacing: 0;

  &.overflowed {
    cursor: pointer;
  }
}
.cellContentDisabled {
  opacity: 0.5;
}
