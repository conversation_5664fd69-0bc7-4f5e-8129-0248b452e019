import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import Button from '@/components/common/core/Button';

import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import Icon from '@/components/common/core/Icon';

export interface WarningDialogProps {
  title?: string;
  message: string;
  onConfirm?: () => void;
  buttons?: WarningDialogButtons[];
}

interface WarningDialogButtons {
  type: 'cancel' | 'confirm';
  label: string;
  onClick: () => void;
}

export const useWarningDialog = () => {
  const { show, hide } = useModalConfirm();

  const showWarningDialog = ({ title, message, onConfirm, buttons }: WarningDialogProps) => {
    show({
      title: title || '',
      content: (
        <Flex align="center" justify="space-between" gap={16}>
          <Icon name="warning" native className="mt-2" />
          <span>{message}</span>
        </Flex>
      ),
      buttons: buttons?.length
        ? buttons.map(button => (
            <Button
              key={button.label}
              onClick={() => {
                hide();
                button.onClick?.();
              }}
              customType={
                button.type === 'cancel' ? ButtonType.SECONDARY_COLOR : ButtonType.PRIMARY
              }
              customSize="lg"
            >
              {button.label}
            </Button>
          ))
        : [
            <Button
              key="キャンセル"
              customType={ButtonType.SECONDARY_COLOR}
              onClick={hide}
              customSize="lg"
            >
              キャンセル
            </Button>,
            <Button
              key="受付登録"
              customType={ButtonType.PRIMARY}
              onClick={() => {
                hide();
                onConfirm?.();
              }}
              customSize="lg"
            >
              受付登録
            </Button>,
          ],
      width: '464px',
      onClose: hide,
    });
  };

  return { showWarningDialog, hideWarningDialog: hide };
};
