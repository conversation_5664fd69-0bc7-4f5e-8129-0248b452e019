import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import { useState } from 'react';

export interface WarningDialogProps {
  title?: string;
  message: React.ReactNode;
  height?: string | number;
  hideIcon?: boolean;
  onConfirm?: (ctx: { setError: (msg: string) => void }) => void | Promise<void>;
  confirmLabel?: string;
  buttons?: WarningDialogButtons[];
  customContent?:
    | React.ReactNode
    | ((ctx: { error: string; setError: (msg: string) => void }) => React.ReactNode);
  onCancel?: () => void;
}

interface WarningDialogButtons {
  type: 'cancel' | 'confirm' | 'delete';
  label: string;
  onClick: () => void;
  style?: React.CSSProperties;
}

export const useWarningDialog = () => {
  const { show, hide, update } = useModalConfirm();

  const showWarningDialog = ({
    title,
    message,
    onConfirm,
    buttons,
    confirmLabel = '受付登録',
    customContent,
    hideIcon = false,
    onCancel,
    height,
  }: WarningDialogProps) => {
    let setErrorRef: (msg: string) => void = () => {};
    let isProcessing = false;

    const getButtons = (loading: boolean): React.ReactNode[] =>
      buttons?.length
        ? buttons.map(btn => (
            <Button
              key={btn.label}
              customSize="lg"
              customType={
                btn.type === 'cancel'
                  ? ButtonType.SECONDARY_COLOR
                  : btn.type === 'delete'
                  ? ButtonType.RED_PRIMARY
                  : ButtonType.PRIMARY
              }
              onClick={() => {
                hide();
                btn.onClick?.();
              }}
              style={btn.style || { width: '120px' }}
            >
              {btn.label}
            </Button>
          ))
        : [
            <Button
              key="cancel"
              customType={ButtonType.SECONDARY_COLOR}
              onClick={hide}
              customSize="lg"
              style={{
                width: '120px',
              }}
            >
              キャンセル
            </Button>,
            <Button
              key="confirm"
              customType={ButtonType.PRIMARY}
              customSize="lg"
              loading={loading}
              disabled={loading}
              onClick={async () => {
                if (isProcessing) return;
                isProcessing = true;
                update({ buttons: getButtons(true) });

                try {
                  const res = onConfirm?.({ setError: setErrorRef });
                  if (res instanceof Promise) await res;
                  hide();
                } catch (e) {
                  console.error(e);
                } finally {
                  isProcessing = false;
                  update({ buttons: getButtons(false) });
                }
              }}
              style={{
                width: '120px',
              }}
            >
              {confirmLabel}
            </Button>,
          ];

    const ContentWrapper = () => {
      const [error, setError] = useState('');
      setErrorRef = setError;

      return (
        <Flex align="start" justify="space-between" gap={16}>
          {!hideIcon && <Icon name="warning" native className="mt-2" />}
          <div style={{ flex: 1 }}>
            {customContent ? (
              <>
                <p style={{ marginBottom: 8 }}>{message}</p>
                <Flex align="flex-start" gap={16} style={{ marginTop: 8 }}>
                  {typeof customContent === 'function'
                    ? customContent({ error, setError })
                    : customContent}
                </Flex>
              </>
            ) : (
              <p>{message}</p>
            )}
            {error && <p style={{ color: '#db4b30', marginTop: 8 }}>{error}</p>}
          </div>
        </Flex>
      );
    };

    show({
      title: title ?? '',
      content: <ContentWrapper />,
      buttons: getButtons(false),
      width: '464px',
      height: height ? height : 'auto',
      onClose: () => {
        hide();
      },
      warning: true,
      onCancel: onCancel,
    });
  };

  return { showWarningDialog, hideWarningDialog: hide };
};
