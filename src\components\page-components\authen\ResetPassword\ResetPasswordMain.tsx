import { ERROR_COMMON_MESSAGE, routerPaths, STATUS_CODES } from '@/types/constants';
import styles from '../LoginMain/LoginMain.module.scss';
import { Form } from 'antd';
import { AppFormItem } from '@/components/common/core/FormItem';
import { useEffect, useRef, useState } from 'react';
import Button from '@/components/common/core/Button';

import { useNav } from '@/hooks/useNav';
import { useLocation } from 'react-router-dom';
import { useResetPasswordMutation, useVerifyResetPasswordTokenMutation } from '@/store/auth/api';
import Loading from '@/components/common/core/Loading/Loading';
import { ResetPasswordParams } from '@/store/auth/type';
import { AuthBox } from '../AuthBox';
import { AppInput } from '@/components/common/core/Input';
import { showFlashNotice } from '@/utils/flashNotice';
import clsx from 'clsx';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';

const initialFormValues = {
  password: '',
  password_confirmation: '',
};

const ChangePasswordSuccess = () => {
  const navigate = useNav();
  const changePasswordSuccessMessage = {
    line1: '新しいパスワードの設定が',
    line2: '完了しました。',
  };

  return (
    <AuthBox
      title={Object.values(changePasswordSuccessMessage).map(item => (
        <p className={`${styles.emailRequestSuccessTitle} fs20-medium`}>{item}</p>
      ))}
    >
      <Button
        type="primary"
        customSize="2xl"
        onClick={() => {
          navigate(routerPaths.login);
        }}
        style={{ width: '100%' }}
        className={styles.loginButton}
      >
        ログイン
      </Button>
    </AuthBox>
  );
};

const VerifyTokenFail = () => {
  const navigate = useNav();
  const verifyTokenFailedMessage = {
    line1: 'パスワード再設定ページのURLの有効期',
    line2: '限がきれています。',
  };
  return (
    <AuthBox
      title={Object.values(verifyTokenFailedMessage).map(item => (
        <p className={`${styles.emailRequestSuccessTitle} fs20-medium`}>{item}</p>
      ))}
    >
      <p className={clsx('fs14-regular', styles.resetPasswordFailedDescription)}>
        再度パスワードの再発行を依頼してください。
      </p>
      <Button
        type="primary"
        customSize="2xl"
        onClick={() => {
          navigate(routerPaths.login, {
            params: {
              state: 'forgotPassword',
            },
          });
        }}
        style={{ width: '100%' }}
        className={styles.loginButton}
      >
        パスワード再発行に戻る
      </Button>
    </AuthBox>
  );
};

type FROM_STATE = 'successful' | 'failed' | 'init' | 'reset';

export default function ResetPasswordMain() {
  const navigate = useNav();
  const [form] = Form.useForm<ResetPasswordParams>();
  const [formState, setFormState] = useState<FROM_STATE>('init');
  const { search, pathname } = useLocation();
  const clinic_cd = pathname.split('/')[1];
  const params = new URLSearchParams(search);

  const [verifyResetPasswordMutation, { isLoading: isVerifyLoading }] =
    useVerifyResetPasswordTokenMutation();
  const [resetPasswordMutation, { isLoading: isResetPasswordLoading }] = useResetPasswordMutation();
  const isLoading = isVerifyLoading || isResetPasswordLoading;

  const { handleValuesChange, submitHandler } = useUnsavedWarning(initialFormValues);

  const handleSubmit = async (values: ResetPasswordParams) => {
    const urlToken = params.get('token');
    resetPasswordMutation({
      ...values,
      token: urlToken!,
      clinic_cd: clinic_cd,
    })
      .then(res => {
        const status = res.data?.status;
        if (status !== STATUS_CODES.OK) {
          setFormState('failed');
          return;
        }
        setFormState('successful');
        showFlashNotice({
          type: 'success',
          message: 'パスワードを再設定しました。',
        });
        form.resetFields();
      })
      .catch(() => {
        setFormState('failed');
      });
  };

  useEffect(() => {
    const verifyResetPasswordTokenHandler = async () => {
      const urlToken = params.get('token');
      if (urlToken && clinic_cd) {
        const res = await verifyResetPasswordMutation({ token: urlToken, clinic_cd });
        const isValid = res.data?.data?.token_is_valid;
        if (!isValid) {
          setFormState('failed');
        } else {
          setFormState('reset');
        }
        return;
      }
      navigate(routerPaths.login);
    };
    verifyResetPasswordTokenHandler();
  }, []);

  switch (formState) {
    case 'successful':
      return <ChangePasswordSuccess />;
    case 'failed':
      return <VerifyTokenFail />;
    case 'init':
      return <Loading />;
    default:
      break;
  }
  const footerContent = (
    <a
      onClick={() => {
        navigate(routerPaths.login);
      }}
    >
      ログイン画面に戻る
    </a>
  );
  return (
    <AuthBox
      title={
        <span className={`${styles.forgotPasswordTitle} fs24-medium`}>新しいパスワードを設定</span>
      }
      footerContent={footerContent}
    >
      <Form
        form={form}
        initialValues={initialFormValues}
        className={styles.loginInput}
        layout="vertical"
        onFinish={values => {
          submitHandler();
          handleSubmit(values);
        }}
        autoComplete="off"
        onValuesChange={handleValuesChange}
      >
        <AppFormItem
          name="password"
          label="新しいパスワード"
          className={styles.input}
          rules={[
            {
              validator: (_, value) => {
                if (!value || !value.trim()) {
                  return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('パスワード'));
                }

                const regex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,255}$/;
                if (!regex.test(value)) {
                  return Promise.reject(ERROR_COMMON_MESSAGE.PASSWORD_INVALID);
                }

                return Promise.resolve();
              },
            },
          ]}
          required
        >
          <AppInput
            type="password"
            className={styles.input}
            width="100%"
            disabled={isLoading}
            placeholder="入力してください"
          />
        </AppFormItem>

        <AppFormItem
          name="password_confirmation"
          label="新しいパスワードの確認"
          className={styles.input}
          rules={[
            {
              validator(_, value) {
                if (!value || !value.trim()) {
                  return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('確認用パスワード'));
                }

                const password = form.getFieldValue('password');
                if (value !== password) {
                  return Promise.reject(ERROR_COMMON_MESSAGE.PASSWORD_NOT_MATCH);
                }

                return Promise.resolve();
              },
            },
          ]}
          required
        >
          <AppInput
            type="password"
            className={styles.input}
            width="100%"
            disabled={isLoading}
            placeholder="入力してください"
          />
        </AppFormItem>

        <p className={clsx('fs14-regular', styles.resetPasswordDescription)}>
          ※パスワードは半角英数字混在させて6文字以上を入力してください。
        </p>
        <Button
          type="primary"
          customSize="2xl"
          htmlType="submit"
          loading={isLoading}
          style={{ width: '100%' }}
          className={styles.resetPasswordButton}
        >
          再設定
        </Button>
      </Form>
    </AuthBox>
  );
}
