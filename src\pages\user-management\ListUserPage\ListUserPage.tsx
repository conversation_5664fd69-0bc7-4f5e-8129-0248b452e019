import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import PageLayout from '@/components/layouts/PageLayout';
import UserManagementContent from '@/components/page-components/user-management/UserManagementContent';

import { ButtonType } from '@/types/enum';
import { redirectToMedixSync } from '@/utils';
import { useAppSelector } from '@/config/redux/store';
import { getAccessToken } from '@/store/auth/selectors';

export default function UserManagementPage() {
  const accessToken = useAppSelector(getAccessToken);
  return (
    <PageLayout
      title={'ユーザー一覧'}
      headerRight={
        <>
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            customSize="md"
            onClick={() =>
              redirectToMedixSync({ from: 'karute', path: 'create', token: accessToken })
            }
          >
            <Icon name="addPerson" />
            ユーザー登録
          </Button>
        </>
      }
    >
      <UserManagementContent />
    </PageLayout>
  );
}
