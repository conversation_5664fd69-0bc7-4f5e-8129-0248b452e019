import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import Image from '@/components/common/core/Image';
import PageLayout from '@/components/layouts/PageLayout';
import { routerPaths } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { useNavigate } from 'react-router-dom';

function UserNotFoundPage() {
  const navigate = useNavigate();
  return (
    <PageLayout
      title={'ユーザー一覧'}
      headerRight={
        <>
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            customSize="md"
            onClick={() => {
              navigate(routerPaths.create);
            }}
          >
            <Icon name="addPerson" />
            ユーザー登録
          </Button>
        </>
      }
    >
      <div
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Image
          name="notFound"
          style={{
            width: '100%',
            height: 'var(--page-content-height)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      </div>
    </PageLayout>
  );
}

export default UserNotFoundPage;
