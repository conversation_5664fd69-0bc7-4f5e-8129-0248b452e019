.modal {
  .modalWrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    max-height: 100%;

    :global(.ant-spin-nested-loading),
    :global(.ant-spin-container) {
      height: 100%;
      max-height: 100%;
    }
  }
  .container {
    flex: 1;
    border: 1px solid $gray-200;
    border-radius: 8px;
    margin: 20px 0;
    max-height: calc(100% - 60px);
    display: flex;
    justify-content: space-between;
    max-width: 100%;
    overflow: auto;
    height: 100%;

    .sidebar {
      display: flex;

      .gridStub {
        flex: 1;
        border-right: 1px solid $gray-200;
        padding: 16px;
      }
    }
    .content {
      flex: 1;
    }
  }
  :global(.ant-modal-body) {
    height: 920px !important;
    max-height: calc(100vh - 80px) !important;
  }
}
