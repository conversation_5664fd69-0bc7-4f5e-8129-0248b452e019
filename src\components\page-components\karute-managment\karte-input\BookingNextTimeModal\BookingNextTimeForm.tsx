import Button from '@/components/common/core/Button';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { useAppSelector } from '@/config/redux/store';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import {
  useCreateBookingMutation,
  useLazyGetListCourseByPaymentTypeQuery,
  useLazyGetListCourseByPractitionerQuery,
} from '@/store/booking/api';
import { BookingNextTimePayload } from '@/store/booking/type';
import { useLazyGetDoctorsQuery } from '@/store/shared/api';
import {
  BOOKING_SUCCESS_MSG,
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PAYMENT_COURSE_TYPE,
} from '@/types/constants';
import {
  ButtonType,
  EPaymentCourse,
  EPaymentCourseMapping,
  ERemindType,
  ERemindTypeMapping,
} from '@/types/enum';
import { showFlashNotice } from '@/utils';
import { DatePicker, Empty, Flex, Form, Spin, TimePicker } from 'antd';
import { useWatch } from 'antd/es/form/Form';
import { DatePickerProps, Select } from 'antd/lib';
import { HttpStatusCode } from 'axios';
import dayjs, { Dayjs } from 'dayjs';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import styles from './BookingNextTime.module.scss';

type Props = {
  handleCancel: () => void;
};

type ISelectData = {
  label: string;
  value: number | string;
  [key: string]: number | string;
};
const initialValues: Partial<
  BookingNextTimePayload & {
    booking_time?: [dayjs.Dayjs | null, dayjs.Dayjs | null];
  }
> = {
  payment_course_type: undefined,
  course_id: undefined,
  booking_from: '',
  booking_to: '',
  booking_remind_type: undefined,
  remind_email: '',
  practitioner_id: undefined,
  booking_time: [null, null],
};

export const BookingNextTimeForm = forwardRef<{ confirmLeave: () => boolean }, Props>(
  ({ handleCancel }, ref) => {
    const [form] = Form.useForm<
      Partial<
        BookingNextTimePayload & {
          booking_time?: [dayjs.Dayjs | null, dayjs.Dayjs | null];
        }
      >
    >();
    const { handleValuesChange, submitHandler, confirmLeave } = useUnsavedWarning(initialValues);
    const [isShowSpecific, setShowSpecific] = useState<boolean>(false);

    const [courseOptions, setCourseOptions] = useState<ISelectData[]>([]);
    const [doctorOptions, setDoctorOptions] = useState<ISelectData[]>([]);
    const [queryCourse, { data: courseOtpByPaymentType, isFetching: fetchCourseByPayment }] =
      useLazyGetListCourseByPaymentTypeQuery();
    const [
      queryCourseByPractitioner,
      { data: courseByPractitioner, isFetching: fetchCoursePractitioner },
    ] = useLazyGetListCourseByPractitionerQuery();

    const [queryPractitioner, { data: doctors, isFetching: fetchDoctorsByCourseId }] =
      useLazyGetDoctorsQuery();

    const [mutation, { isLoading }] = useCreateBookingMutation();

    const { services, doctors: allDoctors } = useAppSelector(state => state.sharedData);
    const { patientInfo } = useAppSelector(state => state.karute.karute.details);

    useImperativeHandle(ref, () => ({
      confirmLeave,
    }));

    const onFormCancel = () => {
      if (confirmLeave()) handleCancel();
    };
    const courseTypeOptions = useMemo(() => {
      const includedIds = new Set(services.map(s => s.service_id));

      return Object.entries(EPaymentCourseMapping)
        .map(([key, label]) => {
          const courseKey = key as keyof typeof PAYMENT_COURSE_TYPE;
          const value = PAYMENT_COURSE_TYPE[courseKey];
          return {
            label,
            value,
            include: includedIds.has(value) || key === EPaymentCourse.JIHI,
          };
        })
        .filter(item => item.include)
        .map(({ label, value }) => ({ label, value }));
    }, [services]);

    const paymentCourseType = useWatch('payment_course_type', form);
    const courseId = useWatch('course_id', form);
    const practitionerId = useWatch('practitioner_id', form);

    const isFetching = useMemo(
      () => fetchCourseByPayment || fetchCoursePractitioner || fetchDoctorsByCourseId,
      [fetchCourseByPayment, fetchCoursePractitioner, fetchDoctorsByCourseId]
    );

    const isJihi = useMemo(() => {
      return paymentCourseType === PAYMENT_COURSE_TYPE.jihi;
    }, [paymentCourseType]);

    const isPractitionerChange = useMemo(
      () => isJihi && practitionerId !== undefined,
      [isJihi, practitionerId]
    );

    const courseData = useMemo(
      () =>
        isPractitionerChange
          ? Array.isArray(courseByPractitioner?.data)
            ? courseByPractitioner.data
            : []
          : Array.isArray(courseOtpByPaymentType?.data)
          ? courseOtpByPaymentType.data
          : [],
      [courseByPractitioner, courseOtpByPaymentType, isPractitionerChange]
    );

    const courseOtps = useMemo(
      () =>
        courseData?.map(item => ({
          label: item.course_name,
          value: item.course_id,
          duration: item.duration,
        })),
      [courseData]
    );

    const doctorsOtp = useMemo(() => {
      if (!courseId && !practitionerId && isJihi) {
        return allDoctors.map(doctor => ({ label: doctor.kanji_name, value: doctor.user_id }));
      }
      if (doctors?.data && Array.isArray(doctors.data)) {
        return (
          doctors.data.map(doctor => ({ label: doctor.kanji_name, value: doctor.user_id })) || []
        );
      }
      return allDoctors.map(doctor => ({ label: doctor.kanji_name, value: doctor.user_id }));
    }, [practitionerId, doctors?.data, courseId]);

    const remindTypeOtps = useMemo(() => {
      return Object.entries(ERemindTypeMapping).map(([key, val]) => ({
        label: val,
        value: key,
        disabled:
          (key === ERemindType.EMAIL && !patientInfo?.email?.length) ||
          (key === ERemindType.LINE && !patientInfo?.line_id?.length),
      }));
    }, [patientInfo]);

    const courseDuration = useMemo(() => {
      const selected = courseOptions?.find(c => c.value === courseId);
      return selected?.duration ?? null;
    }, [courseOptions, courseId]);

    useEffect(() => {
      setCourseOptions(courseOtps || []);
    }, [courseOtps]);

    useEffect(() => {
      setDoctorOptions(doctorsOtp);
    }, [doctorsOtp]);

    useEffect(() => {
      if (paymentCourseType !== undefined) {
        form.setFieldsValue({
          booking_time: [null, null],
          practitioner_id: undefined,
          course_id: undefined,
        });
        if (isJihi) {
          queryCourse({
            payment_type: paymentCourseType,
          });
        } else {
          queryCourse({
            payment_type: paymentCourseType,
          })
            .unwrap()
            .then(res => {
              if (
                Array.isArray(res.data) &&
                res.data.length > 0 &&
                typeof res.data[0] === 'object' &&
                'course_id' in res.data[0]
              ) {
                form.setFieldValue(
                  'course_id',
                  (res.data[0] as { course_id: number | string }).course_id
                );
              }
            });
          setDoctorOptions(
            allDoctors.map(dtor => ({ label: dtor.kanji_name, value: dtor.user_id }))
          );
        }
      } else {
        form.resetFields(['course_id', 'practitioner_id']);
      }
    }, [paymentCourseType]);

    useEffect(() => {
      const [start] = form.getFieldValue('booking_time') || [];

      if (start && courseDuration) {
        const autoEnd = start.add(Number(courseDuration), 'minute');
        const updatedRange = [start, autoEnd];
        form.setFieldsValue({ booking_time: updatedRange });
      } else {
        form.setFieldsValue({ booking_time: [null, null] });
      }
    }, [courseDuration]);

    const handleTimeChange = async (date: dayjs.Dayjs | null) => {
      if (!date) {
        form.setFieldsValue({
          booking_time: [null, null],
        });
        await form.validateFields(['booking_time']);
        return;
      }
      if (date) {
        if (date && courseDuration) {
          const autoEnd = date.add(Number(courseDuration), 'minute');
          form.setFieldsValue({ booking_time: [date, autoEnd] });
          return;
        } else {
          form.setFieldsValue({ booking_time: [date, null] });
        }
      }
    };

    const onFinish = async (
      values: Partial<
        BookingNextTimePayload & {
          practitioner_id?: number;
          booking_time?: [dayjs.Dayjs | null, dayjs.Dayjs | null];
        }
      >
    ) => {
      const [start] = values.booking_time || [];

      if (values.payment_course_type === undefined) {
        throw new Error(ERROR_COMMON_MESSAGE.REQUIRED('予約タイプ'));
      }

      const payload: Omit<BookingNextTimePayload, 'booking_to' | 'practitioner_id'> & {
        practitioner_account_id?: number;
      } = {
        payment_course_type: values.payment_course_type,
        course_id: values.course_id || Number(courseOptions[0].value),
        booking_from: start?.format('YYYY-MM-DD HH:mm:ss') || '',
        booking_remind_type: values.booking_remind_type ?? ERemindType.MUTE,
        practitioner_account_id: values.practitioner_id,
        ...(values.remind_email && values.booking_remind_type === ERemindType.SPECIFIC_EMAIL
          ? { remind_email: values.remind_email }
          : {}),
      };
      try {
        const res = await mutation({ patientId: Number(patientInfo?.patient_id), payload });
        if (res.data?.status !== HttpStatusCode.Ok) {
          const data = res.data?.data ?? {};
          const entries = Object.entries(data);

          const fields = Array.isArray(entries)
            ? entries
                .filter(([field]) => !!field)
                .map(([field, messages]) => ({
                  name: field === 'booking_from' ? 'booking_time' : field,
                  errors: [Array.isArray(messages) ? messages[0] : String(messages)],
                }))
            : [];
          if (fields.length) {
            form.setFields(fields as any);
          } else {
            showFlashNotice({ type: 'error', message: res.data?.message as string });
          }
        } else {
          showFlashNotice({ type: 'success', message: BOOKING_SUCCESS_MSG });
          submitHandler();
          handleCancel();
        }
      } catch (err: any) {
        showFlashNotice({
          type: 'error',
          message: err?.message || (NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL as string),
        });
      }
    };

    const cellRender: DatePickerProps<Dayjs>['cellRender'] = (current, info) => {
      return (
        <div
          className={
            dayjs(current) > dayjs() ? 'ant-picker-cell-inner ant-picker-cell-in-view' : ''
          }
        >
          {info.originNode}
        </div>
      );
    };

    return (
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={onFinish}
        style={{
          flex: 1,
        }}
        onValuesChange={handleValuesChange}
        className={styles.formCustom}
        validateTrigger={['onSubmit', 'onBlur', 'onChange']}
      >
        <Flex vertical gap="middle">
          <AppFormItem
            label={'予約タイプ'}
            name="payment_course_type"
            required
            style={{
              width: '360px',
            }}
            rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('予約タイプ') }]}
          >
            <Select
              className={styles.selectCustom}
              placeholder="選択してください"
              options={courseTypeOptions}
              allowClear={true}
              onChange={val => {
                form.resetFields(['course_id']);
                queryCourse({ payment_type: val });
                return val;
              }}
              dropdownRender={menu =>
                isFetching ? (
                  <div
                    style={{
                      minHeight: '100px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Spin />
                  </div>
                ) : (
                  menu
                )
              }
              filterOption={(input, option) =>
                (typeof option?.label === 'string' ? option.label.toLowerCase() : '').includes(
                  input.toLowerCase()
                )
              }
              size="small"
            />
          </AppFormItem>
          <AppFormItem
            label={'コース'}
            name="course_id"
            required
            rules={[
              {
                validator: (_, value) => {
                  if (!value && isJihi) {
                    return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('コース'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{
              width: '360px',
            }}
          >
            <Select
              className={styles.selectCustom}
              placeholder={
                !isJihi && paymentCourseType !== undefined
                  ? courseOptions[0]?.label || ''
                  : '選択してください'
              }
              options={courseOptions}
              disabled={!isJihi}
              onChange={val => {
                if (val && isJihi) {
                  queryPractitioner({
                    course_id: val,
                  });
                }

                return val;
              }}
              onClear={() => {
                form.resetFields(['practitioner_id']);
              }}
              dropdownRender={menu =>
                isFetching ? (
                  <div
                    style={{
                      minHeight: '100px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Spin />
                  </div>
                ) : (
                  menu
                )
              }
              notFoundContent={
                <Empty
                  style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="データがありません"
                />
              }
              allowClear
              filterOption={(input, option) =>
                (typeof option?.label === 'string' ? option.label.toLowerCase() : '').includes(
                  input.toLowerCase()
                )
              }
              size="small"
            />
          </AppFormItem>
          <AppFormItem
            label={'施術師'}
            name="practitioner_id"
            required
            rules={[
              {
                validator: (_, value) => {
                  const payment_course_type = form.getFieldValue('payment_course_type');
                  if (!value && payment_course_type !== undefined) {
                    return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('施術師'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{
              width: '360px',
            }}
          >
            <Select
              className={styles.selectCustom}
              disabled={paymentCourseType === undefined}
              placeholder="選択してください"
              options={doctorOptions}
              allowClear
              onClear={() => {
                form.resetFields(['course_id']);
              }}
              onChange={val => {
                if (!!val && isJihi) {
                  queryCourseByPractitioner({ practitioner_id: val });
                }
                return val;
              }}
              dropdownRender={menu =>
                isFetching ? (
                  <div
                    style={{
                      minHeight: '100px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Spin />
                  </div>
                ) : (
                  menu
                )
              }
              notFoundContent={
                <Empty
                  style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="データがありません"
                />
              }
              showSearch
              filterOption={(input, option) => {
                return (
                  typeof option?.label === 'string' ? option.label.toLowerCase() : ''
                ).includes(input.toLowerCase());
              }}
              size="small"
            />
          </AppFormItem>
          <Flex gap="middle">
            <AppFormItem
              label="日時"
              name="booking_time"
              required
              style={{ width: '360px' }}
              rules={[
                {
                  validator: (_, value) => {
                    const [start] = form.getFieldValue('booking_time');
                    if (!start || !value) {
                      return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('日時'));
                    } else {
                      return Promise.resolve();
                    }
                  },
                },
              ]}
            >
              <DatePicker
                style={{
                  height: '40px',
                  width: '100%',
                }}
                placeholder="選択してください"
                showTime={{
                  format: 'HH:mm',
                }}
                format={() => {
                  if (!form.getFieldValue('booking_time')) return '';
                  const [start, end] = form.getFieldValue('booking_time');

                  return dayjs(start).isValid()
                    ? `${dayjs(start).format('YYYY/MM/DD HH:mm')} ~ ${
                        end ? dayjs(end).format('HH:mm') : ''
                      }`
                    : '';
                }}
                onChange={handleTimeChange}
                allowClear={true}
                disabledDate={current => current && current <= dayjs().startOf('day')}
                cellRender={cellRender}
                suffixIcon={<Icon name="calendar" width={16} height={16} />}
              >
                <TimePicker />
              </DatePicker>
            </AppFormItem>
          </Flex>

          <AppFormItem
            label={'予約通知の受信方法'}
            name="booking_remind_type"
            rules={[
              { required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('予約通知の受信方法') },
            ]}
            required
            style={{ width: '360px' }}
          >
            <Select
              className={styles.selectCustom}
              placeholder="テキスト"
              options={remindTypeOtps}
              onChange={val => {
                setShowSpecific(val === ERemindType.SPECIFIC_EMAIL);
                return val;
              }}
              allowClear
              size="small"
            />
          </AppFormItem>
          {isShowSpecific && (
            <AppFormItem
              name="remind_email"
              label="メールアドレス"
              required
              rules={[
                {
                  required: isShowSpecific,
                  message: ERROR_COMMON_MESSAGE.REQUIRED('メールアドレス'),
                },
              ]}
              style={{
                backgroundColor: '#F2F4F7',
                width: '360px',
                height: 'auto',
                minHeight: '98px',
                padding: '16px',
                borderRadius: '8px',
              }}
              getValueFromEvent={({ target }) => target?.value?.trim()}
            >
              <AppInput
                style={{ width: '328px', height: '40px' }}
                placeholder="入力してください"
                maxLength={50}
                size="small"
              />
            </AppFormItem>
          )}

          <Flex gap="middle" justify="end" className="pt-2">
            <Button
              customSize="lg"
              customType={ButtonType.SECONDARY_COLOR}
              onClick={onFormCancel}
              className="btn-modal-width"
              loading={isLoading}
            >
              キャンセル
            </Button>
            <Button
              customSize="lg"
              customType={ButtonType.PRIMARY}
              htmlType="submit"
              className="btn-modal-width"
              loading={isLoading}
            >
              登録
            </Button>
          </Flex>
        </Flex>
      </Form>
    );
  }
);
