import React, { ReactNode, useEffect } from 'react';
import styles from './DashboardLayout.module.scss';
import { Outlet, useLocation } from 'react-router-dom';
import Header from '@/components/common/layout/Header';
import Sidebar from '@/components/common/layout/Sidebar';
import { useMasterData } from '@/hooks/useMasterData';
import { useScreen } from '@/hooks/useScreen';
import { useAppDispatch } from '@/config/redux/store';
import { prefetchSharedData } from '@/store/shared/thunk';
import { useParams } from 'react-router-dom';
interface DashboardLayoutProps {
  children?: ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { selectionLoading } = useMasterData();
  const { screen } = useScreen();
  const dispatch = useAppDispatch();
  const { clinicCd, id } = useParams<{ clinicCd: string; id: string }>();
  const { pathname } = useLocation();
  const secondaryPath = pathname.split('/').filter(Boolean)[1] ?? '';

  useEffect(() => {
    dispatch(prefetchSharedData());
  }, [clinicCd, dispatch, secondaryPath, id]);

  return (
    <div className={styles.dashboardLayout}>
      <Header />

      <div className={styles.body}>
        {screen !== 'mobile' && <Sidebar />}

        <div className={styles.mainContent}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
