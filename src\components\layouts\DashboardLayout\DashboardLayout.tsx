import React from 'react';
import styles from './DashboardLayout.module.scss';
import { Outlet } from 'react-router-dom';
import Header from '@/components/common/layout/Header';

const DashboardLayout: React.FC = () => {
  return (
    <div className={styles.dashboardLayout}>
      <Header />
      <div className={styles.body}>
        <div className={styles.mainContent}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
