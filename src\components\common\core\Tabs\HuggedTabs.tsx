// TODO: review needed
import { FC, useRef, useEffect, useState } from 'react';
import styles from './Tabs.module.scss';
import { TabItem } from '.';
import Icon from '../Icon';
import clsx from 'clsx';

interface HuggedTabsProps {
  items: TabItem[];
  activeValues: Record<string, boolean>;
  onChange: (key: string) => void;
  className?: string;
}

export const HuggedTabs: FC<HuggedTabsProps> = ({
  items,
  activeValues,
  onChange,
  className = '',
}) => {
  return (
    <div className={`${styles.hugged_tabs} ${className}`}>
      <div className={styles.tabs_options}>
        {items.map(item => (
          <div
            key={item.key}
            className={`${styles.tab_option} ${activeValues[item.value] ? styles.active : ''}`}
            onClick={() => onChange(item.value)}
          >
            {typeof item.label === 'string'
              ? `${item.label} ${item.count !== undefined ? `(${item.count})` : ''}`
              : item.label}
          </div>
        ))}
      </div>
    </div>
  );
};

interface PagingHuggedTabsProps {
  items: TabItem[];
  activeValues: Record<string, boolean>;
  onChange: (key: string) => void;
  className?: string;
  defaultActiveItem?: string; // Thêm prop mới
}

export const PagingHuggedTabs: FC<PagingHuggedTabsProps> = ({
  items,
  activeValues,
  onChange,
  className = '',
  defaultActiveItem,
}) => {
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [tabWidth, setTabWidth] = useState(0);

  // Get with of the first tab and set it to tabWidth state variable
  useEffect(() => {
    if (tabRefs.current[0]) {
      const firstTabWidth = tabRefs.current[0]?.offsetWidth || 0;
      setTabWidth(firstTabWidth);
    }
  }, []);

  // Scroll to default active item
  // TO-DO: Bug: scroll in-view is stalled in the latter part when item become too large
  // current workable number is 8 -> will be handled when integrating API
  useEffect(() => {
    if (defaultActiveItem && tabsContainerRef.current && tabWidth > 0) {
      const activeIndex = items.findIndex(item => item.value === defaultActiveItem);
      if (activeIndex !== -1) {
        const container = tabsContainerRef.current;
        const containerWidth = container.clientWidth;
        const targetScroll = tabWidth * activeIndex;

        // Calculate scroll position to center the item if possible
        const centerOffset = (containerWidth - tabWidth) / 2;
        const desiredScroll = Math.max(0, targetScroll - centerOffset);

        // Calculate scroll boundaries
        const maxScroll = container.scrollWidth - containerWidth;
        const finalScroll = Math.min(desiredScroll, maxScroll);
        // set active item with default active item
        onChange(defaultActiveItem);
        container.scrollTo({
          left: finalScroll,
          behavior: 'smooth',
        });
      }
    }
  }, [defaultActiveItem, tabWidth]);

  const checkScrollability = () => {
    if (tabsContainerRef.current) {
      const container = tabsContainerRef.current;
      const maxScroll = container.scrollWidth - container.clientWidth;

      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(container.scrollLeft < maxScroll);
    }
  };

  useEffect(() => {
    const container = tabsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollability);
      checkScrollability();
      return () => {
        container.removeEventListener('scroll', checkScrollability);
      };
    }
  }, []);

  useEffect(() => {
    window.addEventListener('resize', checkScrollability);
    return () => {
      window.removeEventListener('resize', checkScrollability);
    };
  }, []);

  const handlePrevious = () => {
    if (tabsContainerRef.current && canScrollLeft) {
      const container = tabsContainerRef.current;
      container.scrollTo({
        left: Math.max(0, container.scrollLeft - tabWidth),
        behavior: 'smooth',
      });
    }
  };

  const handleNext = () => {
    if (tabsContainerRef.current && canScrollRight) {
      const container = tabsContainerRef.current;
      const maxScroll = container.scrollWidth - container.clientWidth;
      container.scrollTo({
        left: Math.min(maxScroll, container.scrollLeft + tabWidth),
        behavior: 'smooth',
      });
    }
  };

  return (
    <div className={`${styles.hugged_tabs} ${styles.paging_hugged_tabs} ${className}`}>
      <div className={styles.paging_controls}>
        <button
          className={`${styles.paging_button} ${styles.prev_button}`}
          onClick={handlePrevious}
          disabled={!canScrollLeft}
        >
          <Icon name="arrowLeft" color={canScrollLeft ? 'white' : 'gray'} />
        </button>

        <div className={styles.tabs_container} ref={tabsContainerRef}>
          <div className={styles.tabs_options}>
            {items.map((item, index) => (
              <div
                key={item.key}
                ref={el => (tabRefs.current[index] = el)}
                className={clsx(
                  'pagination_tab_option',
                  styles.tab_option,
                  activeValues[item.value] && styles.active
                )}
                onClick={() => onChange(item.value)}
              >
                {typeof item.label === 'string'
                  ? `${item.label} ${item.count !== undefined ? `(${item.count})` : ''}`
                  : item.label}
              </div>
            ))}
          </div>
        </div>

        <button
          className={`${styles.paging_button} ${styles.next_button}`}
          onClick={handleNext}
          disabled={!canScrollRight}
        >
          <Icon name="arrowRight" color={canScrollRight ? 'white' : 'gray'} />
        </button>
      </div>
    </div>
  );
};
