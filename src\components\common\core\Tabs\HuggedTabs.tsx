// TODO: review needed
import clsx from 'clsx';
import { FC, useEffect, useRef, useState } from 'react';
import { TabItem } from '.';
import Icon from '../Icon';
import styles from './Tabs.module.scss';

interface HuggedTabsProps {
  items: TabItem[];
  activeValues: Record<string, boolean>;
  onChange: (key: string | number) => void;
  className?: string;
}

export const HuggedTabs: FC<HuggedTabsProps> = ({
  items,
  activeValues,
  onChange,
  className = '',
}) => {
  return (
    <div className={`${styles.hugged_tabs} ${className}`}>
      <div className={styles.tabs_options}>
        {items.map(item => (
          <div
            key={item.key}
            className={clsx(
              styles.tab_option,
              activeValues[item.value!] ? [styles.active, 'fs14-bold'] : 'fs14-medium',
              'hugged_tab_option'
            )}
            onClick={() => onChange(item.value!)}
          >
            {typeof item.label === 'string'
              ? `${item.label} ${item.count !== undefined ? `(${item.count})` : ''}`
              : item.label}
          </div>
        ))}
      </div>
    </div>
  );
};

interface PagingHuggedTabsProps {
  items: TabItem[];
  activeValues: Record<string, boolean>;
  onChange: (key: string | number) => void;
  className?: string;
  defaultActiveItem?: string; // Thêm prop mới
}

export const PagingHuggedTabs: FC<PagingHuggedTabsProps> = ({
  items,
  activeValues,
  onChange,
  className = '',
  defaultActiveItem,
}) => {
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [tabWidth, setTabWidth] = useState(0);

  useEffect(() => {
    if (tabRefs.current[0]) {
      const firstTabWidth = tabRefs.current[0]?.offsetWidth || 0;
      setTabWidth(firstTabWidth);
    }
  }, []);

  useEffect(() => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const maxScroll = container.scrollWidth - container.offsetWidth;
    container.scrollTo({
      left: Math.max(0, maxScroll),
      behavior: 'smooth',
    });
  }, [items.length]);

  // Scroll to default active item
  // TO-DO: Bug: scroll in-view is stalled in the latter part when item become too large
  // current workable number is 8 -> will be handled when integrating API
  useEffect(() => {
    if (defaultActiveItem && tabsContainerRef.current && tabWidth > 0) {
      onChange(defaultActiveItem);
    }
  }, [defaultActiveItem, tabWidth]);

  const checkScrollability = () => {
    if (tabsContainerRef.current) {
      const container = tabsContainerRef.current;
      const maxScroll = container.scrollWidth - container.offsetWidth;

      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(container.scrollLeft < maxScroll);
    }
  };

  useEffect(() => {
    const container = tabsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollability);
      checkScrollability();
      return () => {
        container.removeEventListener('scroll', checkScrollability);
      };
    }
  }, []);

  useEffect(() => {
    window.addEventListener('resize', checkScrollability);
    return () => {
      window.removeEventListener('resize', checkScrollability);
    };
  }, []);

  const handlePrevious = () => {
    if (tabsContainerRef.current && canScrollLeft) {
      const container = tabsContainerRef.current;
      const numberOfItems = Math.floor(container.offsetWidth / tabWidth);
      container.scrollTo({
        left: Math.max(0, container.scrollLeft - numberOfItems * tabWidth),
        behavior: 'smooth',
      });
    }
  };

  const handleNext = () => {
    if (tabsContainerRef.current && canScrollRight) {
      const container = tabsContainerRef.current;
      const maxScroll = container.scrollWidth - container.offsetWidth;
      const numberOfItems = Math.floor(container.offsetWidth / tabWidth);
      container.scrollTo({
        left: Math.min(maxScroll, container.scrollLeft + numberOfItems * tabWidth),
        behavior: 'smooth',
      });
    }
  };

  return (
    <div className={`${styles.hugged_tabs} ${styles.paging_hugged_tabs} ${className}`}>
      <div className={styles.paging_controls}>
        <button
          className={`${styles.paging_button} ${styles.prev_button}`}
          onClick={handlePrevious}
          disabled={!canScrollLeft}
        >
          <Icon width={10} height={10} name="arrowLeft" color={canScrollLeft ? 'white' : 'gray'} />
        </button>

        <div className={styles.tabs_container} ref={tabsContainerRef}>
          <div className={styles.tabs_options}>
            {items.map((item, index) => (
              <div
                key={item.key}
                ref={el => (tabRefs.current[index] = el)}
                className={clsx(
                  'pagination_tab_option',
                  'fs14-medium',
                  styles.tab_option,
                  activeValues[item.value!] && styles.active
                )}
                onClick={() => onChange(item.value!)}
              >
                {typeof item.label === 'string'
                  ? `${item.label} ${item.count !== undefined ? `(${item.count})` : ''}`
                  : item.label}
              </div>
            ))}
          </div>
        </div>

        <button
          className={`${styles.paging_button} ${styles.next_button}`}
          onClick={handleNext}
          disabled={!canScrollRight}
        >
          <Icon
            width={10}
            height={10}
            name="arrowRight"
            color={canScrollRight ? 'white' : 'gray'}
          />
        </button>
      </div>
    </div>
  );
};
