import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse } from '@/types/interface';
import { Clinic } from '@/types/interface/User';
import { ClinicService } from '../shared/type';

export const authApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    getClinics: build.query<APIResponse<Clinic[]>, void>({
      query: () => ({ url: '/api/clinics/manager-by-me' }),
    }),
    getServicesByClinicId: build.query<APIResponse<ClinicService[]>, unknown>({
      query: () => ({
        url: `/api/services`,
        method: 'GET',
      }),
    }),
  }),
  overrideExisting: false,
});

export const { useLazyGetClinicsQuery, useLazyGetServicesByClinicIdQuery } = authApi;
