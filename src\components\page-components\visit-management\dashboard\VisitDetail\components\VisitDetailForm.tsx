import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input/AppInput';
import { AppRadio } from '@/components/common/core/Radio/AppRadio';
import { AppSelect } from '@/components/common/core/Select';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import {
  bookingTypeOptions,
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  EVisitType,
  kohiMap,
  VisitTypeOption,
} from '@/types/constants';
import { EditReceptionSchema } from '@/types/interface';
import { Col, Divider, Flex, Form, Radio, Row, Space } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styles from './VisitDetailForm.module.scss';

import { AppCheckbox } from '@/components/common/core/Checkbox';
import { AppDatePicker } from '@/components/common/core/Datepicker';
import {
  MultiTreaterSelectFormItem,
  TreeCourseFormItem,
} from '@/components/page-components/visit-management/dashboard/VisitDetail/components/CommonFormItem';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { EJyosaiFlag } from '@/types/enum';
import { convertMapToOptions } from '@/utils';
import { mapCoursesToCourseGroups } from '../helper';
import { useVisitDetail } from '../provider';
import { HokenCollapse } from './HokenCollapse';
import { Relationship } from './Relationship';
import VisitDetailMondaiListTable from './VisitDetailMondaiListTable';

const getBookingTime = (dateTime: string, fromTime: string, toTime: string) => {
  if (!dateTime || !fromTime || !toTime) return '';
  const formattedDate = dayjs(dateTime).format('YYYY/MM/DD');
  //e.g:  2025/03/20 16:00～16:30
  return `${formattedDate} ${fromTime}~${toTime}`;
};

export const convertPractitionersToSelectValue = (
  pracInVisit: { user_id: number; kanji_name?: string; kana_name?: string }[]
) =>
  pracInVisit.map(p => ({
    value: p.user_id,
    label: p.kanji_name ?? p.kana_name ?? `ID:${p.user_id}`,
  }));

const VisitDetailForm = () => {
  const {
    form,
    formRef,
    visitDetail,
    treeServices,
    isFetching,
    treaterList,
    handleFinish,
    getInsuranceType,
  } = useVisitDetail();

  const [init, setInit] = useState<EditReceptionSchema>({
    visit_via: null,
    booking_time: '',
    visit_type: EVisitType.FIRST_TIME,
    course_groups: {},
    practitioners: [],
    hoken_type: '',
    hoken_no: '',
    hoken_name: '',
    hoken_kigo: '',
    hoken_shikaku_date: '',
    hoken_yuko_date: '',
    kohi_type: '',
    kohi_no: '',
    kohi_jukyusya_no: '',
    kohi_ritsu: 0,
    kohi_start_date: dayjs(),
    kohi_end_date: dayjs(),
    kohi_jyosei_flg: false,
  });
  const { handleValuesChange, submitHandler } = useUnsavedWarning(init);
  const [isCollapsed, setIsCollapsed] = useState(false);
  useEffect(() => {
    if (!isFetching && visitDetail) {
      const { booking, visit_type, patient_hoken, courses, visit_via, practitioners, patient } =
        visitDetail;
      const initValue = {
        visit_via: visit_via ?? booking.booking_via,
        booking_time: getBookingTime(
          booking?.booking_from as string,
          booking?.booking_time_from as string,
          booking?.booking_time_to as string
        ),

        visit_type: visit_type,
        course_groups: mapCoursesToCourseGroups(
          treeServices ?? [],
          courses.map(c => String(c.course_id))
        ),
        practitioners: convertPractitionersToSelectValue(practitioners ?? []),
        // HOKEN
        hoken_type: getInsuranceType(patient_hoken?.[0]?.hoken_type),
        hoken_no: patient_hoken?.[0]?.hoken_no,
        hoken_name: patient_hoken?.[0]?.hoken_name,
        hoken_kigo: patient_hoken?.[0]?.hoken_kigo,
        hoken_shikaku_date: patient_hoken?.[0]?.hoken_shikaku_date,
        hoken_yuko_date: patient_hoken?.[0]?.hoken_yuko_date,
        office_name: patient_hoken?.[0]?.office_name,
        office_address: patient_hoken?.[0]?.office_address,
        office_post: patient_hoken?.[0]?.office_post,
        remarks: patient?.remarks,
        // KOHI
        kohi_type: patient_hoken?.[0]?.kohi_type?.toString(),
        kohi_no: patient_hoken?.[0]?.kohi_no,
        kohi_jukyusya_no: patient_hoken?.[0]?.kohi_jukyusya_no,
        kohi_ritsu: patient_hoken?.[0]?.kohi_ritsu,
        kohi_start_date: patient_hoken?.[0]?.kohi_start_date
          ? dayjs(patient_hoken[0].kohi_start_date)
          : undefined,

        kohi_end_date: patient_hoken?.[0]?.kohi_end_date
          ? dayjs(patient_hoken[0].kohi_end_date)
          : undefined,
        kohi_jyosei_flg: patient_hoken?.[0]?.kohi_jyosei_flg === EJyosaiFlag.ON,
        zokugara: patient?.zokugara ?? undefined,
        hihoken_name: patient_hoken?.[0]?.hihoken_name || '',
        hihoken_kana: patient_hoken?.[0]?.hihoken_kana || '',
        hihoken_post: patient_hoken?.[0]?.hihoken_post || '',
        hihoken_tel: patient_hoken?.[0]?.hihoken_tel || '',
        ocr_data_hoken: patient_hoken?.[0]?.ocr_date_hoken || '',
        ocr_data_kohi: patient_hoken?.[0]?.ocr_date_kohi || '',
      };
      form.setFieldsValue(initValue as any);
      setInit(initValue as any);
    }
  }, [visitDetail, treeServices, isFetching]);

  return (
    <>
      <Form
        ref={formRef}
        form={form}
        layout="vertical"
        onFinish={values => {
          submitHandler();
          handleFinish(values);
        }}
        className={styles.visitDetailForm}
        onValuesChange={(changedValues, allValues) => {
          handleValuesChange(changedValues, allValues);
          if (changedValues.course_groups) {
            form.validateFields(['course_groups']);
          }
        }}
      >
        <Flex gap="large" vertical>
          <AppBlockHasHeader icon={<Icon name="booking" width={20} height={20} />} title="予約情報">
            <Flex vertical gap="large" className={styles.patient_info}>
              <AppFormItem label="予約形態" name="visit_via">
                <Radio.Group>
                  <Space size={'large'}>
                    {bookingTypeOptions.map(item => (
                      <AppRadio value={item.value} key={item.value} disabled>
                        {item.label}
                      </AppRadio>
                    ))}
                  </Space>
                </Radio.Group>
              </AppFormItem>

              <AppFormItem label="予約時間帯" name="booking_time">
                {/* e.g:  2025/03/20 16:00～ 16:30 */}
                <AppInput disabled />
              </AppFormItem>
            </Flex>
          </AppBlockHasHeader>
          <AppBlockHasHeader
            icon={<Icon name="userInfo" width={20} height={20} />}
            title="来院情報"
          >
            <Flex gap="large">
              <AppFormItem label="来院設定" name="visit_type">
                <AppSelect options={VisitTypeOption} disabled={isFetching} />
              </AppFormItem>

              <AppFormItem
                label="施術師"
                name="practitioners"
                layout="vertical"
                required
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value || value.length === 0) {
                        return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('施術師'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <MultiTreaterSelectFormItem treaterList={treaterList} disabled={isFetching} />
              </AppFormItem>
            </Flex>
          </AppBlockHasHeader>
          <AppBlockHasHeader
            icon={<Icon name="medical" width={20} height={20} />}
            title={
              <>
                <span>受付区分</span>
                <span className={styles.required_icon}>*</span>
              </>
            }
          >
            <AppFormItem
              name="course_groups"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value) return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('受付区分'));
                    const hasAnySelection = Object.values(value).some(
                      group => Array.isArray(group) && group.length > 0
                    );
                    if (!hasAnySelection) {
                      return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('受付区分'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Flex justify="space-between" align="stretch" className={'course-wrapper'}>
                {(treeServices ?? []).map(service => (
                  <Flex vertical className="tree-checkbox" key={service.service_id}>
                    <AppFormItem name={['course_groups', service.service_id]} noStyle>
                      <TreeCourseFormItem
                        serviceCourseTreeData={service.treeData}
                        jihiCourseTreeData={service.jihiCourse}
                        disabled={isFetching}
                      />
                    </AppFormItem>
                  </Flex>
                ))}
              </Flex>
            </AppFormItem>
          </AppBlockHasHeader>
          <Relationship />
          <AppBlockHasHeader
            icon={<Icon name="treatment" width={20} height={20} />}
            title="保険情報"
          >
            <Flex gap="middle" vertical>
              <Flex justify="space-between" gap="large">
                <AppFormItem label="保険種別" name="hoken_type" className={styles.treatment_input}>
                  <AppInput disabled />
                </AppFormItem>

                <AppFormItem label="保険者番号" name="hoken_no" className={styles.treatment_input}>
                  <AppInput disabled />
                </AppFormItem>
              </Flex>

              <Flex justify="space-between" gap="large">
                <AppFormItem
                  label="保険者名称"
                  name="hoken_name"
                  className={styles.treatment_input}
                >
                  <AppInput disabled />
                </AppFormItem>

                <AppFormItem
                  label="記号／番号（枝番）"
                  name="hoken_kigo"
                  className={styles.treatment_input}
                >
                  <AppInput disabled />
                </AppFormItem>
              </Flex>

              <Flex justify="space-between" gap="large">
                <AppFormItem
                  label="資格取得日"
                  name="hoken_shikaku_date"
                  className={styles.treatment_input}
                >
                  <AppInput disabled />
                </AppFormItem>

                <AppFormItem
                  label="有効期限"
                  name="hoken_yuko_date"
                  className={styles.treatment_input}
                >
                  <AppInput disabled />
                </AppFormItem>
              </Flex>
            </Flex>
          </AppBlockHasHeader>
          <AppBlockHasHeader icon={<Icon name="medical" width={20} height={20} />} title="医療助成">
            <Flex gap="middle" vertical>
              <Flex justify="space-between" gap="large">
                <AppFormItem label="種類選択" name="kohi_type" className={styles.treatment_input}>
                  <AppSelect
                    placeholder=""
                    size="small"
                    options={convertMapToOptions(kohiMap)}
                    disabled
                  />
                </AppFormItem>

                <AppFormItem label="公費負担番号" name="kohi_no" className={styles.treatment_input}>
                  <AppInput disabled />
                </AppFormItem>
              </Flex>
              <Flex justify="space-between" gap="large">
                <AppFormItem
                  label="公費有給者番号"
                  name="kohi_jukyusya_no"
                  className={styles.treatment_input}
                >
                  <AppInput disabled />
                </AppFormItem>
                <Flex className={styles.treatment_input} align="center" gap="small">
                  <AppFormItem label="医療助成負担" name="kohi_ritsu">
                    <AppInput disabled />
                  </AppFormItem>
                  <span className={styles.medical_assistance_cost_digit}>割</span>
                </Flex>
              </Flex>
              <Flex justify="space-between" gap="large">
                <AppFormItem
                  label="適用開始日"
                  name="kohi_start_date"
                  className={styles.treatment_input}
                >
                  <AppDatePicker disabled format={DATE_FORMATS.DATE} />
                </AppFormItem>

                <AppFormItem
                  label="適用終了日"
                  name="kohi_end_date"
                  className={styles.treatment_input}
                >
                  <AppDatePicker disabled format={DATE_FORMATS.DATE} />
                </AppFormItem>
              </Flex>
              <AppFormItem name="kohi_jyosei_flg" valuePropName="checked">
                <AppCheckbox disabled className={styles.jyosai_flag_checkbox}>
                  一部助成
                </AppCheckbox>
              </AppFormItem>
              <Divider
                style={{
                  margin: '16px 0',
                }}
              />
              <Row gutter={24}>
                <Col span={24}>
                  <Flex flex={1} className="mb-4">
                    <AppFormItem label={'備考欄'} name="remarks" layout="vertical">
                      <AppInput width={'100%'} disabled />
                    </AppFormItem>
                  </Flex>
                </Col>
              </Row>
              <HokenCollapse onToggleCollapse={setIsCollapsed} />
              {isCollapsed && (
                <>
                  <Row gutter={24}>
                    <Col span={12}>
                      <Flex gap={24} flex={1} className="mb-4">
                        <AppFormItem name="office_name" label="事業所名称" layout="vertical">
                          <AppInput disabled />
                        </AppFormItem>
                      </Flex>
                    </Col>
                    <Col span={12}>
                      <Flex gap={24} flex={1} className="mb-4">
                        <AppFormItem name="office_post" label="郵便番号" layout="vertical">
                          <AppInput disabled />
                        </AppFormItem>
                      </Flex>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Flex gap={24} flex={1} className="mb-4">
                        <AppFormItem name="office_address" label="住所" layout="vertical">
                          <AppInput disabled />
                        </AppFormItem>
                      </Flex>
                    </Col>
                  </Row>
                </>
              )}
            </Flex>
          </AppBlockHasHeader>
          <AppBlockHasHeader title="問題リスト" icon={<Icon name="record" />} isPadding={false}>
            <VisitDetailMondaiListTable />
          </AppBlockHasHeader>
        </Flex>
      </Form>
    </>
  );
};

export default VisitDetailForm;
