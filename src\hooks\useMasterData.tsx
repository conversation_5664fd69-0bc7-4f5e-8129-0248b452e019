import {
  getOptionsByBlock,
  getOptionsByPartType,
  getOptionsBySide,
  getPartType,
} from '@/store/injury-name/api';
import { setCategory } from '@/store/schema';
import { useGetCategoriesQuery } from '@/store/schema/api';
import { getCategories } from '@/store/schema/selectors';
import { setSelection } from '@/store/visit';
import { useGetSelectionMasterDataQuery } from '@/store/visit/api';
import { getSelection } from '@/store/visit/selector';
import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useAuth from './useAuth';
import useInjuryNameStore, { INITIAL_STORAGE } from './useInjuryNameStore';

export const useMasterData = () => {
  const { isAuthenticated, expiredAt } = useAuth();
  const isExpired = expiredAt && expiredAt < Date.now();

  const dispatch = useDispatch();
  const storedSelection = useSelector(getSelection);
  const storedCategory = useSelector(getCategories);

  const { setInjuryNameStorage, isInitialized: isInitializedInjuryName } = useInjuryNameStore();

  const { data: selectionData, isLoading: selectionLoading } = useGetSelectionMasterDataQuery(
    null,
    {
      skip: storedSelection.isInitialized || !isAuthenticated || !!isExpired,
    }
  );

  const { data: categoryData, isLoading: categoryLoading } = useGetCategoriesQuery(undefined, {
    skip: storedCategory.isInitialized || !isAuthenticated || !!isExpired,
  });

  useEffect(() => {
    if (selectionData?.data) {
      dispatch(setSelection({ selection: selectionData.data, isInitialized: true }));
    }
    if (categoryData?.data) {
      dispatch(
        setCategory({
          categories: { list: categoryData.data, isInitialized: true },
        })
      );
    }
  }, [selectionData, categoryData, dispatch]);

  const fetchedRef = useRef(false);
  useEffect(() => {
    if (!isAuthenticated || isExpired || isInitializedInjuryName || fetchedRef.current) {
      return;
    }
    fetchedRef.current = true;

    (async () => {
      const [optRes, ptRes, optByPtRes, optBySideRes] = await Promise.all([
        getOptionsByBlock(),
        getPartType(),
        getOptionsByPartType(),
        getOptionsBySide(),
      ]);
      setInjuryNameStorage({
        optionsByBlock: optRes.data || INITIAL_STORAGE.optionsByBlock,
        partTypes: ptRes.data || INITIAL_STORAGE.partTypes,
        optionsByPartType: optByPtRes.data || INITIAL_STORAGE.optionsByPartType,
        optionsBySide: optBySideRes.data || INITIAL_STORAGE.optionsBySide,
        isInitialized: true,
      });
    })();
  }, [isAuthenticated, expiredAt, isInitializedInjuryName]);

  return {
    selectionLoading,
    categoryLoading,
  };
};
