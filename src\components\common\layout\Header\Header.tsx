import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { ButtonType } from '@/types/enum';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Dropdown, Layout } from 'antd';
import { useNavigate } from 'react-router-dom';
import Button from '../../core/Button';
import Icon from '../../core/Icon';
import styles from './Header.module.scss';
import { routerPaths } from '@/types/constants';
import { logout } from '@/services/medix-sync-api/logout';
import useUser from '@/hooks/useUser';

const { Header: AntHeader } = Layout;

const Header = () => {
  const user = useUser();
  const { show, hide } = useModalConfirm();
  const navigate = useNavigate();
  const handleClickLogout = async () => {
    try {
      logout();
      navigate(routerPaths.notAuthen);
    } catch (error) {
      console.log('logout error', error);
    }
  };

  const confirmLogout = () => {
    show({
      title: <>ログアウトする</>,
      content: <>ログアウトしてもよろしいでしょうか？</>,
      buttons: [
        <Button key="cancel" customType={ButtonType.SECONDARY_COLOR} onClick={hide} customSize="lg">
          キャンセル
        </Button>,
        <Button
          key="confirm"
          customSize="lg"
          customType={ButtonType.PRIMARY}
          onClick={() => {
            hide();
            handleClickLogout();
          }}
        >
          ログアウト
        </Button>,
      ],
      width: '464px',
    });
  };
  const menu = {
    items: [
      // {
      //   key: 'logout',
      //   label: (
      //     <div className={styles.menuItem}>
      //       <Icon name="arrowExit" width={16} height={16} className={styles.menuIcon} />
      //       <span className="fs14-medium">Logout</span>
      //     </div>
      //   ),
      // },
    ],
    onClick: ({ key }: { key: string }) => {
      if (key === 'logout') {
        confirmLogout();
      }
    },
  };

  return (
    <AntHeader className={styles.header}>
      <div className={styles.logoArea}>
        <span className="fs24-bold">LOGO</span>
        <span className="fs16-bold">OSAKA</span>
      </div>
      <div className={styles.userArea}>
        <Avatar size={24} icon={<UserOutlined />} src={user?.avatar ? user.avatar : undefined} />
        <Dropdown menu={menu} trigger={['click']} overlayClassName={styles.customDropdown}>
          <span
            className={styles.userInfo}
            style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', gap: 4 }}
          >
            <span className={styles.userId}>{user?.username}</span>
            <Icon name="downFilled" width={16} height={16} className={styles.icon} />
          </span>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
