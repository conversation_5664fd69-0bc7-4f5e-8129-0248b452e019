import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { useScreen } from '@/hooks/useScreen';
import { useLogoutMutation } from '@/store/auth/api';
import { logoutThunk } from '@/store/auth/logoutThunk';
import { getAccessToken, getUserInfo } from '@/store/auth/selectors';
import { fetchingUserInfo } from '@/store/shared/thunk';
import { ButtonType, ROLE } from '@/types/enum';
import { getUserRole, redirectToMedixSync } from '@/utils';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Dropdown, Layout } from 'antd';
import { useLayoutEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../../core/Button';
import Icon from '../../core/Icon';
import styles from './Header.module.scss';

const { Header: AntHeader } = Layout;

const Header = () => {
  const { screen } = useScreen();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [onLogout] = useLogoutMutation({});
  const { show, hide, update } = useModalConfirm();
  const user = useAppSelector(getUserInfo);
  const accessToken = useAppSelector(getAccessToken);
  const headerRef = useRef(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const isAdmin = getUserRole() !== ROLE.STAFF;

  const handleClickLogout = async () => {
    try {
      setIsLoggingOut(true);
      update({ buttons: getButtons(true) });
      await onLogout({}).unwrap();
      dispatch(logoutThunk());
      navigate('/login');
    } catch (error) {
      return error;
    } finally {
      setIsLoggingOut(false);
      update({ buttons: getButtons(false) });
      hide();
    }
  };

  useLayoutEffect(() => {
    dispatch(fetchingUserInfo());
  }, []);

  const getButtons = (loading: boolean): React.ReactNode[] => [
    <Button key="cancel" customType={ButtonType.SECONDARY_COLOR} onClick={hide} customSize="lg">
      キャンセル
    </Button>,
    <Button
      key="confirm"
      customSize="lg"
      customType={ButtonType.PRIMARY}
      loading={loading}
      disabled={loading}
      onClick={handleClickLogout}
    >
      ログアウト
    </Button>,
  ];

  const confirmLogout = () => {
    show({
      title: <>ログアウトする</>,
      content: <>ログアウトしてもよろしいでしょうか？</>,
      buttons: getButtons(isLoggingOut),
      width: '464px',
    });
  };
  const menu = {
    items: [
      {
        key: 'logout',
        label: (
          <div className={styles.menuItem} onClick={confirmLogout}>
            <Icon name="arrowExit" width={16} height={16} className={styles.menuIcon} />
            <span className="fs14-medium">ログアウト</span>
          </div>
        ),
      },
      ...(isAdmin
        ? [
            {
              key: 'redirect',
              label: (
                <div
                  className={styles.menuItem}
                  onClick={() =>
                    redirectToMedixSync({ from: 'karute', path: 'dashboard', token: accessToken })
                  }
                >
                  <Icon name="outlook" width={16} height={16} className={styles.menuIcon} />
                  <span className="fs14-medium">共通管理画面</span>
                </div>
              ),
            },
          ]
        : []),
    ],
  };

  return (
    <AntHeader className={styles.header} ref={headerRef}>
      {screen === 'mobile' && (
        <Icon name="hamburger" width={18} height={13.5} className={styles.hamburgerIcon} />
      )}
      <div className={styles.logoArea}>
        <span className="fs24-bold">LOGO</span>
        <span className="fs16-bold">OSAKA</span>
      </div>
      <div className={styles.userArea}>
        <Avatar src={user?.avatar} size={24} icon={<UserOutlined />} />
        <Dropdown
          menu={menu}
          trigger={['click']}
          overlayClassName={styles.customDropdown}
          getPopupContainer={() => headerRef.current || document.body}
        >
          <span
            className={styles.userInfo}
            style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', gap: 4 }}
          >
            <span className={styles.userId}>{user?.username}</span>
            <Icon name="downFilled" width={16} height={16} className={styles.icon} />
          </span>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
