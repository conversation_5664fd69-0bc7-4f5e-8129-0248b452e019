import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import PageLayout from '@/components/layouts/PageLayout';
import PatientSidebar from '@/components/page-components/visit-management/dashboard/VisitDetail/components/PatientSidebar';
import VisitDetailAction from '@/components/page-components/visit-management/dashboard/VisitDetail/components/VisitDetailAction';
import VisitDetailForm from '@/components/page-components/visit-management/dashboard/VisitDetail/components/VisitDetailForm';
import {
  useVisitDetail,
  VisitDetailProvider,
} from '@/components/page-components/visit-management/dashboard/VisitDetail/provider';
import { useRouterPath } from '@/hooks/useRouterPath';
import { routerPaths } from '@/types/constants';
import { Spin } from 'antd';

export default function VisitDetailPage() {
  return (
    <VisitDetailProvider>
      <VisitDetailPageContent />
    </VisitDetailProvider>
  );
}

function VisitDetailPageContent() {
  const { getPath } = useRouterPath();
  const { isFetching } = useVisitDetail();
  return (
    <>
      <Spin spinning={isFetching} size="large">
        <PageLayout
          title="受付詳細・編集"
          breadcrumb={
            <AppBreadcrumb
              items={[
                { title: <a href={getPath(routerPaths.dashboard)}>ダッシュボード</a> },
                { title: '受付登録' },
              ]}
            />
          }
          headerRight={<VisitDetailAction />}
          rightSidebar={<PatientSidebar />}
        >
          <VisitDetailForm />
        </PageLayout>
      </Spin>
    </>
  );
}
