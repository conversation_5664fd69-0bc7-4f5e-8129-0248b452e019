// K120_シェーマ
import React from 'react';
import { SchemaProvider } from './context/SchemaContext';
import SchemaModalContent from './SchemaModalContent';

interface SchemaModalProps {
  open: boolean;
  onCancel: () => void;
}

const SchemaModal: React.FC<SchemaModalProps> = ({ open, onCancel }) => {
  return (
    <SchemaProvider>
      <SchemaModalContent open={open} onCancel={onCancel} />
    </SchemaProvider>
  );
};

export default SchemaModal;
