import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse } from '@/types/interface';
import { ForgotPasswordParams, LoginResponse, LoginSchema, ResetPasswordParams } from './type';

export const authApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,
    login: build.mutation<APIResponse<LoginResponse>, LoginSchema>({
      query: params => {
        return {
          url: '/api/login',
          body: params,
          method: 'post',
        };
      },
    }),

    logout: build.mutation<any, unknown>({
      query: () => {
        return {
          url: '/api/logout',
          method: 'post',
        };
      },
    }),
    requestResetPassword: build.mutation<APIResponse<[]>, ForgotPasswordParams>({
      query: params => {
        return {
          url: '/api/forgot-password',
          body: params,
          method: 'post',
        };
      },
    }),
    verifyResetPasswordToken: build.mutation<
      APIResponse<{ token_is_valid: boolean }>,
      { token: string; clinic_cd: string }
    >({
      query: ({ token, clinic_cd }) => {
        return {
          url: `/api/reset-password/check-token`,
          method: 'post',
          params: {
            token,
            clinic_cd,
          },
        };
      },
    }),
    resetPassword: build.mutation<APIResponse<APIResponse<[]>>, ResetPasswordParams>({
      query: params => {
        return {
          url: `/api/reset-password`,
          body: params,
          method: 'post',
        };
      },
    }),
  }),
});

export const {
  useLoginMutation,
  useLogoutMutation,
  useRequestResetPasswordMutation,
  useVerifyResetPasswordTokenMutation,
  useResetPasswordMutation,
} = authApi;
