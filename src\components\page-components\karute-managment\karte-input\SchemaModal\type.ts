export const FAVORITE_CATEGORY_ID = 0;
export const HISTORY_CATEGORY_ID = -1;

export enum SchemaMode {
  SELECT = 'select',
  DRAW = 'draw',
}

export enum DrawTool {
  NONE = 'none',
  PENCIL = 'pencil',
  CIRCLE = 'circle',
  CIRCLE_OUTLINE = 'circleOutline',
  ERASER = 'eraser',
}

export const COLORS = {
  COLOR_1: '#000000',
  COLOR_2: '#7F7F7F',
  COLOR_3: '#880015',
  COLOR_4: '#ED1C24',
  COLOR_5: '#FF7F27',
  COLOR_6: '#FFF200',
  COLOR_7: '#22B14C',
  COLOR_8: '#00A2E8',
  COLOR_9: '#3F48CC',
  COLOR_10: '#A349A4',
};

export type Color = (typeof COLORS)[keyof typeof COLORS] | null;

export interface CanvasContainerHandle {
  exportAll: () => Promise<
    { id: string; blob: Blob; isTemp: boolean; schema_image_id?: string | number | null }[]
  >;
}

export const STAGE_W = 480;
export const STAGE_H = 480;
