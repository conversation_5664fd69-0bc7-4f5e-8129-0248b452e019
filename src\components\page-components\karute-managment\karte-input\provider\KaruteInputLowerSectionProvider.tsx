import { createContext, useContext, useState, ReactNode } from 'react';
import { CourseItem } from '../KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertOptionListForm/ListCourseTable';
import { OptionItem } from '../KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertOptionListForm/ListOptionTable';
const MONDAI_ITEMS = [
  { key: 'M1', label: 'M1', value: 'M1' },
  { key: 'M2', label: 'M2', value: 'M2' },
  { key: 'M3', label: 'M3', value: 'M3' },
  { key: 'M4', label: 'M4', value: 'M4' },
  { key: 'M5', label: 'M5', value: 'M5' },
  { key: 'M6', label: 'M6', value: 'M6' },
  { key: 'M7', label: 'M7', value: 'M7' },
  { key: 'M8', label: 'M8', value: 'M8' },
  // { key: 'M9', label: 'M9', value: 'M9' },
  // { key: 'M10', label: 'M10', value: 'M10' },
  // { key: 'M11', label: 'M11', value: 'M11' },
  // { key: 'M12', label: 'M12', value: 'M12' },
  // { key: 'M13', label: 'M13', value: 'M13' },
  // { key: 'M14', label: 'M14', value: 'M14' },
  // { key: 'M15', label: 'M15', value: 'M15' },
];

interface KaruteInputLowerSectionContextType {
  activeMondai: Record<string, boolean>;
  setActiveMondai: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  mondaiItems: typeof MONDAI_ITEMS;
  // hoken hokenDataSource
  hokenDataSource: CourseItem[];
  setHokenDataSource: React.Dispatch<React.SetStateAction<CourseItem[]>>;
  // options
  optionDataSource: OptionItem[];
  setOptionDataSource: React.Dispatch<React.SetStateAction<OptionItem[]>>;
  optionSelectedKeys: string[];
  setOptionSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
}

const KaruteInputLowerSectionContext = createContext<
  KaruteInputLowerSectionContextType | undefined
>(undefined);

export function useKaruteInputLowerSection() {
  const context = useContext(KaruteInputLowerSectionContext);
  if (!context) {
    throw new Error(
      'useKaruteInputLowerSection must be used within KaruteInputLowerSectionProvider'
    );
  }
  return context;
}

interface KaruteInputLowerSectionProviderProps {
  children: ReactNode;
}
export function KaruteInputLowerSectionProvider({
  children,
}: KaruteInputLowerSectionProviderProps) {
  const [activeMondai, setActiveMondai] = useState<Record<string, boolean>>({});
  const mondaiItems = MONDAI_ITEMS; // Change active tab will change mondaiItes
  const [hokenDataSource, setHokenDataSource] = useState<CourseItem[]>([
    // {
    //   rowId: 1,
    //   date: '2023/10/01',
    //   course: ['1'],
    //   updated_date: '2023/10/01',
    // },
    // {
    //   rowId: 2,
    //   date: '2023/10/01',
    //   course: ['1'],
    //   updated_date: '2023/10/01',
    // },
  ]);
  const [optionDataSource, setOptionDataSource] = useState<OptionItem[]>([]);
  const [optionSelectedKeys, setOptionSelectedKeys] = useState<string[]>([]);

  const value = {
    activeMondai,
    setActiveMondai,
    mondaiItems,
    hokenDataSource,
    setHokenDataSource,
    optionDataSource,
    setOptionDataSource,
    optionSelectedKeys,
    setOptionSelectedKeys,
  };

  return (
    <KaruteInputLowerSectionContext.Provider value={value}>
      {children}
    </KaruteInputLowerSectionContext.Provider>
  );
}
