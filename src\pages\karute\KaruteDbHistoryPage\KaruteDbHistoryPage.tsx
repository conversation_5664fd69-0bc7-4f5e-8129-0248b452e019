import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import PageLayout from '@/components/layouts/PageLayout';
import { useRouterPath } from '@/hooks/useRouterPath';
import { routerPaths } from '@/types/constants';
import Button from '@/components/common/core/Button';
import { ButtonType, EKaruteServicePriority } from '@/types/enum';
import Icon from '@/components/common/core/Icon';
import { DbHistory } from '../../../components/page-components/karute-managment/db-history/DbHistory';
import { useEffect, useRef, useState } from 'react';
import styles from './KaruteDbHistoryPage.module.scss';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import { CheckboxChangeEvent } from 'antd';
import { useGetDbHistoryQuery } from '@/store/db-history/api';
import { useParams } from 'react-router-dom';
import { useAppSelector } from '@/config/redux/store';
import { getAuth } from '@/store/auth/selectors';
import { DbHistoryDetail } from '@/store/db-history/type';
import { useKaruteInput } from '@/components/page-components/karute-managment/karte-input/provider';
import dayjs from 'dayjs';

const MAX_LIMIT_RECORD = 99999;
const DEFAULT_LIMIT_RECORD = 10;

export function KaruteDbHistoryPage() {
  const { serviceId, id: karuteId } = useParams();
  const auth = useAppSelector(getAuth);

  const [page, setPage] = useState(1);
  const [isEnd, setIsEnd] = useState(false);
  const [dbHistorys, setDbHistorys] = useState<DbHistoryDetail[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const { karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;
  const [limit, setLimit] = useState(DEFAULT_LIMIT_RECORD);
  const [isSelectAll, setIsSelectAll] = useState(false);
  const [total, setTotal] = useState<number | undefined>(undefined);

  const {
    isLoading,
    data: apiData,
    isFetching,
  } = useGetDbHistoryQuery(
    {
      clinic_id: auth.currentClinicId,
      patient_id: patientId,
      service_id: Number(serviceId) as EKaruteServicePriority,
      page,
      limit,
    },
    {
      skip: !patientId || !auth.currentClinicId,
    }
  );
  const { getPath } = useRouterPath();
  const { clinicCd } = useParams();

  const [checkedList, setCheckedList] = useState<string[]>([]);

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    const currValue = e.target.checked;

    setIsSelectAll(currValue);

    if (currValue) {
      setPage(1);
      setLimit(MAX_LIMIT_RECORD);

      setCheckedList(dbHistorys.map(item => item.version.toString()));
    } else {
      setCheckedList([]);
    }
  };

  const toKaruteUpdate = () => {
    return routerPaths.karuteManagement.karuteUpdate
      .replace(':clinicCd', String(clinicCd))
      .replace(':id', String(karuteId));
  };

  useEffect(() => {
    if (apiData?.data?.data?.length) {
      const newDbHistorys = apiData.data.data;
      const updateDbHistorys = [...dbHistorys, ...newDbHistorys].filter(
        (record, index, self) =>
          index === self.findIndex(r => r.version.toString() === record.version.toString())
      );
      setDbHistorys(updateDbHistorys);

      const totalRecord = apiData?.data?.total;

      if (totalRecord) {
        setTotal(totalRecord);
      }

      if (apiData?.data?.last_page === page || dbHistorys.length === totalRecord) {
        setIsEnd(true);

        if (isSelectAll) {
          setCheckedList(updateDbHistorys.map(item => item.version.toString()));
        }
      } else {
        setIsEnd(false);
      }
    }
  }, [apiData, page]);

  useEffect(() => {
    const container = containerRef.current;

    if (!container || isFetching || isEnd) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;

      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5;

      if (isAtBottom && !isEnd) {
        setPage(prev => prev + 1);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [isFetching, isEnd]);

  return (
    <PageLayout
      title="データベースの編集履歴"
      breadcrumb={
        <AppBreadcrumb
          items={[
            {
              title: <a href={getPath(routerPaths.dashboard)}>カルテ一覧</a>,
            },
            {
              title: <a href={toKaruteUpdate()}>カルテ入力</a>, // TODO: Change to validated karute_id and patient
            },
            {
              title: 'データベースの編集履歴',
            },
          ]}
        />
      }
      headerRight={
        <Button
          customType={ButtonType.PRIMARY}
          customSize="md"
          loading={isLoading}
          onClick={() => {
            // Print Mutation here
            const originalTitle = document.title;
            document.title = `Database_${dayjs().format('YYYYMMDD_HHmm')}`;

            // Restore title after printing
            setTimeout(() => {
              document.title = originalTitle;
            }, 1000);
            window.print();
          }}
          disabled={checkedList.length === 0 || isFetching}
          style={{ width: '88px' }}
        >
          <Icon name="printAdd" />
          印刷
        </Button>
      }
    >
      <div className={styles.container}>
        <AppFormItem className={styles.checkAll} style={{ marginBottom: '16px' }}>
          <AppCheckbox
            checked={isSelectAll}
            indeterminate={checkedList.length > 0 ? !isSelectAll : undefined}
            onChange={onCheckAllChange}
          >
            全て選択
          </AppCheckbox>
        </AppFormItem>
        <DbHistory
          dbHistorys={dbHistorys}
          checkedList={checkedList}
          setCheckedList={setCheckedList}
          containerRef={containerRef}
          setIsSelectAll={setIsSelectAll}
          isFetching={isFetching}
          total={total}
        />
      </div>
    </PageLayout>
  );
}
