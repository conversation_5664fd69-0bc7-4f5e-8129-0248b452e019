import { AppInput } from '@/components/common/core/Input';
import { Dropdown, Flex } from 'antd';
import styles from './UserManagement.module.scss';
import Table from '@/components/common/core/CommonTable';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ParamGetListUser, User } from '@/types/interface';
import { fetchUserList } from '@/services/medix-sync-api/userApi';
import { ButtonType, Order, ROLE } from '@/types/enum';
import { MoreOutlined } from '@ant-design/icons';
import Icon from '@/components/common/core/Icon';
import { PAGINATION_THRESHOLD, routerPaths } from '@/types/constants';
import { useNavigate } from 'react-router-dom';
import Button from '@/components/common/core/Button';
import {
  getRoleName,
  getRoleOptionsView,
  getUserRole,
  getViewOnlyOptions,
  makeLocationSnapshot,
} from '@/utils/helper';
import clsx from 'clsx';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { MultiSelect } from '@/components/common/core/MultiSelect';

function UserManagementContent() {
  const { show, hide } = useModalConfirm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [userList, setUserList] = useState<User[] | []>([]);
  const [total, setTotal] = useState<number>();
  const [filteredTotal, setFilteredTotal] = useState<number>();
  const [currentPage, setCurrentPage] = useState<number>();
  const [perPage, setPerPage] = useState<number>();
  const [params, setParams] = useState<ParamGetListUser>({
    username: null,
    name: null,
    email: null,
    manageable_type: null,
    is_doctor: null,
    can_access_karute: null,
    can_access_booking: null,
    order_by: null,
    order_type: null,
    page: 1,
    limit: PAGINATION_THRESHOLD,
  });
  const [inputs, setInputs] = useState({
    username: '',
    name: '',
    email: '',
  });

  const viewOnlyOptions = getViewOnlyOptions(getUserRole());
  const handleInputChange = (key: keyof typeof inputs, value: string) =>
    setInputs(prev => ({ ...prev, [key]: value }));

  const runSearch = () =>
    setParams(prev => ({
      ...prev,
      username: inputs.username || null,
      name: inputs.name || null,
      email: inputs.email || null,
      page: 1,
    }));

  const clearSearch = () => {
    setInputs({ username: '', name: '', email: '' });
  };

  const handleSortChange = (sortData: { order_by: string; order_type: Order }[]) => {
    setParams(prev => ({
      ...prev,
      order_by: sortData[0]?.order_by,
      order_type: sortData[0]?.order_type,
    }));
  };
  const fetchUser = async () => {
    setLoading(true);
    const res = await fetchUserList(params);
    setUserList(res?.data?.data || []);
    setTotal(res?.data?.total_items);
    setFilteredTotal(res?.data?.total_filtered);
    setCurrentPage(res?.data?.current_page);
    setPerPage(res?.data?.per_page);
    setLoading(false);
  };

  useEffect(() => {
    fetchUser();
  }, [params]);

  const getValidDisplay = useCallback((value: boolean) => {
    return (
      <div className="validTag">
        <span className={`${value ? 'success' : 'error'}`}>{value ? '有効' : '無効'}</span>
      </div>
    );
  }, []);
  const columns: CustomColumnType<User>[] = useMemo(
    () => [
      {
        title: 'No.',
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        // fixed: 'left',
        width: 80,

        render: (_: any, record, index: number) =>
          index + 1 + (perPage ?? 0) * ((currentPage ?? 1) - 1),
      },
      {
        title: 'ユーザーID',
        dataIndex: 'user_id',
        sortable: true,
        align: 'left',
        sortKey: 'username',
        // fixed: 'left',
        width: 118,
        render: (_, record) => record.username,
      },
      // {
      //   title: '企業',
      //   dataIndex: 'company_name',
      //   sortable: true,
      //   align: 'left',
      //   sortKey: 'username',
      //   // fixed: 'left',
      //   width: 118,
      //   render: (_, record) => record?.company?.name,
      // },
      // {
      //   title: '治療院',
      //   dataIndex: 'clinic_name',
      //   key: 'clinic_name',
      //   sortable: true,
      //   sortKey: 'clinic_name',
      //   width: 118,
      //   render: (_, record) => record?.clinic?.clinic_name,
      // },
      {
        title: '氏名（漢字）',
        dataIndex: 'kanji_name',
        sortable: true,
        sortKey: 'kanji_name',
        width: 118,
      },
      {
        title: '氏名（カナ）',
        dataIndex: 'kana_name',
        sortable: true,
        sortKey: 'kana_name',
        width: 119,
      },
      {
        title: '施術師のマスター',
        dataIndex: 'is_doctor',
        sortable: true,
        sortKey: 'is_doctor',
        width: 119,
        align: 'center',
        render: (_, { is_doctor }) => getValidDisplay(is_doctor),
      },
      {
        title: '予約システム',
        dataIndex: 'can_access_booking',
        sortable: true,
        sortKey: 'can_access_booking',
        width: 119,
        align: 'center',
        render: (_, { can_access_booking }) => getValidDisplay(can_access_booking),
      },
      {
        title: '電子カルテシステム',
        dataIndex: 'can_access_karute',
        sortable: true,
        sortKey: 'can_access_karute',
        width: 119,
        align: 'center',
        render: (_, { can_access_karute }) => getValidDisplay(can_access_karute),
      },
      {
        title: ' 権限タイプ',
        dataIndex: 'role_name',
        sortable: true,
        sortKey: 'manageable_type',
        width: 119,
        align: 'center',
        render: (_, { role }) => getRoleName(role as ROLE),
      },
      {
        title: '',
        key: 'action',
        align: 'center',
        width: 50,
        render: (_, { user_id, role }) => (
          <Dropdown
            menu={{
              items: [
                {
                  key: 'view',
                  label: (
                    <Flex
                      align="center"
                      gap={6}
                      onClick={() => {
                        navigate(routerPaths.userPreview(user_id), {
                          state: { background: makeLocationSnapshot(location) },
                        });
                      }}
                    >
                      <Icon name="eye" height={20} width={20} />
                      <span>プレビュー </span>
                    </Flex>
                  ),
                },
                {
                  key: 'edit',
                  disabled: viewOnlyOptions.some(option => option.value === role),
                  label: (
                    <Flex
                      align="center"
                      gap={6}
                      onClick={() => {
                        const disabled = viewOnlyOptions.some(option => option.value === role);
                        if (disabled) return;
                        navigate(routerPaths.edit(user_id));
                      }}
                    >
                      <Icon name="edit" height={20} width={20} />
                      <span>編集</span>
                    </Flex>
                  ),
                },
                {
                  key: 'delete',
                  disabled: viewOnlyOptions.some(option => option.value === role),
                  label: (
                    <Flex
                      align="center"
                      gap={6}
                      onClick={() => {
                        const disabled = viewOnlyOptions.some(option => option.value === role);
                        if (disabled) return;
                        show({
                          title: '’氏名（漢字）’を削除する',
                          content: (
                            <span>
                              この操作は元に戻せません。’患者名’を削除してもよろしいでしょうか？
                            </span>
                          ),
                          buttons: [
                            <Button
                              key="キャンセル"
                              customType={ButtonType.SECONDARY_COLOR}
                              onClick={hide}
                              customSize="lg"
                            >
                              キャンセル
                            </Button>,
                            <Button
                              key="削除"
                              customType={ButtonType.RED_PRIMARY}
                              onClick={() => {}}
                              customSize="lg"
                            >
                              削除
                            </Button>,
                          ],
                          width: '464px',
                          onClose: hide,
                        });
                      }}
                      className={styles.deleteAction}
                      style={{
                        cursor: viewOnlyOptions.some(option => option.value === role)
                          ? 'not-allowed'
                          : 'pointer',
                      }}
                    >
                      <Icon name="trash" height={20} width={20} />
                      <span>削除</span>
                    </Flex>
                  ),
                },
              ],
            }}
            trigger={['hover']}
          >
            <MoreOutlined style={{ fontSize: 18, cursor: 'pointer' }} />
          </Dropdown>
        ),
      },
    ],
    [getRoleName, getValidDisplay, perPage, currentPage]
  );

  return (
    <>
      <div>
        <Flex justify="space-between" align="center">
          <Flex justify="flex-start" gap={8} align="center">
            <div>
              <AppInput
                placeholder="ユーザーID"
                size="small"
                style={{ width: 220 }}
                value={inputs.username}
                onChange={e => handleInputChange('username', e.target.value)}
                onPressEnter={runSearch}
              />
            </div>
            <div>
              <AppInput
                placeholder="氏名 (漢字）、カナ"
                size="small"
                style={{ width: 220 }}
                value={inputs.name}
                onChange={e => handleInputChange('name', e.target.value)}
                onPressEnter={runSearch}
              />
            </div>
          </Flex>
          <Flex justify="flex-end" gap={8}>
            <Button customType={ButtonType.PRIMARY} onClick={runSearch} loading={loading}>
              検索
            </Button>
            <Button customType={ButtonType.SECONDARY_COLOR} onClick={clearSearch}>
              <Icon name="retry" height={20} width={20} />
              クリア
            </Button>
          </Flex>
        </Flex>
      </div>
      <div className={styles.tableWrapper}>
        <Flex justify="space-between" align="center" className={styles.filterWrapper}>
          <Flex justify="flex-start" gap={8}>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '権限タイプ',
                  style: {
                    height: '32px',
                    width: '160px',
                  },
                  size: 'small',
                }}
                options={getRoleOptionsView(getUserRole())}
                value={params.manageable_type}
                onSelectChange={v =>
                  setParams(p => ({ ...p, manageable_type: v ?? null, page: 1 }))
                }
              />
            </div>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '施術師のマスター',
                  style: {
                    height: '32px',
                    width: '160px',
                  },

                  size: 'small',
                }}
                options={[
                  { value: 1, label: '有効' },
                  { value: 0, label: '無効' },
                ]}
                value={params.is_doctor}
                onSelectChange={v => setParams(p => ({ ...p, is_doctor: v ?? null, page: 1 }))}
              />
            </div>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '予約システム',
                  style: {
                    height: '32px',
                    width: '160px',
                  },

                  size: 'small',
                }}
                options={[
                  { value: 1, label: '有効' },
                  { value: 0, label: '無効' },
                ]}
                value={params.can_access_booking}
                onSelectChange={v =>
                  setParams(p => ({ ...p, can_access_booking: v ?? null, page: 1 }))
                }
              />
            </div>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '電子カルテシステム',
                  style: {
                    height: '32px',
                    width: '160px',
                  },

                  size: 'small',
                }}
                options={[
                  { value: 1, label: '有効' },
                  { value: 0, label: '無効' },
                ]}
                value={params.can_access_karute}
                onSelectChange={v =>
                  setParams(p => ({ ...p, can_access_karute: v ?? null, page: 1 }))
                }
              />
            </div>
          </Flex>
          <div className={clsx(styles.recordCount, 'fs12-regular')}>
            人数: {filteredTotal || 0}/{total || 0}人
          </div>
        </Flex>
        <Table
          rowKey="user_id"
          columns={columns}
          dataSource={userList}
          loading={loading}
          scroll={{ x: 'max-content', y: '500px' }}
          onSortChange={handleSortChange}
          pagination={
            filteredTotal! > PAGINATION_THRESHOLD
              ? {
                  current: params.page || 1,
                  pageSize: params.limit || PAGINATION_THRESHOLD,
                  total: filteredTotal,
                  showSizeChanger: false,
                  onChange: (page: number, pageSize: number) =>
                    setParams(prev => ({ ...prev, page, limit: pageSize })),
                }
              : false
          }
          total={total}
        />
      </div>
    </>
  );
}
export default UserManagementContent;
