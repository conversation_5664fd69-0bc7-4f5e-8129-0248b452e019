// example:
// <Button customType={ButtonType.PRIMARY} disabled={true} loading={true} customSize="sm">
//   <Icon name="eye" />
// </Button>

import React from 'react';
import { Button as AntdButton, ConfigProvider } from 'antd';
import classNames from 'classnames';
import styles from './Button.module.scss';
import { ButtonType } from '@/types/enum';
import { ButtonProps } from '@/types/interface';

const Button: React.FC<ButtonProps> = ({
  customType = ButtonType.PRIMARY,
  customSize = 'md',
  children,
  className,
  ...rest
}) => {
  const wrapperClass = classNames(styles.wrapper, className, {
    [styles[customType]]: customType,
  });

  const sizeClass = classNames({
    [styles.sm]: customSize === 'sm',
    [styles.md]: customSize === 'md',
    [styles.lg]: customSize === 'lg',
    [styles.xl]: customSize === 'xl',
    [styles['xxl']]: customSize === '2xl',
    [styles.xs]: customSize === 'xs',
    [styles.xxs]: customSize === 'xxs',
  });

  return (
    <div className={wrapperClass}>
      <ConfigProvider wave={{ disabled: true }}>
        <AntdButton className={sizeClass} autoInsertSpace={false} {...rest}>
          {children}
        </AntdButton>
      </ConfigProvider>
    </div>
  );
};

export default Button;
