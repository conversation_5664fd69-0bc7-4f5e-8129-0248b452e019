// Screen B105
import Button from '@/components/common/core/Button';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';

import Icon from '@/components/common/core/Icon';
import { useCreatePatientCdMutation } from '@/store/patient/api';
import { NOTICE_COMMON_MESSAGE, STATUS_CODES } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';

export const usePatientNumberWarning = () => {
  const { show, hide } = useModalConfirm();
  const [generatePatientCd, { isLoading }] = useCreatePatientCdMutation();

  const showPatientNumberWarning = (id: number, successHandler: () => void) => {
    show({
      title: '',
      content: (
        <Flex align="center" justify="space-between" gap={16}>
          <Icon name="warning" native className="mt-2" />
          <span>{NOTICE_COMMON_MESSAGE.WARNING_PATIENT_CD}</span>
        </Flex>
      ),
      buttons: [
        <Button
          key="キャンセル"
          customType={ButtonType.SECONDARY_COLOR}
          onClick={hide}
          customSize="lg"
        >
          キャンセル
        </Button>,
        <Button
          key="登録"
          customType={ButtonType.PRIMARY}
          onClick={() => {
            generatePatientCd(id)
              .then(data => {
                if (data.data?.status === STATUS_CODES.OK) {
                  successHandler();
                }
                return;
              })
              .finally(hide);
          }}
          loading={isLoading}
          customSize="lg"
        >
          登録
        </Button>,
      ],
      width: '464px',
      onClose: hide,
      warning: true,
    });
  };

  return { showPatientNumberWarning };
};
