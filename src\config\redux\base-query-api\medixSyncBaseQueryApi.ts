import { LOCAL_STORAGE_KEY } from '@/types/constants/app';
import { isTokenExpired } from '@/utils/helper';
import {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
  createApi,
  fetchBaseQuery,
} from '@reduxjs/toolkit/query/react';
import { Mutex } from 'async-mutex';

import { RootState } from '../store';
import { showFlashNotice } from '@/utils/flashNotice';
import { NOTICE_COMMON_MESSAGE } from '@/types/constants/error';
import { logoutThunk } from '@/store/auth/logoutThunk';
import { ENV } from '@/types/constants';
import { redirectTo } from '@/utils/navigate';

const mutex = new Mutex();

const baseQuery = fetchBaseQuery({
  baseUrl: ENV.MEDIX_SYNC_API,
  prepareHeaders: (headers, { getState, endpoint }) => {
    headers.set('cache-control', 'no-cache');
    // headers.set('Content-Type', 'application/json');

    const state = getState() as RootState;
    const token = state.auth.accessToken;
    const clinicCd = state.auth.currentClinicCd;

    if (token && endpoint !== 'refresh') {
      headers.set('Authorization', `Bearer ${token}`);
    }
    if (clinicCd) {
      headers.set('clinic-cd', String(clinicCd));
    }
    return headers;
  },
});

const baseQueryWithReauth: BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> = async (
  args,
  api,
  extraOptions
) => {
  let result;
  try {
    await mutex.waitForUnlock();
    const { dispatch, getState } = api;
    result = await baseQuery(args, api, extraOptions);
    const isLogged = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);

    if (result.error && result.error.status === 'FETCH_ERROR' && !navigator.onLine) {
      // reload page when website offline to get default disconnected browser
      window.location.reload();
      const errorResponse: FetchBaseQueryError = { status: 400, data: result.data };
      return { error: errorResponse };
    }

    if (result.error && result.error.status === 401) {
      if (isLogged) {
        if (!mutex.isLocked()) {
          const release = await mutex.acquire();
          try {
            const token = (getState() as RootState).auth.accessToken || '';
            dispatch(logoutThunk());
            if (!isTokenExpired(token)) {
              return result;
            }
          } finally {
            release();
          }
        } else {
          await mutex.waitForUnlock();
        }
      }
      const errorResponse: FetchBaseQueryError = { status: 401, data: result.data };
      return { error: errorResponse };
    }

    if (result.data && typeof result.data === 'object') {
      const payload = result.data as { status: number; data: any; message: string };

      const clinicCd = (getState() as RootState).auth.currentClinicCd;

      if (payload.status === 4004) {
        if (clinicCd) {
          redirectTo(`/${clinicCd}/404-clinic-not-found`);
        }
        return result;
      }
      if (payload.status === 4003) {
        if (clinicCd) {
          redirectTo(`/${clinicCd}/403-clinic-unauthorized`);
        }
        return result;
      }

      if (payload.status === 401) {
        if (isLogged) {
          if (!mutex.isLocked()) {
            const release = await mutex.acquire();
            try {
              const token = (getState() as RootState).auth.accessToken || '';
              dispatch(logoutThunk());
              if (!isTokenExpired(token)) {
                return result;
              }
            } finally {
              release();
            }
          } else {
            await mutex.waitForUnlock();
          }
        }
        const errorResponse: FetchBaseQueryError = { status: 401, data: result.data };
        return { error: errorResponse };
      }

      if (payload.status === 500) {
        showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL });
        const errorResponse: FetchBaseQueryError = { status: 500, data: payload };
        return { error: errorResponse };
      }

      // if (payload.status === 422) {
      //   showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL });
      //   const errorResponse: FetchBaseQueryError = { status: 422, data: payload };
      //   return { error: errorResponse };
      // }
    }
  } catch (error) {
    showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.ERROR_UNUSUAL });
    const errorResponse: FetchBaseQueryError = { status: 500, data: error };
    return { error: errorResponse };
  }
  return result;
};

export const medixSyncBaseQueryApi = createApi({
  reducerPath: 'medix-sync-api',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['medix-sync-user-list'],
  endpoints: () => ({}),
  keepUnusedDataFor: 0,
});
