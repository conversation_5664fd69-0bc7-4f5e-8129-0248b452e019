export type FromType = 'karute' | 'booking';
export type PathType = 'dashboard' | 'create' | `edit/${string}` | `dashboard/user/${string}`;

export interface Payload {
  token: string | null;
  from: FromType;
  path: PathType;
}

export const redirectToMedixSync = ({
  from,
  path,
  token,
}: {
  from: FromType;
  path: PathType;
  token: string | null;
}) => {
  const MEDIX_SYNC_URL = import.meta.env.VITE_MEDIX_SYNC_URL;

  const child = window.open(`${MEDIX_SYNC_URL}/token-bridge`, '_blank');
  if (!child) return;
  const syncOrigin = new URL(MEDIX_SYNC_URL).origin;
  const payload: Payload = { token, from, path };
  function onMessage(e: MessageEvent) {
    if (e.source !== child) return;
    if (e.data?.type === 'READY') {
      child?.postMessage({ type: 'MEDIX_SYNC_TOKEN', payload }, syncOrigin);
      window.removeEventListener('message', onMessage);
    }
  }

  window.addEventListener('message', onMessage);
};
