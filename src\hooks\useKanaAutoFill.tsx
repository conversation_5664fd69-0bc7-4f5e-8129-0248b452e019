import { useRef, useEffect } from 'react';
import Form, { FormInstance } from 'antd/lib/form';
import { extractKatakana, mergeKana } from '@/utils/helper';

const isFullRepeat = (oldKana: string, newKana: string) =>
  !!oldKana &&
  newKana.length > oldKana.length &&
  newKana.length % oldKana.length === 0 &&
  newKana === oldKana.repeat(newKana.length / oldKana.length);

export const useKanaAutoFill = (
  form: FormInstance,
  srcField: string,
  destField: string,
  isInitialized = false
) => {
  const lastRaw = useRef('');
  const lastKana = useRef('');

  const raw = Form.useWatch(srcField, form) ?? '';

  useEffect(() => {
    if (isInitialized) return;

    const value = raw.trim();
    if (!value) {
      lastRaw.current = lastKana.current = '';
      form.setFieldsValue({ [destField]: '' });
      return;
    }

    const newKana = extractKatakana(value);

    if (!newKana.trim()) {
      lastRaw.current = value;
      return;
    }

    if (isFullRepeat(lastKana.current, newKana)) {
      lastRaw.current = value;
      return;
    }

    const isDel = value.length < lastRaw.current.length;
    const merged = mergeKana(lastKana.current, newKana, isDel);

    if (merged !== lastKana.current) {
      lastKana.current = merged;
      form.setFieldsValue({ [destField]: merged });
    }
    lastRaw.current = value;
  }, [raw, form, destField, isInitialized]);
};
