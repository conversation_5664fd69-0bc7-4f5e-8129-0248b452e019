.container {
  height: calc(100vh - 210px);
  // width: 100%;
}

.dbHistoryList {
  overflow-y: auto;
  max-height: calc(100% - $footer-height);
  width: 100%;
}

.dbHistory {
  border-radius: 12px;
  background-color: white;
  width: 100%;
  // max-width: 1120px;
}

.historyDate {
  background-color: $brand-50;
  color: $gray-500;
  padding: 12px 10px 0px 12px;
  border: 1px solid $brand-500;
  min-width: 130px;
}

.historyContentType {
  color: $gray-800;
  font-weight: 700;
  font-size: 12px;
}

.historyInfo {
  border: 1px solid $gray-200;
  padding: 10px 12px;
  // width: 100%;
  // max-width: 1120px;
}

.updatedDateLabel {
  color: $gray-500;
  font-weight: 400;
}

.updatedDateValue {
  color: $gray-800;
}
