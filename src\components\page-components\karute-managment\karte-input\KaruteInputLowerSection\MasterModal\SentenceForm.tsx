// Screen: K601_文章作成

import { Flex, Form, Spin } from 'antd';

import clsx from 'clsx';

import styles from './MasterModal.module.scss';

import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';

import { MasterReference } from '@/store/patient/type';
import EditableTable from '@/components/common/core/CommonTable/EditableTable';
import { useCallback, useEffect, useState } from 'react';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { SentencesActionBtn } from './SentencesActionBtn';
import { isEmpty } from 'lodash';
import { showFlashNotice } from '@/utils';
import { NOTICE_COMMON_MESSAGE, STATUS_CODES } from '@/types/constants';
import { v4 as uuidv4 } from 'uuid';
import { ButtonType, KaruteMasterField } from '@/types/enum';
import {
  useChangeOrderSentenceMutation,
  useCreateMasterSentenceMutation,
  useDeleteSentenceMutation,
  useLazyGetMasterSentencesQuery,
  useLazyGetReferenceCategoryQuery,
  useUpdateMasterSentenceMutation,
} from '@/store/master/api';
import { ClinicReferenceCategory } from '@/store/master/type';

export type SentencesItem = {
  rowId: string;
  content: string;
  isInit?: boolean;
  isDraggable?: boolean;
  order: number;
};

type Props = {
  masterReferencesData: MasterReference[];
  isFetchingCommon: boolean;
};
export default function SentenceForm({ masterReferencesData, isFetchingCommon }: Props) {
  const handleAddSentenceModal = () => {
    console.log('TODO: handleAddSentenceModal - PIC: <EMAIL>');
  };
  const [form] = Form.useForm<Record<string, SentencesItem>>();
  const [activeReference, setActiveReference] = useState<MasterReference>(masterReferencesData[0]);
  const [activeCategory, setActiveCategory] = useState<number>(0);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(['11']);
  const [sentencesDataSource, setSentencesDataSource] = useState<SentencesItem[]>([]);
  // API
  const [getReferenceCategory, { data: referenceCategoryData, isFetching: isFetchingCategory }] =
    useLazyGetReferenceCategoryQuery();
  const [
    lazyGetMasterSentencesQuery,
    { data: masterSentencesData, isFetching: isFetchingMasterSentencesDataLazy },
  ] = useLazyGetMasterSentencesQuery();
  const [updateMasterSentenceMutation, { isLoading: isLoadingUpdateMasterSentence }] =
    useUpdateMasterSentenceMutation();
  const [createMasterSentenceMutation, { isLoading: isLoadingCreateMasterSentence }] =
    useCreateMasterSentenceMutation();
  const [changeOrderSentenceMutation, { isLoading: isLoadingChangeOrderSentence }] =
    useChangeOrderSentenceMutation();
  const [
    deleteSentenceMutation,
    { isLoading: isLoadingDeleteSentence, isSuccess: isSuccessDeleteSentence },
  ] = useDeleteSentenceMutation();

  const isLoading =
    isFetchingCommon ||
    isFetchingCategory ||
    isFetchingMasterSentencesDataLazy ||
    isLoadingUpdateMasterSentence ||
    isLoadingCreateMasterSentence ||
    isLoadingChangeOrderSentence ||
    isLoadingDeleteSentence;

  const handleFetchMasterSentences = (referenceCd: string, referenceId: number, categoryId: number) => {
    const newRowId = uuidv4();
    lazyGetMasterSentencesQuery({
      reference_cd: referenceCd,
      category_id: categoryId,
    })
      .unwrap()
      .then(response => {
        if (response.status !== STATUS_CODES.OK) return;
        const data = (response?.data ?? []).map(item => ({
          rowId: item.clinic_reference_sentence_id.toString(),
          isInit: false,
          reference_id: item.clinic_reference_id,
          content: item.content,
          order: item.order,
          isDraggable: true,
        }));
        const placeHolderData = {
          rowId: newRowId,
          isInit: true,
          reference_id: referenceId,
          content: '',
          order: 0,
          isDraggable: false,
        };
        setSentencesDataSource([...data, placeHolderData]);
        setSelectedKeys([newRowId]);

      });
  };

  const handleSelectReference = (item: MasterReference) => {
    setActiveReference(item);
    getReferenceCategory({
      reference_cd: item.reference_cd as KaruteMasterField,
    })
      .unwrap()
      .then(res => {
        if (res.status !== STATUS_CODES.OK || !res.data) return;
        const categoryId = res.data[0].clinic_reference_category_id;
        setActiveCategory(categoryId);
        handleFetchMasterSentences(item.reference_cd, item.reference_id, categoryId);
      });
  };

  const handleSelectCategory = (item: ClinicReferenceCategory) => {
    const selectedId = item.clinic_reference_category_id
    setActiveCategory(selectedId);
    handleFetchMasterSentences(activeReference.reference_cd, activeReference.reference_id, selectedId);
  };

  useEffect(() => {
    if (masterReferencesData.length) {
      getReferenceCategory({
        reference_cd: masterReferencesData[0].reference_cd as KaruteMasterField,
      })
        .unwrap()
        .then(res => {
          if (res.status !== STATUS_CODES.OK || !res.data) return;
          const categoryId = res.data[0].clinic_reference_category_id;
          const referenceCd = masterReferencesData[0].reference_cd;
          const referenceId = masterReferencesData[0].reference_id;
          setActiveCategory(categoryId);
          handleFetchMasterSentences(referenceCd, referenceId, categoryId);
        });
    }
  }, []);

  const isSentenceValid = ({
    record,
    givenDataUpdated,
  }: {
    record: SentencesItem;
    givenDataUpdated?: SentencesItem;
  }) => {
    const dataUpdated = givenDataUpdated ?? form.getFieldValue(record.rowId);
    console.log("🚀 ~ SentenceForm ~ dataUpdated:", dataUpdated)

    const contentIsEmpty = isEmpty(dataUpdated.content);
    if (contentIsEmpty) {
      form.setFields([
        {
          name: [record.rowId, 'content'],
          errors: [''],
        },
      ]);
      return false;
    }

    return true;
  };


  const isEditable = useCallback(
    (data: SentencesItem) => {
      return selectedKeys.includes(data.rowId?.toString() || '');
    },
    [selectedKeys]
  );

  const scrollToRow = (rowId: string) => {
    setTimeout(() => {
      document
        .querySelector(`.scroll-row_sentences_${rowId}`)
        ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 1);
  };

  const handleCancelRecord = (record: SentencesItem) => {
    const originalRecord = sentencesDataSource.find(item => item.rowId === record.rowId);
    form.setFieldValue([record.rowId, 'content'], originalRecord?.content);
    setSelectedKeys(selectedKeys.filter(item => item !== record.rowId!));
  };

  const handleConfirmEdit = async (record: SentencesItem) => {
    const dataUpdated: SentencesItem = form.getFieldValue(record.rowId);
    if (!isSentenceValid({ record, givenDataUpdated: dataUpdated })) {
      return;
    }
    let upsertResponse = null;
    if (dataUpdated.isInit) {
      upsertResponse = await createMasterSentenceMutation({
        category_id: activeCategory,
        reference_id: activeReference.reference_id,
        content: dataUpdated.content,
      });
    } else {
      upsertResponse = await updateMasterSentenceMutation({
        sentence_id: Number(dataUpdated.rowId),
        content: dataUpdated.content,
      });
    }
    if (upsertResponse?.data?.status !== STATUS_CODES.OK) {
      return;
    }
    showFlashNotice({
      message: NOTICE_COMMON_MESSAGE.UPDATE_MASTER_DATA,
      type: 'success',
    });
  };
  const handleOpenEdit = (record: SentencesItem) => {
    setSelectedKeys(prevState => [...prevState, record?.rowId?.toString() || '']);

  };

  const handleAddSentences = () => {
    scrollToRow(sentencesDataSource[sentencesDataSource.length - 1].rowId!);
  };

  const columns: CustomColumnType<any>[] = [
    {
      title: '表示順',
      dataIndex: 'order',
      key: 'order',
      align: 'center',
      width: 76,
      render: (value, record) => {
        return record.order ? record.order : '';
      },
      sortable: true,
      sortKey: 'order',
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
          },
        };
      },
    },
    {
      title: '文章',
      dataIndex: 'content',
      key: 'content',
      sortKey: 'content',
      // width: 537,
      align: 'left',
      sortable: true,
      editable: {
        type: 'input',
        rules: [{ required: true, whitespace: true, message: '' }],
        maxLength: 100,
        style: {
          height: '40px',
          width: '473px',
        },
      },
      render: (_, record) => (
        <RenderWithTooltip style={{ fontSize: '14px' }} text={record.content} />
      ),
    },
    {
      title: '',
      dataIndex: 'sentences_actions',
      key: 'sentences_actions',
      sortKey: 'sentences_actions',
      width: 40,
      align: 'left',
      render: (_, record) => (
        <SentencesActionBtn
          key={record.rowId}
          isEdit={isEditable(record)}
          handleCancelRecord={() => handleCancelRecord(record)}
          handleConfirmEdit={() => handleConfirmEdit(record)}
          handleOpenEdit={() => handleOpenEdit(record)}
          record={record}
        />
      ),
    },
  ];
  useEffect(() => {
    const formValues = sentencesDataSource.reduce((acc, item) => {
      acc[item.rowId!] = item;
      return acc;
    }, {} as Record<string, object>);
    form.setFieldsValue(formValues);
  }, [form, sentencesDataSource]);

  const handleValuesChange = (changedValues: Record<string, SentencesItem>) => {
    Object.entries(changedValues).forEach(([rowId, changedFields]) => {
      Object.keys(changedFields).forEach(field => {
        form.setFields([{ name: [rowId, field as any], errors: [] }]);
      });
    });
  };

  return (
    <>
      <Flex
        justify="space-between"
        align="center"
        className={clsx('py-6 px-6', styles.master_title)}
      >
        <ModalTitle title={'新規作成'} />
        <Button customType={ButtonType.PRIMARY} onClick={handleAddSentenceModal} customSize="sm">
          <Icon name="plus" />
          新規作成
        </Button>
      </Flex>
      <Form
        form={form}
        className={clsx('pb-5', styles.category_modal_table)}
        onValuesChange={changedValues => {
          handleValuesChange(changedValues);
        }}
      >
        <Flex className={styles.sentences_tab}>
          <div className={styles.sentences_tab_sidebar_container}>
            <Flex vertical gap={0} className={styles.content}>
              {masterReferencesData.map((item, index) => (
                <div
                  key={index}
                  className={clsx(styles.row_item, 'fs14-medium', {
                    [styles.active]: item.reference_id === activeReference.reference_id,
                  })}
                  onClick={() => handleSelectReference(item)}
                >
                  <RenderWithTooltip text={item.name} maxLines={1} />
                </div>
              ))}
            </Flex>
          </div>
          <div className={styles.sentences_tab_content_container}>
            <Flex vertical>
              <Flex
                wrap
                gap={12}
                align="flex-start"
                justify={`${isFetchingCategory ? 'center' : 'flex-start'}`}
                className={styles.category}
              >
                {isFetchingCategory ? (
                  <Spin />
                ) : (
                  referenceCategoryData?.data?.map((cat) => {
                    return (
                      <Button
                        customSize="xxs"
                        customType={
                          cat.clinic_reference_category_id === activeCategory ? ButtonType.PRIMARY : ButtonType.SECONDARY_COLOR
                        }
                        onClick={() => handleSelectCategory(cat)}
                      >
                        {cat.name}
                      </Button>
                    );
                  })
                )}
              </Flex>

              <Form component={false}
                onValuesChange={(changedValues) => {
                  handleValuesChange(changedValues);
                }}>
                <EditableTable
                  isDnD
                  rowKey={record => record.rowId!}
                  columns={columns}
                  pagination={false}
                  dataSource={sentencesDataSource}
                  scroll={{ y: 217 }}
                  loading={isLoading}
                  isMargin
                  editingKeys={selectedKeys}
                  rowClassName={record =>
                    clsx(
                      `scroll-row_sentences_${record.rowId}`,
                      selectedKeys.includes(record.rowId?.toString() || '')
                        ? styles.editing_row
                        : styles.viewing_row
                    )
                  }
                  canDragToEnd={false}
                />
              </Form>
            </Flex>
          </div>
        </Flex>
      </Form>
    </>
  );
}
