import { Spin } from 'antd';
import useTokenBridgeAuth from '@/hooks/useTokenBridgeAuth';
import { useEffect } from 'react';
import { clearToken } from '@/utils/auth';

export default function TokenBridge() {
  useEffect(() => clearToken(), []);

  const rawPatterns = (import.meta.env.VITE_ALLOWED_ORIGINS || '')
    .split(',')
    .map((o: string) => o.trim())
    .filter(Boolean);

  useTokenBridgeAuth({ allowedOrigins: rawPatterns, timeout: 3000 });

  return (
    <div
      style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Spin tip="Authorising…" size="large" />
    </div>
  );
}
