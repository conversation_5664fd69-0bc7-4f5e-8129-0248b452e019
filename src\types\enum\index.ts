export enum ROLE {
  STORE_ADMIN = 'store-admin',
  STAFF = 'staff',
  MEDIX_ADMIN = 'medix-admin',
  COMPANY_ADMIN = 'company-admin',
}
export enum EKarute {
  VALID = 1,
  INVALID = 2,
}

export enum ESubmitType {
  SAVE = 'SAVE',
  SAVE_AND_RECEIVE = 'SAVE_AND_RECEIVE',
}

export enum HttpErrorCode {
  WRONG_PASSWORD = 4001,
  // Unauthorized
  UNAUTHORIZED = 4010,
  // Forbiden
  FORBIDDEN_RESOURCE = 4030,
  // Not found
  ADMIN_NOT_FOUND = 4040,
  USER_NOT_FOUND = 4041,
  // System
  OTHER_SYSTEM_ERROR = 5000,
  // unknown code
  UNKNOWN_ERROR = 9999,
  SUCCESS = 200,
}
export enum EInsurance {
  ALL = '0,5,6,7,1,1,4',
  SELF_PAID = '5',
  AUTOMOBILE_ACCIDENT = '6',
  WORK_ACCIDENT = '7',
  HEALTH_INSURANCE = '1,1,4',
  NONE = '0',
}
export enum EBooking {
  IN_PERSON = 1, // '来店',
  CONTINUE = 2, //'継続',
  PHONE = 3, // '電話',
  NET = 4, //  'ネット',
  FREE = 5, //'フリー',
}
export enum ECanAccess {
  YES = 1,
  NO = 0,
}
export enum EKaruteStatus {
  TEMPORARY = 1,
  CONFIRMED = 2,
}

export enum EJyosaiFlag {
  OFF = 0,
  ON = 1,
}
export * from './ButtonType';
export * from './ESort';
export * from './karute';
export enum EBookingType {
  insuredOrthopedic,
}

export enum EPaymentType {
  INSURED_JUDO = 'INSURED_JUDO',
  INSURED_ACUPUNCTURE = 'INSURED_ACUPUNCTURE',
  INSURED_MASSAGE = 'INSURED_MASSAGE',
  SELF_PAY = 'SELF_PAY',
}
export enum ECourse {
  SELF_PAID = 'jihi',
  WORK_RELATED = 'workers_compensation',
  HOKEN = 'hoken',
  AUTO_INSURANCE = 'compulsory_auto_insurance',
}
export enum EBurden {
  None = 0, //'なし'
  PreschoolUnder6_20Percent = 1, //'6歳以下の未就学児『2割負担』'
  Certificate_100Percent = 2, //'資格証明『10割負担』'
  Others_0Percent = 3, //'その他『0割』'
  Elderly_10Percent = 4, //'前期高齢１割'
  Elderly_20Percent = 5, //'前期高齢２割'
  Elderly_30Percent = 6, //'前期高齢３割'
}

export enum EDetailInsurance {
  None = 0,
  General = 1,
  LateElderly = 2,
  LivelihoodProtection = 4,
  OthersSelfPay = 5,
  CompulsoryAutoInsurance = 6,
  WorkersCompensation = 7,
}

export enum EKohi {
  None = 0,
  Welfare = 91, // 福祉
  Disability = 92, // 障害
  Child = 19, // 子ども
  Infant = 93, // 乳幼児
  SingleParent = 94, // 一人親
  AtomicBomb = 95, // 原爆
  Minamata = 20, // 水俣
}

export enum EKaruteService {
  M2 = '柔整', //Judo
  A2 = '鍼灸', //Acupuncture
  R2 = '訪問マッサージ', //HomeMassage
}

export enum EKaruteServicePriority {
  M2 = 1, //Judo
  A2 = 2, //Acupuncture
  R2 = 3, //HomeMassage
}
export enum EKaruteServieId {
  Judo = 3,
  Acupuncture = 1,
  HomeMassage = 2,
}
export enum ERemindType {
  LINE = 'line',
  EMAIL = 'email',
  SPECIFIC_EMAIL = 'specific_email',
  MUTE = 'mute',
}

export enum EPaymentCourse {
  JIHI = 'jihi',
  M2_HOKEN = 'm2_hoken',
  R2_HOKEN = 'r2_hoken',
  A2_HOKEN = 'a2_hoken',
}

export enum ERemindTypeMapping {
  line = 'LINEに送信',
  email = '登録済みのメールアドレスに送信',
  specific_email = '指定したメールアドレスに送信',
  mute = '通知不要',
}

export enum EPaymentCourseMapping {
  m2_hoken = '保険柔整',
  a2_hoken = '保険鍼灸',
  r2_hoken = '保険訪問マッサージ',
  jihi = '自費',
}
