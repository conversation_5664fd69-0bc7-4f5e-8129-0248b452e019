export enum ROLE {
  STORE_ADMIN = 'store-admin',
  STAFF = 'staff',
  MEDIX_ADMIN = 'medix-admin',
  COMPANY_ADMIN = 'company-admin',
}

export enum ESubmitType {
  SAVE = 'SAVE',
  SAVE_AND_RECEIVE = 'SAVE_AND_RECEIVE',
}

export enum HttpErrorCode {
  WRONG_PASSWORD = 4001,
  // Unauthorized
  UNAUTHORIZED = 4010,
  // Forbiden
  FORBIDDEN_RESOURCE = 4030,
  // Not found
  ADMIN_NOT_FOUND = 4040,
  USER_NOT_FOUND = 4041,
  // System
  OTHER_SYSTEM_ERROR = 5000,
  // unknown code
  UNKNOWN_ERROR = 9999,
  SUCCESS = 200,
}
export enum EInsurance {
  ALL = '0',
  SELF_PAID = '5',
  AUTOMOBILE_ACCIDENT = '6',
  WORK_ACCIDENT = '7',
  HEALTH_INSURANCE = '1,1,4',
}
export enum EBooking {
  IN_PERSON = '1',
  CONTINUE = '2',
  PHONE = '3',
  NET = '4',
  FREE = '5',
}

export enum EKarute {
  VALID = 1,
  INVALID = 2,
}

export * from './ButtonType';
export * from './ESort';
