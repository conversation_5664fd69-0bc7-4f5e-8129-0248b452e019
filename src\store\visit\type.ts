import { EVisitType } from '@/types/constants';
import { UserItem } from '../user-sync/type';
import { TreeDataNode } from 'antd';
import { PatientItem } from '../patient/type';
// import { Option } from '@/types/interface'; // TO-DO consider
export interface OptionItem {
  label: string;
  value: number;
  description: string | null;
}

export interface SelectionMasterData {
  insurance_types: OptionItem[];
  status: OptionItem[];
  visit_types: OptionItem[];
  courses: OptionItem[];
}

export interface VisitListParams {
  search?: string;
  order_by?: string | null;
  order_type?: 'asc' | 'desc' | null;
  visit_date?: string; // Format: YYYY-MM-DD
  status?: number[] | null;
  hoken_type?: number[] | null;
  courses?: number[] | null;
  practitioners?: number[] | null;
  page?: number;
  limit?: number;
}

export interface Visit {
  visit_id: number;
  visit_date: string; // e.g., "2025/04/14 12:00"
  visit_created_at: string; // e.g., "2025/04/14 12:00"
  visit_type?: EVisitType;
  patient_id: number;
  patient_cd: null;
  birthday: string; // e.g., "2020/06/23"
  gender: number;
  gender_text: string;
  name: string;
  kana: string;
  booking_id?: string;
  booking_from: null;
  booking_time_from: null; // TBD
  booking_to: null;
  booking_time_to: null; // TBD
  dashboard_status: number;
  practitioner: string;
  practitioner_ids: string;
  hoken_type: null;
  patient_hoken: null;
  course: string;
  course_ids: string;
  karute_id?: number | string;
  karute_input_enabled: boolean;
}

export interface VisitUpdatePayload {
  visit_type: EVisitType;
  course_groups: number[];
  practitioners: number[];
}

export type Doctor = UserItem;

export interface Course {
  course_id: number;
  name: string;
  duration: number;
  price: number;
  status: string;
}

export interface VisitRegistrationPayload {
  booking_id: number | undefined;
  patient_id: number;
  visit_date: string;
  visit_via: string;
  visit_type: string;
  course_ids: number[];
  practitioner_ids: number[];
}

export type SelectionStates = {
  selection: SelectionMasterData;
  isInitialized: boolean;
};

export interface ActiveCourses {
  service_name: string;
  data: PaymentCourse[];
}

interface PaymentCourse {
  payment_course_name: string;
  data: CourseItem[];
}

export interface CourseItem {
  course_id: number;
  course_name: string;
  status: string;
  cost: number;
  category_id: number;
  category_name: string;
  category_cd: string;
  payment_course_type: number;
  payment_course_name: string;
  duration: string;
  hide_type: number;
  services: ServiceInfo;
}

export interface ServiceInfo {
  service_id: number;
  name: string;
}

export interface CourseService {
  service_id: string;
  treeData: TreeDataNode[];
  jihiCourse: TreeDataNode[];
}

export interface VisitItem {
  visit_id: number;
  visit_via: number;
  visit_type: EVisitType;
  visit_date: string; // e.g., "2025\/05\/06 15:47",
  status: number;
  booking: Booking;
  courses: CourseVisit[];
  practitioners: Practitioner[];
  patient: PatientItem;
  patient_hoken: PatientHoken[];
  created_at: string;
  updated_at: string;
}

interface Booking {
  booking_id: number | null;
  booking_from: string | null;
  booking_time_from: string | null;
  booking_time_to: string | null;
  booking_to: string | null;
  booking_via: EVisitType | null;
}

interface Practitioner {
  user_id: number;
  kanji_name: string;
  kana_name: string;
}
interface CourseVisit {
  course_id: number;
  course_name: string;
  cost: number;
  duration: string;
}

interface PatientHoken {
  patient_insurance_id: number;
  clinic_id: number;
  patient_id: number;
  hoken_status: number;
  hoken_type: number;
  hokensya_type: number;
  hoken_no: string;
  hoken_name: string;
  hoken_kigo: string;
  hoken_bango: string;
  futan_ritsu: number;
  futan_shubetsu: number;
  hoken_shikaku_date: string;
  hoken_yuko_date: string;
  office_name: string | null;
  office_post: string | null;
  office_address: string | null;
  print_post_flg: number;
  print_tel_flg: number;
  print_add_flg: number;
  hihoken_post: string;
  hihoken_name: string;
  hihoken_kana: string;
  hihoken_address1: string;
  hihoken_address2: string | null;
  hihoken_tel: string;
  kohi_type: string;
  kohi_no: string;
  kohi_jukyusya_no: string;
  kohi_ritsu: number;
  kohi_start_date: string;
  kohi_end_date: string;
  kohi_kanrece_flg: number;
  kohi_jyosei_flg: number;
  shinsei_type: number;
  payment_1: string | null;
  payment_2: string | null;
  karte_print_post_flg: number;
  karte_print_tel_flg: number;
  jibai_hoken_logic_type: number;
  hoken_co_code: string | null;
  hoken_co_name: string | null;
  hoken_co_busyo: string | null;
  hoken_co_charger: string | null;
  hoken_co_memo: string | null;
  bank_name: string | null;
  branch_name: string | null;
  account_type: string | null;
  account_number: string | null;
  account_name: string | null;
  account_kana: string | null;
  ocr_data_hoken: string | null;
  ocr_date_hoken: string | null;
  ocr_data_kohi: string | null;
  ocr_date_kohi: string | null;
  created_user_id: number | null;
  modified_user_id: number | null;
  is_default: number;
  shoukan_flg: number;
  created_at: string | null;
  updated_at: string | null;
}
