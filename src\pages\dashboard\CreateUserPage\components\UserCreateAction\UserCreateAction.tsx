import Button from '@/components/common/core/Button';
import { routerPaths } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import { useNavigate } from 'react-router-dom';

interface UserCreateActionProps {
  formRef: any;
  mode: 'create' | 'edit';
}

const UserCreateAction = ({ formRef, mode }: UserCreateActionProps) => {
  const navigate = useNavigate();
  if (!formRef) {
    return <></>;
  }
  return (
    <>
      <Flex gap={8}>
        <Button
          customType={ButtonType.SECONDARY_COLOR}
          onClick={() => {
            navigate(routerPaths.dashboard);
          }}
        >
          キャンセル
        </Button>
        <Button
          customType={ButtonType.PRIMARY}
          onClick={() => {
            formRef?.current?.submit();
          }}
        >
          {mode === 'create' ? '登録' : '保存'}
        </Button>
      </Flex>
    </>
  );
};

export default UserCreateAction;
