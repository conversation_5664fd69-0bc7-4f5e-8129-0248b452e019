.modal {
  .modalContent {
    margin-top: 16px;
    .title {
      padding: 8px 16px;
      background-color: $brand-200;
      border-radius: 8px;
      color: $gray-600;
      text-align: center;
    }
    .content {
      margin-top: 16px;
    }
    .example {
      margin-top: 16px;
      padding: 16px 24px;
      border-radius: 8px;
      background-color: $blue-50;
      border: 1px solid $gray-200;

      .exampleTitle {
        color: $gray-800;
        margin-right: 16px;
      }

      .exampleContent {
        color: $gray-400;
        text-decoration: underline;
        text-decoration-color: $gray-400;
        text-decoration-style: solid;
      }
    }

    .stepList {
      margin-top: 16px;
      padding-left: 20px;
      list-style: decimal;
      color: $gray-800;
      .stepItem {
        margin-bottom: 12px;
      }

      .stepNum {
        color: $error-600;
      }

      .tagRow {
        margin-top: 6px;
      }
      .tag {
        border: 1px solid $error-600;
        border-radius: 4px;
        &:not(:last-child) {
          margin-right: 16px;
        }
      }
    }

    .warning {
      margin-top: 16px;
      padding: 16px;
      border-radius: 8px;
      background-color: $error-100;
      display: flex;
      align-items: start;
      gap: 8px;

      .warningTitle {
        color: $gray-800;
      }
      .warningContent {
        color: $gray-400;
        p {
          margin-top: 8px;
        }
      }
    }

    p {
      span.highlight {
        color: $error-600;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: flex-end;
  }
}
