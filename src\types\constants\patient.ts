import { PatientFilterItem } from '@/types/interface';
import { EBooking, EInsurance } from '../enum';
import { Option } from '../interface/Reception';

export const patientListFilterItems: PatientFilterItem[] = [
  {
    key: 'all',
    label: '全て',
    value: EInsurance.ALL,
  },
  {
    key: 'self_paid',
    label: '自費',
    value: EInsurance.SELF_PAID,
  },
  {
    key: 'health_insurance',
    label: '健康保険',
    value: EInsurance.HEALTH_INSURANCE,
  },
  {
    key: 'automobile_accident',
    label: '自賠責',
    value: EInsurance.AUTOMOBILE_ACCIDENT,
  },
  {
    key: 'work_accident',
    label: '労災',
    value: EInsurance.WORK_ACCIDENT,
  },
  {
    key: 'none',
    label: '保険なし',
    value: EInsurance.NONE,
  },
];
export const bookingTypeOptions: Option<EBooking>[] = [
  {
    value: EBooking.IN_PERSON,
    label: '来店',
  },
  {
    value: EBooking.CONTINUE,
    label: '継続',
  },
  {
    value: EBooking.PHONE,
    label: '電話',
  },
  {
    value: EBooking.NET,
    label: 'ネット',
  },
  {
    value: EBooking.FREE,
    label: 'フリー',
  },
] as const;
export const bookingTypeMap = {
  [EBooking.IN_PERSON]: '来店',
  [EBooking.CONTINUE]: '継続',
  [EBooking.PHONE]: '電話',
  [EBooking.NET]: 'ネット',
  [EBooking.FREE]: 'フリー',
};

export const DEFAULT_INSURANCE_FILTER: Record<EInsurance, boolean> = {
  [EInsurance.ALL]: true,
  [EInsurance.SELF_PAID]: false,
  [EInsurance.AUTOMOBILE_ACCIDENT]: false,
  [EInsurance.WORK_ACCIDENT]: false,
  [EInsurance.HEALTH_INSURANCE]: false,
  [EInsurance.NONE]: false,
};
