import { useState, useEffect, useCallback } from 'react';

export function useIdleTimer(idleTime: number = 30000): [boolean, () => void] {
  const [isIdle, setIsIdle] = useState(false);

  const resetTimer = useCallback(() => {
    setIsIdle(false);
    if (window.__idleTimerId) {
      clearTimeout(window.__idleTimerId);
    }
    window.__idleTimerId = setTimeout(() => {
      setIsIdle(true);
    }, idleTime);
  }, [idleTime]);

  useEffect(() => {
    const handleActivity = () => resetTimer();
    window.addEventListener('mousemove', handleActivity);
    window.addEventListener('keydown', handleActivity);
    window.addEventListener('click', handleActivity);
    window.addEventListener('scroll', handleActivity);
    resetTimer();

    return () => {
      window.removeEventListener('mousemove', handleActivity);
      window.removeEventListener('keydown', handleActivity);
      window.removeEventListener('click', handleActivity);
      window.removeEventListener('scroll', handleActivity);
      if (window.__idleTimerId) {
        clearTimeout(window.__idleTimerId);
      }
    };
  }, [resetTimer]);

  return [isIdle, resetTimer];
}
