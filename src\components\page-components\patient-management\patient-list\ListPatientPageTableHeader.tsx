import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input/AppInput';
import { AppRadio } from '@/components/common/core/Radio/AppRadio';
import { ButtonType } from '@/types/enum';
import { AppRangeDatePicker } from '@/components/common/core/Datepicker';
import { Flex, Form, Radio, Space } from 'antd';
import styles from './ListPatientPage.module.scss';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import { useListPatient, PatientSearchParams } from './provider/ListPatientProvider';
import clsx from 'clsx';
import { DATE_FORMATS } from '@/types/constants';

export default function ListPatientPageTableHeader() {
  const { handleSearch, handleClear, isFetching } = useListPatient();
  const [form] = Form.useForm<PatientSearchParams>();

  const onFormSubmit = (values: PatientSearchParams) => {
    handleSearch(values);
  };

  const onClear = () => {
    form.resetFields();
    handleClear();
  };

  const is_and = Form.useWatch('is_and', form);

  return (
    <Form
      form={form}
      onFinish={onFormSubmit}
      initialValues={{
        search: '',
        intake_case_1: true,
        intake_case_2: true,
        is_and: false,
      }}
    >
      <Flex
        className={styles.list_patient_page_table_header}
        gap="middle"
        justify="space-between"
        wrap="nowrap"
        align="flex-end"
      >
        <Flex
          className={styles.searchRow}
          gap="middle"
          align="flex-start"
          wrap="nowrap"
          justify="space-start"
        >
          <Flex gap="middle" align="flex-end">
            <Form.Item className={clsx(styles.input_common, styles.common_form_item)} name="search">
              <AppInput
                placeholder="氏名・カナ、患者番号"
                style={{
                  height: '40px',
                  padding: 'unset !important',
                  paddingRight: '12px',
                  backgroundColor: 'white',
                }}
                className={styles.searchInput}
                suffix={<Icon name="search" height={20} width={20} color="#667085" />}
              />
            </Form.Item>
            <Flex align="flex-end" gap="small">
              <Flex vertical justify="flex-start" align="flex-start" gap="small">
                <Form.Item name="intake_case_1" className={styles.common_form_item}>
                  <Radio.Group className={styles.radioGroup}>
                    <Space>
                      <AppRadio value={true}>来院期間</AppRadio>
                      <AppRadio value={false}>来院していない期間</AppRadio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  validateStatus={undefined}
                  className={clsx(styles.input_common, styles.common_form_item)}
                  name="intake_date_1"
                >
                  <AppRangeDatePicker
                    className={styles.datePicker}
                    format={DATE_FORMATS.DATE}
                    singlePlaceholder="From ~ to"
                    placeholder={['from', 'to']}
                  />
                </Form.Item>
              </Flex>

              <Form.Item name="is_and" valuePropName="checked" className={styles.common_form_item}>
                <AppCheckbox className={styles.checkbox}>AND検索</AppCheckbox>
              </Form.Item>
              <Flex
                vertical
                justify="flex-start"
                align="center"
                gap="small"
                className={`${styles.secondRowContainer} ${is_and ? styles.visible : ''}`}
              >
                <Flex vertical justify="flex-start" align="flex-start" gap={'small'} wrap="nowrap">
                  <Form.Item className={styles.common_form_item} name="intake_case_2">
                    <Radio.Group className={styles.radioGroup}>
                      <Space>
                        <AppRadio value={true}>来院期間</AppRadio>
                        <AppRadio value={false}>来院していない期間</AppRadio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item
                    className={clsx(styles.input_common, styles.common_form_item)}
                    name="intake_date_2"
                  >
                    <AppRangeDatePicker
                      className={styles.datePicker}
                      format={DATE_FORMATS.DATE}
                      placeholder={['from', 'to']}
                      singlePlaceholder="From ~ to"
                    />
                  </Form.Item>
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Flex>

        <Flex className={styles.buttonRow} gap="small" align="flex-start">
          <Button
            customType={ButtonType.PRIMARY}
            customSize="md"
            htmlType="submit"
            loading={isFetching}
          >
            検索
          </Button>
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            customSize="md"
            onClick={onClear}
            disabled={isFetching}
          >
            <Icon name="retry" />
            クリア
          </Button>
        </Flex>
      </Flex>
    </Form>
  );
}
