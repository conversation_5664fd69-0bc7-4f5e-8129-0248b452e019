.container {
  display: flex;
  width: 100%;
  flex: 1;
}

.withSidebar {
  // adjust width based if there is sidebar
  .outerContainer {
    width: calc(100% * 3 / 4);
  }
  .rightSidebar {
    width: calc(100% * 1 / 4);
  }
}
.outerContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.pageLayout {
  width: 100%; // Full width when no sidebar
  flex: 1;
  .pageLayoutHeader {
    background-color: white;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 32px;

    .headerLeft {
      display: flex;
      flex-direction: column;
      align-items: left;
    }
    .title{
      color: $gray-800;
    }
  }

  .content {
    padding: 20px 32px;
  }
}

.rightSidebar {
  background-color: var(--white);
  padding: 20px 16px;
  height: 100%;
}

@media screen and (max-width: 800px) {
  .pageLayout {
    .pageLayoutHeader {
      height: auto;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 16px;
      .headerLeft {
        text-align: left;
      }
    }
    .content {
      padding: 24px 16px;
    }
  }
}
