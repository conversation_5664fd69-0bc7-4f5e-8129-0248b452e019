export enum ERelationship {
  INDIVIDUAL = 1,
  FAMILY = 2,
  CUSTOM = 3,
}

export interface UpdatePatientSchema {
  patient_id: number;
  name: string;
  kana: string;
  birthday: string;
  gender: string;
  post: string;
  address1: string;
  address2: string;
  cellphone: string;
  email: string;
  patient_cmt: string;
  zokugara: ERelationship;
  defaultInsurance: {
    hihoken_name: string;
    hihoken_kana: string;
    hihoken_post: string;
    hihoken_address1: string;
    hihoken_tel: string;
  };
}

export const initialValuesUpdatePatient: UpdatePatientSchema = {
  patient_id: 0,
  name: '',
  kana: '',
  birthday: '',
  gender: '',
  post: '',
  address1: '',
  address2: '',
  cellphone: '',
  email: '',
  patient_cmt: '',
  zokugara: ERelationship.INDIVIDUAL,
  defaultInsurance: {
    hihoken_name: '',
    hihoken_kana: '',
    hihoken_post: '',
    hihoken_address1: '',
    hihoken_tel: '',
  },
};
