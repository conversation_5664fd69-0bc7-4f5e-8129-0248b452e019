import dayjs from 'dayjs';
export interface InsuranceInfoSchema {
  hoken_type?: string | null;
  shoukan_flg?: number | null;
  hoken_no?: string | null; //保険者番号
  hokensya_type?: number | null;
  hoken_name?: string | null;
  futan_ritsu?: number | null;
  futan_shubetsu?: string | null; // ?dropdown
  hoken_kigo?: string | null;
  hoken_bango?: string | null;
  hoken_shikaku_date?: dayjs.Dayjs | null; // YYYY-MM-DD
  hoken_yuko_date?: dayjs.Dayjs | null; // YYYY-MM-DD
}

export const initialValuesInsuranceInfo: InsuranceInfoSchema = {
  hoken_type: null,
  shoukan_flg: 0,
  hoken_no: '',
  hokensya_type: null,
  hoken_name: '',
  futan_ritsu: null,
  futan_shubetsu: null,
  hoken_kigo: '',
  hoken_bango: '',
  hoken_shikaku_date: null,
  hoken_yuko_date: null,
};
