import Table from '@/components/common/core/CommonTable';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import Icon from '@/components/common/core/Icon';
import { ImageGroup } from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/ImageGroup';
import { useKaruteInput } from '@/components/page-components/karute-managment/karte-input/provider';
import { MainKeika, MainKeikaDetail } from '@/store/karute/type';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import { MONDAI_TYPE } from '@/types/constants';
import { Empty, Flex, TableProps } from 'antd';
import clsx from 'clsx';
import styles from './ProgressRecords.module.scss';

interface ProgressRecordsProps {
  keika: MainKeika;
  showAddImage: boolean;
  hideHeader?: boolean;
  isEmpty?: boolean;
}

export function ProgressRecords({
  keika,
  showAddImage,
  hideHeader,
  isEmpty,
}: ProgressRecordsProps) {
  const { activeTrauma, openSchema } = useKaruteInput();

  const AddBtn: React.FC<{ keikaId: number; disease: DiseaseType }> = ({ keikaId, disease }) => (
    <button
      className={styles.addImageButton}
      onClick={() => {
        openSchema(SchemaType.KEIKA, disease, 1, keikaId); // use 1 just for mask key only,
      }}
    >
      <Icon name="addImage" color="white" />
    </button>
  );

  const makeTraumaLabel = (detail: MainKeikaDetail) => {
    if (!detail.disease_base) return '';
    const { trauma_type, trauma_counter } = detail.disease_base;
    return trauma_type && trauma_counter
      ? `${MONDAI_TYPE[trauma_type as keyof typeof MONDAI_TYPE]}${trauma_counter}`
      : '';
  };

  const hasActiveTrauma = (keika?: MainKeika) =>
    !!keika?.keika_details.some(d => makeTraumaLabel(d) === activeTrauma);

  const getColumns = (keika?: MainKeika): TableProps<MainKeikaDetail>['columns'] => {
    const totalRows = keika?.keika_details.length ?? 0;
    return [
      {
        title: '',
        dataIndex: 'keika_detail_id',
        width: 40,
        render: (_, detail) => (
          <strong className={styles.traumaLabel}>{makeTraumaLabel(detail)}</strong>
        ),
      },
      {
        title: '問診情報（S）',
        dataIndex: 'subjective',
        width: 101,
      },
      {
        title: '所見（O）',
        dataIndex: 'objective',
        width: 180,
        onCell: (_r, idx) =>
          idx === 0
            ? {
                rowSpan: totalRows,
                className: hasActiveTrauma(keika) ? styles.highlightRow : '',
              }
            : { rowSpan: 0 },
        render: (_val, _r, idx) =>
          idx === 0 ? (
            <div className={styles.findingsCell}>
              <RenderWithTooltip text={keika?.keika_details[0]?.objective || ''} />
              {keika && (
                <ImageGroup
                  imgs={keika.objective_images.map(i => ({
                    id: `${keika.keika_id}_${i.keika_image_id}`,
                    url: i.url,
                  }))}
                  onEdit={imageId => {
                    openSchema(SchemaType.KEIKA, DiseaseType.O, 1, keika?.keika_id, imageId);
                  }}
                  numberRow={2}
                  className={styles.imageGroup}
                />
              )}
              {showAddImage && keika && keika.objective_images.length < 6 && (
                <AddBtn disease={DiseaseType.O} keikaId={keika.keika_id!} />
              )}
            </div>
          ) : null,
      },
      {
        title: '分析（A）',
        dataIndex: 'assessment',
        width: 101,
      },
      {
        title: '計画（P）',
        dataIndex: 'plan',
        width: 101,
      },
      {
        title: '施術（T）',
        dataIndex: 'treatment',
        width: 180,
        onCell: (_r, idx) =>
          idx === 0
            ? {
                rowSpan: totalRows,
                className: hasActiveTrauma(keika) ? styles.highlightRow : '',
              }
            : { rowSpan: 0 },
        render: (_val, _r, idx) =>
          idx === 0 ? (
            <div className={styles.findingsCell}>
              <RenderWithTooltip text={keika?.keika_details[0]?.treatment || ''} />
              {keika && (
                <ImageGroup
                  imgs={keika.treatment_images.map(i => ({
                    id: `${keika.keika_id}_${i.keika_image_id}`,
                    url: i.url,
                  }))}
                  onEdit={imageId => {
                    openSchema(SchemaType.KEIKA, DiseaseType.T, 1, keika?.keika_id, imageId);
                  }}
                  numberRow={2}
                  className={styles.imageGroup}
                />
              )}
              {showAddImage && keika && keika.treatment_images.length < 6 && (
                <AddBtn disease={DiseaseType.T} keikaId={keika.keika_id!} />
              )}
            </div>
          ) : null,
      },
      {
        title: '備考（R）',
        dataIndex: 'remarks',
        width: 101,
      },
    ];
  };

  return (
    <Flex vertical className={styles.container}>
      <Table<MainKeikaDetail>
        columns={getColumns(keika)}
        dataSource={keika.keika_details}
        bordered
        pagination={false}
        showHeader={!hideHeader}
        scroll={{ x: 'max-content', y: 500 }}
        className={clsx(styles.keikaTable, isEmpty ? styles.emptyTable : '')}
        rowClassName={detail =>
          makeTraumaLabel(detail) === activeTrauma ? styles.highlightRow : ''
        }
      />
      {isEmpty && (
        <Empty
          image={<Icon name="emptyTable" native />}
          description={'データが登録されていません。'}
        />
      )}
    </Flex>
  );
}
