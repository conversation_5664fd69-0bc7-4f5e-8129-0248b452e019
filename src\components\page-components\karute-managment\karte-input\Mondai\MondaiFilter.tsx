import Button from '@/components/common/core/Button';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppSelect } from '@/components/common/core/Select';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import {
  MondaiQueryParams,
  resetModalQuery,
  setMondaiModalQuery,
  setMondaiQueryParams,
} from '@/store/karute/mondaiSlice';
import { COMMA_SEPARATED, MONDAI_TYPE_OPTIONS } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex, Tooltip } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib';
import { debounce } from 'lodash';
import { useEffect, useState } from 'react';
import styles from './Mondai.module.scss';
import { MondaiModal } from './MondaiTableModal';
import clsx from 'clsx';

interface IMondaiFilterProps {
  isModal?: boolean;
  trigger?: ({ ...args }: MondaiQueryParams) => void;
  setLocalQuery?: ({ ...args }: MondaiQueryParams) => void;
  localQuery?: MondaiQueryParams;
  handleAddRow?: () => void;
  handleCloseModal?: () => void;
}

export const MondaiFilter = ({
  isModal,
  localQuery,
  handleAddRow,
  handleCloseModal,
}: IMondaiFilterProps) => {
  const [selectedVal, setSelectedVal] = useState<string[]>([]);
  const dispatch = useAppDispatch();
  const [openMondaiModal, setOpenMondaiModal] = useState<boolean>(false);
  const { queryParams, modalQuery } = useAppSelector(state => state.karute.mondais);
  const [search, setSearch] = useState<string>('');

  const debouncedSettled = debounce((searchVal: string) => {
    if (isModal) {
      setSearch(searchVal);
    }
  }, 300);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    debouncedSettled(value);
  };

  const handleChangeMondaiType = (vals: any) => {
    if (isModal) {
      dispatch(
        setMondaiModalQuery({
          ...modalQuery,
          trauma_type: vals.filter((val: string) => val !== '0').join(','),
        })
      );
    } else {
      dispatch(
        setMondaiQueryParams({
          ...queryParams,
          trauma_type: vals.filter((val: string) => val !== '0').join(','),
        })
      );
    }
    return setSelectedVal(vals);
  };

  const handleSelectMondaiType = (val: any) => {
    if (val === '0') {
      setSelectedVal(['0']);

      return isModal
        ? dispatch(
            setMondaiModalQuery({
              ...modalQuery,
              trauma_type: undefined,
            })
          )
        : dispatch(setMondaiQueryParams({ ...queryParams, trauma_type: undefined }));
    }
    return setSelectedVal([...selectedVal.filter(val => val !== '0'), val]);
  };

  const handleCheckMondaiStatus = ({ target }: CheckboxChangeEvent) => {
    return isModal
      ? dispatch(
          setMondaiModalQuery({
            ...modalQuery,
            status: target.checked ? 1 : 0,
          })
        )
      : dispatch(setMondaiQueryParams({ ...queryParams, status: target.checked ? 1 : 0 }));
  };

  useEffect(() => {
    const handleEnter = () => {
      dispatch(setMondaiModalQuery({ ...queryParams, search: search }));
    };
    const keydownListener = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleEnter();
      }
    };
    window.addEventListener('keydown', keydownListener);
    return () => {
      window.removeEventListener('keydown', keydownListener);
      dispatch(resetModalQuery());
    };
  }, [search]);

  return (
    <>
      <Flex
        justify="space-between"
        align="center"
        className={isModal ? styles.headerModal : styles.header}
      >
        <h2 className={clsx(styles.title, 'fs14-bold')}>問題リスト</h2>
        <Flex style={{ width: '100%', marginRight: '24px' }} justify="end">
          <Flex
            align="center"
            gap={'small'}
            style={{
              paddingRight: '24px',
              borderRight: '1px solid var(--gray-200)',
              fontSize: '14px',
            }}
          >
            <Icon name={'filter'} color="$gray-500" width={16} height={16} />
            <span style={{ color: '#667085' }}>問題種類</span>
            <AppSelect
              className={styles.selectMondai}
              mode="multiple"
              placeholder=""
              maxTagCount={'responsive'}
              maxTagPlaceholder={omittedValues => (
                <Tooltip
                  styles={{ root: { pointerEvents: 'none' } }}
                  title={omittedValues.map(({ label }) => label).join(COMMA_SEPARATED)}
                >
                  <span>{omittedValues?.[0]?.label}...</span>
                </Tooltip>
              )}
              value={selectedVal.length ? selectedVal : Object.keys(MONDAI_TYPE_OPTIONS)[0]}
              showSearch={false}
              autoClearSearchValue
              onChange={handleChangeMondaiType}
              onSelect={handleSelectMondaiType}
              options={Object.entries(MONDAI_TYPE_OPTIONS).map(([key, val]) => ({
                label: val,
                value: key,
              }))}
            />
          </Flex>
          <Flex align="center" gap={'small'} style={{ marginLeft: '24px', fontSize: '14px' }}>
            <AppCheckbox
              defaultChecked={true}
              className={styles.checkboxCustom}
              style={{ width: '16px', height: '16px' }}
              checked={isModal ? !!modalQuery.status : !!queryParams.status}
              onChange={handleCheckMondaiStatus}
            />
            <span style={{ color: '#344054' }}>非活性を非表示にする</span>
          </Flex>
        </Flex>
        <Flex gap={'middle'}>
          {!isModal && (
            <Button
              customType={ButtonType.PRIMARY}
              onClick={() => setOpenMondaiModal(true)}
              customSize="sm"
            >
              <Icon width={20} height={20} name="paper" />
              全問題
            </Button>
          )}

          <Button customType={ButtonType.PRIMARY} onClick={handleAddRow} customSize="sm">
            <Icon width={20} height={20} name="plus" />
            新規作成
          </Button>
        </Flex>
        {isModal && (
          <Flex
            style={{ marginLeft: '24px', cursor: 'pointer' }}
            onClick={() => {
              if (handleCloseModal) return handleCloseModal();
            }}
          >
            <Icon color="$gray-500" width={20} height={20} name="close" />
          </Flex>
        )}
      </Flex>
      {isModal && (
        <>
          <Flex className={styles.mondaiInputModal}>
            <AppInput
              style={{
                maxWidth: '300px',
                height: '40px',
                marginRight: '8px',
                fontSize: '14px',
              }}
              placeholder="問題名"
              suffix={<Icon name="search" width={20} height={20} />}
              onChange={handleInputChange}
            />
            <Button
              customSize="md"
              className={styles.searchBtn}
              customType={ButtonType.PRIMARY}
              style={{
                width: '100%',
              }}
              onClick={() =>
                dispatch(
                  setMondaiModalQuery({
                    ...modalQuery,
                    search: search,
                  })
                )
              }
            >
              検索
            </Button>
          </Flex>
        </>
      )}
      {openMondaiModal && (
        <MondaiModal
          isOpen={openMondaiModal}
          queryParams={localQuery}
          onClose={() => setOpenMondaiModal(false)}
        />
      )}
    </>
  );
};
