// Gray
$gray-25: #fcfcfd;
$gray-50: #f9fafb;
$gray-100: #f2f4f7;
$gray-200: #e4e7ec;
$gray-300: #d0d5dd;
$gray-400: #98a2b3;
$gray-500: #667085;
$gray-600: #475467;
$gray-700: #344054;
$gray-800: #1d2939;
$gray-900: #000000;

// Brand
$brand-50: #edf6f7;
$brand-100: #eafcf9;
$brand-200: #d7f5f6;
$brand-300: #bdefef;
$brand-400: #a5dbdf;
$brand-500: #84c0ca;
$brand-600: #609cad;
$brand-700: #427991;
$brand-800: #2a5875;
$brand-900: #194060;

// Error
$error-100: #ffecd9;
$error-200: #ffd5b3;
$error-300: #ffb88d;
$error-400: #ff9c71;
$error-500: #ff6e42;
$error-600: #db4b30;
$error-700: #b72e21;
$error-800: #931715;
$error-900: #7a0c12;

// Warning
$warning-100: #fdfbcf;
$warning-200: #fcf6a0;
$warning-300: #f6e06f;
$warning-400: #ede14b;
$warning-500: #e2d114;
$warning-600: #c2b10e;
$warning-700: #a2930a;
$warning-800: #837506;
$warning-900: #6c6003;

// Success
$success-100: #f0fcd9;
$success-200: #ddfb83;
$success-300: #cdeb8a;
$success-400: #a1d869;
$success-500: #77c43c;
$success-600: #5aa82b;
$success-700: #418d1e;
$success-800: #2c7113;
$success-900: #1c5e0b;

// Blue
$blue-100: #cdf0ff;
$blue-200: #9bdcff;
$blue-300: #6ac3ff;
$blue-400: #45abff;
$blue-500: #0783ff;
$blue-600: #0555db;
$blue-700: #034bb7;
$blue-800: #023593;
$blue-900: #01257a;

// Purple
$purple-100: #ebe9fe;
$purple-200: #d9d6fe;
$purple-300: #bdb4fe;
$purple-400: #9b8afb;
$purple-500: #7a5af8;
$purple-600: #6938ef;
$purple-700: #5925dc;
$purple-800: #4a1fb8;
$purple-900: #3e1c96;

// Pink
$pink-50: #fdf2fa;
$pink-100: #fce7f6;
$pink-200: #fcecee;
$pink-300: #faa7e0;
$pink-400: #f670c7;
$pink-500: #ee46bc;
$pink-600: #dd2590;
$pink-700: #c11574;
$pink-800: #9e165f;
$pink-900: #851651;

// Gray Gradients
$gray-gradient-600: linear-gradient(90deg, #475467 0%, #667085 100%);
$gray-gradient-700: linear-gradient(45deg, #344054 0%, #475467 100%);
$gray-gradient-800: linear-gradient(90deg, #1d2939 0%, #475467 100%);
$gray-gradient-900: linear-gradient(45deg, #101828 0%, #344054 100%);

// Brand Gradients
$brand-gradient-600: linear-gradient(90deg, #7f56d9 0%, #66ced6 100%);
$brand-gradient-700: linear-gradient(45deg, #6941c6 0%, #7f56d9 100%);
$brand-gradient-800: linear-gradient(90deg, #53389e 0%, #7f56d9 100%);
$brand-gradient-900: linear-gradient(45deg, #42307d 0%, #7f56d9 100%);

$text-primary2: #121926;
$text-tertiary: #697586;

:root {
  --white: #ffffff;
  --black: #000000;
  --gray-25: #{$gray-25};
  --gray-50: #{$gray-50};
  --gray-100: #{$gray-100};
  --gray-200: #{$gray-200};
  --gray-300: #{$gray-300};
  --gray-400: #{$gray-400};
  --gray-500: #{$gray-500};
  --gray-600: #{$gray-600};
  --gray-700: #{$gray-700};
  --gray-800: #{$gray-800};
  --gray-900: #{$gray-900};

  --brand-100: #{$brand-100};
  --brand-200: #{$brand-200};
  --brand-300: #{$brand-300};
  --brand-400: #{$brand-400};
  --brand-500: #{$brand-500};
  --brand-600: #{$brand-600};
  --brand-700: #{$brand-700};
  --brand-800: #{$brand-800};
  --brand-900: #{$brand-900};

  --error-600: #{$error-600};

  --input-text: #{$gray-800};
  --input-border-hover: #{$brand-800};
  --input-text-placeholder: #{$gray-500};
  --input-border: #{$gray-300};
  --input-bg-disabled: #{$gray-50};
  --input-border-error: #db4b30;

  --checkbox-border-unchecked: #{$gray-300};
  --checkbox-bg-checked: #{$brand-100};
  --checkbox-border-checked: #{$brand-500};
  --checkbox-border-disabled: #{$gray-200};

  --text-primary2: #{$text-primary2};
  --text-tertiary: #{$text-tertiary};
}

// <Icon name="error" color="var(--gray-25)" width={16} height={16} />

// ...modules.scss
//background-color: var(--white);
