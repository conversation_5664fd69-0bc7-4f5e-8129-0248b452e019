import {
  APIResponse,
  Clinic,
  Company,
  ListResponse,
  ParamGetListUser,
  User,
  UserCreatePayload,
  UserInfo,
  WorkSchedule,
} from '@/types/interface';
import api from './api';
import { buildQueryString, cleanParams, toFormData } from '@/utils/helper';

const fetchCurrentUser = async () => {
  const res = await api.get<APIResponse<UserInfo>>('/api/user-info');
  return res.data;
};

const fetchUserList: (
  params: ParamGetListUser
) => Promise<APIResponse<ListResponse<User>>> = async (params: ParamGetListUser) => {
  const res = await api.get<APIResponse<ListResponse<User>>>(
    `/api/users${buildQueryString(params)}`
  );
  return res.data;
};

const createUser = async (payload: UserCreatePayload) => {
  const formData = toFormData(cleanParams(payload, false));

  const res = await api.postForm<APIResponse<User | any>>('/api/users', formData);

  return res.data;
};

const fetchUserById = async (id: number) => {
  const res = await api.get<APIResponse<User>>(`/api/users/${id}`);
  return res.data;
};

const updateUser = async (id: number, payload: UserCreatePayload) => {
  const formData = toFormData(cleanParams(payload, false));
  const res = await api.postForm<APIResponse<User | any>>(`/api/users/${id}/update`, formData);
  return res.data;
};

const fetchAllCompany = async () => {
  const res = await api.get<APIResponse<Company[]>>('/api/companies/all');
  return res.data;
};

const fetchClinics = async () => {
  const res = await api.get<APIResponse<Clinic[]>>('/api/clinics/manager-by-me');
  return res.data;
};

const fetchWorkSchedule = async ({
  user_id,
  start_date,
  end_date,
}: {
  user_id: number;
  start_date: string;
  end_date: string;
}) => {
  const res = await api.get<APIResponse<WorkSchedule[]>>('/api/users/working-schedule', {
    params: {
      user_id,
      start_date,
      end_date,
    },
  });
  return res.data;
};

export const userDegrees = [
  {
    label: '柔道整復師',
    value: '1',
  },
  {
    label: 'はり師',
    value: '2',
  },
  {
    label: 'きゅう師',
    value: '3',
  },
  {
    label: 'あん摩マッサージ指圧師',
    value: '4',
  },
];

export {
  fetchCurrentUser,
  fetchUserList,
  createUser,
  fetchUserById,
  updateUser,
  fetchAllCompany,
  fetchClinics,
  fetchWorkSchedule,
};
