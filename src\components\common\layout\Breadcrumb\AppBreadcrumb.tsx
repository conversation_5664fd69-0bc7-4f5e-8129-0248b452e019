import { useMemo } from 'react';

import { Breadcrumb, BreadcrumbProps } from 'antd';
import clsx from 'clsx';
import Icon from '../../core/Icon';
import './Breadcrumb.modules.scss';

export const AppBreadcrumb = ({ items, ...props }: BreadcrumbProps) => {
  const displayItems = useMemo(() => {
    return items?.map((item, index) => {
      const className = clsx(item.className, {
        'fs12-regular text-gray-500': index < items.length - 1,
        'fs12-medium text-gray-700': index >= items.length - 1,
      });
      return {
        ...item,
        className,
      };
    });
  }, [items]);

  return (
    <Breadcrumb
      className="breadcrumb-content"
      items={displayItems}
      separator={<Icon width={11} height={11} color="#344054" name={'breadcrumb'} />}
      {...props}
    />
  );
};
