import { Flex } from 'antd/lib';
import styles from './DbHistory.module.scss';
import { RevisionEntry } from '@/store/db-history/type';
import dayjs from 'dayjs';
import clsx from 'clsx';
import { HistoryContent } from './HistoryContent';

interface IHistoryRevisionProps {
  revision: RevisionEntry;
  revisionDate: string;
}
export function HistoryRevision({ revision, revisionDate }: IHistoryRevisionProps) {
  const {
    // schemas,
    injury_name,
    subjective,
    objective,
    assessment,
    plan,
    doctor,
    objective_images,
    courses,
    options,
    treatment,
    remarks,
    treatment_images,
    treatment_content,
  } = revision;

  return (
    <Flex style={{ width: '100%' }}>
      <div className={clsx(styles.historyDate, 'fs12-medium')}>
        {/* {historyDate} */}
        {dayjs(revisionDate).format('YYYY/MM/DD(dd)')}
      </div>

      <Flex gap={'12px'} vertical className={styles.historyInfo}>
        {/* {historyContent} */}
        <Flex gap={'32px'} wrap>
          {injury_name.map((item, index) => (
            <div
              key={`${item.name}-${index}`}
              className={clsx('fs12-medium', styles.historyContent)}
            >
              <span className={styles.historySubLabel}>{item.name} :</span>
              <span className={styles.historyText}>{item.content}</span>
            </div>
          ))}
        </Flex>

        {/* S */}
        <HistoryContent historyLabel="S1" historyContent={subjective}></HistoryContent>

        {/* O */}
        <HistoryContent
          historyLabel="O"
          historyContent={objective}
          additionalImgs={objective_images}
        />

        {/* A */}
        <HistoryContent historyLabel="A" historyContent={assessment} />

        {/* P */}
        <HistoryContent historyLabel="P" historyContent={plan} />

        {/* T */}
        <HistoryContent
          historyLabel="T"
          historyContent={treatment}
          additionalImgs={treatment_images}
          subContent={treatment_content}
        />

        {/* R */}
        <HistoryContent historyLabel="R" historyContent={remarks} />

        {/* 施術師 */}
        <HistoryContent historyLabel="施術師" historyContent={doctor} />

        {/* courses */}
        {courses.length > 0 && (
          <Flex gap={'8px'}>
            <span className="fs12-bold" style={{ minWidth: '36px' }}>
              コース
            </span>

            <span style={{ flexGrow: 1 }} className="fs12-regular">
              {courses.join('、')}
            </span>
          </Flex>
        )}

        {/* options */}
        {options.length > 0 && (
          <Flex gap={'8px'} justify="space-between">
            <span style={{ minWidth: '60px' }} className="fs12-bold">
              オプション
            </span>

            <Flex style={{ flexGrow: 1 }} vertical>
              {options.map((option, index) => (
                <span key={`${option}-${index}`} className="fs12-regular">
                  {option}
                </span>
              ))}
            </Flex>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
}
