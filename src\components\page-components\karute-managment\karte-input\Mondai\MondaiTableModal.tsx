import { AppModal } from '@/components/common/core/Modal';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { MondaiQueryParams, mondaiThunks } from '@/store/karute/mondaiSlice';
import { useLazyGetMondaiListQuery } from '@/store/mondai/api';
import { KaruteMondai } from '@/types/interface/KaruteMondai';
import { useEffect, useState } from 'react';
import { useKaruteInput } from '../provider';
import styles from './Mondai.module.scss';
import { MondaiTable } from './MondaiTable';

type KaruteInputUpperSectionWithModalProps = {
  isOpen?: boolean;
  onClose?: () => void;
  loadingData?: boolean;
  totalData?: number;
  mondaiData?: KaruteMondai[];
  queryParams?: MondaiQueryParams;
};

export const MondaiModal = ({ isOpen, onClose }: KaruteInputUpperSectionWithModalProps) => {
  const { modalQuery, queryParams } = useAppSelector(state => state.karute.mondais);
  const [trigger, { data, isFetching }] = useLazyGetMondaiListQuery();
  const dispatch = useAppDispatch();

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const { karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;
  useEffect(() => {
    if (!patientId) return;
    trigger({ ...modalQuery, patient_id: patientId });
  }, [modalQuery, patientId]);

  const handleCloseModal = () => {
    if (onClose) {
      onClose();
      dispatch(mondaiThunks.fetchMondai({ ...queryParams, patient_id: patientId! }));
    }
  };

  return (
    <AppModal
      closable={false}
      className={styles.mondaiModal}
      destroyOnClose={true}
      onCancel={handleCloseModal}
      open={isOpen}
      width={1000}
      height={842}
    >
      <MondaiTable
        selectedKeys={selectedKeys}
        setSelectedKeys={setSelectedKeys}
        handleCloseModal={handleCloseModal}
        mondaiData={(data?.data?.data as KaruteMondai[]) || []}
        queryWithParams={trigger}
        isModal={true}
        pagination={{
          total: (data?.data?.total_items as number) || 0,
          total_filter: (data?.data?.total_filtered as number) || 0,
          pageSize: data?.data?.per_page as number,
        }}
        loadingData={isFetching}
      />
    </AppModal>
  );
};
