import { Dayjs } from 'dayjs';

export interface MedicalBenefitsSchema {
  kohi_type?: string | null;
  kohi_no?: string | null;
  kohi_jukyusya_no?: string | null;
  kohi_ritsu?: number | null;
  kohi_jyosei_flg?: number | null;
  kohi_start_date?: Dayjs | null;
  kohi_end_date?: Dayjs | null;
  remarks?: string | null;
  office_name?: string | null;
  office_post?: string | null;
  office_address?: string | null;
}

export const initialValuesMedicalBenefits: MedicalBenefitsSchema = {
  kohi_type: null,
  kohi_no: '',
  kohi_jukyusya_no: '',
  kohi_ritsu: null,
  kohi_jyosei_flg: 0,
  kohi_start_date: null,
  kohi_end_date: null,
  remarks: null,
  office_name: null,
  office_post: null,
  office_address: null,
};
