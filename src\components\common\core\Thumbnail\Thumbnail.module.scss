$tile: 80px;

.thumb {
  width: $tile;
  height: $tile;
  border: 1px solid $gray-100;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.chosen {
  box-shadow: none;
}

.star {
  position: absolute;
  top: 4px;
  left: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: $brand-500;
}
.trash {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px;
  border-radius: 32px;
  color: $error-600;
  background-color: $gray-200;
}
.favActive {
  color: $brand-700;
}

.checkbox {
  position: absolute;
  top: 2px;
  left: 2px;
  :global(.ant-checkbox-inner) {
    border-radius: 50%;
    width: 20px;
    height: 20px;
  }
  :global(.ant-checkbox-checked .ant-checkbox-inner::after) {
    border-color: white;
    inset-inline-start: 28%;
  }
  :global(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: $brand-500 !important;
    border-color: $brand-500 !important;
  }
}
