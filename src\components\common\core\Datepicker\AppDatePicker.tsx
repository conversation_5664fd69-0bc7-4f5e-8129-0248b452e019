import { DatePicker, DatePickerProps } from 'antd';
import clsx from 'clsx';
import { useCallback, useEffect, useRef, useState } from 'react';
import Icon from '../Icon';
import styles from './AppDatePicker.module.scss';
import { useInView } from 'react-intersection-observer';
export const gray_500 = '#667085';

export const AppDatePicker = ({
  className,
  iconPosition = 'suffix',
  allowClear = true,
  inputReadOnly = true,
  ...props
}: DatePickerProps & {
  iconPosition?: 'suffix' | 'prefix';
}) => {
  const internalRef = useRef<HTMLElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const { ref: observerRef, inView } = useInView({
    threshold: 1,
    triggerOnce: false,
  });
  const classNames = clsx(className, styles.appPicker, {
    [styles.errorInput]: props.status === 'error',
    [styles.notDisabled]: !props.disabled,
    [styles.prefixIcon]: iconPosition === 'prefix',
  });

  const triggerBlur = useCallback(() => {
    if (internalRef.current) {
      internalRef.current.blur();
    }
  }, []);
  useEffect(() => {
    if (!inView) {
      triggerBlur();
    }
  }, [inView, triggerBlur]);
  return (
    <div ref={observerRef}>
      <DatePicker
        className={classNames}
        placeholder=""
        {...props}
        ref={internalRef as any}
        suffixIcon={
          isHovered && allowClear && props.value && !props.disabled ? (
            false
          ) : (
            <div style={{ opacity: props.disabled ? '50%' : '100%', color: gray_500 }}>
              {props?.suffixIcon ?? <Icon name="calendar" width={20} height={20} />}
            </div>
          )
        }
        inputReadOnly={inputReadOnly}
        allowClear={allowClear}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      />
    </div>
  );
};
