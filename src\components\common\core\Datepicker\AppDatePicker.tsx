import { DatePicker, DatePickerProps } from 'antd';
import clsx from 'clsx';
import Icon from '../Icon';
import styles from './AppDatePicker.module.scss';
const gray_500 = '#667085';

export const AppDatePicker = ({ className, ...props }: DatePickerProps) => {
  const classNames = clsx(className, styles.appPicker, {
    [styles.errorInput]: props.status === 'error',
    [styles.notDisabled]: !props.disabled,
  });

  return (
    <DatePicker
      className={classNames}
      placeholder=""
      suffixIcon={<Icon name="calendar" width={20} height={20} color={gray_500} />}
      {...props}
    />
  );
};
