# Medix Karute Web

## 📦 Requirements

- **Node.js v20.15.0**
- **Yarn** (recommended)

To check your Node version:

```bash
node -v
# Expected: v20.15.0
```

To install Yarn (if not already installed):

```bash
npm install -g yarn
```

## 🚀 To Run Locally

```bash
yarn install
yarn dev
```

---

## 📁 Folder Structure Overview

```
src/
├── assets/                # Icons, images, and other asset files
├── components/
│   ├── common/
│   │   ├── core/          # Core components (Button, Input, etc.)
│   │   ├── features/      # Feature-specific components (PatientTable, etc.)
│   │   ├── layout/        # Layout components (Header, Footer, Breadcrumb, PageTitle, etc.)
│   │   └── modal/         # Common modal components
│   ├── layout/            # Layout components using children/outlet
│   ├── page-components/   # Components specific to pages
│   └── provider/          # Provider components (AuthProvider, etc.)
├── config/                # Library and project configurations
├── hooks/                 # Custom React hooks (useAuth, useDebounce, etc.)
├── pages/                 # Pages linked to the router (<PERSON>ginPage, DashboardPage, etc.)
├── router/                # Router configuration
├── store/                 # API and store API configurations
├── styles/                # Global styles (SCSS global, variables, resets)
├── types/                 # Shared TypeScript types and definitions
│   ├── constants/         # Common constants used across the project
│   ├── enum/              # Enum definitions
│   └── interface/         # Interface definitions
├── ultis/                 # Helper functions and utilities not related to components
├── App.tsx
└── main.tsx
```

---

## 🧭 Folder Conventions

- **`components/`**:

  - `common/core/`: Core components such as Button, Input, etc.
  - `common/features/`: Feature-specific components.
  - `common/layout/`: Layout components like Header, Footer, Breadcrumb, PageTitle, etc.
  - `common/modal/`: Common modal components.
  - `layout/`: Layout components utilizing children or outlet.
  - `page-components/`: Components tailored to specific pages.
  - `provider/`: Provider components such as AuthProvider.
  - Use SCSS modules (`.module.scss`) for styling.
  - Export components via `index.ts`.

- **`pages/`**:

  - Contains only page components linked to the router, without sub-components.

- **`types/`**:

  - `constants/`: Common constants used throughout the project.
  - `enum/`: Enum definitions.
  - `interface/`: Interface definitions.

- **Naming Conventions**:
  - Folders: kebab-case (e.g., `page-components`).
  - Component folders and function components: PascalCase (e.g., `PageTitle`).
  - Other files and folders: camelCase (e.g., `ultis`).

---

## 🧪 Development Guidelines

- Use **SCSS modules** (`.module.scss`) for component styling.
- Place page-specific logic and UI in `pages/` or `page-components/`.
- Store reusable components in `components/common/core/` or `components/common/features/`.
- Manage routing configuration in `router/`.
- Organize shared types in `types/` with subfolders like `constants/`, `enum/`, and `interface/`.

---

## 🐳 Run with Docker

Ensure Docker and Docker Compose are installed.

### ▶️ Run in Production Mode

Start the production server:

```bash
docker compose up -d --build
```

The app will be accessible at:  
 [http://localhost:6789](http://localhost:6789)

### ▶️ Run in Staging Mode

Start the staging server:

```bash
docker compose -f docker-compose.staging.yml up -d --build
```

The app will be accessible at:  
 [http://localhost:6790](http://localhost:6790)

To stop the container:

```bash
docker compose down
```

---
