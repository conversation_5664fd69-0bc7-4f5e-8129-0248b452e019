# Medix Sync Web

## 📦 Requirements

- **Node.js v20.15.0**
- **Yarn** (recommended)

To check your Node version:

```bash
node -v
# Expected: v20.15.0
```

To install Yarn (if not already installed):

```bash
npm install -g yarn
```

To run Local:

1. Run development server:

```bash
yarn install
yarn dev
```

---

## 📁 Folder Structure Overview

```
src/
├── assets/                # Static assets (images, icons, fonts)
├── components/            # UI components (divided below)
├── common/
│   ├── layouts/           # Layouts components
│   ├── common/            # Shared, reusable UI (Button, Modal, etc.)
│   └── features/          # Page-specific or domain-specific UI (PatientTable, BookingFilter, etc.)
│       ├── patient/
│       └── booking/
├── hooks/                 # Custom React hooks (useAuth, useDebounce...)
├── pages/                 # Routed pages (LoginPage, DashboardPage...)
│   └── [PageName]/        # Each page can have components/, api/, hooks/, etc.
├── router/                # React Router config
├── services/              # API services
├── store/                 # Store setup and rootReducer
├── styles/                # Global SCSS, variables, resets
├── types/                 # Shared types, models, enums
├── utils/                 # Pure utility functions (formatDate, etc.)
├── App.tsx
├── main.tsx
└── vite-env.d.ts
```

---

## 🧭 Folder Conventions

### 🔹 `components/`

- `layouts/`: Layouts components used to wrap groups of pages
- `common/`: Globally reusable UI components (Button, Modal, Spinner...)
- `features/`: UI gắn với domain hoặc page cụ thể (PatientTable, BookingFilter...)
- All components should use `.module.scss` for scoped styling

### 🔹 `pages/`

- Routed pages (connected to React Router)
- Can contain `components/`, `api/`, `hooks/`
- Serve as the entry point of 1 screen

### 🔹 `hooks/`

- Custom React hooks (e.g., `useAuth`)
- Must follow React hook rules

### 🔹 `utils/`

- Pure JS helpers (no React dependencies)
- For formatting, parsing, etc.

### 🔹 `store/`

- Setup global Redux store and rootReducer
- Avoid putting business logic directly here

### 🔹 `types/`

- Centralized type declarations
- Includes `interface`, `type`, `enum` used across the app
- Suggest using subfiles like:
  - `auth.d.ts`
  - `user.d.ts`
  - `common.d.ts`

---

## 🧪 Development Guidelines

- Use `SCSS module` per component
- Keep page-specific logic and UI inside the corresponding `pages/` folder
- Place reusable UI components inside `components/common/`
- Place domain-specific or page-specific UI components inside `components/features/`
- Define routing inside `router/index.tsx`
- Organize shared types in `types/` for reuse and clarity

## 🐳 Run with Docker

Make sure you have Docker and Docker Compose installed.

### ▶️ Run in production mode:

1. Run production server:

```bash
docker compose up -d --build
```

The app will be available at:  
[http://localhost:3456](http://localhost:3456)

To stop the container:

```bash
docker compose down
```

---
