import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { KarutesParams } from '../karute/type';
import { PatientSummary } from './type';
import { APIResponse } from '@/types/interface';

export const patientSummariesApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,
    getPatientSummaries: build.query<PatientSummary[], KarutesParams>({
      query: params => {
        const filteredParams = Object.fromEntries(
          Object.entries(params || {}).filter(
            ([, value]) => value !== null && value !== undefined && value !== ''
          )
        );
        return {
          url: `/api/patient-summaries`,
          method: 'GET',
          params: filteredParams,
        };
      },
      providesTags: ['patient-summaries'],
    }),

    updatePatientSummaryById: build.mutation<APIResponse<PatientSummary>, Partial<PatientSummary>>({
      query: ({ patient_summary_id, ...body }) => ({
        url: `api/patient-summaries/${patient_summary_id}`,
        body: body,
        method: 'PUT',
      }),
    }),
    createPatientSummary: build.mutation<APIResponse<PatientSummary>, Partial<PatientSummary>>({
      query: ({ ...body }) => ({
        url: `api/patient-summaries`,
        body: body,
        method: 'POST',
      }),
    }),
    deletePatientSummary: build.mutation<
      APIResponse<unknown>,
      Pick<PatientSummary, 'patient_summary_id'>
    >({
      query: ({ patient_summary_id }: Pick<PatientSummary, 'patient_summary_id'>) => ({
        url: `api/patient-summaries/${patient_summary_id}`,
        method: 'DELETE',
      }),
    }),
  }),
});

export const {
  useCreatePatientSummaryMutation,
  useDeletePatientSummaryMutation,
  useLazyGetPatientSummariesQuery,
  useUpdatePatientSummaryByIdMutation,
} = patientSummariesApi;
