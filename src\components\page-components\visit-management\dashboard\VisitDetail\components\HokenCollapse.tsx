import Icon from '@/components/common/core/Icon';
import { Col } from 'antd';
import React, { useState, forwardRef, useImperativeHandle } from 'react';

export interface HokenCollapseRef {
  collapse: boolean;
}

export interface HokenCollapseProps {
  onToggleCollapse?: (val: boolean) => void;
}

export const HokenCollapse = forwardRef<HokenCollapseRef, HokenCollapseProps>(
  ({ onToggleCollapse }, ref) => {
    const [collapse, setCollapse] = useState<boolean>(false);

    useImperativeHandle(ref, () => ({
      collapse: collapse,
    }));

    const toggle = () => {
      setCollapse(!collapse);
      if (onToggleCollapse) {
        onToggleCollapse(!collapse);
      }
    };

    return (
      <Col
        span={24}
        className="fs16-medium"
        style={{
          flexDirection: 'row',
          display: 'flex',
          justifyContent: 'space-evenly',
          alignItems: 'center',
          gap: 8,
          textWrap: 'nowrap',
          marginBottom: '16px',
        }}
      >
        <div
          style={{
            transform: collapse ? 'rotate(-90deg)' : 'rotate(90deg)',
            display: 'inline-block',
            width: '20px',
            height: '20px',
            cursor: 'pointer',
          }}
          onClick={toggle}
        >
          <Icon name="arrowSortDown" color="$gray-500" width={20} height={20} />
        </div>
        <p
          style={{
            color: '$gray-700',
            cursor: 'pointer',
          }}
          onClick={toggle}
        >
          {collapse === false ? '表示する' : '折りたたむ'}
        </p>
        <div
          style={{
            transform: collapse ? 'rotate(-90deg)' : 'rotate(90deg)',
            display: 'inline-block',
            width: '20px',
            height: '20px',
            cursor: 'pointer',
          }}
          onClick={toggle}
        >
          <Icon name="arrowSortUp" color="$gray-500" width={20} height={20} />
        </div>
        <div
          style={{
            border: '1px solid #E4E7EC',
            display: 'inline-block',
            width: '100%',
          }}
        />
      </Col>
    );
  }
);
