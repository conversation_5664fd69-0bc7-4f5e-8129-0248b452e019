import {
  EPaymentType,
  EStatus,
} from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import {
  EDatabaseMedicalField,
  EKaruteFieldCode,
  EKaruteService,
  EKaruteServicePriority,
  EKaruteStatus,
  KaruteMasterFieldCode,
} from '../enum';

export const karuteStatusMap = {
  [EKaruteStatus.TEMPORARY]: 'カルテ (仮)',
  [EKaruteStatus.CONFIRMED]: 'カルテ (確)',
} as const;
export const serviceMap = {
  [EKaruteServicePriority.M2]: EKaruteService.M2,
  [EKaruteServicePriority.A2]: EKaruteService.A2,
  [EKaruteServicePriority.R2]: EKaruteService.R2,
};
// KEIKA
export type KeikaImage = {
  keika_image_id: number;
  schema_image_id?: number | null;
  keika_id?: number;
  path?: string;
  type?: number;
  is_draft?: number; // 0 hoặc 1
  url: string;
};

export interface KeikaItem {
  disease_base_id: number;
  trauma_type?: number; // default 1 - Mondai
  trauma_counter?: number;
  trauma_name?: string;
  injury_name?: string;
  injury_name_display?: string;
  status: EStatus;
  subjective?: string; // 	問診情報（S）
  objective?: string; // 所見（O）
  assessment?: string; // 分析（A）
  plan?: string; // 計画（P）
  payment_type: EPaymentType | null; // 施術 (T) - 保険, 自賠責, 労災, 自費
  option_checked?: boolean | null | 1 | 0; // オプション checkbox
  treatment?: string | null; // treatment
  remarks?: string; // 備考 (R) - optional
  doctor_id: number; // 施術師
  treatment_images: KeikaImage[];
  objective_images: KeikaImage[];
}
// KEIKA ITEM
// KEIKA SCHEMA
export interface UpsertKeikaSchema {
  visit_date: string | null;
  keika: KeikaItem[];
}

export const WARNING_REMOVE_COURSE_OPTION =
  'この変更を行うと、コース・オプションが非表示になる可能性があります。変更してもよろしいでしょうか？';

export const REMOVE_CONFIRM = '変更する';
export const mapKaruteLabel: Record<KaruteMasterFieldCode, string> = {
  [EKaruteFieldCode.S]: 'S',
  [EKaruteFieldCode.O]: 'O',
  [EKaruteFieldCode.A]: 'A',
  [EKaruteFieldCode.P]: 'P',
  [EKaruteFieldCode.T]: 'T',
  [EKaruteFieldCode.R]: 'R',
  [EDatabaseMedicalField.Personal]: '既往歴',
  [EDatabaseMedicalField.Family]: '家族歴',
  [EDatabaseMedicalField.Social]: '社会歴',
};

export const optionDatabaseField = [
  EKaruteFieldCode.S,
  EKaruteFieldCode.O,
  EKaruteFieldCode.A,
  EKaruteFieldCode.P,
  EKaruteFieldCode.T,
  EKaruteFieldCode.R,
  EDatabaseMedicalField.Personal,
  EDatabaseMedicalField.Family,
  EDatabaseMedicalField.Social,
].map(field => ({
  value: field,
  label: mapKaruteLabel[field],
}));

export const optionKeikaField = [
  EKaruteFieldCode.S,
  EKaruteFieldCode.O,
  EKaruteFieldCode.A,
  EKaruteFieldCode.P,
  EKaruteFieldCode.T,
  EKaruteFieldCode.R,
].map(field => ({
  value: field,
  label: mapKaruteLabel[field],
}));
