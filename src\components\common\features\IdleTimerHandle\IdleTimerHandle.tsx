import { useEffect, useRef, useState } from 'react';
import { useIdleTimer } from '@/hooks/useIdleTimer';
import Button from '@/components/common/core/Button';
import { ButtonType } from '@/types/enum';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import useAuth from '@/hooks/useAuth';
import { showFlashNotice } from '@/utils/flashNotice';
import { useLogoutMutation } from '@/store/auth/api';
import { useNavigate } from 'react-router-dom';
import { logoutThunk } from '@/store/auth/logoutThunk';
import { NOTICE_COMMON_MESSAGE } from '@/types/constants/error';
import { useAppDispatch } from '@/config/redux/store';

function CountdownContent({
  initialCountdown,
  onCountdownEnd,
  isPopupOpen,
}: {
  initialCountdown: number;
  onCountdownEnd: () => void;
  isPopupOpen: React.MutableRefObject<boolean>;
}) {
  const [countdown, setCountdown] = useState(initialCountdown);

  useEffect(() => {
    const countdownTimer = setInterval(() => {
      if (isPopupOpen.current) {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownTimer);
            setTimeout(() => {
              onCountdownEnd();
            }, 0);
            return 0;
          }
          return prev - 1;
        });
      } else {
        clearInterval(countdownTimer);
      }
    }, 1000);

    return () => clearInterval(countdownTimer);
  }, [onCountdownEnd, isPopupOpen]);

  return (
    <>
      セキュリティ上の理由により、一定時間操作が行われていない場合は自動ログアウトする処理を行っています。
      {countdown}秒以内に［続けて使用する］をクリックしないと、ログアウトします。
    </>
  );
}

export default function IdleTimerHandler() {
  const { isAuthenticated, expiredAt } = useAuth();
  const [onLogout] = useLogoutMutation({});
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const isExpired = expiredAt && expiredAt < Date.now();

  const countdownRef = useRef(10);
  const popupOpenRef = useRef(false);
  const [showLogoutPopup, setShowLogoutPopup] = useState(false);

  const idleTime = 10200000; // 2 hours and 50 minutes
  // const idleTime = 60000;

  const [isIdle, resetTimer] = useIdleTimer(idleTime);
  const hasLoggedOutRef = useRef(false);

  const { show, hide } = useModalConfirm();

  const handleClickLogout = async () => {
    try {
      await onLogout({}).unwrap();
      dispatch(logoutThunk());
      window.dispatchEvent(new Event('auth-change'));
      navigate('/login');
      hide();
    } catch (error) {
      console.log('logout error', error);
    }
  };

  useEffect(() => {
    if (isAuthenticated && hasLoggedOutRef.current) {
      hasLoggedOutRef.current = false;
    }
  }, [isAuthenticated]);

  // Logout timer
  useEffect(() => {
    if (isIdle && isAuthenticated && !isExpired && !showLogoutPopup && !hasLoggedOutRef.current) {
      setShowLogoutPopup(true);
      popupOpenRef.current = true;
      countdownRef.current = 10;

      show({
        title: <>作業を続行しますか？</>,
        content: (
          <CountdownContent
            key={Date.now()}
            initialCountdown={10}
            onCountdownEnd={() => {
              if (popupOpenRef.current) {
                setShowLogoutPopup(false);
                popupOpenRef.current = false;
                hasLoggedOutRef.current = true;
                handleClickLogout();
                showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.SESSION_INVALID });
              }
            }}
            isPopupOpen={popupOpenRef}
          />
        ),
        buttons: [
          <Button
            key="logout"
            customType={ButtonType.SECONDARY_COLOR}
            onClick={() => {
              setShowLogoutPopup(false);
              popupOpenRef.current = false;
              handleClickLogout();
            }}
            customSize="lg"
          >
            ログアウト
          </Button>,
          <Button
            key="continue"
            customType={ButtonType.PRIMARY}
            onClick={() => {
              setShowLogoutPopup(false);
              popupOpenRef.current = false;
              hide();
              resetTimer();
            }}
            customSize="lg"
          >
            続けて使用する
          </Button>,
        ],
        width: '464px',
        onClose: () => {
          setShowLogoutPopup(false);
          popupOpenRef.current = false;
          hide();
          resetTimer();
        },
      });
    }
  }, [isIdle, isAuthenticated, isExpired, hide, resetTimer, show, showLogoutPopup]);

  // Logout if session expired
  useEffect(() => {
    if (isAuthenticated && isExpired) {
      logoutThunk();
    }
  }, [isAuthenticated, isExpired, logoutThunk]);

  return null;
}
