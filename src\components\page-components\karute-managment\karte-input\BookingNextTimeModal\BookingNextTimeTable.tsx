import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import Table, { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { BookingResponse } from '@/store/booking/type';
import { formatDate } from '@/utils';
import dayjs from 'dayjs';
import styles from './BookingNextTime.module.scss';
interface IBookingNextTimeTableProps {
  data: BookingResponse[];
  isFetching?: boolean;
}

export function BookingNextTimeTable({ data, isFetching }: IBookingNextTimeTableProps) {
  const columns: CustomColumnType<BookingResponse>[] = [
    {
      title: '予約番号',
      dataIndex: 'booking_no',
      align: 'right',
      width: 60,
    },
    {
      title: '予約日時',
      dataIndex: 'booking_from',
      sortKey: 'booking_time',
      align: 'right',
      width: 146,
      render: (_, record) => {
        const formatedValue = formatDate.toJapanDate(record.booking_from);
        return (
          <>
            {formatedValue.concat(
              ' ' +
                dayjs(record.booking_from).format('HH:mm') +
                ' ~ ' +
                dayjs(record.booking_to).format('HH:mm')
            )}
          </>
        );
      },
    },
    {
      title: 'コース',
      dataIndex: 'course_name',
      sortable: false,
      align: 'left',
      render: (_: any, record) => <RenderWithTooltip text={record.course_name ?? ''} />,

      width: 120,
    },
    {
      title: '施術師',
      dataIndex: 'practitioner_account_name',
      align: 'left',
      render: (_: any, record) => (
        <RenderWithTooltip text={record.practitioner_account_name ?? ''} />
      ),
      width: 120,
    },
    {
      title: 'ステータス',
      dataIndex: 'status',
      sortKey: 'status',
      align: 'center',
      width: 120,
      render: (_: unknown, record) => {
        return record.status === 'created' ? <>{'予約済'}</> : <>{'キャンセル済み'}</>;
      },
    },
  ];

  return (
    <Table<BookingResponse>
      rowKey={record => record.booking_cd}
      columns={columns}
      dataSource={data}
      pagination={false}
      scroll={{ y: data.length ? 35 * 4 : undefined }}
      loading={isFetching}
      className={!data?.length ? styles.noData : ''}
    />
  );
}
