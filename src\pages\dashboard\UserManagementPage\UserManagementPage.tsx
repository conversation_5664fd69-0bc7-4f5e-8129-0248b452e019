import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import PageLayout from '@/components/layouts/PageLayout';
import { routerPaths } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { useNavigate } from 'react-router-dom';
import UserManagementContent from './components/UserManagementContent/UserManagementContent';

function UserManagementPage() {
  const navigate = useNavigate();
  return (
    <PageLayout
      title={'ユーザー一覧'}
      headerRight={
        <>
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            customSize="md"
            onClick={() => {
              navigate(routerPaths.create);
            }}
          >
            <Icon name="addPerson" />
            ユーザー登録
          </Button>
        </>
      }
    >
      <UserManagementContent />
    </PageLayout>
  );
}

export default UserManagementPage;
