import { OrderItem } from '../patient/type';

export interface ClinicReferenceCategory {
  clinic_id: number;
  clinic_reference_category_id: number;
  name: string;
  order: number;
}

export interface ClinicReference {
  clinic_reference_id: number;
  clinic_reference_category_id: number;
  reference_id: number;
  name: string;
}

export interface ClinicCategoryWithReferences extends ClinicReferenceCategory {
  references: ClinicReference[];
}

export interface ClinicReferenceSentence {
  clinic_reference_id: number;
  clinic_reference_sentence_id: number;
  content: string;
  order: number;
}

export interface MasterCategoryPayload extends OrderItem {
  limit: number;
  page: number;
}
export interface CreateCategoryPayload {
  clinic_id: number;
  name: string;
  references: number[];
}
export interface UpdateCategoryPayload extends Omit<CreateCategoryPayload, 'clinic_id'> {
  category_id: number;
}
export interface CreateSentencePayload {
  category_id: number;
  reference_id: number;
  content: string;
}
export interface UpdateSentencePayload
  extends Omit<CreateSentencePayload, 'category_id' | 'reference_id'> {
  sentence_id: number;
}
export interface ClinicReferenceSentence {
  clinic_reference_id: number;
  clinic_reference_sentence_id: number;
  content: string;
  order: number;
}
