import { forwardRef, useState } from 'react';
import { Input, InputProps, InputRef } from 'antd';
import clsx from 'clsx';
import styles from './AppInput.module.scss';
import Icon from '@/components/common/core/Icon';

export const AppInput = forwardRef<InputRef, InputProps>(
  ({ className, disabled, type, ...props }: InputProps, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    const classNames = clsx(styles.appInput, className, {
      [styles.errorInput]: status === 'error',
      [styles.password]: type === 'password' && !showPassword,
    });

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <Input
        ref={ref}
        disabled={disabled}
        className={clsx(classNames)}
        type={type === 'password' && !showPassword ? 'password' : 'text'}
        {...props}
        suffix={
          type === 'password' ? (
            <span onClick={togglePasswordVisibility} style={{ cursor: 'pointer' }}>
              <Icon
                width={20}
                height={20}
                name={showPassword ? 'eye' : 'eyeOff'}
                className={styles.passwordIcon}
                color="#667085"
              />
            </span>
          ) : (
            props.suffix
          )
        }
      />
    );
  }
);

AppInput.displayName = 'AppInput';
