import { authReducer } from '@/store/auth';
import { patientReducer } from '@/store/patient';
import { userReducer } from '@/store/user-sync';
import { combineReducers } from 'redux';
import { medixKaruteBaseQueryApi, medixSyncBaseQueryApi } from './base-query-api';
import { selectionReducer } from '@/store/visit';
import { karuteReducer } from '@/store/karute';
import { schemaReducer } from '@/store/schema';
import { sharedReducer } from '@/store/shared/slice';

const appReducer = combineReducers({
  user: userReducer,
  auth: authReducer,
  patient: patientReducer,
  [medixKaruteBaseQueryApi.reducerPath]: medixKaruteBaseQueryApi.reducer,
  [medixSyncBaseQueryApi.reducerPath]: medixSyncBaseQueryApi.reducer,
  selection: selectionReducer,
  karute: karuteReducer,
  schema: schemaReducer,
  sharedData: sharedReducer,
});

export const rootReducer = (state: any, action: any) => appReducer(state, action);
