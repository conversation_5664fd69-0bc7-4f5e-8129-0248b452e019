import Table from '@/components/common/core/CommonTable';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import Icon from '@/components/common/core/Icon';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import { Spin } from 'antd';
import clsx from 'clsx';
import { useKaruteInput } from '../../../provider';
import styles from '../../KaruteInputLowerSection.module.scss';
import { useDatabase } from '../DatabaseContext';
import { ImageGroup } from '../ImageGroup';
import { EPaymentType, paymentMap } from '../UpsertDatabaseModal/UpsertDatabaseForm/types';

const getPaymentLabel = (type?: string | number | null): string => {
  const key = String(type) as EPaymentType;
  return Object.values(EPaymentType).includes(key) ? paymentMap[key] : '';
};

export function DatabaseList() {
  const { openSchema } = useKaruteInput();
  const { list, activeId, isLoading } = useDatabase();
  const items = Array.isArray(list) ? list : [];
  const activeDb = items.find(item => item.disease_base_id === activeId);

  const tableData = [
    { key: 'visit_date', title: '初診日', value: activeDb?.visit_date, minHeight: '45px' },
    { key: 'subjective', title: '主訴 (S)', value: activeDb?.subjective, minHeight: '70px' },
    {
      key: 'present_illness',
      title: '現病歴 (S)',
      value: activeDb?.present_illness,
      minHeight: '70px',
    },
    {
      key: 'objective',
      title: '現症 (O)',
      value: activeDb?.objective,
      images: activeDb?.objective_images,
      disease: DiseaseType.O,
      minHeight: '130px',
    },
    { key: 'assessment', title: '病態把握 (A)', value: activeDb?.assessment, minHeight: '70px' },
    { key: 'plan', title: '施術計画 (P)', value: activeDb?.plan, minHeight: '70px' },
    {
      key: 'treatment',
      title: '施術 (T)',
      value: activeDb?.treatment,
      paymentType: getPaymentLabel(activeDb?.payment_type),
      images: activeDb?.treatment_images,
      disease: DiseaseType.T,
      minHeight: '130px',
    },
    { key: 'remarks', title: '備考 (R)', value: activeDb?.remarks || '', minHeight: '60px' },
    { key: 'doctor_name', title: '施術師', value: activeDb?.doctor_name, minHeight: '70px' },
  ];

  const columns = [
    {
      title: '',
      dataIndex: 'title',
      key: 'title',
      width: 180,
      render: (text: any) => <p className="fs12-medium">{text}</p>,
    },
    {
      title: '',
      dataIndex: 'value',
      key: 'value',
      render: (value: any, record: any) => (
        <div
          style={{
            minHeight: record.minHeight,
          }}
          className={clsx(styles.contentCell, 'fs12-regular')}
        >
          {record.key === 'treatment' && <p>請求区分：{record.paymentType}</p>}
          <RenderWithTooltip
            text={value || ''}
            maxLines={record.disease === DiseaseType.O ? 3 : 2}
          />
          {record.images && record.images.length > 0 && (
            <ImageGroup
              imgs={record.images.map((i: any) => ({
                id: `${i.disease_base_id}_${i.disease_base_image_id}`,
                url: i.url,
              }))}
              onEdit={imageId =>
                openSchema(
                  SchemaType.DATABASE,
                  record.key === 'objective' ? DiseaseType.O : DiseaseType.T,
                  activeDb!.disease_base_id,
                  undefined,
                  imageId
                )
              }
              // maxCount={2}
              className={styles.imageGroup}
            />
          )}
          {record.disease !== undefined && record.images?.length < 6 && (
            <button
              className={styles.addImageBtn}
              onClick={() =>
                openSchema(SchemaType.DATABASE, record.disease, activeDb?.disease_base_id || '')
              }
            >
              <Icon name="addImage" color="white" />
            </button>
          )}
        </div>
      ),
    },
  ];

  const dataSource = tableData.map(row => ({ ...row, key: row.key }));

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        overflow: 'auto',
        borderBottomLeftRadius: '5px',
        borderBottomRightRadius: '5px',
        borderTop: '1px solid var(--gray-200)',
      }}
      className={styles.database_list_table}
    >
      <Spin spinning={isLoading}>
        <Table columns={columns} dataSource={dataSource} pagination={false} showHeader={false} />
      </Spin>
    </div>
  );
}
