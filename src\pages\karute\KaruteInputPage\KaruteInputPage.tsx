import { KaruteInputContent } from '@/components/page-components/karute-managment/karte-input';

import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import PageLayout from '@/components/layouts/PageLayout';
import { KaruteInputAction } from '@/components/page-components/karute-managment/karte-input/KaruteInputAction';
import KaruteInputDelete from '@/components/page-components/karute-managment/karte-input/KaruteInputDelete';
import UpsertDatabaseModalWrapper from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal';

import UpsertKeikaModalWrapper from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputRightPane/UpsertKeikaModal/UpsertKeikaModalWrapper';
import { useKaruteInput } from '@/components/page-components/karute-managment/karte-input/provider';
import SchemaModal from '@/components/page-components/karute-managment/karte-input/SchemaModal';
import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { useVisitStatusSync } from '@/hooks/useChangeStatusVisitFromKaruteInput';
import { useRouterPath } from '@/hooks/useRouterPath';
import { resetKaruteDetails } from '@/store/karute';
import { resetMondais } from '@/store/karute/mondaiSlice';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import { routerPaths } from '@/types/constants';
import { EDatabaseUpsertMode, EKaruteUpsertType } from '@/types/enum';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Spin } from 'antd';

export function KaruteInputPage() {
  const { getPath } = useRouterPath();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { loading } = useAppSelector(state => state.karute.karute);

  const { karuteDetailData } = useKaruteInput();
  const { schemaType, disease, upsertType, upsertMode } = useParams<{
    schemaType?: SchemaType;
    disease?: DiseaseType;
    upsertType?: EKaruteUpsertType;
    upsertMode?: string;
  }>();

  const handleCloseModal = () => {
    const basePath = location.pathname.split('/schema')[0];
    navigate(basePath, { replace: true });
  };

  const closeUpsert = () => {
    const base = location.pathname
      .split(`/${EKaruteUpsertType.DATABASE}`)[0]
      .split(`/${EKaruteUpsertType.KEIKA}`)[0];
    navigate(base, { replace: true });
  };

  useEffect(() => {
    return () => {
      dispatch(resetKaruteDetails());
      dispatch(resetMondais());
    };
  }, []);

  useVisitStatusSync(karuteDetailData?.patient_id);

  return (
    <>
      <PageLayout
        title="カルテ入力"
        breadcrumb={
          <AppBreadcrumb
            items={[
              {
                title: <a href={getPath(routerPaths.karuteManagement.karuteList)}>カルテ一覧</a>,
              },
              { title: 'カルテ入力' },
            ]}
          />
        }
        headerRight={<KaruteInputAction />}
      >
        <>
          <Spin size="default" fullscreen spinning={loading} />
          <KaruteInputContent />
          <KaruteInputDelete />
        </>
      </PageLayout>
      {schemaType && disease && <SchemaModal open onCancel={handleCloseModal} />}

      {upsertType === EKaruteUpsertType.DATABASE &&
        upsertMode &&
        upsertMode !== EDatabaseUpsertMode.CHANGE_TYPE && (
          <UpsertDatabaseModalWrapper
            mode={upsertMode.split('_')[0] as 'upsert'}
            disease_base_id={Number(upsertMode.split('_')[1] || 0)}
            handleClose={closeUpsert}
          />
        )}

      {upsertType === EKaruteUpsertType.KEIKA && upsertMode && (
        <UpsertKeikaModalWrapper
          mode={upsertMode === 'create' ? 'create' : 'update'}
          id={upsertMode === 'create' ? undefined : Number(upsertMode.split('_')[0])}
          disease_base_id={Number(upsertMode.split('_')[1] || 0)}
          handleClose={closeUpsert}
        />
      )}
    </>
  );
}
