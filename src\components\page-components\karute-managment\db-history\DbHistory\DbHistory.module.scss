.container {
  height: calc(100vh - 210px);
}

.historySection {
  padding: 4px 24px 12px 24px;
  max-width: 100%;

  .historySectionDetail {
    display: flex;
    gap: 2px;
    align-items: start;
    padding: 12px 0;
    width: 100%;

    .sectionLabel {
      min-width: fit-content;
    }

    .sectionText {
      white-space: normal;
      overflow-wrap: break-word;
      word-break: break-word;
    }

    &:nth-child(1),
    &:nth-child(2) {
      border-bottom: 1px solid $gray-200;
    }
  }
}

.dbHistoryList {
  overflow-y: scroll;
  max-height: calc(100% - $footer-height);
  color: $gray-800;
}

.dbHistory {
  border-radius: 12px;
  background-color: white;
  width: 100%;

  .patientNamesInfo {
    max-width: 200px;
    white-space: normal;
    overflow-wrap: break-word;
    word-break: break-word;
  }
}

.historyDate {
  flex: 0 0 130px;
  background-color: $brand-50;
  color: $gray-500;
  font-weight: 500;
  padding: 12px;
  border: 1px solid $gray-200;
}

.historyInfo {
  flex: 1 1 0;
  border-top: 1px solid $gray-200;
  border-bottom-right-radius: 12px;
  padding: 10px 12px;
  max-width: 100%;
  min-width: 0;

  .historyContent {
    display: flex;
    gap: 2px;
    align-items: start;

    .historySubLabel {
      min-width: fit-content;
    }

    .historyText {
      // max-width: 95%;
      // width: 95%;
      white-space: normal;
      overflow-wrap: break-word;
      word-break: break-word;
    }
  }

  .historySubContent {
    white-space: normal;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  .historyImages {
    display: flex;
    gap: 16px;
    flex-wrap: nowrap;
    overflow-x: auto;
    margin-top: 4px;
    width: 100%;
    min-width: 0;

    img {
      flex: 0 0 173px;
      width: 173px;
      height: 117px;
      object-fit: cover;
    }
  }
}

.updatedDateLabel {
  color: $gray-500;
}

.updatedDateValue {
  color: $gray-800;
}

.checkBox {
}

.checkAll {
}

@media print {
  body * {
    visibility: hidden;
  }

  .container,
  .container * {
    visibility: visible;
  }

  .checkAll {
    display: none;
  }

  .container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .dbHistoryList {
    max-height: max-content;
  }

  .checkBox {
    display: none;
  }

  .dbHistory {
    border: 1px solid $gray-200;

    .historyDate {
      flex: 0 0 130px;
      background-color: $brand-50 !important;
      color: $gray-500 !important;
      font-weight: 500;
      padding: 12px;
      border: 1px solid $gray-200 !important;
    }

    &.hidden {
      display: none;
    }
  }

  .historyInfo {
    .historyImages {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(173px, 1fr));
      margin-top: 4px;
      height: fit-content;
      overflow: visible;

      img {
        margin: 4px 0px;
        width: 100%;
        height: 117px;
        object-fit: cover;
        break-inside: avoid;
      }
    }
  }
}
