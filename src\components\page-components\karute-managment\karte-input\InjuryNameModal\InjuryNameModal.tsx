// K103_負傷名/Injury name
import Button from '@/components/common/core/Button';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { ERROR_COMMON_MESSAGE } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { isInjuryNameEditable, showFlashNotice } from '@/utils';
import { Flex, Form, Modal } from 'antd';
import { useEffect, useState } from 'react';
import { EPaymentType } from '../KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import { InjuryNameSelectByDiagram } from './components/InjuryNameSelectByDiagram';
import { InjuryNameSelectBySelect } from './components/InjuryNameSelectBySelect';
import { InjuryNameSelectLeftRight } from './components/InjuryNameSelectLeftRight';
import { InjuryNameProvider, useInjuryName } from './context/InjuryNameContext';
import styles from './InjuryNameModal.module.scss';
import { InjuryNameIdentifier } from './type';

interface InjuryNameModalProps {
  open: boolean;
  onCancel: () => void;
  initialValue: InjuryNameIdentifier;
  onApply?: (payload: InjuryNameIdentifier) => void;
  paymentType: EPaymentType;
}

const InjuryNameModal: React.FC<InjuryNameModalProps> = ({ initialValue, ...rest }) => (
  <InjuryNameProvider>
    <InjuryNameModalContent {...rest} initialValue={initialValue} />
  </InjuryNameProvider>
);

const InjuryNameModalContent: React.FC<InjuryNameModalProps> = ({
  paymentType,
  open,
  onCancel,
  onApply,
  initialValue,
}) => {
  const [form] = Form.useForm();
  const { injuryNamePayload, setInjuryNamePayload, setIsInjuryNameEditable } = useInjuryName();
  const [init, setInit] = useState<InjuryNameIdentifier>(initialValue);
  const { handleValuesChange, confirmLeave, submitHandler } =
    useUnsavedWarning<InjuryNameIdentifier>(init);

  const handleCancelModal = () => {
    const confirm = confirmLeave();
    if (confirm) {
      onCancel?.();
    }
  };

  const handleOk = async () => {
    try {
      await form.validateFields();
      onApply?.(injuryNamePayload);
      submitHandler();
      showFlashNotice({ type: 'success', message: ERROR_COMMON_MESSAGE.INJURY_NAME_APPLIED });
      onCancel();
    } catch {}
  };

  useEffect(() => {
    handleValuesChange({}, injuryNamePayload);
  }, [injuryNamePayload, handleValuesChange]);

  useEffect(() => {
    const init = {
      part_type: initialValue.part_type === 0 ? null : initialValue.part_type,
      part_id: initialValue.part_id === 0 ? null : initialValue.part_id,
      lr_type: initialValue.lr_type === 0 ? null : initialValue.lr_type,
      name: initialValue.name === '' ? null : initialValue.name,
      name_other: initialValue.name_other ?? null,
    };
    setInit(init);
    setInjuryNamePayload(init);
  }, [initialValue]);

  useEffect(() => {
    setIsInjuryNameEditable(isInjuryNameEditable(paymentType));
  }, [paymentType]);
  return (
    <Modal
      title="負傷名"
      open={open}
      footer={null}
      onCancel={handleCancelModal}
      centered
      destroyOnClose
      className={styles.modal}
      maskClosable={false}
    >
      <Form form={form} layout="vertical" onFinish={handleOk} className={styles.form}>
        <div className={styles.modalWrapper}>
          <InjuryNameSelectBySelect form={form} />
          <div className={styles.container}>
            <InjuryNameSelectLeftRight />
            <InjuryNameSelectByDiagram />
          </div>

          <Flex justify="end" gap="small">
            <Button
              customType={ButtonType.SECONDARY_COLOR}
              customSize="lg"
              style={{ width: '120px' }}
              onClick={handleCancelModal}
            >
              キャンセル
            </Button>
            <Button
              customType={ButtonType.PRIMARY}
              customSize="lg"
              style={{ width: '120px' }}
              onClick={handleOk}
            >
              適用
            </Button>
          </Flex>
        </div>
      </Form>
    </Modal>
  );
};

export default InjuryNameModal;
