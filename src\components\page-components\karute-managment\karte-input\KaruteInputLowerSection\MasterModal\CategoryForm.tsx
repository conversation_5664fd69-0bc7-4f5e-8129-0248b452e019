// Screen: K600_カテゴリー作成;

import { useCallback, useEffect, useMemo, useState } from 'react';
import { Flex, Form } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import clsx from 'clsx';

import styles from './MasterModal.module.scss';

import { CustomColumnType } from '@/components/common/core/CommonTable/Table';

import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import EditableTable from '@/components/common/core/CommonTable/EditableTable';

import { ButtonType } from '@/types/enum';
import {
  COMMA_SEPARATED,
  NOTICE_COMMON_MESSAGE,
  PAGINATION_THRESHOLD,
  PLACEHOLDER_MESSAGE_DEFAULT,
  STATUS_CODES,
} from '@/types/constants';

import { showFlashNotice } from '@/utils';
import { isEmpty } from 'lodash';
import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import {
  useChangeOrderCategoryMutation,
  useCreateMasterCategoryMutation,
  useDeleteCategoryMutation,
  useLazyGetMasterCategoriesQuery,
  useUpdateMasterCategoryMutation,
} from '@/store/master/api';
import { MasterCategoryPayload } from '@/store/master/type';
import { ESort } from '@/types/enum';
import { useAppSelector } from '@/config/redux/store';
import { getAuth } from '@/store/auth/selectors';
import { MasterActionBtn } from './MasterActionBtn';
import { MasterReference } from '@/store/patient/type';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';

export type CagoryFormItem = {
  rowId: string;
  isInit?: boolean;
  name: string;
  references: number[];
  order: number;
  isDraggable?: boolean;
};

const WARNING_DELETE_CATEGORY = {
  title: 'カテゴリーを削除する',
  content: 'この操作は元に戻せません。カテゴリーを削除してもよろしいでしょうか？',
};
type Props = {
  handleUnsavedValueChange: (_: any, all: Record<string, CagoryFormItem>) => void;
  resetInitial: (init?: Record<string, { name: string; references: number[] }>) => void;
  setCurrent: React.Dispatch<
    React.SetStateAction<
      Record<
        string,
        {
          name: string;
          references: number[];
        }
      >
    >
  >;
  masterReferencesData: MasterReference[];
  isFetchingCommon: boolean;
  initialValue: Record<string, { name: string; references: number[] }>;
  resetAll: (init?: Record<string, { name: string; references: number[] }>) => void;
};
export default function CategoryForm({
  handleUnsavedValueChange,
  resetInitial,
  setCurrent,
  masterReferencesData,
  isFetchingCommon,
  initialValue,
  resetAll,
}: Props) {
  // Store
  const auth = useAppSelector(getAuth);
  // States
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [categoryDataSource, setCategoryDataSource] = useState<CagoryFormItem[]>([]);
  const [listCategoryParams, setListCategoryParams] = useState<MasterCategoryPayload>({
    limit: PAGINATION_THRESHOLD,
    page: 1,
  });

  // Hooks
  const [form] = Form.useForm<Record<string, CagoryFormItem>>();
  const { show, hide } = useModalConfirm();

  // API
  const [
    lazyGetMasterCategoriesQuery,
    { data: masterCategoriesData, isFetching: isFetchingMasterCategoriesDataLazy },
  ] = useLazyGetMasterCategoriesQuery();
  const [updateMasterCategoryMutation, { isLoading: isLoadingUpdateMasterCategory }] =
    useUpdateMasterCategoryMutation();
  const [createMasterCategoryMutation, { isLoading: isLoadingCreateMasterCategory }] =
    useCreateMasterCategoryMutation();
  const [changeOrderCategoryMutation, { isLoading: isLoadingChangeOrderCategory }] =
    useChangeOrderCategoryMutation();
  const [
    deleteCategoryMutation,
    { isLoading: isLoadingDeleteCategory, isSuccess: isSuccessDeleteCategory },
  ] = useDeleteCategoryMutation();

  // Effects
  useEffect(() => {
    lazyGetMasterCategoriesQuery(listCategoryParams)
      .unwrap()
      .then(res => {
        if (res.status !== STATUS_CODES.OK) return;
        const newRowId = uuidv4();

        const data = (res?.data?.data ?? []).map(item => ({
          rowId: item.clinic_reference_category_id.toString(),
          isInit: false,
          name: item.name,
          references: item.references.map(ref => ref.reference_id),
          order: item.order,
          isDraggable: true,
        }));
        const placeHolderData = {
          rowId: newRowId,
          isInit: true,
          name: '',
          references: [],
          order: 0,
          isDraggable: false,
        };
        setCategoryDataSource([...data, placeHolderData]);
        // update again
        const initialState = {
          [newRowId]: {
            name: '',
            references: [],
          },
        };
        resetAll(initialState);
        setSelectedKeys([newRowId]);
        scrollToRow(newRowId);
      });
  }, [
    listCategoryParams,
    lazyGetMasterCategoriesQuery,
    // resetAll,
  ]);
  useEffect(() => {
    if (isSuccessDeleteCategory && !isLoadingDeleteCategory) {
      hide();
    }
  }, [isSuccessDeleteCategory, isLoadingDeleteCategory]);
  // Fucntion
  const isEditable = useCallback(
    (data: CagoryFormItem) => {
      return selectedKeys.includes(data.rowId?.toString() || '');
    },
    [selectedKeys]
  );
  const isCategoryValid = ({
    record,
    givenDataUpdated,
  }: {
    record: CagoryFormItem;
    givenDataUpdated?: CagoryFormItem;
  }) => {
    const dataUpdated: CagoryFormItem = givenDataUpdated
      ? givenDataUpdated
      : form.getFieldValue(record.rowId);
    const errors: { [key: string]: string[] } = {};

    if (isEmpty(dataUpdated.name)) {
      errors[`${record.rowId}.name`] = [''];
    }
    if (isEmpty(dataUpdated.references)) {
      errors[`${record.rowId}.references`] = [''];
    }

    if (Object.keys(errors).length > 0) {
      form.setFields(
        Object.entries(errors).map(
          ([name, errors]) =>
            ({
              name: name.split('.'),
              errors,
            } as any)
        )
      );
      return false;
    }
    return true;
  };
  const handleCancelRecord = (record: CagoryFormItem) => {
    const originalRecord = categoryDataSource.find(item => item.rowId === record.rowId);
    form.setFieldValue([record.rowId, 'name'], originalRecord?.name);
    form.setFieldValue([record.rowId, 'references'], originalRecord?.references);
    form.setFieldValue([record.rowId, 'order'], originalRecord?.order);
    setSelectedKeys(selectedKeys.filter(item => item !== record.rowId!));

    // Reset unsaved changes
    const newInitValue = { ...initialValue };
    delete newInitValue[record.rowId];
    resetInitial(newInitValue);
    setCurrent(prev => {
      const newCurrentChanges = { ...prev };
      delete newCurrentChanges[record.rowId];
      return newCurrentChanges;
    });
  };
  const handleConfirmEdit = async (record: CagoryFormItem) => {
    const dataUpdated: CagoryFormItem = form.getFieldValue(record.rowId);

    if (!isCategoryValid({ record, givenDataUpdated: dataUpdated })) {
      return;
    }
    let upsertResponse = null;
    if (dataUpdated.isInit) {
      upsertResponse = await createMasterCategoryMutation({
        name: dataUpdated.name,
        references: dataUpdated.references,
        clinic_id: auth.currentClinicId!,
      });
    } else {
      upsertResponse = await updateMasterCategoryMutation({
        category_id: Number(dataUpdated.rowId),
        name: dataUpdated.name,
        references: dataUpdated.references,
      });
    }
    if (upsertResponse?.data?.status !== STATUS_CODES.OK) {
      return;
    }
    showFlashNotice({
      message: NOTICE_COMMON_MESSAGE.UPDATE_MASTER_DATA,
      type: 'success',
    });
    const newCategoryList = await lazyGetMasterCategoriesQuery(listCategoryParams).unwrap();
    if (newCategoryList?.status !== STATUS_CODES.OK) {
      return;
    }
    const newRowId = uuidv4();
    const data = (newCategoryList?.data?.data ?? []).map(item => ({
      rowId: item.clinic_reference_category_id.toString(),
      isInit: false,
      name: item.name,
      references: item.references.map(ref => ref.reference_id),
      order: item.order,
      isDraggable: true,
    }));
    const placeHolderData = {
      rowId: newRowId,
      isInit: true,
      name: '',
      references: [],
      order: 0,
      isDraggable: false,
    };
    setCategoryDataSource([...data, placeHolderData]);
    const newInitValue = { ...initialValue };
    let lastKey = '';
    setSelectedKeys(prev => {
      const latestKey = prev.slice(-1)[0];
      lastKey = latestKey;
      const newSelectedKeys = prev.filter(item => item !== String(record.rowId));
      return [...newSelectedKeys, newRowId];
    });
    setCurrent(prev => {
      const newCurrentChanges = { ...prev };
      delete newCurrentChanges[record.rowId];
      delete newCurrentChanges[lastKey];
      return newCurrentChanges;
    });
    delete newInitValue[record.rowId];
    delete newInitValue[lastKey];
    newInitValue[newRowId] = {
      name: '',
      references: [],
    };
    resetInitial(newInitValue);
    if (dataUpdated.isInit) {
      scrollToRow(newRowId);
    }
  };
  const handleOpenEdit = (record: CagoryFormItem) => {
    setSelectedKeys(prevState => [...prevState, record?.rowId?.toString() || '']);
    const newInitialValue = { ...initialValue };
    newInitialValue[record.rowId] = {
      name: form.getFieldValue(record.rowId)?.name || '',
      references: form.getFieldValue(record.rowId)?.references || [],
    };

    setCurrent(prev => {
      const newCurrentChanges = { ...prev };
      newCurrentChanges[record.rowId] = {
        name: form.getFieldValue(record.rowId)?.name || '',
        references: form.getFieldValue(record.rowId)?.references || [],
      };
      return newCurrentChanges;
    });
    resetInitial(newInitialValue);
  };
  const handleDeleteRecord = useCallback(
    (record: CagoryFormItem) => {
      show({
        title: WARNING_DELETE_CATEGORY.title,
        content: WARNING_DELETE_CATEGORY.content,

        buttons: [
          <Button
            key="cancel"
            customType={ButtonType.SECONDARY_COLOR}
            onClick={hide}
            customSize="lg"
            style={{ width: '120px' }}
          >
            キャンセル
          </Button>,
          <Button
            key="confirm"
            customSize="lg"
            customType={ButtonType.RED_PRIMARY}
            onClick={async () => {
              const response = await deleteCategoryMutation({
                category_id: Number(record.rowId),
              }).unwrap();
              if (response?.status !== STATUS_CODES.OK) {
                return;
              }
              const newCategoryList = await lazyGetMasterCategoriesQuery(
                listCategoryParams
              ).unwrap();
              if (newCategoryList?.status !== STATUS_CODES.OK) {
                return;
              }
              const newRowId = uuidv4();
              const data = (newCategoryList?.data?.data ?? []).map(item => ({
                rowId: item.clinic_reference_category_id.toString(),
                isInit: false,
                name: item.name,
                references: item.references.map(ref => ref.reference_id),
                order: item.order,
                isDraggable: true,
              }));
              const placeHolderData = {
                rowId: newRowId,
                isInit: true,
                name: '',
                references: [],
                order: 0,
                isDraggable: false,
              };
              setCategoryDataSource([...data, placeHolderData]);

              setSelectedKeys(prev =>
                [...prev.slice(0, -1), newRowId].filter(value => value != record.rowId)
              );
              const newInitValue = { ...initialValue };
              delete newInitValue[record.rowId];
              newInitValue[newRowId] = {
                name: '',
                references: [],
              };
              resetInitial(newInitValue);
              setCurrent(prev => {
                const newCurrentChanges = { ...prev };
                delete newCurrentChanges[record.rowId];
                return newCurrentChanges;
              });
            }}
            style={{ width: '120px' }}
          >
            続行する
          </Button>,
        ],
        width: '464px',
      });
    },
    [
      deleteCategoryMutation,
      hide,
      initialValue,
      lazyGetMasterCategoriesQuery,
      listCategoryParams,
      resetInitial,
      setCurrent,
      show,
    ]
  );
  const handleChangeOrder = (payload: { category_id: number; order: number }) => {
    changeOrderCategoryMutation(payload);
  };
  const scrollToRow = (rowId: string) => {
    setTimeout(() => {
      document
        .querySelector(`.scroll-row_category_${rowId}`)
        ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 1);
  };
  const handleAddCategoryModal = () => {
    scrollToRow(categoryDataSource[categoryDataSource.length - 1].rowId!);
  };

  const referencesOptions = useMemo(
    () =>
      (masterReferencesData ?? []).map(item => ({
        value: item.reference_id,
        label: item.name,
      })),
    [masterReferencesData]
  );
  const getReferenceList = (record: CagoryFormItem) => {
    const references = masterReferencesData
      ?.filter(item => record.references.includes(item.reference_id))
      .map(item => item.name);
    return references?.join(COMMA_SEPARATED) ?? '';
  };
  const columns: CustomColumnType<CagoryFormItem>[] = [
    {
      title: '表示順',
      dataIndex: 'order',
      key: 'order',
      align: 'center',
      width: 76,
      render: (value, record) => {
        return record.order ? record.order : '';
      },
      sortable: true,
      sortKey: 'order',
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
          },
        };
      },
    },
    {
      title: 'カテゴリー名称',
      dataIndex: 'name',
      key: 'name',
      align: 'left',
      width: 300,
      editable: {
        type: 'input',
        maxLength: 100,
        placeholder: PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT,
        rules: [{ required: true, whitespace: true, message: '' }],
        style: {
          // width: '316px',
          height: '40px',
        },
      },
      render: (value, record) => {
        return <RenderWithTooltip text={record.name} />;
      },
      sortable: true,
      sortKey: 'name',
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
          },
        };
      },
    },
    {
      title: '項目名',
      dataIndex: 'references',
      key: 'references',
      align: 'left',
      width: 300,
      editable: {
        type: 'select',
        options: referencesOptions,
        mode: 'multiple',
        showSearch: true,
        placeholder: PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN,
        style: {
          height: '40px',
          // width: '100px',
        },
        rules: [
          {
            required: true,
            validator: (_, value) => {
              if (!value || (Array.isArray(value) && value.length === 0) || value === '') {
                return Promise.reject(new Error(''));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      render: (value, record) => {
        return <RenderWithTooltip text={getReferenceList(record)} />;
      },
      sortable: true,
      sortKey: 'reference_name',
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
          },
        };
      },
    },
    {
      title: '',
      key: 'actions',
      align: 'center',
      width: 70,
      fixed: 'right',
      render: (_: any, record: CagoryFormItem) => {
        return (
          <MasterActionBtn
            key={record.rowId}
            isEdit={isEditable(record)}
            record={record}
            handleCancelRecord={() => {
              handleCancelRecord(record);
            }}
            handleConfirmEdit={() => {
              handleConfirmEdit(record);
            }}
            handleOpenEdit={() => {
              handleOpenEdit(record);
            }}
            handleDeleteRecord={() => {
              handleDeleteRecord(record);
            }}
          />
        );
      },
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
          },
        };
      },
    },
  ];
  useEffect(() => {
    const formValues = categoryDataSource.reduce((acc, item) => {
      acc[item.rowId!] = item;
      return acc;
    }, {} as Record<string, object>);
    form.setFieldsValue(formValues);
  }, [form, categoryDataSource]);

  const handleValuesChange = (changedValues: Record<string, CagoryFormItem>) => {
    Object.entries(changedValues).forEach(([rowId, changedFields]) => {
      Object.keys(changedFields).forEach(field => {
        form.setFields([{ name: [rowId, field as any], errors: [] }]);
      });
    });
  };
  const handlePageChange = (newPage: number) => {
    setListCategoryParams(prev => ({ ...prev, page: newPage }));
  };
  const handleSortChange = (sortData: { order_by: string; order_type: ESort }[]) => {
    const newSort = sortData.length > 0 ? sortData[0] : null;

    if (!newSort || !newSort.order_type) {
      setListCategoryParams(prev => ({ ...prev, order_by: undefined, order_type: undefined }));
    } else {
      setListCategoryParams(prev => ({
        ...prev,
        order_by: newSort.order_by,
        order_type: newSort.order_type,
        page: 1,
      }));
    }
  };

  const isLoading =
    isFetchingCommon ||
    isFetchingMasterCategoriesDataLazy ||
    isLoadingUpdateMasterCategory ||
    isLoadingCreateMasterCategory ||
    isLoadingChangeOrderCategory ||
    isLoadingDeleteCategory;

  return (
    <>
      <Flex
        justify="space-between"
        align="center"
        className={clsx('py-6 px-6', styles.master_title)}
      >
        <ModalTitle title={'カテゴリー作成'} />
        <Button
          customType={ButtonType.PRIMARY}
          onClick={handleAddCategoryModal}
          customSize="sm"
          style={{ width: '112px' }}
        >
          <Icon name="plus" />
          新規作成
        </Button>
      </Flex>
      <Form
        form={form}
        className={clsx('pb-5', styles.category_modal_table)}
        onValuesChange={(changedValues, allValues) => {
          handleValuesChange(changedValues);
          handleUnsavedValueChange(changedValues, allValues);
        }}
      >
        <EditableTable
          isDnD
          rowKey={record => record.rowId!}
          columns={columns}
          editingKeys={selectedKeys}
          pagination={
            (masterCategoriesData?.data?.total ?? 0) > PAGINATION_THRESHOLD
              ? {
                  current: masterCategoriesData?.data?.current_page ?? 1,
                  pageSize: masterCategoriesData?.data?.per_page ?? PAGINATION_THRESHOLD,
                  showSizeChanger: false,
                  total: masterCategoriesData?.data?.total ?? 0,
                  onChange: handlePageChange,
                }
              : false
          }
          onSortChange={handleSortChange}
          dataSource={categoryDataSource}
          scroll={{ y: 450 }}
          loading={isLoading}
          isMargin
          isNonAdjustable
          onRowOrderChange={value => {
            setCategoryDataSource(value.newData);
            handleChangeOrder({
              category_id: Number(value.positionChange.olderPositionData.rowId),
              order: value.positionChange.newPositionData.order,
            });
          }}
          rowClassName={record => `scroll-row_category_${record.rowId}`}
          canDragToEnd={false}
        />
      </Form>
    </>
  );
}
