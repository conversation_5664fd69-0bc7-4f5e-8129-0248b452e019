import { Select, SelectProps } from 'antd';
import clsx from 'clsx';
import { forwardRef } from 'react';
import styles from './styles/AppSelect.module.scss';
import './styles/AppSelect.scss';
import Icon from '../Icon';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface AppSelectProps extends SelectProps {}

export const AppSelect = forwardRef<HTMLSelectElement, AppSelectProps>(
  (
    { placeholder = 'Select', size = 'middle', status = '', className, ...props }: AppSelectProps,
    ref
  ) => {
    const classNames = clsx(className, styles.appSelect, styles[size]);

    return (
      <Select
        ref={() => ref}
        placeholder={placeholder}
        size={size}
        status={status}
        className={classNames}
        suffixIcon={<Icon name="downFilled" />}
        {...props}
      />
    );
  }
);

AppSelect.displayName = 'AppSelect';
