import { Select, SelectProps } from 'antd';
import clsx from 'clsx';
import { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import styles from './styles/AppSelect.module.scss';
import './styles/AppSelect.scss';
import Icon from '../Icon';
import { mergeRefs } from 'react-merge-refs';
import { useInView } from 'react-intersection-observer';
export const gray_500 = '#667085';
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface AppSelectProps extends SelectProps {}

export const AppSelect = forwardRef<HTMLSelectElement, AppSelectProps>(
  (
    {
      placeholder = 'Select',
      size = 'middle',
      status = '',
      className,
      allowClear = true,
      ...props
    }: AppSelectProps,
    ref
  ) => {
    const internalRef = useRef<HTMLElement>(null);
    const [isHovered, setIsHovered] = useState(false);
    const { ref: observerRef, inView } = useInView({
      threshold: 1,
      triggerOnce: false,
    });
    const classNames = clsx(className, styles.appSelect, styles[size]);
    const hasValue =
      props.value !== undefined &&
      props.value !== null &&
      (Array.isArray(props.value) ? props.value.length > 0 : true);

    const triggerBlur = useCallback(() => {
      if (internalRef.current) {
        internalRef.current.blur();
      }
    }, []);
    useEffect(() => {
      if (!inView) {
        triggerBlur();
      }
    }, [inView, triggerBlur]);
    return (
      <div ref={observerRef}>
        <Select
          ref={mergeRefs([ref, internalRef]) as any}
          placeholder={placeholder}
          size={size}
          status={status}
          className={classNames}
          {...props}
          suffixIcon={
            isHovered && allowClear && !props.disabled && hasValue ? (
              false
            ) : (
              <div style={{ opacity: props.disabled ? '50%' : '100%', color: gray_500 }}>
                {props?.suffixIcon ?? <Icon name="downFilled" width={20} height={20} />}
              </div>
            )
          }
          allowClear={allowClear}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        />
      </div>
    );
  }
);

AppSelect.displayName = 'AppSelect';
