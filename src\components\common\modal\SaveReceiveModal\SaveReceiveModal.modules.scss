.save-receive-modal {
  .content-modal {
    margin: 24px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .information-block {
      padding: 16px 12px;
      border: 1px solid $gray-200;
      border-radius: 8px;
    }
  }
}
@media screen and (max-width: 800px) {
  .save-receive-modal {
    height: 100%;
  }
}
.paid-item {
  display: flex;
  align-items: center;
  .paid-btn-action {
    margin-left: 16px !important;

    button {
      display: flex !important;
      align-items: center !important;
      border: none !important;
      padding-left: 12px !important;
      padding-right: 12px !important;
      color: var(--brand-700) !important;
      background-color: var(--brand-100) !important;
      font-size: 12px !important;
      height: 22px !important;

      &:hover {
        background-color: var(--brand-300) !important;
      }

      &:not([disabled]):focus-within {
        border: none !important;
      }
      &:not([disabled]):focus {
        border: none !important;
      }
    }
  }
}

.course-wrapper {
  overflow-x: auto;
  // min-height: 200px;
  // max-height: 200px;
  padding-bottom: 5px;

  .tree-checkbox {
    flex: 1;
  }
  .tree-checkbox:first-child {
    padding-right: 16px;
    border-right: 1px solid $gray-200;
  }
  .tree-checkbox:nth-child(2) {
    padding: 0 16px;
  }
  .tree-checkbox:last-child {
    padding-left: 16px;
    border-left: 1px solid $gray-200;
  }
}

@media screen and (max-width: 800px) {
  .course-wrapper {
    flex-direction: column;
    height: 100%;
    min-height: unset;
    max-height: unset;
    .tree-checkbox {
      padding: unset !important;
      border-left: none !important;
      border-right: none !important;
    }
  }
}
.required_icon {
  margin-left: 2px;
  color: $error-600 !important;
}
