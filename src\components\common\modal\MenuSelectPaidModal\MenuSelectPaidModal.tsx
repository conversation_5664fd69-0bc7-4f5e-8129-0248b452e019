import Button from '@/components/common/core/Button';
import { AppModal } from '@/components/common/core/Modal';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import { useScreen } from '@/hooks/useScreen';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { ButtonType } from '@/types/enum';
import { Option } from '@/types/interface';
import { Empty, Flex, ModalProps } from 'antd';
import { useEffect, useRef, useState } from 'react';
import Icon from '../../core/Icon';
import './MenuSelectPaidModal.modules.scss';
import PaidMenuItem from './PaidMenuItem';

interface Props extends ModalProps {
  isModalOpen: boolean;
  listPaid?: Option<string>[];
  selectedListPaid: string[];
  onCancel?: () => void;
  onConfirm?: (paidMenu: Option<string>[]) => void;
  paidMenu: Option<string>[];
}

const AppMenuSelectPaidModal = ({
  isModalOpen,
  onCancel,
  onConfirm,
  selectedListPaid,
  paidMenu = [],
  ...props
}: Props) => {
  const initialRef = useRef<{ list: string[] }>({ list: selectedListPaid });
  const { screen } = useScreen();
  const { handleValuesChange, confirmLeave, submitHandler, resetInitial } = useUnsavedWarning(
    initialRef.current
  );

  const [listPaidSelect, setListPaidSelect] = useState<Option<string>[]>(
    paidMenu.filter(item => selectedListPaid.includes(item.value))
  );

  useEffect(() => {
    if (isModalOpen) {
      const fresh = paidMenu.filter(o => selectedListPaid.includes(o.value));
      setListPaidSelect(fresh);

      initialRef.current = { list: selectedListPaid };
      resetInitial();
    }
  }, [isModalOpen]);

  const handleCancel = () => {
    const confirm = confirmLeave();
    if (confirm) {
      onCancel?.();
    }
  };

  const handleClickMenuItem = (opt: Option<string>) => {
    setListPaidSelect(prev => {
      const next = prev.some(i => i.value === opt.value)
        ? prev.filter(i => i.value !== opt.value)
        : [...prev, opt];

      handleValuesChange({}, { list: next.map(i => i.value) });
      return next;
    });
  };

  return (
    <AppModal
      className="menu-select-paid-modal"
      destroyOnClose
      open={isModalOpen}
      onCancel={handleCancel}
      width={800}
      isPadding={false}
      {...props}
    >
      <ModalTitle title="自費メニュー" className="pt-5 px-6" />
      {paidMenu?.length === 0 && (
        <Empty
          style={{
            height: '300px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '0 16px 20px 16px',
          }}
          image={<Icon name="emptyTable" native />}
          description={'データが登録されていません。'}
        />
      )}

      <div className="paid-menu px-6">
        {paidMenu?.map((paid: { value: string; label: string }, index: number) => (
          <PaidMenuItem
            key={index}
            activeMenu={
              !!(listPaidSelect ?? []).find((item: Option<string>) => item.value === paid.value)
            }
            paid={paid}
            onClick={handleClickMenuItem}
          />
        ))}
      </div>
      {paidMenu?.length > 0 && (
        <Flex gap="12px" className="pb-5 px-6" justify={screen === 'desktop' ? 'end' : 'center'}>
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            onClick={handleCancel}
            className="btn-modal-width"
            customSize="lg"
          >
            キャンセル
          </Button>
          <Button
            customType={ButtonType.PRIMARY}
            htmlType="submit"
            className="btn-modal-width"
            onClick={() => {
              submitHandler();
              onConfirm?.(listPaidSelect);
            }}
            disabled={listPaidSelect.length === 0}
            customSize="lg"
          >
            登録
          </Button>
        </Flex>
      )}
    </AppModal>
  );
};

export default AppMenuSelectPaidModal;
