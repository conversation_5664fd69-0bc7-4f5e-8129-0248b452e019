import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { AppModal } from '@/components/common/core/Modal';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import { useRouterPath } from '@/hooks/useRouterPath';
import { DATE_FORMATS, routerPaths } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import styles from './KaruteInputUpperSection.module.scss';
import { useKaruteInput } from '../provider';
import { isNil } from 'lodash';
import nl2br from 'react-nl2br';
const MAX_LINES = 1;

export function KaruteInputHeader() {
  const { getPath } = useRouterPath();
  const contentRef = useRef<HTMLDivElement>(null);

  const [isOverflow, setIsOverflow] = useState(false);
  useEffect(() => {
    const el = contentRef.current;
    if (el) {
      const computedStyle = window.getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 16;
      const lines = Math.round(el.scrollHeight / lineHeight);
      setIsOverflow(lines > MAX_LINES);
    }
  }, [contentRef.current?.clientWidth]);
  const [showPatientMemo, setShowPatientMemo] = useState(false);
  const { karuteDetailData } = useKaruteInput();

  const handleOpenNewPatientTab = () => {
    const patientUpdatePath = getPath(
      routerPaths.patientManagement.patientUpdate(karuteDetailData?.patient_id)
    );
    window.open(patientUpdatePath, '_blank', 'noopener,noreferrer');
  };
  const renderPatientName = (
    <p className={styles.header_clamp} ref={contentRef}>
      <span className={`${styles.headerLabel} fs14-regular`}>氏名:</span>
      <span className={`${styles.headerValue} fs14-medium`}>
        {karuteDetailData?.patient_name}
        {karuteDetailData?.patient_kana && `（${karuteDetailData?.patient_kana}）`}
      </span>
    </p>
  );
  return (
    <>
      <div className={styles.mondaiHeader}>
        <div className={styles.headerItem}>
          {isOverflow && (
            <Tooltip
              title={
                <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                  {karuteDetailData?.patient_name}
                  {karuteDetailData?.patient_kana && `（${karuteDetailData?.patient_kana}）`}
                </div>
              }
              arrow={{ pointAtCenter: true }}
              placement="top"
              trigger={['hover', 'click']}
              color="#101828"
              getPopupContainer={() => document.body}
              styles={{
                root: {
                  maxWidth: '800px',
                  width: 'fit-content',
                },
                body: {
                  maxWidth: '800px',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                },
              }}
            >
              <p style={{ cursor: 'pointer' }}>{renderPatientName}</p>
            </Tooltip>
          )}
          {!isOverflow && renderPatientName}
        </div>
        <div className={styles.headerItem}>
          <p>
            <span className={`${styles.headerLabel} fs14-regular`}>患者番号:</span>
            <span className={`${styles.headerValue} fs14-medium`}>
              {karuteDetailData?.patient_cd}
            </span>
          </p>
        </div>
        <div className={styles.headerItem} style={{ whiteSpace: 'nowrap' }}>
          <p>
            <span className={`${styles.headerLabel} fs14-regular`}>生年月日:</span>
            <span className={`${styles.headerValue} fs14-medium`}>
              {karuteDetailData?.birthday
                ? dayjs(karuteDetailData?.birthday).format(DATE_FORMATS?.DATE)
                : undefined}
            </span>
            <span className={`${styles.headerValue} fs14-medium`}>
              {!isNil(karuteDetailData?.age) && `（${karuteDetailData?.age}歳）`}
            </span>
          </p>
          <Button
            customType={ButtonType.RED_PRIMARY}
            onClick={() => {
              setShowPatientMemo(true);
            }}
            disabled={!karuteDetailData?.patient_cmt}
            customSize="sm"
            className="ml-3"
            style={{ minWidth: '112px' }}
          >
            <Icon name="notePencil" />
            患者メモ
          </Button>
        </div>
      </div>
      <AppModal
        width={600}
        destroyOnClose
        open={showPatientMemo}
        onCancel={() => {
          setShowPatientMemo(false);
        }}
        isPadding={false}
      >
        <ModalTitle title="患者メモ" className="pt-5 px-6" />
        <p className={styles.patientCmt}>
          {/* {karuteDetailData?.patient_cmt} */}
          {nl2br(karuteDetailData?.patient_cmt)}
        </p>
        <Flex justify="end" className="mt-6 px-6 pb-5">
          <Button
            customType={ButtonType.PRIMARY}
            customSize="lg"
            onClick={handleOpenNewPatientTab}
            style={{ width: '150px' }}
          >
            患者詳細・編集
          </Button>
        </Flex>
      </AppModal>
    </>
  );
}
