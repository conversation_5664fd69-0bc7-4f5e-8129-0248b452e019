import useInjuryNameStore from '@/hooks/useInjuryNameStore';
import { Flex } from 'antd';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import { useInjuryName } from '../../context/InjuryNameContext';
import { LeftRightOption, LRType } from '../../type';
import styles from './InjuryNameSelectLeftRight.module.scss';

type SideBlock = {
  side: LeftRightOption;
  title: string;
  options: Array<{
    value: string;
    label: string;
    part_type: number;
    part_id: number;
    lr_type: LRType;
  }>;
};

export const InjuryNameSelectLeftRight: React.FC = () => {
  const { getOptionsBySide } = useInjuryNameStore();
  const { setInjuryNamePayload, injuryNamePayload } = useInjuryName();

  const leftOptions = useMemo(
    () =>
      getOptionsBySide(LeftRightOption.Left).map(o => ({
        ...o,
        label: o.name,
      })),
    [getOptionsBySide]
  );
  const rightOptions = useMemo(
    () =>
      getOptionsBySide(LeftRightOption.Right).map(o => ({
        ...o,
        label: o.name,
      })),
    [getOptionsBySide]
  );

  const blocks: SideBlock[] = [
    { side: LeftRightOption.Left, title: '名称（左）', options: leftOptions },
    { side: LeftRightOption.Right, title: '名称（右）', options: rightOptions },
  ];

  const [selectedValue, setSelectedValue] = useState<string | null>(null);

  useEffect(() => {
    if (!selectedValue) return;
    const [pt, pid, lr] = selectedValue.split('_').map(x => Number(x));
    if (
      injuryNamePayload.part_type !== pt ||
      injuryNamePayload.part_id !== pid ||
      injuryNamePayload.lr_type !== (lr === 0 ? null : lr)
    ) {
      setSelectedValue(null);
    }
  }, [injuryNamePayload.part_type, injuryNamePayload.part_id, injuryNamePayload.lr_type]);

  const handleSelect = (opt: (typeof blocks)[0]['options'][0]) => {
    setSelectedValue(opt.value);
    const [pt, pid, lr] = opt.value.split('_').map(x => Number(x));
    setInjuryNamePayload(prev => ({
      ...prev,
      part_type: pt,
      part_id: pid,
      lr_type: lr === 0 ? null : lr,
      name: opt.label,
    }));
  };

  return (
    <Flex className={styles.leftRightWrapper}>
      {blocks.map(block => (
        <div key={block.side} className={styles.block}>
          <div className={clsx(styles.title, 'fs14-bold')}>{block.title}</div>
          <div className={styles.list}>
            {block.options.map(opt => (
              <div
                key={opt.value}
                className={clsx(
                  styles.listItem,
                  selectedValue === opt.value && styles.selected,
                  'fs14-medium'
                )}
                onClick={() => handleSelect(opt)}
              >
                <div>{opt.label}</div>
                {selectedValue === opt.value && (
                  <div>
                    <span className={styles.checkMark}>✓</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </Flex>
  );
};
