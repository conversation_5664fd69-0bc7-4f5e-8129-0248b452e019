import { AppModal } from '@/components/common/core/Modal';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import { useAppSelector } from '@/config/redux/store';
import { useGetBookingsQuery } from '@/store/booking/api';
import { DATE_FORMATS } from '@/types/constants';
import { Calendar, Flex } from 'antd';
import dayjs from 'dayjs';
import { useMemo, useRef, useState } from 'react';
import styles from './BookingNextTime.module.scss';
import { BookingNextTimeForm } from './BookingNextTimeForm';
import { BookingNextTimeTable } from './BookingNextTimeTable';
import { CalendarHeader } from './CalendarHeader';
import clsx from 'clsx';

type Props = {
  handleCancel: () => void;
};

export function BookingNextTimeModal({ handleCancel }: Props) {
  const patientInfo = useAppSelector(state => state.karute.karute.details.patientInfo);
  const { data, isFetching } = useGetBookingsQuery(Number(patientInfo?.patient_id), {
    skip: !patientInfo?.patient_id,
  });

  const [dateValue, setDateValue] = useState(dayjs());

  const formRef = useRef<{ confirmLeave: () => boolean }>(null);

  const onModalCancel = () => {
    if (!formRef.current || formRef.current.confirmLeave()) {
      handleCancel();
    }
  };

  const bookedData = useMemo(() => {
    return data?.data?.data?.map(item => dayjs(item.booking_from).format('YYYY-MM-DD')) ?? [];
  }, [data]);

  return (
    <AppModal
      width={800}
      destroyOnClose
      open
      onCancel={onModalCancel}
      className={styles.create_booking}
    >
      <Flex vertical>
        <ModalTitle title="次回予約の登録" />
        <BookingNextTimeTable isFetching={isFetching} data={data?.data?.data || []} />
        <Flex className={clsx('px-6', styles.booking_container)}>
          <Calendar
            fullscreen={false}
            className={styles.booking_calendar}
            value={dateValue}
            onSelect={() => {}}
            headerRender={({ value }) => {
              const handleFirst = () => {
                setDateValue(value.clone().subtract(1, 'year'));
              };
              const handlePrev = () => {
                setDateValue(value.clone().subtract(1, 'month'));
              };
              const handleNext = () => {
                setDateValue(value.clone().add(1, 'month'));
              };
              const handleLast = () => {
                setDateValue(value.clone().add(1, 'year'));
              };
              return (
                <div className={styles.calendarHeader}>
                  <CalendarHeader
                    onFirst={handleFirst}
                    onPrev={handlePrev}
                    onNext={handleNext}
                    onLast={handleLast}
                  >
                    <p className={styles.calendarHeaderLabel}>
                      {dayjs(value).isValid()
                        ? value.format(DATE_FORMATS.JAPAN_DATE_WITH_OUT_DAY)
                        : ''}
                    </p>
                  </CalendarHeader>
                </div>
              );
            }}
            fullCellRender={date => {
              const isToday = date.isSame(dayjs(), 'day');
              const isBooked = bookedData.includes(date.format('YYYY-MM-DD'));
              const className =
                isBooked && isToday
                  ? styles.isBookedAndCurrent
                  : isBooked
                  ? styles.isBooking
                  : isToday
                  ? styles.isToday
                  : '';
              return (
                <div
                  className={clsx(className, styles.dayInCalendar, {
                    [styles.notInMonth]: date.month() !== dateValue.month(),
                  })}
                >
                  {date.date()}
                </div>
              );
            }}
          />
          <BookingNextTimeForm ref={formRef} handleCancel={handleCancel} />
        </Flex>
      </Flex>
    </AppModal>
  );
}
