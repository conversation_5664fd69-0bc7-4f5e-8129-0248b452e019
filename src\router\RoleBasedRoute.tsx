import React from 'react';
import { Navigate, useParams } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import { ROLE } from '@/types/enum';

interface RoleBasedRouteProps {
  roles: (ROLE | null)[];
  children: React.ReactElement;
}

export default function RoleBasedRoute({ roles, children }: RoleBasedRouteProps) {
  const { isAuthenticated, expiredAt, role } = useAuth();
  const isExpired = expiredAt && expiredAt < Date.now();
  const hasAuth = isAuthenticated && !isExpired;
  const { clinicCd } = useParams<{ clinicCd: string }>();

  if (!hasAuth) {
    return <Navigate to="/login" replace />;
  }

  if (!role || !roles.includes(role)) {
    return <Navigate to={`/${clinicCd}/403-page-not-authorized`} replace />;
  }

  return children;
}
