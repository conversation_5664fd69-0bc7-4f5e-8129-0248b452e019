import React, { createContext, useContext, useState, useCallback } from 'react';
import ModalConfirm from '@/components/common/core/ModalConfirm';
import { ModalProps } from 'antd';

type ConfirmModalOptions = ModalProps & {
  title: React.ReactNode;
  content: React.ReactNode;
  buttons: React.ReactNode[];
  onClose?: () => void;
};

type ConfirmModalContextType = {
  show: (options: ConfirmModalOptions) => void;
  hide: () => void;
};

const ConfirmModalContext = createContext<ConfirmModalContextType | null>(null);

export const useModalConfirm = () => {
  const context = useContext(ConfirmModalContext);
  if (!context) throw new Error('useGlobalConfirm must be used within ConfirmModalProvider');
  return context;
};

export default function ConfirmModalProvider({ children }: { children: React.ReactNode }) {
  const [visible, setVisible] = useState(false);
  const [options, setOptions] = useState<ConfirmModalOptions | null>(null);

  const show = useCallback((opts: ConfirmModalOptions) => {
    setOptions(opts);
    setVisible(true);
  }, []);

  const hide = useCallback(() => {
    setVisible(false);
    if (options?.onClose) {
      options.onClose();
    }
  }, [options]);

  return (
    <ConfirmModalContext.Provider value={{ show, hide }}>
      {children}
      {options && visible && (
        <ModalConfirm open={visible} onClose={hide} footerButtons={options.buttons} {...options} />
      )}
    </ConfirmModalContext.Provider>
  );
}
