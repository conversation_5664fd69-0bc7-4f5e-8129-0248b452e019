import ModalConfirm from '@/components/common/core/ModalConfirm';
import { ModalProps } from 'antd';
import React, { createContext, useCallback, useContext, useState } from 'react';

type ConfirmModalOptions = ModalProps & {
  title: React.ReactNode;
  content: React.ReactNode;
  buttons: React.ReactNode[];
  onClose?: () => void;
  onCancel?: () => void;
  height?: string | number;
  warning?: boolean;
};

type ConfirmModalContextType = {
  show: (options: ConfirmModalOptions) => void;
  hide: () => void;
  update: (patch: Partial<ConfirmModalOptions>) => void;
};

const ConfirmModalContext = createContext<ConfirmModalContextType | null>(null);

export const useModalConfirm = () => {
  const context = useContext(ConfirmModalContext);
  if (!context) throw new Error('useGlobalConfirm must be used within ConfirmModalProvider');
  return context;
};

export default function ConfirmModalProvider({ children }: { children: React.ReactNode }) {
  const [visible, setVisible] = useState(false);
  const [options, setOptions] = useState<ConfirmModalOptions | null>(null);

  const show = useCallback((opts: ConfirmModalOptions) => {
    setOptions(opts);
    setVisible(true);
  }, []);

  const hide = useCallback(() => {
    setVisible(false);
    if (options?.onClose) {
      options.onClose();
    }
  }, [options]);

  const update = useCallback((patch: Partial<ConfirmModalOptions>) => {
    setOptions(prev => (prev ? { ...prev, ...patch } : prev));
  }, []);

  return (
    <ConfirmModalContext.Provider value={{ show, hide, update }}>
      {children}
      {options && visible && (
        <ModalConfirm
          height={options.height ?? 'auto'}
          open={visible}
          onClose={hide}
          warning={options.warning}
          footerButtons={options.buttons}
          {...options}
        />
      )}
    </ConfirmModalContext.Provider>
  );
}
