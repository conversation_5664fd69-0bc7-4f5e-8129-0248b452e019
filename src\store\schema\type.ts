export type ImageableType = 'database' | 'keika' | null;

export enum SchemaImageType {
  TYPE_DEFAULT = 1,
  TYPE_CUSTOM = 2,
  TYPE_ONE_USE = 3, // nhat.nguyen: delete this type
}

export interface Category {
  category_schema_id: number;
  name: string;
  status: number;
}

export interface SchemaImage {
  id: string; //
  schema_image_id: number | string | null;
  category_schema_id?: number;
  imageable_type?: ImageableType;
  type?: SchemaImageType;
  favorite?: boolean;
  url: string; //url
  file?: File | Blob; //url
  isTemp?: boolean;
  disease_base_image_id?: number;
  keika_image_id?: number;
  isHistory?: boolean;
  created_at?: string;
  upload_by?:number;
}

export interface DrawnImage {
  id: string;
  schema_image_id: number | null;
  type: number;
  url: string;
}

export enum SchemaType {
  DATABASE = 'database',
  KEIKA = 'keika',
}

export enum DiseaseType {
  O = '1',
  T = '2',
}

export interface KeiKaImages {
  isTemp?: boolean;
  schema_image_id?: number | string | null;
  type?: DiseaseType;
  file: File | Blob;
}
