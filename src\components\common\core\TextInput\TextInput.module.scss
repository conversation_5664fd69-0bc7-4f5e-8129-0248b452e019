.wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;

  :global(input:-webkit-autofill) {
    background-color: transparent !important;
  }
  .inputWrapper {
    :global(span[class*='ant-input']:first-child) {
      border-color: $gray-300;
      border-radius: 8px;
      border-width: 1px;
      padding: 10px 12px;
      color: $gray-500;
      transition: border-color 0.2s ease, color 0.2s ease;
      box-shadow: none;
      outline: none;

      // Add background color when autofill
      &:has(:global(input:-webkit-autofill)) {
        background-color: #e6f0fa;
        border-radius: 8px;
        padding: 8px;
      }
    }

    &.filled:not(.focused):not(.hovered) :global(span[class*='ant-input']:first-child) {
      border-color: $gray-300;
      color: $gray-800;
    }

    &.hovered :global(span[class*='ant-input']:first-child),
    &.focused :global(span[class*='ant-input']:first-child) {
      border-color: $brand-800;
      color: $gray-900;
    }

    &.disabled :global(span[class*='ant-input']:first-child) {
      background-color: $gray-50;
      color: $gray-500 !important;
      cursor: default;
      input {
        cursor: default;
      }
    }
  }

  &.error {
    .inputWrapper {
      :global(span[class*='ant-input']:first-child) {
        border-color: $error-400;
      }

      &.filled:not(.focused):not(.hovered) :global(span[class*='ant-input']:first-child) {
        border-color: $error-400;
      }

      &.hovered :global(span[class*='ant-input']:first-child),
      &.focused :global(span[class*='ant-input']:first-child) {
        border-color: $error-600;
      }

      &.disabled :global(span[class*='ant-input']:first-child) {
        background-color: $gray-50;
        cursor: default;
        input {
          cursor: default;
        }
      }
    }
    .message {
      color: $error-600 !important;
    }
  }
  .label {
    width: fit-content;
    cursor: pointer;
    color: $gray-500;
  }
  .message {
    color: $gray-600 !important;
  }
  .iconWrapper {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .icon {
    display: inline-flex;
    align-items: center;
  }
  .passwordIcon {
    cursor: pointer;
    transition: transform 0.3s;
    &:hover {
      transform: scale(1.1);
    }
  }
}
