.mondaiHeader {
  display: flex;
  background-color: var(--white);
  padding: 12px 24px;
  margin-top: 8px;
  border-radius: 8px;
  justify-content: space-between;
  overflow-x: auto;
  .headerItem {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;


    &:not(:last-child) {
      border-right: 1px solid $gray-200;
    }
    &:not(:first-child) {
      margin-left: 40px;
    }
  }

  .headerLabel {
    color: $gray-500;
    margin-right: 8px;
    white-space: nowrap;
  }

  .headerValue {
    color: $gray-800;
  }
  .header_clamp {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.mondaiSectionTable {
  margin-top: 8px;
  border-radius: 8px;
  overflow: auto;
  background-color: var(--white);
  height: 100%;
  .header {
    border-radius: 8px 8px 0 0;
    background-color: var(--white);
    padding: 12px;
    border-bottom: 1px solid $gray-200;
    overflow: auto !important;
  }
  .mainTable,
  .subTable {
    :global(.ant-table-cell) {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
    }
  }
  .mainTable {
    :global(.ant-table-row) {
      text-align: center;
    }

    :global(.ant-table-expanded-row-fixed) {
      height: auto !important;
      background: var(--white) !important;
    }
    :global(.ant-table-expanded-row) {
      :global(> .ant-table-cell) {
        height: auto !important;
        background: var(--white) !important;
        padding-left: unset !important;
        padding-right: unset !important;
      }
    }

    :global(.ant-table-cell .ant-table-row-expand-icon-cell) {
      width: 50px !important;
    }
  }
  .subTable {
    :global(.ant-table-content) {
      > table {
        table-layout: fixed;
      }
    }
    :global(.ant-table-row) {
      background-color: var(--gray-50);
    }
  }
}

.mondaiType {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  > p:nth-child(2) {
    padding-left: 16px;
    border-left: 1px solid var(--gray-200);
  }
}
.mondaiStatus {
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
}
.active {
  width: 72px;
  height: 23px;
  border-radius: 16px;
  color: $success-700;
  background-color: #eef4e2;
  padding: 3px 16px;
}
.inactive {
  width: 72px;
  height: 23px;
  border-radius: 16px;
  color: $error-600;
  padding: 3px 16px;
  background-color: $error-100;
}

.karuteInputModal {
}

.title {
  color: $gray-800;
  text-wrap: nowrap;
}

.patientCmt {
  color: $gray-500;
  max-height: 450px;
  overflow-y: auto;
  padding: 0 24px 0 24px;
}
