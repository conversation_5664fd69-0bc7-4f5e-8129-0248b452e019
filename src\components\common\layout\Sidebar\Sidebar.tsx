import React, { useMemo, useState } from 'react';
import { Layout, Menu, MenuProps } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import styles from './Sidebar.module.scss';
import Icon from '../../core/Icon';
import { iconMap } from '../../core/Icon';
import clsx from 'clsx';
import useAuth from '@/hooks/useAuth';
import { ROLE } from '@/types/enum';
import { useRouterPath } from '@/hooks/useRouterPath';
import { routerPaths } from '@/types/constants';

const { Sider } = Layout;
const brand700 = '#427991';
type MenuItem = {
  key: string;
  title: string;
  icon: string;
  path: string;
  role: (ROLE | null)[];
  children: MenuItem[];
};
const Sidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { getPath } = useRouterPath();
  const navigate = useNavigate();
  const location = useLocation();
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      title: 'ダッシュボード',
      icon: 'home',
      path: getPath(routerPaths.dashboard),
      role: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
      children: [],
    },
    {
      key: 'list_karute',
      title: 'カルテ管理',
      icon: 'folder',
      path: getPath(routerPaths.karuteManagement.karuteList),
      role: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
      children: [],
    },
    {
      key: 'list_patient',
      title: '患者管理',
      icon: 'paper',
      path: getPath(routerPaths.patientManagement.patientList),
      role: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
      children: [],
    },
    {
      key: 'user_management',
      title: 'ユーザー管理',
      icon: 'group',
      path: getPath(routerPaths.userManagement.userList),
      role: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN],
      children: [],
    },
  ];

  const toggle = () => {
    setCollapsed(prev => !prev);
  };

  const { role: userRole } = useAuth();

  const buildMenu = (items = menuItems): MenuProps['items'] => {
    const isValidIcon = (name: string): name is keyof typeof iconMap => {
      return name in iconMap;
    };
    return items
      .filter(item => {
        const normalizedRoles = item.role;
        return normalizedRoles?.includes(userRole);
      })
      .map(item => ({
        key: item.path,
        icon: isValidIcon(item.icon) ? (
          <Icon name={item.icon} height={20} width={20} color={brand700} />
        ) : null,
        label: <span className={clsx(styles.menuLabel, 'fs14-medium')}>{item.title}</span>,
        children: item.children?.length ? buildMenu(item.children) : undefined,
      }));
  };
  const menuItemsRender = useMemo(() => buildMenu(menuItems), [userRole]);
  return (
    <Sider
      width={220}
      collapsedWidth={76}
      collapsible
      collapsed={collapsed}
      trigger={null}
      className={styles.sidebar}
    >
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        onClick={({ key }) => navigate(key)}
        items={menuItemsRender}
      />

      <div className={styles.toggleBtn} onClick={toggle}>
        <Icon name={collapsed ? 'arrowRight' : 'arrowLeft'} />
      </div>
    </Sider>
  );
};

export default Sidebar;
