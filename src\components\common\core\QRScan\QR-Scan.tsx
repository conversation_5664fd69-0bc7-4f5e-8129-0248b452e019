import { NOTICE_COMMON_MESSAGE } from '@/types/constants';
import { showFlashNotice } from '@/utils';
import { Result } from '@zxing/library';
import { useCallback, useEffect, useRef, useState } from 'react';
import { QrReader } from 'react-qr-reader';
import Loading from '../Loading/Loading';
interface QRScanProps {
  callback: (data: any) => void;
  closeModal?: () => void;
}

export const QRScan = ({ callback, closeModal }: QRScanProps) => {
  const [hasResult, setHasResult] = useState<boolean>(false);
  const [isLoading, setLoading] = useState<boolean>(false);
  const isProcessingRef = useRef<boolean>(false);
  const mounting = useRef<boolean>(false);
  const selectedDevice = useCallback(() => {
    navigator.permissions.query({ name: 'camera' as PermissionName }).then(res => {
      if (res.state !== 'granted') {
        if (closeModal) {
          closeModal();
        }
        showFlashNotice({ type: 'error', message: 'カメラへのアクセスが許可されていません。' });
      }
    });
  }, []);

  useEffect(() => {
    if (!mounting.current) {
      mounting.current = true;
      selectedDevice();
    }
  }, []);

  const handleScan = (result: Result | null | undefined) => {
    if (!hasResult && result && !isProcessingRef.current) {
      setLoading(true);
      isProcessingRef.current = true;
      const timer = setTimeout(() => {
        try {
          const based64Data = result
            .getText()
            .trim()
            .replace(/^['"]+|['"]+$/g, '');
          const dataDecoded = window.atob(based64Data);

          const sanitizedData = dataDecoded.trim().replace(/^['"]+|['"]+$/g, '');
          const parsedData = JSON.parse(sanitizedData);

          if (!hasResult) {
            callback({ ...parsedData });
            setHasResult(true);
          } else {
            showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.QR_SCAN_ERROR });
          }
        } catch {
          showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.QR_SCAN_ERROR });
          return;
        } finally {
          clearTimeout(timer);
          setLoading(false);
          isProcessingRef.current = false;
        }
      }, 3000);
    }
  };

  return (
    <div style={{ position: 'relative', width: '100%' }}>
      <p
        style={{
          color: 'var(--brand-900)',
          fontSize: '16px',
          lineHeight: '24px',
          fontWeight: 700,
          marginBottom: '24px',
        }}
      >
        QRコード読み取り
      </p>
      <QrReader
        onResult={result => handleScan(result)}
        scanDelay={2000}
        constraints={{
          facingMode: 'environment',
        }}
        containerStyle={{
          marginLeft: 'auto',
          marginRight: 'auto',
          padding: 'unset',
          width: '100%',
          aspectRatio: '1/1',
        }}
        videoContainerStyle={{
          width: '100%',
          aspectRatio: '1/1',
        }}
        videoStyle={{
          width: '100%',
          objectFit: 'cover',
        }}
      />
      {isLoading && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
          }}
        >
          <Loading />
        </div>
      )}
    </div>
  );
};
