import { courseMap, JIHI_COURSE_NAME } from '@/types/constants';
import { unicodeToKanji } from '../../core/Input/InputNumber';
import { TreeDataNode } from 'antd';
import { useEffect, useState } from 'react';
import { ECourse } from '@/types/enum';
import { useAppSelector } from '@/config/redux/store';

export default function useGetCourseServices() {
  const { courses, isLoading: isFetching } = useAppSelector(state => state.sharedData);
  const [treeServices, setTreeServices] = useState<
    {
      service_id: string;
      treeData: TreeDataNode[];
      jihiCourse: TreeDataNode[];
    }[]
  >([]);

  useEffect(() => {
    if (courses) {
      const allServices = (courses ?? []).map(activeCourse => {
        const jihiCourse: TreeDataNode[] = [];

        const children = activeCourse.data?.map(subCourse => {
          if (subCourse.payment_course_name === ECourse.SELF_PAID) {
            const subSubChildren =
              subCourse.data?.map(subSubCourse => ({
                title: subSubCourse.course_name,
                key: String(subSubCourse.course_id),
              })) || [];

            jihiCourse.push(...subSubChildren);

            return {
              title: JIHI_COURSE_NAME,
              key: subCourse.payment_course_name,
            };
          }

          return {
            title: courseMap[subCourse.payment_course_name as keyof typeof courseMap],
            key: String(subCourse.data[0].course_id),
          };
        });

        return {
          service_id: activeCourse.service_name,
          treeData: [
            {
              title: unicodeToKanji(activeCourse.service_name),
              key: unicodeToKanji(activeCourse.service_name),
              children,
            },
          ],
          jihiCourse,
        };
      });

      setTreeServices(allServices);
    }
  }, [courses]);

  return { treeServices, isFetching };
}
