export type FieldError<T> = {
  [K in keyof T]: string | string[];
};

export interface APIResponse<T> {
  status: number;
  data?: T;
  message: string;
  metadata: {
    // currentPageSize: string;
    // hasNextPage: boolean;
    // hasPreviousPage: boolean;
    limit: string;
    page: string;
    total: number;
    totalCurrentPage: number;
  };
}

export interface ListResponse<T> {
  current_page: number;
  per_page: number;
  from: number;
  to: number;
  total: number;
  total_filtered: number;
  total_items: number;
  last_page: number;
  data: T[] | null;
}
