import { Flex, Tooltip } from 'antd';
import Button from '@/components/common/core/Button';
import { ButtonType } from '@/types/enum';
import styles from './UpsertDatabaseForm.module.scss';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
type LabelRenderProps = {
  label: React.ReactNode;
  required?: boolean;
  mondai_name?: string;
  firstBtn?: {
    label?: string;
    onClick?: () => void;
    disabled?: boolean;
    hidden?: boolean;
  };
  secondBtn?: {
    label?: string;
    onClick?: () => void;
    disabled?: boolean;
    hidden?: boolean;
  };
};
const MAX_LINES = 1;
export default function LabelRender({
  label,
  required = false,
  mondai_name,
  firstBtn,
  secondBtn,
}: LabelRenderProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isOverflow, setIsOverflow] = useState(false);
  // Logic from RenderWithTooltip.tsx
  useEffect(() => {
    const el = contentRef.current;
    if (el) {
      const computedStyle = window.getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 16;
      const lines = Math.round(el.scrollHeight / lineHeight);
      setIsOverflow(lines > MAX_LINES);
    }
  }, [mondai_name]);
  return (
    <Flex
      justify="space-between"
      align="center"
      style={{
        width: '100%',
        marginBottom: (firstBtn && !firstBtn?.hidden) || secondBtn ? '8px' : '6px',
        height: secondBtn ? '30px' : '',
      }}
      className={clsx(styles.database_keika_form_item_label)}
    >
      <div ref={contentRef} className={clsx(styles.item_label, 'fs14-medium')}>
        {!isOverflow && (
          <p>
            {label}
            {required && <span className={styles.required_asterisk}>*</span>}
            {mondai_name}
          </p>
        )}
        {isOverflow && (
          <Tooltip
            title={
              <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{mondai_name}</div>
            }
            arrow={{ pointAtCenter: true }}
            placement="top"
            trigger={['hover', 'click']}
            color="#101828"
            getPopupContainer={() => document.body}
            styles={{
              root: {
                maxWidth: '800px',
                width: 'fit-content',
              },
              body: {
                maxWidth: '800px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              },
            }}
          >
            <p className={styles.toolip_item}>
              {label}
              {required && <span className={styles.required_asterisk}>*</span>}
              {mondai_name}
            </p>
          </Tooltip>
        )}
      </div>

      <Flex gap={8}>
        {firstBtn && !firstBtn?.hidden && (
          <Button
            onClick={firstBtn.onClick}
            customType={ButtonType.SECONDARY_BLUE}
            className={`${styles.action_btn} fs12-bold`}
            customSize="sm"
            disabled={firstBtn.disabled}
          >
            {firstBtn.label}
          </Button>
        )}
        {/* Phase 1: doesn't require */}
        {/* Phase 2:  Continue */}
        {secondBtn && !secondBtn?.hidden && (
          <Button
            onClick={secondBtn.onClick}
            customType={ButtonType.SECONDARY_COLOR}
            className={`${styles.action_btn} fs12-bold`}
            customSize="sm"
            disabled={secondBtn.disabled}
          >
            {secondBtn.label}
          </Button>
        )}
      </Flex>
    </Flex>
  );
}
