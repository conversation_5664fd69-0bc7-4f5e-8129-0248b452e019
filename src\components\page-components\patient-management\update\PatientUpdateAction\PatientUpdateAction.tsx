import Button from '@/components/common/core/Button';
import { useRouterPath } from '@/hooks/useRouterPath';
import { routerPaths } from '@/types/constants/routerPath';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import { useNavigate } from 'react-router-dom';

interface Props {
  formRef: any;
}

export default function PatientUpdateAction({ formRef }: Props) {
  const navigate = useNavigate();
  const { getPath } = useRouterPath();
  const patientListPath = getPath(routerPaths.patientManagement.patientList);

  const handleClickSubmitForm = () => {
    formRef.current?.submit();
  };

  if (!formRef) {
    return <></>;
  }

  return (
    <Flex gap={8}>
      <Button customType={ButtonType.SECONDARY_COLOR} onClick={() => navigate(patientListPath)}>
        キャンセル
      </Button>
      <Button onClick={handleClickSubmitForm}>保存</Button>
      <Button customType={ButtonType.RED_PRIMARY}>患者を削除</Button>
    </Flex>
  );
}
