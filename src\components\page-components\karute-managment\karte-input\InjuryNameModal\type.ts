export enum LRType {
  Left = 2,
  Right = 1,
  None = 0,
}

export enum HumanBodyDiagramImage {
  BruisesSprainsContusions = 'bruises_sprains',
  DislocationFractureIncompleteFracture = 'dislocation_fracture',
}

export const TabOption = [
  {
    key: HumanBodyDiagramImage.BruisesSprainsContusions,
    value: HumanBodyDiagramImage.BruisesSprainsContusions,
    label: '打撲・捻挫・挫傷',
  },
  {
    key: HumanBodyDiagramImage.DislocationFractureIncompleteFracture,
    value: HumanBodyDiagramImage.DislocationFractureIncompleteFracture,
    label: '脱臼・骨折・不全骨折',
  },
];

export enum DiagramType {
  Front = 'front',
  Back = 'back',
}

export const SideOption = [
  {
    name: 'none',
    value: LRType.None,
  },
  {
    name: 'right',
    value: LRType.Right,
  },
  {
    name: 'left',
    value: LRType.Left,
  },
];

export const SideLabelOption = [
  {
    label: '右',
    value: LRType.Right,
  },
  {
    label: '左',
    value: LRType.Left,
  },
];

export interface InjuryNameIdentifier {
  part_type: number | null;
  part_id: number | null;
  lr_type: LRType | null;
  name: string | null;
  name_other: string | null;
}

export interface BlockSelect {
  options: BlockOptionSelect[];
  lr_type: LRType;
}

export interface BlockOptionSelect {
  part_type: number;
  part_id: number;
  lr_type: LRType;
  disp_name: string;
}

export interface PartIdOption {
  part_id: number;
  part_name: string;
  part_name_original: string;
  lr_type: number;
}

export enum LeftRightOption {
  Left = 'left',
  Right = 'right',
}

export const initialInjuryName: InjuryNameIdentifier = {
  part_type: null,
  part_id: null,
  lr_type: null,
  name: null,
  name_other: null,
};

export interface BlockResult {
  blocks: number[];
}
