.fs72-regular {
  font-size: 72px;
  font-weight: 400;
  line-height: 90px;
  letter-spacing: calc(72px * -0.02);
}
.fs72-medium {
  font-size: 72px;
  font-weight: 500;
  line-height: 90px;
  letter-spacing: calc(72px * -0.02);
}
.fs72-bold {
  font-size: 72px;
  font-weight: 700;
  line-height: 90px;
  letter-spacing: calc(72px * -0.02);
}
.fs72-black {
  font-size: 72px;
  font-weight: 900;
  line-height: 90px;
  letter-spacing: calc(72px * -0.02);
}

.fs60-regular {
  font-size: 60px;
  font-weight: 400;
  line-height: 72px;
  letter-spacing: calc(60px * -0.02);
}
.fs60-medium {
  font-size: 60px;
  font-weight: 500;
  line-height: 72px;
  letter-spacing: calc(60px * -0.02);
}
.fs60-bold {
  font-size: 60px;
  font-weight: 700;
  line-height: 72px;
  letter-spacing: calc(60px * -0.02);
}
.fs60-black {
  font-size: 60px;
  font-weight: 900;
  line-height: 72px;
  letter-spacing: calc(60px * -0.02);
}

.fs48-regular {
  font-size: 48px;
  font-weight: 400;
  line-height: 60px;
  letter-spacing: calc(48px * -0.02);
}
.fs48-medium {
  font-size: 48px;
  font-weight: 500;
  line-height: 60px;
  letter-spacing: calc(48px * -0.02);
}
.fs48-bold {
  font-size: 48px;
  font-weight: 700;
  line-height: 60px;
  letter-spacing: calc(48px * -0.02);
}
.fs48-black {
  font-size: 48px;
  font-weight: 900;
  line-height: 60px;
  letter-spacing: calc(48px * -0.02);
}

.fs36-regular {
  font-size: 36px;
  font-weight: 400;
  line-height: 44px;
  letter-spacing: calc(36px * -0.02);
}
.fs36-medium {
  font-size: 36px;
  font-weight: 500;
  line-height: 44px;
  letter-spacing: calc(36px * -0.02);
}
.fs36-bold {
  font-size: 36px;
  font-weight: 700;
  line-height: 44px;
  letter-spacing: calc(36px * -0.02);
}
.fs36-black {
  font-size: 36px;
  font-weight: 900;
  line-height: 44px;
  letter-spacing: calc(36px * -0.02);
}

.fs30-regular {
  font-size: 30px;
  font-weight: 400;
  line-height: 38px;
  letter-spacing: 0;
}
.fs30-medium {
  font-size: 30px;
  font-weight: 500;
  line-height: 38px;
  letter-spacing: 0;
}
.fs30-bold {
  font-size: 30px;
  font-weight: 700;
  line-height: 38px;
  letter-spacing: 0;
}
.fs30-black {
  font-size: 30px;
  font-weight: 900;
  line-height: 38px;
  letter-spacing: 0;
}

.fs24-regular {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0;
}
.fs24-medium {
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: 0;
}
.fs24-bold {
  font-size: 24px;
  font-weight: 700;
  line-height: 32px;
  letter-spacing: 0;
}
.fs24-black {
  font-size: 24px;
  font-weight: 900;
  line-height: 32px;
  letter-spacing: 0;
}

.fs20-regular {
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0;
}
.fs20-medium {
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
  letter-spacing: 0;
}
.fs20-bold {
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
  letter-spacing: 0;
}
.fs20-black {
  font-size: 20px;
  font-weight: 900;
  line-height: 30px;
  letter-spacing: 0;
}

.fs18-regular {
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: 0;
}
.fs18-medium {
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0;
}
.fs18-bold {
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;
  letter-spacing: 0;
}
.fs18-black {
  font-size: 18px;
  font-weight: 900;
  line-height: 28px;
  letter-spacing: 0;
}

.fs16-regular {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0;
}
.fs16-medium {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0;
}
.fs16-bold {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0;
}
.fs16-black {
  font-size: 16px;
  font-weight: 900;
  line-height: 24px;
  letter-spacing: 0;
}

.fs14-regular {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0;
}
.fs14-medium {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0;
}
.fs14-bold {
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0;
}
.fs14-black {
  font-size: 14px;
  font-weight: 900;
  line-height: 20px;
  letter-spacing: 0;
}

.fs12-regular {
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0;
}
.fs12-medium {
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  letter-spacing: 0;
}
.fs12-bold {
  font-size: 12px;
  font-weight: 700;
  line-height: 18px;
  letter-spacing: 0;
}
.fs12-black {
  font-size: 12px;
  font-weight: 900;

  line-height: 18px;
  letter-spacing: 0;
}

$space-unit: 0.25rem;
$max-space: 10;

.text-primary2 {
  color: var(--text-primary2);
}
.text-tertiary {
  color: var(--text-tertiary);
}

@for $i from 0 through $max-space {
  .m-#{$i} {
    margin: $i * $space-unit !important;
  }
  .mt-#{$i} {
    margin-top: $i * $space-unit !important;
  }
  .mr-#{$i} {
    margin-right: $i * $space-unit !important;
  }
  .mb-#{$i} {
    margin-bottom: $i * $space-unit !important;
  }
  .ml-#{$i} {
    margin-left: $i * $space-unit !important;
  }
  .mx-#{$i} {
    margin-left: $i * $space-unit !important;
    margin-right: $i * $space-unit !important;
  }
  .my-#{$i} {
    margin-top: $i * $space-unit !important;
    margin-bottom: $i * $space-unit !important;
  }

  .p-#{$i} {
    padding: $i * $space-unit !important;
  }
  .pt-#{$i} {
    padding-top: $i * $space-unit !important;
  }
  .pr-#{$i} {
    padding-right: $i * $space-unit !important;
  }
  .pb-#{$i} {
    padding-bottom: $i * $space-unit !important;
  }
  .pl-#{$i} {
    padding-left: $i * $space-unit !important;
  }
  .px-#{$i} {
    padding-left: $i * $space-unit !important;
    padding-right: $i * $space-unit !important;
  }
  .py-#{$i} {
    padding-top: $i * $space-unit !important;
    padding-bottom: $i * $space-unit !important;
  }
}
