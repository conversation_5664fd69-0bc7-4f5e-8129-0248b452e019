import { PagingHuggedTabs } from '@/components/common/core/Tabs';
import styles from '../../KaruteInputLowerSection.module.scss';

import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { ButtonType, EDatabaseUpsertMode, EKaruteUpsertType } from '@/types/enum';
import { Flex, Spin } from 'antd';
import clsx from 'clsx';
import { useNavigate, useParams } from 'react-router-dom';
import { useKaruteInput } from '../../../provider';
import { useDatabase } from '../DatabaseContext';

export function DatabaseHeader() {
  const navigate = useNavigate();
  const { isLoading } = useDatabase();

  const {
    list: mondaiItems,
    activeId: activeMondai,
    setActiveId: setActiveMondai,
    showHistory,
  } = useDatabase();
  const { openUpsertModal } = useKaruteInput();
  const serviceId = useParams<{ serviceId: string }>().serviceId;

  const showEditMondaiHistory = () => {
    // TO DO: Open K102_編集履歴 to show edit history
    navigate('db-history');
  };

  const safeItems = Array.isArray(mondaiItems) ? mondaiItems : [];
  const tabItems = safeItems.map(item => ({
    key: item.disease_base_id,
    value: String(item.disease_base_id),
    label: <p className={styles.mondaiItem}>{item.trauma}</p>,
  }));

  const activeValues: Record<string, boolean> =
    activeMondai != null ? { [activeMondai.toString()]: true } : {};

  return (
    <>
      <div className={styles.database_header}>
        <Flex gap={'middle'} justify="space-between" align="center" className={styles.action}>
          <h2 className={clsx(styles.title, 'fs14-bold')}>データベース</h2>
          <Flex gap={'middle'}>
            {(mondaiItems ?? []).length <= 0 ? (
              <Button
                customSize="sm"
                style={{ padding: '8px 16px 8px 16px' }}
                customType={ButtonType.PRIMARY}
                onClick={() =>
                  openUpsertModal(EKaruteUpsertType.DATABASE, EDatabaseUpsertMode.UPSERT)
                }
              >
                <Icon name="plus" />
                新規作成
              </Button>
            ) : (
              <Button
                customSize="sm"
                style={{ padding: '8px 28px 8px 28px' }}
                customType={ButtonType.PRIMARY}
                onClick={() =>
                  openUpsertModal(EKaruteUpsertType.DATABASE, EDatabaseUpsertMode.UPSERT)
                }
              >
                編集
              </Button>
            )}

            {showHistory && (
              <Button
                customSize="sm"
                style={{ padding: '8px 14px 8px 14px' }}
                customType={ButtonType.PRIMARY}
                onClick={showEditMondaiHistory}
              >
                編集履歴
              </Button>
            )}
          </Flex>
        </Flex>
        <Spin spinning={isLoading}>
          <Flex gap={'middle'} className={styles.mondai}>
            <h3 className={clsx(styles.title, 'fs14-medium')}>問題№</h3>
            {tabItems.length > 0 && (
              <PagingHuggedTabs
                key={serviceId}
                items={tabItems}
                activeValues={activeValues}
                defaultActiveItem={activeMondai?.toString()}
                onChange={key => setActiveMondai(Number(key))}
                className={styles.tab}
              />
            )}
          </Flex>
        </Spin>
      </div>
    </>
  );
}
