import type { ThemeConfig } from 'antd';

const theme: ThemeConfig = {
  token: {
    borderRadius: 8,
    colorError: '#dd3b3a',
    colorBgBase: '#fff',
    colorTextBase: '#000',
    colorFillTertiary: '#EDF6F7', // Brand-50: Color for the tertiary fill
    colorPrimary: '#84c0ca', // Brand-600: Color for the primary component
    fontFamily: 'Noto Sans JP, sans-serif',
  },
  components: {
    Checkbox: {
      colorPrimary: '#84c0ca', // Brand-600: Color for the checkbox component when it’s active
      colorBorder: '#D0D5DD', // Gray-300: Color for the border of the checkbox component
      colorBgContainer: '#fff', // White: Color for the container of the checkbox component
    },
    Radio: {
      colorPrimary: '#84c0ca', // Brand-600: Color for the radio component when it’s active
      colorBorder: '#D0D5DD', // Gray-300: Color for the border of the radio component
      colorBgContainer: '#fff', // White: Color for the container of the radio component
      colorBgContainerDisabled: '#F2F4F7',
      dotColorDisabled: '#E4E7EC',
    },
    Select: {
      colorPrimary: '#84c0ca', // Brand-600: Color for the select component when it’s active
      colorBorder: '#D0D5DD', // Gray-300: Color for the border of the select component
      colorBgContainer: '#fff', // White: Color for the container of the select component
    },
    Switch: {
      colorPrimary: '#84C0CA', // Brand-600: Color for the switch when it’s turned on
    },
  },
};

export default theme;
