import { KaruteInputProvider } from '@/components/page-components/karute-managment/karte-input/provider';
import LoginPage from '@/pages/authen/LoginPage';
import ResetPasswordPage from '@/pages/authen/ResetPasswordPage';
import { KaruteDbHistoryPage } from '@/pages/karute/KaruteDbHistoryPage';
import { KaruteInputPage } from '@/pages/karute/KaruteInputPage';
import ListKarute from '@/pages/karute/ListKarutePage';
import CreatePatientPage from '@/pages/patient-management/CreatePatientPage/CreatePatientPage';
import ListPatientPage from '@/pages/patient-management/ListPatientPage';
import UpdatePatientPage from '@/pages/patient-management/UpdatePatientPage/UpdatePatientPage';
import UserManagementPage from '@/pages/user-management/ListUserPage';
import DashboardPage from '@/pages/visit-management/DashboardPage';
import VisitDetailPage from '@/pages/visit-management/VisitDetailPage';
import { routerPaths } from '@/types/constants/routerPath';
import { ROLE } from '@/types/enum';
import { ReactElement } from 'react';

export interface PublicRoute {
  path: string;
  element: ReactElement;
}

const schemaParents: string[] = [
  routerPaths.karuteManagement.karuteUpdateByService,
  routerPaths.karuteManagement.karuteUpsertModal,
];

export interface PrivateRoute extends PublicRoute {
  element: ReactElement;
  roles: (ROLE | null)[];
}

export const publicRouterMenu: PublicRoute[] = [
  {
    path: routerPaths.login,
    element: <LoginPage />,
  },
  {
    path: routerPaths.resetPassword,
    element: <ResetPasswordPage />,
  },
];

const schemaRoutes: PrivateRoute[] = schemaParents.map(parentPath => ({
  path: `${parentPath}${routerPaths.karuteManagement.schemaSegment}`,
  element: (
    <KaruteInputProvider>
      <KaruteInputPage />
    </KaruteInputProvider>
  ),
  roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
}));

export const privateRouterMenu: PrivateRoute[] = [
  {
    path: routerPaths.dashboard,
    element: <DashboardPage />,
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.karuteManagement.karuteList,
    element: <ListKarute />,
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.karuteManagement.karuteUpdate,
    element: (
      <KaruteInputProvider>
        <KaruteInputPage />
      </KaruteInputProvider>
    ),
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.karuteManagement.karuteUpdateByService,
    element: (
      <KaruteInputProvider>
        <KaruteInputPage />
      </KaruteInputProvider>
    ),
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },

  {
    path: routerPaths.karuteManagement.karuteUpsertModal,
    element: (
      <KaruteInputProvider>
        <KaruteInputPage />
      </KaruteInputProvider>
    ),
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.karuteManagement.karuteDbHistory(),
    element: (
      <KaruteInputProvider>
        <KaruteDbHistoryPage />
      </KaruteInputProvider>
    ),
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  ...schemaRoutes,
  {
    path: routerPaths.patientManagement.patientList,
    element: <ListPatientPage />,
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.patientManagement.patientCreate,
    element: <CreatePatientPage />,
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.patientManagement.patientUpdate(),
    element: <UpdatePatientPage />,
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
  {
    path: routerPaths.userManagement.userList,
    element: <UserManagementPage />,
    // roles: [ROLE.ADMIN], TODO : check role
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN], // temporarily open
  },
  {
    path: routerPaths.visitManagement.visitUpdate(),
    element: <VisitDetailPage />,
    roles: [ROLE.STORE_ADMIN, ROLE.MEDIX_ADMIN, ROLE.COMPANY_ADMIN, ROLE.STAFF],
  },
];
