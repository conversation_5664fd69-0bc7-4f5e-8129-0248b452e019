import { ReactElement } from 'react';
import { routerPaths } from '@/types/constants/routerPath';
import NotAuthenPage from '@/pages/authen/NotAuthenPage';
import UserManagementPage from '@/pages/dashboard/UserManagementPage';
import TokenBridge from '@/pages/authen/TokenBridge';
import CreateUserPage from '@/pages/dashboard/CreateUserPage';
import ViewAndEditUserPage from '@/pages/dashboard/ViewAndEditUserPage';
import UserPreviewModal from '@/pages/dashboard/UserPreviewModal';
import UserNotFoundPage from '@/pages/dashboard/UserNotFoundPage';

export interface PublicRoute {
  path: string;
  element: ReactElement;
}

export interface PrivateRoute extends PublicRoute {
  element: ReactElement;
}

export const publicRouterMenu: PublicRoute[] = [
  { path: routerPaths.tokenBridge, element: <TokenBridge /> },
  { path: routerPaths.notAuthen, element: <NotAuthenPage /> },
];
export const privateRouterMenu: PrivateRoute[] = [
  {
    path: routerPaths.dashboard,
    element: <UserManagementPage />,
  },
  { path: routerPaths.userPreview(), element: <UserPreviewModal /> },
  {
    path: routerPaths.create,
    element: <CreateUserPage />,
  },
  {
    path: routerPaths.edit(),
    element: <ViewAndEditUserPage />,
  },
  {
    path: routerPaths.userNotFound,
    element: <UserNotFoundPage />,
  },
];

export const privateModalMenu = [
  { path: routerPaths.userPreview(), element: <UserPreviewModal /> },
];
