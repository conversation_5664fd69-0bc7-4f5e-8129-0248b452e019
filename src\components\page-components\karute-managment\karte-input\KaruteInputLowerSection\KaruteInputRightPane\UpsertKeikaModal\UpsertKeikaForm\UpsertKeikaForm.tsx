import Button from '@/components/common/core/Button';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import { AppDivider } from '@/components/common/core/Divider/AppDivider';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppTextArea } from '@/components/common/core/Input';
import { AppRadio } from '@/components/common/core/Radio';
import { AppSelect } from '@/components/common/core/Select';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import { ERROR_COMMON_MESSAGE, PLACEHOLDER_MESSAGE_DEFAULT } from '@/types/constants';
import { KeikaImage, KeikaItem, UpsertKeikaSchema } from '@/types/constants/karute';
import { ButtonType, EKaruteFieldCode, IsReferenceModalOpen } from '@/types/enum';
import { transformTreatersToOptions } from '@/utils';
import { Flex, Radio } from 'antd';
import { useWatch } from 'antd/es/form/Form';
import FormList from 'antd/es/form/FormList';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import clsx from 'clsx';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useKaruteInput } from '../../../../provider';
import LabelRender from '../../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/LabelRender';
import {
  EPaymentType,
  EStatus,
  paymentOptions,
} from '../../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import { DoctorByVisitDate } from '../../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types/database_form_get';
import { CourseTableProps } from '../../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertOptionListForm/ListCourseTable';
import { OptionTableProps } from '../../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertOptionListForm/ListOptionTable';
import styles from './UpsertKeikaForm.module.scss';

export const DEFAULT_DISEASE_ID = '1'; // use for navigate in state only
const MONDAI_NAME = 'M';
const MAX_TEXT_LENGTH = 1000;

type Props = {
  isFirstActiveMondai: Record<number, Partial<KeikaItem> | null>;
  setIsSetFromHistoryModalOpen: React.Dispatch<React.SetStateAction<number | null>>;
  calculateFirstActiveMondai: (keika: Partial<KeikaItem>[]) => void;
  handleAddCourse: () => void;
  handleCleanCourse: () => void;
  handleAddOption: () => void;
  handleCleanOption: () => void;
  hasSetUp: boolean;
  // visitDateOptions: VisitDateOptions[];
  disease_base_id?: number;
  initialDoctorByVisitDate: DoctorByVisitDate;
  handleWarningRemoveCourseOption: ({
    name,
    changeType,
  }: {
    name?: number;
    changeType: 'visit_date' | 'payment_type' | 'option_checked';
  }) => Promise<boolean>;
  handleOpenReference: (isReferenceModalOpen: IsReferenceModalOpen) => void;
} & Partial<OptionTableProps & CourseTableProps>;
export interface UpsertKeikaFormRef {
  scrollToMondai: (mondai?: Partial<KeikaItem> | null) => void;
}
export const UpsertKeikaForm = forwardRef<UpsertKeikaFormRef, Props>(
  (
    {
      hasSetUp,
      isFirstActiveMondai,
      // setCourseDataSource,
      // setCourseSelectedKeys,
      setOptionDataSource,
      setOptionSelectedKeys,
      setIsSetFromHistoryModalOpen,
      calculateFirstActiveMondai,
      handleAddCourse,
      handleAddOption,
      handleCleanCourse,
      handleCleanOption,
      // visitDateOptions,
      disease_base_id,
      initialDoctorByVisitDate,
      handleOpenReference,
      handleWarningRemoveCourseOption,
    },
    ref
  ) => {
    const scrollTargetRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    // States
    const [selectedKeikaItem, setSelectedKeikaItem] = useState<KeikaItem | null>(null);
    const { doctorList, isInitalLoading, openSchema, drawnImages, setDrawnImages } =
      useKaruteInput();
    const form = useFormInstance<UpsertKeikaSchema>();
    const currentVisitDate = useWatch('visit_date');

    const isAllFormDisabled = !currentVisitDate;

    useEffect(() => {
      if (!hasSetUp) return;
      setTimeout(() => {
        scrollTargetRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }, [hasSetUp]);

    useImperativeHandle(ref, () => ({
      scrollToMondai: (mondai?: Partial<KeikaItem> | null) => {
        setSelectedKeikaItem((mondai as KeikaItem) ?? null);
        setTimeout(() => {
          scrollTargetRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      },
    }));

    const handleSchema = (diseaseType: DiseaseType) => {
      const currentMondai: KeikaItem = form.getFieldValue(['keika', 0]);
      const edittedImage = {
        [SchemaType.KEIKA]: {
          [String(DEFAULT_DISEASE_ID)]: {
            [DiseaseType.O]: (currentMondai?.objective_images ?? []).map(item => ({
              id: `${item.keika_id}_${item.keika_image_id}`,
              keika_image_id: item.keika_image_id,
              url: item.url,
              schema_image_id: item?.schema_image_id,
            })),
            [DiseaseType.T]: (currentMondai?.treatment_images ?? []).map(item => ({
              id: `${item.keika_id}_${item.keika_image_id}`,
              keika_image_id: item.keika_image_id,
              url: item.url,
              schema_image_id: item?.schema_image_id,
            })),
          },
        },
      };
      setDrawnImages(edittedImage as any);
      openSchema(SchemaType.KEIKA, diseaseType, DEFAULT_DISEASE_ID);
    };
    const handleOptionCheck = async (value: boolean) => {
      if (!setOptionDataSource || !setOptionSelectedKeys) return;
      if (value) {
        handleAddOption();
        handleAddCourse();
      } else {
        const isVisitDateChangeChangbleCourseOption = await handleWarningRemoveCourseOption({
          changeType: 'option_checked',
        });
        if (!isVisitDateChangeChangbleCourseOption) return;
      }
      handleCleanCourse();
      handleCleanOption();
      calculateFirstActiveMondai(form.getFieldValue('keika') || []);
    };
    const handleCheckPaymentType = async (value: EPaymentType, name: number) => {
      if (value === String(EPaymentType.SELF_PAID)) {
        handleAddCourse();
      } else {
        const isVisitDateChangeChangble = await handleWarningRemoveCourseOption({
          name,
          changeType: 'payment_type',
        });
        if (!isVisitDateChangeChangble) return;
      }
      handleCleanCourse();
      calculateFirstActiveMondai(form.getFieldValue('keika') || []);
    };

    useEffect(() => {
      if (!drawnImages.keika || !form) return;
      const drawImageObjective = (
        drawnImages?.keika?.[DEFAULT_DISEASE_ID]?.[DiseaseType.O] ?? []
      ).map(img => ({
        keika_image_id: img.keika_image_id,
        url: img.url,
        schema_image_id: img.schema_image_id,
      }));
      const drawImageTreatment = (
        drawnImages?.keika?.[DEFAULT_DISEASE_ID]?.[DiseaseType.T] ?? []
      ).map(img => ({
        keika_image_id: img.keika_image_id,
        url: img.url,
        schema_image_id: img.schema_image_id,
      }));
      const mondaiItems: KeikaItem[] = form.getFieldValue(['keika']);
      (mondaiItems ?? []).forEach((_, index) => {
        form.setFieldValue(['keika', index, 'objective_images'], drawImageObjective);
        form.setFieldValue(['keika', index, 'treatment_images'], drawImageTreatment);
      });
    }, [drawnImages]);
    useEffect(() => {
      const formContainer = containerRef.current;
      if (!formContainer) return;

      let isDragging = false;
      let startX = 0;
      let scrollLeft = 0;

      const handleMouseDown = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        if (
          target instanceof HTMLInputElement ||
          target instanceof HTMLTextAreaElement ||
          target instanceof HTMLSelectElement ||
          target instanceof HTMLSpanElement ||
          target.closest('.ant-select-dropdown') ||
          target.closest('.ant-picker-dropdown')
        ) {
          return;
        }

        isDragging = true;
        startX = e.pageX - formContainer.offsetLeft;
        scrollLeft = formContainer.scrollLeft;
        formContainer.style.cursor = 'grabbing';
      };

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDragging) return;

        const target = e.target as HTMLElement;
        if (
          !(target instanceof HTMLInputElement) &&
          !(target instanceof HTMLTextAreaElement) &&
          !(target instanceof HTMLSelectElement) &&
          !(target instanceof HTMLSpanElement) &&
          !target.closest('.ant-select-dropdown') &&
          !target.closest('.ant-picker-dropdown')
        ) {
          e.preventDefault();
        }

        const x = e.pageX - formContainer.offsetLeft;
        const walk = (x - startX) * 1.5;
        formContainer.scrollLeft = scrollLeft - walk;
      };

      const handleMouseUp = () => {
        isDragging = false;
        formContainer.style.cursor = 'grab';
      };

      const handleMouseLeave = () => {
        isDragging = false;
        formContainer.style.cursor = 'grab';
      };

      formContainer.addEventListener('mousedown', handleMouseDown);
      formContainer.addEventListener('mousemove', handleMouseMove);
      formContainer.addEventListener('mouseup', handleMouseUp);
      formContainer.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        if (formContainer) {
          formContainer.removeEventListener('mousedown', handleMouseDown);
          formContainer.removeEventListener('mousemove', handleMouseMove);
          formContainer.removeEventListener('mouseup', handleMouseUp);
          formContainer.removeEventListener('mouseleave', handleMouseLeave);
        }
      };
    }, []);
    useEffect(() => {
      if (!hasSetUp) return;
      const checkOverflow = () => {
        const element = containerRef.current;
        if (element) {
          const hasOverflowX = element.scrollWidth > element.clientWidth;
          const hasOverflowY = element.scrollHeight > element.clientHeight;
          if (hasOverflowX || hasOverflowY) {
            element.style.paddingBottom = '16px';
          } else {
            element.style.paddingBottom = '0';
          }
        }
      };
      checkOverflow();
    }, [hasSetUp]);
    return (
      <div>
        <Flex gap="middle" className={styles.upsert_keika_form} ref={containerRef}>
          <Flex vertical gap="middle" className={styles.keika_form_group_wrapper}>
            <FormList name="keika">
              {fields => {
                return (
                  <Flex gap="large">
                    {fields.map(({ key, name, ...restField }) => {
                      const isLast = name === (fields ?? []).length - 1;
                      const currentMondai: KeikaItem = form.getFieldValue(['keika', name]);
                      const tmpDbO: KeikaImage[] = form.getFieldValue([
                        'keika',
                        name,
                        'objective_images',
                      ]);
                      const tmpDbT: KeikaImage[] = form.getFieldValue([
                        'keika',
                        name,
                        'treatment_images',
                      ]);
                      // Case 1: if disease_base_id provided -> scroll to that mondai
                      // Case 2: if not -> Scroll to the last mondai
                      const isAssignedScrollRef =
                        (selectedKeikaItem &&
                          selectedKeikaItem?.disease_base_id === currentMondai?.disease_base_id) ||
                        (!selectedKeikaItem &&
                          disease_base_id &&
                          disease_base_id === currentMondai?.disease_base_id) ||
                        (!selectedKeikaItem && !disease_base_id && isLast);
                      const isFormDisabled =
                        currentMondai?.status === EStatus.INACTIVE || isAllFormDisabled;
                      const traumaName = `${MONDAI_NAME}${currentMondai?.trauma_counter ?? ''}${
                        currentMondai?.injury_name_display
                          ? ` : ${currentMondai.injury_name_display}`
                          : ''
                      }`;
                      // isAssignedScrollRef;
                      const isHideInputs = !isFirstActiveMondai?.[name];
                      const seen = new Set<string>();

                      const currentDoctor =
                        currentMondai.disease_base_id &&
                        initialDoctorByVisitDate[currentMondai?.disease_base_id as number]
                          ?.visit_date === currentVisitDate
                          ? [
                              ...doctorList,
                              ...(
                                initialDoctorByVisitDate[
                                  currentMondai?.disease_base_id as number
                                ] ?? []
                              ).doctors,
                            ].filter(doctor => {
                              const key = `${doctor.user_id}`;
                              if (!seen.has(key)) {
                                seen.add(key);
                                return true;
                              }
                              return false;
                            })
                          : doctorList;
                      return (
                        <Flex
                          ref={isAssignedScrollRef ? scrollTargetRef : undefined}
                          key={key}
                          vertical
                          align="start"
                          gap="middle"
                        >
                          <Button
                            customType={ButtonType.PRIMARY}
                            onClick={() => {
                              setIsSetFromHistoryModalOpen(name);
                            }}
                            disabled={isFormDisabled}
                          >
                            履歴からセット
                          </Button>
                          <div
                            className={clsx(styles.keika_form_group, {
                              [styles.first_same_day_active_item]: isFirstActiveMondai?.[name],
                              [styles.is_form_disabled]: isFormDisabled,
                              [styles.is_selected_mondai]: disease_base_id && isAssignedScrollRef,
                            })}
                            data-index={name}
                          >
                            <div
                              // style={{minHeight: "80px"}}
                              className={styles.keika_form_item}
                            >
                              <Flex
                                className={styles.treatment_header_row}
                                justify="space-between"
                                align="start"
                                wrap
                              >
                                <AppFormItem
                                  {...restField}
                                  name={[name, 'payment_type']}
                                  required
                                  style={{
                                    flex: 1,
                                    flexShrink: 'none',
                                    height: '46px',
                                  }}
                                  className={styles.hoken_type}
                                  rules={
                                    currentMondai?.status === EStatus.INACTIVE || isAllFormDisabled
                                      ? []
                                      : [
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: ERROR_COMMON_MESSAGE.REQUIRED('請求区分'),
                                          },
                                        ]
                                  }
                                >
                                  <Radio.Group
                                    onChange={value => {
                                      handleCheckPaymentType(value.target.value, name);
                                    }}
                                    disabled={isFormDisabled}
                                  >
                                    <Flex gap="small" wrap>
                                      {paymentOptions.map(item => (
                                        <AppRadio value={item.value} key={item.value}>
                                          {item.label}
                                        </AppRadio>
                                      ))}
                                    </Flex>
                                  </Radio.Group>
                                </AppFormItem>
                                <AppFormItem
                                  {...restField}
                                  name={[name, 'option_checked']}
                                  valuePropName="checked"
                                  className={clsx(
                                    styles.option,
                                    isHideInputs && styles.disable_form_item
                                  )}
                                >
                                  <AppCheckbox
                                    onChange={value => {
                                      handleOptionCheck(value.target.checked);
                                    }}
                                    disabled={isFormDisabled}
                                  >
                                    オプション
                                  </AppCheckbox>
                                </AppFormItem>
                              </Flex>
                            </div>
                            <AppDivider className="mb-4 mt-0" />

                            <div style={{ minHeight: '120px' }} className={styles.keika_form_item}>
                              <LabelRender
                                required
                                label="問診情報（S）"
                                mondai_name={traumaName}
                                secondBtn={{
                                  label: '参照',
                                  onClick: () =>
                                    handleOpenReference({
                                      name,
                                      field: 'subjective',
                                      code: EKaruteFieldCode.S,
                                      label: '問診情報（S）',
                                    }),
                                  disabled: isFormDisabled,
                                }}
                              />
                              <AppFormItem {...restField} name={[name, 'subjective']} required>
                                <AppTextArea
                                  style={{ minHeight: '112px' }}
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                  disabled={isFormDisabled}
                                  maxLength={MAX_TEXT_LENGTH}
                                />
                              </AppFormItem>
                            </div>
                            <AppDivider className="my-4" />
                            <div
                              style={{ minHeight: '150px' }}
                              className={clsx(styles.keika_form_item)}
                            >
                              <div className={clsx(isHideInputs && styles.disable_form_item)}>
                                <LabelRender
                                  required
                                  label="所見（O）"
                                  firstBtn={{
                                    label: 'シェーマ',
                                    onClick: () => {
                                      handleSchema(DiseaseType.O);
                                    },
                                    disabled: isHideInputs || isFormDisabled,
                                  }}
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        field: 'objective',
                                        code: EKaruteFieldCode.O,
                                        label: '所見（O）',
                                      }),
                                    disabled: isHideInputs || isFormDisabled,
                                  }}
                                />
                                <div
                                  className={clsx(
                                    styles.schema_form_item,
                                    isFormDisabled
                                      ? styles.schema_form_item_disabled
                                      : styles.schema_form_item_active
                                  )}
                                  style={{ minHeight: '112px', height: '112px' }}
                                >
                                  <AppFormItem {...restField} name={[name, 'objective']}>
                                    <AppTextArea
                                      style={{ border: 'none' }}
                                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                      maxLength={MAX_TEXT_LENGTH}
                                      disabled={isHideInputs || isFormDisabled}
                                      autoSize
                                    />
                                  </AppFormItem>
                                  <div className={clsx(styles.imagePreview)}>
                                    <figure>
                                      {(tmpDbO ?? []).map(item => (
                                        <img src={item.url} key={item?.keika_image_id} />
                                      ))}
                                    </figure>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <AppDivider className="my-4" />

                            <div className={styles.keika_form_item}>
                              <LabelRender
                                required
                                label="分析（A）"
                                secondBtn={{
                                  label: '参照',
                                  onClick: () =>
                                    handleOpenReference({
                                      name,
                                      field: 'assessment',
                                      code: EKaruteFieldCode.A,
                                      label: '分析（A）',
                                    }),
                                  disabled: isFormDisabled,
                                }}
                              />
                              <AppFormItem {...restField} name={[name, 'assessment']}>
                                <AppTextArea
                                  style={{ minHeight: '112px' }}
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                  disabled={isFormDisabled}
                                  maxLength={MAX_TEXT_LENGTH}
                                />
                              </AppFormItem>
                            </div>
                            <AppDivider className="my-4" />

                            <div style={{ minHeight: '150px' }} className={styles.keika_form_item}>
                              <div className={clsx(isHideInputs && styles.disable_form_item)}>
                                <LabelRender
                                  required
                                  label="計画（P）"
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        field: 'plan',
                                        code: EKaruteFieldCode.P,
                                        label: '計画（P）',
                                      }),
                                    disabled: isFormDisabled,
                                  }}
                                />
                                <AppFormItem {...restField} name={[name, 'plan']}>
                                  <AppTextArea
                                    style={{ minHeight: '112px' }}
                                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                    disabled={isFormDisabled}
                                    maxLength={MAX_TEXT_LENGTH}
                                  />
                                </AppFormItem>
                              </div>
                            </div>
                            <AppDivider className="my-4" />
                            <div style={{ minHeight: '150px' }} className={styles.keika_form_item}>
                              <div className={clsx(isHideInputs && styles.disable_form_item)}>
                                <LabelRender
                                  required
                                  label="施術（T）"
                                  firstBtn={{
                                    label: 'シェーマ',
                                    onClick: () => {
                                      handleSchema(DiseaseType.T);
                                    },
                                    disabled: isHideInputs || isFormDisabled,
                                  }}
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        field: 'treatment',
                                        code: EKaruteFieldCode.T,
                                        label: '施術（T）',
                                      }),
                                    disabled: isHideInputs || isFormDisabled,
                                  }}
                                />
                                <div
                                  className={clsx(
                                    styles.schema_form_item,
                                    isFormDisabled && styles.schema_form_item_disabled
                                  )}
                                  style={{ height: '112px', minHeight: '112px' }}
                                >
                                  <AppFormItem {...restField} name={[name, 'treatment']}>
                                    <AppTextArea
                                      style={{ border: 'none' }}
                                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                      maxLength={MAX_TEXT_LENGTH}
                                      disabled={isHideInputs || isFormDisabled}
                                      autoSize
                                    />
                                  </AppFormItem>
                                  <div
                                    className={clsx(
                                      styles.imagePreview,
                                      isHideInputs && styles.schema_form_item_disabled
                                    )}
                                  >
                                    <figure>
                                      {(tmpDbT ?? []).map(item => (
                                        <img src={item.url} key={item?.keika_image_id} />
                                      ))}
                                    </figure>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <AppDivider className="my-4" />

                            <div style={{ minHeight: '150px' }} className={styles.keika_form_item}>
                              <div className={clsx(isHideInputs && styles.disable_form_item)}>
                                <LabelRender
                                  label="備考（R）"
                                  secondBtn={{
                                    label: '参照',
                                    onClick: () =>
                                      handleOpenReference({
                                        name,
                                        field: 'remarks',
                                        code: EKaruteFieldCode.R,
                                        label: '備考（R）',
                                      }),
                                    disabled: isFormDisabled,
                                  }}
                                />
                                <AppFormItem
                                  {...restField}
                                  className={clsx(isHideInputs && styles.disable_form_item)}
                                  name={[name, 'remarks']}
                                >
                                  <AppTextArea
                                    style={{ minHeight: '112px' }}
                                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_AREA}
                                    disabled={isFormDisabled}
                                    maxLength={MAX_TEXT_LENGTH}
                                  />
                                </AppFormItem>
                              </div>
                            </div>
                            <AppDivider className="my-4" />

                            <div className={styles.keika_form_item}>
                              <LabelRender required label="施術師" />
                              <AppFormItem {...restField} name={[name, 'doctor_id']}>
                                <AppSelect
                                  options={transformTreatersToOptions(currentDoctor)}
                                  disabled={isInitalLoading || isFormDisabled}
                                  showSearch
                                  filterOption={(input, option) =>
                                    String(option?.label)
                                      .toLowerCase()
                                      .trim()
                                      .startsWith(input.toLowerCase().trim())
                                  }
                                  placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                                  allowClear
                                  size="small"
                                  getPopupContainer={triggerNode => {
                                    return triggerNode.parentNode as HTMLElement;
                                  }}
                                />
                              </AppFormItem>
                            </div>
                          </div>
                        </Flex>
                      );
                    })}
                  </Flex>
                );
              }}
            </FormList>
          </Flex>
        </Flex>
      </div>
    );
  }
);
