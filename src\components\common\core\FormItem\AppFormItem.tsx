import { useMemo, useId, useRef, cloneElement } from 'react';

import { Form } from 'antd';
import { FormItemProps } from 'antd/es/form/FormItem';
import clsx from 'clsx';

import styles from './AppFormItem.module.scss';

interface AppFormItemProps extends FormItemProps {
  labelSize?: 'small' | 'medium' | 'large';
  required?: boolean;
  subLabel?: React.ReactNode;
}

interface FormItemLabelProps {
  label: React.ReactNode;
  labelSize?: 'small' | 'medium' | 'large';
  className?: string;
  required?: boolean;
  subLabel?: React.ReactNode;
}

export const FormItemLabel = ({
  label,
  labelSize = 'medium',
  className,
  required = false,
  subLabel,
  htmlFor,
  onClick,
}: FormItemLabelProps & { htmlFor?: string; onClick?: () => void }) => {
  return (
    <label
      className={clsx(styles.formLabel, className, 'fs14-medium', {
        [styles.labelSmall]: labelSize === 'small',
        [styles.labelLarge]: labelSize === 'large',
      })}
      htmlFor={htmlFor}
      onClick={onClick}
    >
      {label}
      {required && <span className={styles.requiredIcon}>*</span>}
      {subLabel}
    </label>
  );
};

export const AppFormItem = ({
  className,
  label,
  labelSize = 'medium',
  required,
  children,
  subLabel,
  ...props
}: AppFormItemProps) => {
  const classNames = clsx(styles.formItem, className);
  const id = useId(); // generate unique id for label and input -> avoid forgetting to apply in AntInput
  const inputRef = useRef<any>(null);

  const handleLabelClick = () => {
    if (inputRef.current && typeof inputRef.current.focus === 'function') {
      inputRef.current.focus();
    }
  };

  const internalLabel = useMemo(() => {
    if (typeof label !== 'string') return label;
    return (
      <FormItemLabel
        label={label}
        labelSize={labelSize}
        required={required}
        htmlFor={id}
        onClick={handleLabelClick}
        subLabel={subLabel}
      />
    );
  }, [label, labelSize, required, id]);

  const childWithId = useMemo(() => {
    if (!children) return children;
    if (Array.isArray(children)) return children;
    // clone new node to apply more props
    return cloneElement(children as React.ReactElement, {
      id,
      ref: inputRef,
      ...((children as any).props || {}),
    });
  }, [children, id]);

  return (
    <Form.Item className={classNames} label={internalLabel} {...props}>
      {childWithId}
    </Form.Item>
  );
};
