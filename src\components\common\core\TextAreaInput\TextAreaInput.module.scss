.wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  .inputWrapper {
    overflow: hidden;
    :global(textarea[class*='ant-input']) {
      border-color: $gray-300;
      border-radius: 8px;
      border-width: 1px;
      padding: 10px 12px;
      color: $gray-500;
      transition: border-color 0.2s ease, color 0.2s ease;
      box-shadow: none;
      outline: none;
      // resize: none;
    }

    &.filled:not(.focused):not(.hovered) :global(textarea[class*='ant-input']) {
      border-color: $gray-300;
      color: $gray-800;
    }

    &.hovered :global(textarea[class*='ant-input']),
    &.focused :global(textarea[class*='ant-input']) {
      border-color: $brand-800;
      color: $gray-500;
      box-shadow: none !important;
    }

    &.disabled :global(textarea[class*='ant-input']) {
      background-color: $gray-50;
      color: $gray-500 !important;
      cursor: default;
    }
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: $gray-300;
    }
  }

  &.error {
    .inputWrapper {
      :global(textarea[class*='ant-input']) {
        border-color: $error-400;
      }

      &.filled:not(.focused):not(.hovered) :global(textarea[class*='ant-input']) {
        border-color: $error-400;
      }

      &.hovered :global(textarea[class*='ant-input']),
      &.focused :global(textarea[class*='ant-input']) {
        border-color: $error-600;
      }

      &.disabled :global(textarea[class*='ant-input']) {
        background-color: $gray-50;
        cursor: default;
      }
    }
    .message {
      color: $error-600 !important;
    }
  }

  .label {
    color: $gray-500;
  }

  .message {
    color: $gray-600 !important;
  }
}
