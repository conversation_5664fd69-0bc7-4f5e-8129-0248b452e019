import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { SelectionStates } from './type';

export const initSelectionState: SelectionStates = {
  selection: {
    insurance_types: [],
    status: [],
    visit_types: [],
    courses: [],
  },
  isInitialized: false,
};

// slice
export const selectionSlice = createSlice({
  name: 'selection',
  initialState: initSelectionState,
  reducers: {
    setSelection: (state, actions: PayloadAction<SelectionStates>) => {
      state.selection = actions.payload.selection;
      state.isInitialized = actions.payload.isInitialized;
    },
  },
});

// actions
export const { setSelection } = selectionSlice.actions;
// reducer
export const selectionReducer = selectionSlice.reducer;
