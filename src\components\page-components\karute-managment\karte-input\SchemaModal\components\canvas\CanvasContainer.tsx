import Icon from '@/components/common/core/Icon';
import { SCHEMA_COMMON } from '@/types/constants';
import { getImgKey } from '@/utils';
import { Spin } from 'antd';
import clsx from 'clsx';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useSchema } from '../../context/SchemaContext';
import { CanvasContainerHandle, Color, COLORS, DrawTool, SchemaMode } from '../../type';
import styles from './Canvas.module.scss';
import CanvasMainView from './CanvasMainView';
import { DrawingCanvasHandle } from './DrawingCanvas';
import Toolbar from './Toolbar';

export const TOOL_CONFIG: Record<
  Exclude<DrawTool, DrawTool.NONE>,
  { icon?: string; min: number; max: number; isFill?: boolean }
> = {
  pencil: { icon: 'edit', min: 1, max: 60 },
  circle: { isFill: true, min: 1, max: 60 },
  circleOutline: { min: 1, max: 60 },
  eraser: { icon: 'eraser', min: 1, max: 60 },
};

interface Props {
  onRemoveImage: (id: string) => void;
}

const CanvasContainer = forwardRef<CanvasContainerHandle, Props>(({ onRemoveImage }, ref) => {
  const [current, setCurrent] = useState(0);
  const [activeColor, setActiveColor] = useState<Color>(COLORS.COLOR_1);
  const {
    selectedImages,
    drawnIds,
    mode,
    setDrawnImages,
    initLoading,
    showConfirmRemoveImage,
    savedIds,
  } = useSchema();
  const disabled = mode !== SchemaMode.DRAW;
  const images = selectedImages;
  const img = images[current];
  const canvasRefs = useRef<Record<string, DrawingCanvasHandle | null>>({});
  const [search] = useSearchParams();
  const focusId = search.get('focus');
  useEffect(() => {
    setDrawnImages(prev => {
      const next = prev.filter(d => images.some(i => getImgKey(i) === d.id));
      selectedImages.forEach(img => {
        const id = getImgKey(img);
        if (!next.some(e => e.id === id)) {
          next.push({ id, blob: new Blob() });
        }
      });
      return next;
    });
  }, [selectedImages, setDrawnImages]);

  useEffect(() => {
    if (!focusId) return;
    const idx = selectedImages.findIndex(img => img.id === String(focusId));
    if (idx >= 0) setCurrent(idx);
  }, [focusId, selectedImages]);

  useImperativeHandle(
    ref,
    () => ({
      exportAll: async () => {
        const results = await Promise.all(
          images.map(async i => {
            const uid = getImgKey(i);
            const canvas = canvasRefs.current[uid];
            const blob = await canvas!.exportImage();
            const schema_image_id = i.schema_image_id;
            return { id: uid, blob, isTemp: i.isTemp || false, schema_image_id };
          })
        );
        setDrawnImages(results);
        return results;
      },
    }),
    [images, setDrawnImages]
  );

  const confirmRemove = () => {
    const id = getImgKey(img);
    if (!drawnIds.includes(id) && !savedIds.includes(id)) {
      onRemoveImage(id);
      if (current > 0) setCurrent(i => i - 1);
      return;
    }
    showConfirmRemoveImage(() => {
      onRemoveImage(id);
      if (current > 0) setCurrent(i => i - 1);
    });
  };

  return (
    <Spin spinning={initLoading}>
      <div className={clsx(styles.viewer, disabled && styles.disabled)}>
        <>
          {images.length !== 0 ? (
            <CanvasMainView
              images={images}
              current={current}
              setCurrent={setCurrent}
              confirmRemove={confirmRemove}
              activeColor={activeColor}
              canvasRefs={canvasRefs.current}
            />
          ) : (
            <div className={clsx(styles.center, styles.empty)}>
              <div style={{ width: '480px' }}>
                <div className={styles.center}>
                  <Icon name="emptyTable" native />
                </div>
                <div className={clsx(styles.gray500, 'fs14-regular', 'text-center')}>
                  {SCHEMA_COMMON.EMPTY_IMAGE}
                </div>
              </div>
            </div>
          )}

          <Toolbar disabled={disabled} activeColor={activeColor} setActiveColor={setActiveColor} />

          <div className={clsx(styles.shortcut, styles.gray500, 'fs14-regular')}>
            {SCHEMA_COMMON.SHORTCUT}
          </div>
        </>
      </div>
    </Spin>
  );
});

export default CanvasContainer;
