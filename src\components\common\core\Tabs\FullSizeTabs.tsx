// TODO: review needed
import { FC } from 'react';
import styles from './Tabs.module.scss';
import { TabItem } from '.';

interface FullSizeTabsProps {
  items: TabItem[];
  activeValues: Record<string, boolean>;
  onChange: (key: string) => void;
  className?: string;
}

export const FullSizeTabs: FC<FullSizeTabsProps> = ({
  items,
  activeValues,
  onChange,
  className = '',
}) => {
  return (
    <div className={`${styles.fullsize_tabs} ${className}`}>
      <div className={styles.tabs_options}>
        {items.map(item => (
          <div
            key={item.key}
            className={`${styles.tab_option} ${activeValues[item.value] ? styles.active : ''}`}
            onClick={() => onChange(item.value)}
          >
            {typeof item.label === 'string'
              ? `${item.label} ${item.count !== undefined ? `(${item.count})` : ''}`
              : item.label}
          </div>
        ))}
      </div>
    </div>
  );
};
