// TODO: review needed
import clsx from 'clsx';
import { FC } from 'react';
import { TabItem } from '.';
import styles from './Tabs.module.scss';

interface FullSizeTabsProps {
  items: TabItem[];
  activeValues: number | string;
  onChange: (key: string | number | any) => void;
  className?: string;
  tabOptionConfig?: {
    className?: string;
    showTitle?: boolean;
  };
}

export const FullSizeTabs: FC<FullSizeTabsProps> = ({
  items,
  activeValues,
  onChange,
  className = '',
  tabOptionConfig = {},
}) => {
  return (
    <div className={`${styles.fullsize_tabs} ${className}`}>
      <div className={clsx(styles.tabs_options, 'fs14-bold')}>
        {items.map(item => (
          <div
            key={item.key}
            className={clsx(
              styles.tab_option,
              'fs14-bold',
              activeValues === item.value ? styles.active : '',
              tabOptionConfig?.className || ''
            )}
            onClick={() => onChange(item.value)}
            title={
              tabOptionConfig?.showTitle ? (typeof item.label === 'string' ? item.label : '') : ''
            }
          >
            {typeof item.label === 'string'
              ? `${item.label} ${item.count !== undefined ? `(${item.count})` : ''}`
              : item.label}
          </div>
        ))}
      </div>
    </div>
  );
};
