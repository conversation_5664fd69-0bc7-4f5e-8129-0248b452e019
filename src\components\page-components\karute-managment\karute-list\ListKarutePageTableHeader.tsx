import { Flex, Form } from 'antd';
import styles from './ListKarutePage.module.scss';
import { KaruteSearchParams, useListKarute } from './provider';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppRangeDatePicker } from '@/components/common/core/Datepicker';
import Button from '@/components/common/core/Button';
import { ButtonType } from '@/types/enum';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppSelect } from '@/components/common/core/Select';
import { convertMapToOptions } from '@/utils';
import { karuteStatusMap } from '@/types/constants/karute';
import { DATE_FORMATS, TIME_FORMATS } from '@/types/constants';

export function ListKarutePageTableHeader() {
  const { handleSearch, handleClear, isFetching } = useListKarute();

  // Provider
  const [form] = Form.useForm<KaruteSearchParams>();
  const onFormSubmit = (values: KaruteSearchParams) => {
    handleSearch(values);
  };

  const onClear = () => {
    form.resetFields();
    handleClear();
  };

  return (
    <Form
      form={form}
      onFinish={onFormSubmit}
      initialValues={{
        prefix_search: '',
        aimai_search: '',
      }}
    >
      <Flex
        className={styles.list_karute_page_table_header}
        gap="middle"
        justify="space-between"
        align="end"
      >
        <div className={styles.inputRow}>
          <Flex align="flex-start" justify="flex-start" gap="middle">
            <AppFormItem name="prefix_search" className={styles.input_common}>
              <AppInput placeholder="氏名・カナ、番号（前方一致）" disabled={isFetching} />
            </AppFormItem>
            <AppFormItem name="aimai_search" className={styles.input_common}>
              <AppInput placeholder="氏名・カナ、番号（あいまい）" disabled={isFetching} />
            </AppFormItem>
          </Flex>
          <Flex align="flex-start" justify="flex-start" gap="middle" className="mt-4">
            <AppFormItem name={'status'} className={styles.input_common}>
              <AppSelect
                placeholder={'ステータス'}
                options={convertMapToOptions(karuteStatusMap)}
                disabled={isFetching}
                className={styles.appSelect}
              />
            </AppFormItem>
            <AppFormItem
              name="first_submit"
              className={styles.input_common}
              style={{ width: '309px' }}
            >
              <AppRangeDatePicker
                showTime={{ format: TIME_FORMATS.HH_MM }}
                format={DATE_FORMATS.DATE_HOUR_MINUTE}
                className={styles.datePicker}
                singlePlaceholder="登録日時(From ~ to)"
                placeholder={['From', 'To']}
              />
            </AppFormItem>
            <AppFormItem name="latest_treatment" className={styles.input_common}>
              <AppRangeDatePicker
                format={DATE_FORMATS.DATE}
                className={styles.datePicker}
                singlePlaceholder="最終施術日(From ~ to)"
                style={{ padding: '10px 4px 10px 12px' }}
                placeholder={['From', 'To']}
              />
            </AppFormItem>
          </Flex>
        </div>
        <Flex className={styles.buttonRow} gap={'small'}>
          <Button
            customType={ButtonType.PRIMARY}
            customSize="md"
            htmlType="submit"
            loading={isFetching}
          >
            検索
          </Button>
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            customSize="md"
            onClick={onClear}
            disabled={isFetching}
          >
            <Icon name="retry" />
            クリア
          </Button>
        </Flex>
      </Flex>
    </Form>
  );
}
