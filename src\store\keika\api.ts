import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';
import { APIResponse, ListResponse } from '@/types/interface';
import { HistoryDetailKeika, KarutesParams, MainKeika } from '../karute/type';
import { ConditionCheckCreateKeika } from './type';

type GetMainKeikasArg = KarutesParams & { patient_id: number };

export const keikaApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,
    getMainKeikas: build.query<APIResponse<ListResponse<MainKeika>>, GetMainKeikasArg>({
      query: ({ patient_id, ...params }) => ({
        url: '/api/keikas',
        method: 'GET',
        params,
      }),

      providesTags: result =>
        result?.data?.data
          ? [
              ...result.data.data.map(item => ({ type: 'mainKeika' as const, id: item.keika_id })),
              { type: 'mainKeika' as const, id: 'LIST' },
            ]
          : [{ type: 'mainKeika' as const, id: 'LIST' }],

      async onQueryStarted({ karute_id, service_id, patient_id }, { dispatch }) {
        dispatch(
          keikaApi.endpoints.getConditionCheckCreateKeika.initiate(
            {
              karute_id: Number(karute_id),
              service_id: Number(service_id),
              patient_id: Number(patient_id),
            },
            { forceRefetch: true }
          )
        );
      },
    }),

    getHistoryKeikas: build.query<
      APIResponse<ListResponse<HistoryDetailKeika>>,
      { keika_id: number; limit?: number }
    >({
      query: params => {
        return {
          url: `/api/keikas/${params.keika_id}/history/${
            params.limit ? `?limit=${params.limit}` : ''
          }`,
          method: 'GET',
        };
      },
      providesTags: (_result, _error, { keika_id }) => [
        { type: 'historyKeika' as const, id: keika_id },
      ],
    }),

    getConditionCheckCreateKeika: build.query<
      APIResponse<ConditionCheckCreateKeika>,
      { karute_id: number; service_id: number; patient_id: number }
    >({
      query: params => {
        return {
          url: `/api/keikas/check-create`,
          method: 'GET',
          params,
        };
      },
      providesTags: () => [{ type: 'conditionCheckCreateKeika' as const }],
    }),
  }),
});
export const {
  useGetMainKeikasQuery,
  useGetHistoryKeikasQuery,
  useGetConditionCheckCreateKeikaQuery,
} = keikaApi;
