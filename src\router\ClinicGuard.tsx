import OnlyHeaderLayout from '@/components/layouts/OnlyHeaderLayout';
import { useAppSelector } from '@/config/redux/store';
import { ClinicNotFoundPage } from '@/pages/error-pages/ErrorPages';
import React from 'react';
import { Outlet, useParams } from 'react-router-dom';

const ClinicGuard: React.FC = () => {
  const { clinicCd } = useParams<{ clinicCd?: string }>();
  const clinics = useAppSelector(state => state.auth.clinics);
  if (!clinicCd) {
    return (
      <OnlyHeaderLayout>
        <ClinicNotFoundPage />
      </OnlyHeaderLayout>
    );
  }

  if (!clinics || clinics.length === 0) {
    return (
      <OnlyHeaderLayout>
        <ClinicNotFoundPage />
      </OnlyHeaderLayout>
    );
  }

  const isAllowed = clinics.some(c => c.clinic_cd === clinicCd);
  if (!isAllowed) {
    return (
      <OnlyHeaderLayout>
        <ClinicNotFoundPage />
      </OnlyHeaderLayout>
    );
  }

  return <Outlet />;
};

export default ClinicGuard;
