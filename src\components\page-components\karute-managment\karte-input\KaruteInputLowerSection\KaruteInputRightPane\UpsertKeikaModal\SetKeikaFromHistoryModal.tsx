// Screen: K201_経過記録の履歴からセット;
import { AppModal } from '@/components/common/core/Modal';
import styles from '../../KaruteInputLowerSection.module.scss';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import { Flex, Form } from 'antd';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppInput } from '@/components/common/core/Input';
import Button from '@/components/common/core/Button';
import { ButtonType, EKaruteUpsertType, ESort, Order } from '@/types/enum';
import Table, { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import {
  // PAGINATION_THRESHOLD,
  routerPaths,
} from '@/types/constants';
import { useEffect, useState } from 'react';
import Icon from '@/components/common/core/Icon';

import { useGetKeikaHistoryQuery, useLazyGetTotalKeikaHistoryItemQuery } from '@/store/karute/api';
import { useParams } from 'react-router-dom';
import { KeikaDetailHistoryItem } from '@/store/karute/type';
import clsx from 'clsx';
import { clearSpace } from '@/utils';
const PAGINATION_THRESHOLD = 20;

const SetFromHistoryKeikaHeader = '経過記録の履歴からセット' as const;

type SetFromHistoryParams = {
  injury_name?: string;
  search?: string;
};

export function SetKeikaFromHistoryModal({
  handleClose,
  handleSubmit,
}: {
  handleClose: () => void;
  handleSubmit: (item: KeikaDetailHistoryItem) => void;
}) {
  const [form] = Form.useForm<SetFromHistoryParams>();
  const { clinicCd, serviceId, id: karuteId } = useParams();

  const [selectedItem, setSelectedItem] = useState<KeikaDetailHistoryItem | null>(null);
  const [searchParams, setSearchPrams] = useState<{
    page: number;
    injury_name?: string;
    search?: string;
    order_by?: string;
    order_type?: Order;
  }>({ page: 1 });
  const { data: keikaHistory, isFetching } = useGetKeikaHistoryQuery(
    {
      service_id: serviceId!,
      limit: PAGINATION_THRESHOLD,
      ...searchParams,
    },
    {
      skip: !serviceId,
    }
  );

  const [lazyGetTotalKeikaItem, { data: totalKeikaItem, isFetching: isFetchingTotalKeikaItem }] =
    useLazyGetTotalKeikaHistoryItemQuery();
  useEffect(() => {
    if (serviceId) {
      lazyGetTotalKeikaItem({ service_id: serviceId });
    }
  }, [serviceId]);
  useEffect(() => {
    if (
      !keikaHistory ||
      !totalKeikaItem ||
      !keikaHistory?.data?.total_items ||
      !totalKeikaItem?.data?.count ||
      isFetchingTotalKeikaItem
    ) {
      return;
    }
    if (keikaHistory?.data?.total_items > totalKeikaItem?.data?.count) {
      lazyGetTotalKeikaItem({ service_id: serviceId! });
    }
  }, [keikaHistory, totalKeikaItem]);
  const handlePageChange = (page: number) => {
    setSearchPrams({
      ...searchParams,
      page,
    });
  };
  const handleSortChange = (
    sortData: {
      order_by: string;
      order_type: ESort.ASC | ESort.DESC;
    }[]
  ) => {
    const newSort = sortData.length > 0 ? sortData[0] : null;
    if (!newSort || !newSort.order_type) {
      setSearchPrams({
        ...searchParams,
        order_by: undefined,
        order_type: undefined,
      });
    } else {
      setSearchPrams({
        ...searchParams,
        order_by: newSort.order_by,
        order_type: newSort.order_type,
      });
    }
  };
  const showDetailItem = (record: KeikaDetailHistoryItem) => {
    // TODO: show detail tab
    if (!clinicCd || !karuteId) return;
    const windowPath = window.location.pathname.split(`/karute-management`)[0];

    const path = routerPaths.karuteManagement.karuteUpsertModalSegment
      .replace(':upsertType', EKaruteUpsertType.KEIKA)
      .replace(':upsertMode', `${record.keika_id}_${record.disease_base_id}`);
    const fullPath = `${windowPath}/karute-management/${record.karute_id!}/${serviceId}${path}`;
    window.open(fullPath, '_blank', 'noopener,noreferrer');
  };

  const onFormSearchSubmit = (values: SetFromHistoryParams) => {
    if (isFetching) {
      return;
    }
    setSearchPrams({
      ...values,
      injury_name: clearSpace(values.injury_name),
      search: clearSpace(values.search),
      page: 1,
    });
    setSelectedItem(null);
  };
  const handleClear = () => {
    if (isFetching) {
      return;
    }

    setSelectedItem(null);
    form.setFieldsValue({
      injury_name: '',
      search: '',
    });
  };
  const onHandleSubmit = () => {
    if (selectedItem) {
      handleSubmit(selectedItem);
    }
  };

  const columns: CustomColumnType<KeikaDetailHistoryItem>[] = [
    {
      title: '問題名',
      dataIndex: 'injury_name',
      sortable: true,
      sortKey: 'injury_name',
      width: 90,
    },
    {
      title: '問診情報（S）',
      dataIndex: 'subjective',
      sortable: true,
      sortKey: 'subjective',
      width: 160,
    },
    // {
    //   title: '現病歴（S）',
    //   dataIndex: 'present_illness',
    //   sortable: true,
    //   sortKey: 'present_illness',
    //   width: 160,
    // },
    {
      title: '所見（O）',
      dataIndex: 'objective',
      sortable: true,
      sortKey: 'objective',
      width: 160,
    },
    {
      title: '分析（A）',
      dataIndex: 'assessment',
      sortable: true,
      sortKey: 'assessment',
      width: 160,
    },
    {
      title: '計画（P）',
      dataIndex: 'plan',
      sortable: true,
      sortKey: 'plan',
      width: 160,
    },
    {
      title: '施術（T）',
      dataIndex: 'treatment',
      sortable: true,
      sortKey: 'treatment',
      // render: (_, record) => {
      //   return (
      //     <>
      //       <p>{record.payment_type_label}</p>
      //       <p>{record.treatment}</p>
      //     </>
      //   );
      // },
      width: 160,
    },
    {
      title: '備考（R）',
      dataIndex: 'remarks',
      sortable: true,
      sortKey: 'remarks',
      width: 160,
    },
    {
      title: '患者名',
      dataIndex: 'patient_name',
      sortable: true,
      sortKey: 'patient_name',
      width: 110,
    },
    {
      title: '施術師',
      dataIndex: 'doctor_name',
      sortable: true,
      sortKey: 'doctor_name',
      width: 80,
    },
    {
      title: '',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 50,
      render: record => (
        <button
          onClick={() => {
            showDetailItem(record as KeikaDetailHistoryItem);
          }}
        >
          <Icon name="eyeFilled" color="#609CAD" />
        </button>
      ),
      onCell: () => {
        return {
          onDoubleClick: e => {
            e.preventDefault();
            e.stopPropagation();
          },
        };
      },
    },
  ];

  return (
    <AppModal
      width="1000px"
      className={styles.set_from_history_modal}
      onCancel={handleClose}
      isPadding={false}
      open
    >
      <div className={styles.set_from_history_modal_header}>
        <ModalTitle title={SetFromHistoryKeikaHeader} />
        <Form
          form={form}
          onFinish={onFormSearchSubmit}
          initialValues={{
            injury_name: null,
            search: null,
          }}
        >
          <Flex gap="small" align="flex-start">
            <AppFormItem className={styles.input_common} name="injury_name">
              <AppInput placeholder="問題名" size="small" disabled={isFetching} />
            </AppFormItem>
            <AppFormItem className={styles.input_common} name="search">
              <AppInput placeholder="氏名・カナ、患者番号" size="small" disabled={isFetching} />
            </AppFormItem>
            <Button
              customType={ButtonType.PRIMARY}
              htmlType="submit"
              loading={isFetching}
              customSize="md"
              className={styles.submit_btn}
            >
              検索
            </Button>
            <Button
              customType={ButtonType.SECONDARY_COLOR}
              onClick={handleClear}
              customSize="md"
              className={styles.submit_btn}
              disabled={isFetching}
            >
              <Icon name="retry" height={20} width={20} />
              クリア
            </Button>
          </Flex>
        </Form>
      </div>
      <Table<KeikaDetailHistoryItem>
        columns={columns}
        rowKey={record => record.keika_detail_id}
        dataSource={keikaHistory?.data?.data ?? []}
        pagination={
          keikaHistory && (keikaHistory?.data?.total ?? 0) > PAGINATION_THRESHOLD
            ? {
                current: keikaHistory?.data?.current_page ?? 1,
                pageSize: keikaHistory?.data?.per_page ?? PAGINATION_THRESHOLD,
                showSizeChanger: false,
                total: keikaHistory?.data?.total ?? 0,
                onChange: handlePageChange,
              }
            : false
        }
        onSortChange={handleSortChange}
        scroll={{ y: 540 }}
        loading={isFetching}
        className={styles.set_from_history_modal_table}
        rowClassName={record =>
          selectedItem && record.keika_detail_id === selectedItem?.keika_detail_id
            ? clsx('ant-table-row-selected', styles.fixedRowHeight)
            : styles.fixedRowHeight
        }
        onRow={record => {
          return {
            onClick: () => {
              setSelectedItem(record);
            },
            onDoubleClick: () => {
              onHandleSubmit();
            },
            style: {
              cursor: 'pointer',
            },
          };
        }}
        total={totalKeikaItem?.data?.count ?? keikaHistory?.data?.total}
        style={{ marginBottom: keikaHistory?.data?.total ? '0' : '20px' }}
      />
      {!!keikaHistory?.data?.total && (
        <Flex
          className={clsx(
            styles.set_from_history_modal_footer,
            (keikaHistory?.data?.total ?? 0) > PAGINATION_THRESHOLD ? 'mt-2' : 'mt-6'
          )}
          gap="middle"
          justify="end"
        >
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            onClick={handleClose}
            className="btn-modal-width"
            customSize="lg"
          >
            キャンセル
          </Button>
          <Button
            customType={ButtonType.PRIMARY}
            className="btn-modal-width"
            onClick={onHandleSubmit}
            disabled={!selectedItem || isFetching}
            customSize="lg"
          >
            適用
          </Button>
        </Flex>
      )}
    </AppModal>
  );
}
