import { ERemindType } from '@/types/enum';
import { BookingStatusMapping, PAYMENT_COURSE_TYPE } from './../../types/constants/index';

export type BookingResponse = {
  booking_cd: number;
  booking_no: number;
  booking_from: string;
  booking_to: string;
  course_id: number;
  practitioner_account_id: number;
  status: keyof typeof BookingStatusMapping | string;
  booking_id: number;
  practitioner_account_name: string;
  course_name: string;
};

export type BookingNextTimePayload = {
  payment_course_type: (typeof PAYMENT_COURSE_TYPE)[keyof typeof PAYMENT_COURSE_TYPE];
  course_id: number;
  booking_from: string;
  booking_to: string;
  booking_remind_type: ERemindType;
  remind_email?: string;
  practitioner_id?: number;
};
