import useGetCourseServices from '@/components/common/modal/SaveReceiveModal/useGetCourseServices';
import { useAppSelector } from '@/config/redux/store';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useGetDetailsPatientQuery } from '@/store/patient/api';
import { PatientItem } from '@/store/patient/type';
import { useGetVisitQuery, useUpdateVisitMutation } from '@/store/visit/api';
import { getSelection } from '@/store/visit/selector';
import { CourseService, Doctor, VisitItem } from '@/store/visit/type';
import { REGEX_NUMBER, routerPaths, STATUS_CODES } from '@/types/constants';
import { EditReceptionSchema } from '@/types/interface';
import { showFlashNotice } from '@/utils/flashNotice';
import { FormInstance } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React, { createContext, ReactNode, useCallback, useContext, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
interface VisitDetailContextType {
  visitDetail?: VisitItem | null;
  patient?: PatientItem | null;
  treaterList?: Doctor[];
  treeServices?: CourseService[];

  formRef: React.RefObject<any>;

  isSubmitting: boolean;
  isFetching: boolean;

  handleClickSubmitForm: () => void;
  handleFinish: (values: EditReceptionSchema) => void;
  getInsuranceType: (hoken_type?: number) => string;
  navigateToDashboard: () => void;
  form: FormInstance<EditReceptionSchema>;
}

// Tạo context
const VisitDetailContext = createContext<VisitDetailContextType | undefined>(undefined);

// Props cho Provider
interface VisitDetailProviderProps {
  children: ReactNode;
}

// Provider component
export const VisitDetailProvider: React.FC<VisitDetailProviderProps> = ({ children }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const formRef = useRef<any>(null);
  const storedSelectionState = useSelector(getSelection);
  const [form] = useForm<EditReceptionSchema>();
  // API calls
  const [updateVisitMutation, { isLoading: isSubmitting }] = useUpdateVisitMutation();
  const { data: visitDetail, isLoading: isFetchingVisit } = useGetVisitQuery(Number(id), {
    skip: !id,
  });

  const selectionOptions = useMemo(
    () => ({
      status: storedSelectionState.selection.status || [],
      insurance_types: storedSelectionState.selection.insurance_types || [],
      treaters: [],
      courses: storedSelectionState.selection.courses || [],
    }),
    [storedSelectionState.selection]
  );

  const { data: patient, isLoading: isFetchingPatient } = useGetDetailsPatientQuery(
    Number(visitDetail?.data?.patient?.patient_id),
    {
      skip: !visitDetail?.data?.patient?.patient_id || isFetchingVisit,
    }
  );

  const { doctors: treaterData, isLoading } = useAppSelector(state => state.sharedData);

  const { treeServices, isFetching: isFetchingCourse } = useGetCourseServices();
  const { getPath } = useRouterPath();
  const dashboardPath = getPath(routerPaths.dashboard);

  // Actions
  const handleClickSubmitForm = useCallback(() => {
    formRef.current?.submit();
  }, []);

  const navigateToDashboard = useCallback(() => {
    navigate(dashboardPath);
  }, [navigate]);

  const handleFinish = useCallback(
    (values: EditReceptionSchema) => {
      if (
        isFetchingVisit ||
        isSubmitting ||
        isFetchingPatient ||
        !visitDetail?.data ||
        !patient?.data
      ) {
        return;
      }
      const allCourseIds = Object.values(values.course_groups || {}).flat();
      const flatCourseIds = allCourseIds.filter(id => REGEX_NUMBER.test(id)).map(id => Number(id));
      updateVisitMutation({
        id: Number(id),
        payload: {
          visit_type: values.visit_type,
          course_groups: flatCourseIds, //  only course_id is number other is text

          practitioners: values.practitioners.map(p => p.value),
        },
      }).then(res => {
        if (res?.data?.status === STATUS_CODES.INVALID_FIELD) {
          const fields = Object.entries<string[]>(res?.data?.data).map(([name, msgs]) => ({
            name,
            errors: msgs,
          }));
          form.setFields(fields as any);
          return;
        }
        if (res.data?.status === STATUS_CODES.OK) {
          showFlashNotice({
            type: 'success',
            message: '受付情報を更新しました。',
          });
          navigate(dashboardPath);
        }
      });
    },
    [
      id,
      isFetchingVisit,
      isSubmitting,
      isFetchingPatient,
      visitDetail,
      patient,
      updateVisitMutation,
      navigate,
    ]
  );
  const getInsuranceType = useCallback(
    (hoken_type?: number) => {
      if (!hoken_type) return '';
      const hoken = selectionOptions.insurance_types.find(i =>
        String(i.value).split(',').includes(String(hoken_type))
      );
      return hoken ? hoken.label : '';
    },
    [selectionOptions.insurance_types]
  );
  const isFetching = !!(isFetchingVisit || isFetchingPatient || isLoading || isFetchingCourse);

  const contextValue = useMemo(
    () => ({
      visitDetail: visitDetail?.data,
      patient: patient?.data,
      treaterList: treaterData ?? [],
      treeServices,
      formRef,
      isSubmitting,
      isFetching,
      handleClickSubmitForm,
      handleFinish,
      getInsuranceType,
      navigateToDashboard,
      form,
    }),
    [
      visitDetail,
      patient,
      treaterData,
      treeServices,
      isSubmitting,
      isFetching,
      handleClickSubmitForm,
      handleFinish,
      navigateToDashboard,
      getInsuranceType,
      form,
    ]
  );

  return <VisitDetailContext.Provider value={contextValue}>{children}</VisitDetailContext.Provider>;
};

export const useVisitDetail = (): VisitDetailContextType => {
  const context = useContext(VisitDetailContext);
  if (context === undefined) {
    throw new Error('useVisitDetail must be used within a VisitDetailProvider');
  }
  return context;
};
