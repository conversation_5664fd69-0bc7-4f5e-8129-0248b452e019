import Button from '@/components/common/core/Button';
import Table from '@/components/common/core/CommonTable';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { AppDatePicker } from '@/components/common/core/Datepicker';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppModal } from '@/components/common/core/Modal';
import { MultiSelect } from '@/components/common/core/MultiSelect';
import { QRScan } from '@/components/common/core/QRScan/QR-Scan';
import AppSaveReceiveModal from '@/components/common/modal/SaveReceiveModal/SaveReceiveModal';
import { RootState, useAppSelector } from '@/config/redux/store';
import { useInsuranceWarning } from '@/hooks/useInsuranceWarning';
import { usePatientNumberWarning } from '@/hooks/usePatientNumberWarning';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useScreen } from '@/hooks/useScreen';
import { useLazyCheckPatientCdQuery } from '@/store/patient/api';
import {
  useChangeVisitStatusInProgressMutation,
  useLazyGetListVisitQuery,
} from '@/store/visit/api';
import { getSelection } from '@/store/visit/selector';
import { OptionItem, Visit, VisitListParams, VisitRegistrationPayload } from '@/store/visit/type';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  NOTICE_COMMON_MESSAGE,
  PAGINATION_THRESHOLD,
  routerPaths,
  VISIT_TABLE_ACTION,
} from '@/types/constants';
import { ButtonType, ESort } from '@/types/enum';
import { RegisterEvents } from '@/types/interface';
import { showFlashNotice } from '@/utils';
import { getDisabledAction, transformTreatersToOptions } from '@/utils/helper';
import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Flex } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import styles from './DashboardContent.module.scss';
type FORM_STATE = 'visit_registration';
type Props = {
  filteredDate: dayjs.Dayjs;
  setFilteredDate: React.Dispatch<React.SetStateAction<dayjs.Dayjs>>;
};

export default function DashboardContent({
  registerEvents,
  filteredDate,
  setFilteredDate,
}: RegisterEvents & Props) {
  const { screen } = useScreen();
  const { clinicCd } = useParams();

  const storedSelectionState = useSelector(getSelection);
  const [searchInput, setSearchInput] = useState<string>('');
  const [selectionOptions, setSelectionOptions] = useState<{
    status: OptionItem[];
    insurance_types: OptionItem[];
    treaters: OptionItem[];
    courses: OptionItem[];
  }>({
    status: storedSelectionState.selection.status || [],
    insurance_types: storedSelectionState.selection.insurance_types || [],
    treaters: [],
    courses: storedSelectionState.selection.courses || [],
  });
  useEffect(() => {
    setSelectionOptions({
      status: storedSelectionState.selection.status || [],
      insurance_types: storedSelectionState.selection.insurance_types || [],
      treaters: [],
      courses: storedSelectionState.selection.courses || [],
    });
  }, [storedSelectionState]);
  const [params, setParams] = useState<VisitListParams>({
    search: '',
    page: 1,
    limit: PAGINATION_THRESHOLD,
    status: null,
    practitioners: null,
    hoken_type: null,
    courses: null,
    visit_date: dayjs().format('YYYY-MM-DD'),
    order_by: null,
    order_type: null,
  });
  // const [showModalVisitRegistration, setShowModalVisitRegistration] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const formState = (searchParams.get('form_state') as FORM_STATE) || '';
  const [modalInitialData, setModalInitialData] = useState<any>(undefined);
  const [openQRScanModal, setOpenQRScanModal] = useState<boolean>(false);
  const masterData = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const { showInsuranceWarning } = useInsuranceWarning();
  const { showPatientNumberWarning } = usePatientNumberWarning();
  const [fetchVisit, { data: visitData, isFetching: isLoadingVisit }] = useLazyGetListVisitQuery();
  const [changeVisitStatusInProgress] = useChangeVisitStatusInProgressMutation();
  const { doctors: doctorsData, isLoading: isFetchingDoctorData } = useAppSelector(
    state => state.sharedData
  );

  const { getPath } = useRouterPath();
  const totalRecords = visitData?.data?.total || 0;

  const visitUpdatePath = (id: string | number) =>
    getPath(routerPaths.visitManagement.visitUpdate(id));

  const [checkPatientCd] = useLazyCheckPatientCdQuery();
  const patientUpdatePath = (id: string | number) =>
    getPath(routerPaths.patientManagement.patientUpdate(id));
  const karuteUpdatePath = (karute_id: string | number) => {
    return routerPaths.karuteManagement.karuteUpdate
      .replace(':clinicCd', String(clinicCd))
      .replace(':id', String(karute_id));
  };
  const handleTableVisitViewAndEdit = (record: Visit) => {
    if (record.visit_id) {
      navigate(visitUpdatePath(record.visit_id));
    }
  };
  const handleTableAddKarute = (record: Visit) => {
    if (record.karute_id) {
      navigate(karuteUpdatePath(record.karute_id));
    }
  };

  const registerVisit = (record: Visit | null) => {
    setModalInitialData(record);
    setSearchParams({ form_state: 'visit_registration' });
  };

  const scanQRCode = () => {
    return setOpenQRScanModal(true);
  };

  const handleCallbackQRScan = (data: VisitRegistrationPayload) => {
    if ('store_id' in data && Number(data.store_id) !== Number(masterData.currentClinicId)) {
      setOpenQRScanModal(false);
      showFlashNotice({ type: 'error', message: NOTICE_COMMON_MESSAGE.QR_SCAN_ERROR });
    } else {
      setOpenQRScanModal(false);
      const successMsg = '予約情報を取得しました。';
      showFlashNotice({ type: 'success', message: successMsg });
      setModalInitialData({
        ...data,
      });

      setSearchParams({ form_state: 'visit_registration', from: 'qr-scan' });
    }
  };

  const handleTablePatientViewAndEdit = useCallback(
    async (record: Visit) => {
      await checkPatientCd(record.patient_id).then(data => {
        if (data.data?.data?.has_patient_code) {
          navigate(patientUpdatePath(record.patient_id));
          return;
        } else {
          showPatientNumberWarning(record.patient_id, () => {
            navigate(patientUpdatePath(record.patient_id));
          });
        }
        return;
      });
    },
    [navigate, showPatientNumberWarning]
  );

  const handleTableRegisterVisit = useCallback(
    (record: Visit) => {
      if (record.hoken_type == null) {
        showInsuranceWarning(() => {
          registerVisit(record);
        });
      } else {
        registerVisit(record);
      }
    },
    [showInsuranceWarning, registerVisit]
  );

  const handleSortChange = (sortData: { order_by: string; order_type: ESort }[]) => {
    setParams(prev => ({
      ...prev,
      order_by: sortData[0]?.order_by,
      order_type: sortData[0]?.order_type,
    }));
  };

  const getBookingTime = (record: Visit) => {
    let result = '';
    if (record.booking_id && record.booking_time_from && record.booking_time_to) {
      result = `${record.booking_time_from} - ${record.booking_time_to}`;
    }
    return result;
  };

  const getTreaterString = (record: Visit) => {
    return record?.practitioner?.split(',')?.join(COMMA_SEPARATED);
  };

  const getInsurerString = useCallback(
    (record: Visit) => {
      if (!record?.hoken_type) return '';
      const hoken = selectionOptions.insurance_types.find(i =>
        String(i.value).split(',').includes(String(record?.hoken_type))
      );
      return hoken ? hoken.label : '';
    },
    [selectionOptions.insurance_types]
  );

  const getCourseString = (record: Visit) => {
    let result = '';
    if (record.course && record.course.length > 0) {
      result = record.course.replace(',', '、');
    }
    return result;
  };

  const getStatusName = (status: number) => {
    const statusItem = selectionOptions.status.find(s => s.value === status);
    return (
      <div className={`statusTag`}>
        <p className={`status-${status}`}>{statusItem?.label || ''}</p>
      </div>
    );
  };

  const handleSearch = () => {
    setParams(prev => ({ ...prev, search: searchInput, page: 1 }));
  };

  const confirmOnline = () => {
    // TODO Phase 2~3
  };
  const columns: CustomColumnType<Visit>[] = useMemo(
    () => [
      {
        title: 'No',
        dataIndex: 'index',
        key: 'index',
        align: 'right',
        width: 60,
        render: (_: any, record: Visit, index: number) =>
          index + 1 + (visitData?.data?.per_page ?? 0) * ((visitData?.data?.current_page ?? 1) - 1),
      },
      {
        title: '患者番号',
        dataIndex: 'patientNo',
        key: 'patientNo',
        align: 'right',
        width: 100,
        render: (_: any, record: Visit) => record?.patient_cd || '',
        sortable: true,
        sortKey: 'patient_cd',
      },
      {
        title: '氏名（漢字）',
        dataIndex: 'fullName',
        key: 'fullName',
        align: 'left',
        width: 140,
        render: (_: any, record: Visit) => record?.name || '',
        sortable: true,
        sortKey: 'name',
      },
      {
        title: '氏名（カナ）',
        dataIndex: 'kanaName',
        key: 'kanaName',
        align: 'left',
        width: 140,
        render: (_: any, record: Visit) => record?.kana || '',
        sortable: true,
        sortKey: 'kana',
      },
      {
        title: '時間帯予約',
        dataIndex: 'bookingTime',
        key: 'bookingTime',
        align: 'right',
        width: 140,
        render: (_: any, record: Visit) => getBookingTime(record),
        sortable: true,
        sortKey: 'booking_time',
      },
      {
        title: '受付日時',
        dataIndex: 'reception_date',
        key: 'reception_date',
        align: 'right',
        width: 150,
        render: (_: any, record: Visit) => record?.visit_created_at || '',
        sortable: true,
        sortKey: 'visit_created_at',
      },
      {
        title: '性別',
        dataIndex: 'gender',
        key: 'gender',
        align: 'left',
        width: 68,
        render: (_: any, record: Visit) => record?.gender_text || '',
        sortable: true,
        sortKey: 'gender',
      },
      {
        title: '生年月日',
        dataIndex: 'dob_formated',
        key: 'dob_formated',
        align: 'right',
        width: 150,
        render: (_: any, record: Visit) => record?.birthday,
        sortable: true,
        sortKey: 'birthday',
      },
      {
        title: 'ステータス',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        width: 120,
        render: (_: any, record: Visit) => <>{getStatusName(record?.dashboard_status || 0)}</>,
        sortable: true,
        sortKey: 'status',
      },
      {
        title: '施術師',
        dataIndex: 'treater',
        key: 'treater',
        align: 'left',
        width: 150,
        render: (_: any, record: Visit) => getTreaterString(record),
        sortable: true,
        sortKey: 'treater_id',
      },
      {
        title: '保険種類',
        dataIndex: 'insurer_name',
        key: 'insurer_name',
        align: 'left',
        width: 120,
        render: (_: any, record: Visit) => getInsurerString(record),
        sortable: true,
        sortKey: 'hoken_type',
      },
      {
        title: '施術区分',
        dataIndex: 'course',
        key: 'course',
        align: 'left',
        width: 150,
        render: (_: any, record: Visit) => getCourseString(record),
        sortable: true,
        sortKey: 'course_id',
      },
      {
        title: '',
        key: 'actions',
        align: 'center',
        width: 50,
        // fixed: screen !== 'mobile' ? 'right' : undefined,
        render: (_: any, record: Visit) => (
          <Dropdown
            menu={{
              items: [
                {
                  key: VISIT_TABLE_ACTION.REGISTER_VISIT,
                  label: (
                    <Flex align="center" gap={6}>
                      <Icon name="addSquare" height={20} width={20} />
                      <span>受付登録</span>
                    </Flex>
                  ),
                  disabled: getDisabledAction(
                    record.dashboard_status,
                    VISIT_TABLE_ACTION.REGISTER_VISIT,
                    filteredDate
                  ),
                  onClick: () => handleTableRegisterVisit(record),
                },
                {
                  key: VISIT_TABLE_ACTION.PATIENT_VIEW_AND_EDIT,
                  label: (
                    <Flex align="center" gap={6}>
                      <Icon name="eye" height={20} width={20} />
                      <span>患者詳細・編集</span>
                    </Flex>
                  ),
                  disabled: getDisabledAction(
                    record.dashboard_status,
                    VISIT_TABLE_ACTION.PATIENT_VIEW_AND_EDIT,
                    filteredDate
                  ),
                  onClick: () => handleTablePatientViewAndEdit(record),
                },
                {
                  key: VISIT_TABLE_ACTION.ADD_KARUTE,
                  label: (
                    <Flex align="center" gap={6}>
                      <Icon name="doctor" height={20} width={20} />
                      <span>カルテ入力</span>
                    </Flex>
                  ),
                  disabled: !record?.karute_input_enabled,
                  onClick: () => handleTableAddKarute(record),
                },
                {
                  key: VISIT_TABLE_ACTION.VISIT_VIEW_AND_EDIT,
                  label: (
                    <Flex align="center" gap={6}>
                      <Icon name="eye" height={20} width={20} />
                      <span>受付詳細・編集</span>
                    </Flex>
                  ),
                  disabled: getDisabledAction(
                    record.dashboard_status,
                    VISIT_TABLE_ACTION.VISIT_VIEW_AND_EDIT,
                    filteredDate
                  ),
                  onClick: () => handleTableVisitViewAndEdit(record),
                },
                // {
                //   // TODO: Pending
                //   key: VISIT_TABLE_ACTION.VISIT_DETAIL_EDIT_AND_DELETE,
                //   label: (
                //     <Flex align="center" gap={6}>
                //       <Icon name="edit" height={20} width={20} />
                //       <span>予約編集・削除</span>
                //     </Flex>
                //   ),
                // },
              ],
            }}
            trigger={['hover']}
          >
            <MoreOutlined style={{ fontSize: 18, cursor: 'pointer' }} />
          </Dropdown>
        ),
      },
    ],
    [handleTableRegisterVisit, getDisabledAction, filteredDate]
  );

  useEffect(() => {
    if (registerEvents) {
      registerEvents({ registerVisit: () => registerVisit(null), confirmOnline });
      registerEvents({ scanQRCode: scanQRCode });
    }
  }, [registerEvents]);

  // Fetch visit
  useEffect(() => {
    fetchVisit(params);
  }, [params]);

  useEffect(() => {
    if (doctorsData) {
      setSelectionOptions(prev => ({
        ...prev,
        treaters: transformTreatersToOptions(doctorsData),
      }));
    }
  }, [doctorsData]);

  return (
    <>
      <div className={clsx(styles.pageContent, 'fs12-regular')}>
        <Flex justify="space-between" wrap="wrap" align="center" style={{ marginBottom: '16px' }}>
          <Flex gap={8} align="center" style={{ marginBottom: '8px' }}>
            <AppInput
              placeholder="氏名・カナ、患者番号"
              style={{
                height: '40px',
                padding: 'unset !important',
                paddingRight: '12px',
                backgroundColor: 'white',
              }}
              size="small"
              suffix={<Icon name="search" height={20} width={20} />}
              value={searchInput}
              onChange={e => setSearchInput(e.target.value)}
              onPressEnter={handleSearch}
            />
            <Button
              customSize="md"
              style={{ width: '100px', flexShrink: 0 }}
              loading={isLoadingVisit}
              onClick={handleSearch}
            >
              検索
            </Button>
            <Button
              customSize="md"
              style={{ width: '100px', flexShrink: 0 }}
              customType={ButtonType.SECONDARY_COLOR}
              onClick={() => {
                setSearchInput('');
              }}
            >
              <Icon name="retry" height={20} width={20} />
              クリア
            </Button>
          </Flex>
          <AppDatePicker
            className={styles.datePicker}
            style={{
              height: '40px',
              width: screen === 'mobile' ? '100%' : '200px',
              backgroundColor: 'white',
            }}
            format={DATE_FORMATS.DATE}
            value={params.visit_date ? dayjs(params.visit_date) : null}
            onChange={date => {
              setParams(prev => ({
                ...prev,
                visit_date: date?.format('YYYY-MM-DD') || '',
                page: 1,
                search: searchInput,
              }));

              setFilteredDate(date);
            }}
            allowClear={false}
            iconPosition="prefix"
          />
        </Flex>
        <div className={styles.tableWrapper}>
          <Flex align="center" justify="space-between" className={styles.tableHeader}>
            <Flex gap={8} className={styles.filters}>
              <MultiSelect
                inputProps={{
                  placeholder: 'ステータス',
                  ...(screen !== 'mobile' && {
                    style: {
                      height: '32px',
                      width: '140px',
                    },
                  }),
                  size: 'small',
                }}
                options={selectionOptions.status}
                value={params.status}
                onSelectChange={value => setParams(prev => ({ ...prev, status: value, page: 1 }))}
              />
              <MultiSelect
                inputProps={{
                  placeholder: '保険種類',
                  ...(screen !== 'mobile' && {
                    style: {
                      height: '32px',
                      width: '140px',
                    },
                  }),
                  size: 'small',
                }}
                options={selectionOptions.insurance_types}
                value={params.hoken_type}
                onSelectChange={value => {
                  setParams(prev => ({ ...prev, hoken_type: value, page: 1 }));
                }}
              />
              <MultiSelect
                inputProps={{
                  placeholder: '施術者',
                  size: 'small',
                  ...(screen !== 'mobile' && {
                    style: {
                      height: '32px',
                      width: '140px',
                    },
                  }),
                }}
                options={selectionOptions.treaters}
                value={params.practitioners}
                onSelectChange={value =>
                  setParams(prev => ({ ...prev, practitioners: value, page: 1 }))
                }
              />
              <MultiSelect
                inputProps={{
                  placeholder: screen === 'desktop' ? '施術区分' : '受付区分',
                  size: 'small',
                  ...(screen !== 'mobile' && {
                    style: {
                      height: '32px',
                      width: '140px',
                    },
                  }),
                }}
                options={selectionOptions.courses}
                value={params.courses}
                onSelectChange={value => setParams(prev => ({ ...prev, courses: value, page: 1 }))}
              />
            </Flex>
            <div className={styles.recordCount}>
              人数: {visitData?.data?.total_filtered || 0}/{visitData?.data?.total_items || 0}人
            </div>
          </Flex>
          <Table
            rowKey={record => record.patient_id}
            columns={columns}
            dataSource={visitData?.data?.data || []}
            onSortChange={handleSortChange}
            loading={isLoadingVisit}
            total={visitData?.data?.total_items}
            pagination={
              totalRecords > PAGINATION_THRESHOLD
                ? {
                    current: params.page || 1,
                    pageSize: params.limit || PAGINATION_THRESHOLD,
                    total: totalRecords,
                    showSizeChanger: false,
                    onChange: (page: number, pageSize: number) =>
                      setParams(prev => ({ ...prev, page, limit: pageSize })),
                  }
                : false
            }
            scroll={{
              y: '500px',
            }}
          />
        </div>
      </div>
      {formState === 'visit_registration' && !dayjs(params.visit_date).isAfter(dayjs()) && (
        <AppSaveReceiveModal
          isModalOpen
          initialData={
            modalInitialData?.course_ids ||
            modalInitialData?.practitioner_ids ||
            modalInitialData?.booking_type
              ? {
                  ...modalInitialData,
                  course_ids: modalInitialData?.course_ids
                    ? typeof modalInitialData.course_ids === 'string'
                      ? modalInitialData?.course_ids?.split(',').map((item: string) => Number(item))
                      : modalInitialData?.course_ids
                    : undefined,
                  practitioner_ids: modalInitialData?.practitioner_ids
                    ? typeof modalInitialData.practitioner_ids === 'string'
                      ? modalInitialData?.practitioner_ids
                          ?.split(',')
                          .map((item: string) => Number(item))
                      : modalInitialData?.practitioner_ids
                    : undefined,
                  visit_type: modalInitialData?.booking_type ?? modalInitialData.visit_type,
                }
              : modalInitialData
          }
          doctorList={doctorsData || []}
          onCancel={() => setSearchParams({})}
          onSubmitVisit={() => fetchVisit(params)}
          isLoadingInitialData={isFetchingDoctorData}
          visitDate={params.visit_date}
        />
      )}
      {openQRScanModal && (
        <AppModal
          wrapClassName="qr-scan-modal"
          style={{
            height: screen === 'mobile' ? '100%' : '600px',
          }}
          mask={false}
          open={openQRScanModal}
          onCancel={() => setOpenQRScanModal(false)}
        >
          <QRScan closeModal={() => setOpenQRScanModal(false)} callback={handleCallbackQRScan} />
        </AppModal>
      )}
    </>
  );
}
