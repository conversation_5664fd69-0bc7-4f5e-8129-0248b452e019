import { EPaymentCourse, ROL<PERSON> } from '../enum';

export const PAGE_LIMIT_NUMBER = 20;
export const DATE_FORMATS = {
  DATE: 'YYYY/MM/DD',
  SUBMIT_DATE: 'YYYY-MM-DD',
  DATE_TIME: 'YYYY-MM-DD HH:mm:ss',
  DATE_TIME_CSV: 'YYYYMMDD_HHmm',
  DATE_HOUR_MINUTE: 'YYYY/MM/DD HH:mm',
  JAPEN_DATE_TIME: 'YYYY年MM月DD日 HH:mm',
  JAPAN_DATE: 'YYYY年MM月DD日',
  DDMMYYYYHHmm: 'DD/MM/YYYY HH:mm',
  JAPAN_DATE_WITH_OUT_DAY: 'YYYY年MM月',
} as const;
export const TIME_FORMATS = {
  HH_MM: 'HH:mm',
  HH_MM_SS: 'HH:mm:ss',
};

export const STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  INVALID_FIELD: 422,
};

export const MONDAI_TYPE_OPTIONS = {
  0: '全て',
  1: 'M',
  2: 'P',
  3: 'S',
};

export const MONDAI_STATUS_OPTION = {
  1: '活性',
  0: '非活性',
};

export const MONDAI_TYPE = {
  1: 'M',
  2: 'P',
  3: 'S',
};

export const ENV = {
  DOMAIN_URL: import.meta.env.VITE_DOMAIN_URL,
  MEDIX_SYNC_API: import.meta.env.VITE_MEDIX_SYNC_API,
};

export const ROLE_OPTIONS = [
  { value: ROLE.MEDIX_ADMIN, label: 'メディックス担当者' },
  { value: ROLE.COMPANY_ADMIN, label: '企業担当者' },
  { value: ROLE.STORE_ADMIN, label: '店舗担当者' },
  { value: ROLE.STAFF, label: 'スタッフ' },
];

export const PAGINATION_THRESHOLD = 100;

export const MAX_IMAGE_SIZE = 20;
export const ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png'];
export const ALLOWED_IMAGE_TYPES = ['image/jpg', 'image/jpeg', 'image/png'];
export const COMMA_SEPARATED = '、';
export const SPACE = '\u00A0'; // Non-breaking space

const DEFAULT_TRAUMA_TYPE = 1;
const WARNING_DATABASE_MSG = 'が未入力です。このまま登録してもよろしいでしょうか？';
const K107_SUFFIX_WARNING_FREE_INJURY_NAME =
  '　入力した負傷名は既に存在します。既存の負傷名を非活性にするか別の負傷名を入力してください。';
const K101_SUFFIX_WARNING_DUPLICATE_INJURY_NAME = {
  line1:
    '　入力された負傷名は、システムに登録されているものと一致しないため、保険適用での施術は行えません。',
  line2: '保険施術をご希望の場合は、マスタに登録されている負傷名からご選択ください。',
};
const SUBMIT_BTN = {
  CREATE: '登録',
  EDIT: '編集',
};

export const WARNING_CHANGE_MONDAI_SP_TO_M =
  '「S」または「P」種別から「M」種別への変更は、当問題のデータベースを登録する必要があります。変更する場合は、下記のサービスを選択して【続行】をクリックしてください。 ';
export const WARNING_CHANGE_MONDAI_M_TO_SP =
  '「M」種別から「S」または「P」種別への変更は、当問題に紐づいたデータベースの編集ができなくなります。本当に続行してもよろしいでしょうか？ ';

export const BOOKING_SUCCESS_MSG = '予約を登録しました。';

// K100
const databaseEmptyWarningField = [
  { priority: 1, key: 'subjective', label: '主訴（S）' }, // 主訴 (S)
  { priority: 2, key: 'present_illness', label: '現病歴（S）' }, // 現病歴 (S)
  { priority: 3, key: 'objective', label: '現症（O）', isCommon: true }, // 現症 (O)
  { priority: 4, key: 'assessment', label: '病態把握（A）' }, // 病態把握 (A)
  { priority: 5, key: 'plan', label: '施術計画（P）', isCommon: true }, // 施術計画 (P)
  { priority: 6, key: 'treatment', label: '施術（T）', isCommon: true }, // 計画 (T)
  { priority: 7, key: 'doctor_id', label: '施術師' }, // 施術師
];

// K200
const keikaEmptyWarningField = [
  { priority: 1, key: 'subjective', label: '問診情報（S）' }, // 問診情報（S）
  { priority: 2, key: 'objective', label: '所見（O）', isCommon: true }, // 所見（O）
  { priority: 3, key: 'assessment', label: '分析（A）' }, // 分析 (A)
  { priority: 4, key: 'plan', label: '計画（P）', isCommon: true }, // 計画（P）
  { priority: 5, key: 'treatment', label: '施術（T）', isCommon: true }, // 計画 (T)
  { priority: 6, key: 'doctor_id', label: '施術師' }, // 施術師
];

export {
  databaseEmptyWarningField,
  DEFAULT_TRAUMA_TYPE,
  K101_SUFFIX_WARNING_DUPLICATE_INJURY_NAME,
  K107_SUFFIX_WARNING_FREE_INJURY_NAME,
  keikaEmptyWarningField,
  SUBMIT_BTN,
  WARNING_DATABASE_MSG,
};

export const BookingStatusMapping = {
  1: '予約済',
  2: 'キャンセル済み',
};

export const PAYMENT_COURSE_TYPE: Record<EPaymentCourse, number> = {
  [EPaymentCourse.JIHI]: 0,
  [EPaymentCourse.M2_HOKEN]: 1,
  [EPaymentCourse.R2_HOKEN]: 2,
  [EPaymentCourse.A2_HOKEN]: 3,
};

export const ZOKUGARA_RELATIONSHIP = {
  1: '本人',
  2: '家族',
  3: '67退（本人）',
};

export * from './app';
export * from './error';
export * from './http';
export * from './patient';
export * from './regex';
export * from './routerPath';
export * from './user';
export * from './visit';
