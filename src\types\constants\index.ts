import { Order, ROLE } from '../enum';

export const PAGE_LIMIT_NUMBER = 20;
export const DATE_FORMAT = 'YYYY-MM-DD' as const;
export const STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  INVALID_FIELD: 422,
};
export const ENV = {
  DOMAIN_URL: import.meta.env.VITE_DOMAIN_URL,
};

export interface OrderItem {
  sort_by?: string;
  order?: Order;
}

export const PAGINATION_THRESHOLD = 100;
export const MAX_IMAGE_SIZE = 20;
export const ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png'];
export const ALLOWED_IMAGE_TYPES = ['image/jpg', 'image/jpeg', 'image/png'];

export const ROLE_OPTIONS = [
  { value: ROLE.STAFF, label: 'スタッフ' },
  { value: ROLE.STORE_ADMIN, label: '店舗管理者' },
  { value: ROLE.COMPANY_ADMIN, label: '企業管理者' },
  { value: ROLE.MEDIX_ADMIN, label: 'Medix管理者' },
];

export * from './http';
export * from './app';
export * from './routerPath';
export * from './regex';
export * from './error';
