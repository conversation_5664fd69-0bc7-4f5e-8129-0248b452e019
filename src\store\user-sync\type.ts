import { ROLE } from '@/types/enum';
import { Order } from '../patient/type';
import { Clinic, PractitionerInfo } from '@/types/interface/User';
export interface LoginModel {
  username: string;
  password: string;
  store_id: string;
}

export interface ParamGetListUser {
  username?: string | null;
  name?: string | null;
  email?: string | null;
  manageable_type?: ROLE[] | null;
  is_doctor?: boolean[] | null;
  can_access_karute?: boolean[] | null;
  can_access_booking?: boolean[] | null;
  order_by?: string | null;
  order_type?: Order;
  page?: number | null;
  limit?: number | null;
  clinic_id?: number | null;
}

export interface GetUserData {
  data: UserItem[];
  current_page: number;
  per_page: number;
  to: number;
  total: number;
  last_page: number;
}
export interface UserItem {
  user_id: number;
  username: string;
  email: string;
  provider: string | null;
  kana_name: string;
  kanji_name: string;
  status: number;
  role: ROL<PERSON>;
  manageable_type: string;
  manageable_id: number;
  is_doctor: boolean;
  can_access_karute: boolean;
  can_access_booking: boolean;
  clinic_id: number;
  avatar: string;
}

export interface User {
  user_id: number;
  username: string;
  email: string;
  phone: string | null;
  provider: string | null;
  kana_name: string;
  kanji_name: string;
  status: number;
  role: string;
  manageable_type: string;
  manageable_id: number;
  is_doctor: boolean;
  can_access_karute: boolean;
  can_access_booking: boolean;
  birthday: string | null;
  postal_code: string | null;
  address1: string | null;
  address2: string | null;
  avatar: string | null;
  gender: number;
  practitioner_info?: PractitionerInfo | null;
  clinic: Clinic;
}
