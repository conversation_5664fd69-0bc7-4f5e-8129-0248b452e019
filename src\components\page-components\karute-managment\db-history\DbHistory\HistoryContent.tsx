import { ContentItem } from '@/store/db-history/type';
import { Flex } from 'antd/lib';
import styles from './DbHistory.module.scss';
import clsx from 'clsx';

interface IHistoryContentProps {
  historyLabel: string;
  historyContent: ContentItem[];
  additionalImgs?: string[];
  subContent?: string;
}

export function HistoryContent({
  historyLabel,
  historyContent,
  additionalImgs,
  subContent,
}: IHistoryContentProps) {
  return (
    <Flex vertical style={{ width: '100%', minWidth: 0 }}>
      <Flex gap={'8px'} justify="space-between">
        <span className="fs12-bold">{historyLabel}</span>

        <Flex style={{ flexGrow: 1 }} vertical>
          {historyContent.map(item => (
            <div
              key={`${item.name}-${item.content}`}
              className={clsx('fs12-regular', styles.historyContent)}
            >
              {item.name && <span className={styles.historySubLabel}>{item.name} :</span>}
              <span className={styles.historyText}>{item.content}</span>
            </div>
          ))}

          {subContent && (
            <span className={clsx('fs12-regular', styles.historySubContent)}>{subContent}</span>
          )}
        </Flex>
      </Flex>

      {/* O images */}
      {additionalImgs && additionalImgs.length > 0 && (
        <Flex gap={'16px'} className={styles.historyImages}>
          {additionalImgs.map((img, index) => (
            <img key={`${img}-${index}`} src={img} alt="object_image" />
          ))}
        </Flex>
      )}
    </Flex>
  );
}
