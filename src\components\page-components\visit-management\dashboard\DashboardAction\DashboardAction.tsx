import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useScreen } from '@/hooks/useScreen';
import { routerPaths } from '@/types/constants/routerPath';
import { ButtonType } from '@/types/enum';
import { EventHandlers } from '@/types/interface';
import { Flex } from 'antd';
import { useNavigate } from 'react-router-dom';
import styles from './DashboardAction.module.scss';
import dayjs from 'dayjs';
type Props = {
  filteredDate: dayjs.Dayjs;
};
export default function DashboardAction({
  eventHandlers = {},
  filteredDate,
}: EventHandlers & Props) {
  const navigate = useNavigate();
  const { getPath } = useRouterPath();
  const createPatientPath = getPath(routerPaths.patientManagement.patientCreate);
  const { screen } = useScreen();

  return (
    <Flex gap={8} wrap={screen === 'mobile' ? 'wrap' : 'nowrap'} className={styles.dashboardAction}>
      {screen === 'mobile' && (
        <Button
          onClick={eventHandlers.scanQRCode}
          style={{ width: '100%' }}
          customType={ButtonType.SECONDARY_COLOR}
          disabled={filteredDate.isAfter(dayjs())}
        >
          <Icon name="qrScan" color="$brand-800" /> QRコード
        </Button>
      )}
      <Button
        style={{ width: '100%' }}
        customType={ButtonType.SECONDARY_COLOR}
        onClick={() => navigate(createPatientPath)}
      >
        <Icon name="plus" />
        患者登録
      </Button>
      <Button
        style={{ width: '100%' }}
        customType={ButtonType.SECONDARY_COLOR}
        onClick={eventHandlers.registerVisit}
        disabled={filteredDate.isAfter(dayjs())}
      >
        <Icon name="plus" />
        受付登録
      </Button>
      {/* <Button style={{ width: '100%' }} onClick={eventHandlers.confirmOnline}>
        資格確認
      </Button> */}
    </Flex>
  );
}
