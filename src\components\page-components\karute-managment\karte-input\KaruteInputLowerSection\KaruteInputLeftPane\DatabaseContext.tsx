import { useGetDatabaseQuery } from '@/store/karute/api';
import { Database } from '@/store/karute/type';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useKaruteInput } from '../../provider';
import { useAppDispatch } from '@/config/redux/store';
import { setLoading } from '@/store/karute';

interface DatabaseContextValue {
  list: Database[];
  activeId: number | null;
  setActiveId: (id: number) => void;
  isLoading: boolean;
  error: any;
  showHistory: boolean;
}

const DatabaseContext = createContext<DatabaseContextValue | undefined>(undefined);

export const DatabaseProvider: React.FC<React.PropsWithChildren<{}>> = ({ children }) => {
  const { id: karuteId, serviceId } = useParams<{ id: string; serviceId: string }>();
  const dispatch = useAppDispatch();
  const {
    data,
    isFetching,
    isLoading,
    isError: error,
  } = useGetDatabaseQuery(
    {
      karute_id: karuteId!,
      service_id: serviceId!,
    },
    {
      skip: !karuteId || !serviceId,
    }
  );
  const [showHistory, setShowHistory] = useState(false);

  const { setActiveTrauma } = useKaruteInput();

  const [list, setList] = useState<Database[]>([]);
  const [activeId, setActiveId] = useState<number | null>(null);

  useEffect(() => {
    if (data?.data) {
      setList(data.data.disease_bases);
      const last = data.data.disease_bases[data.data.disease_bases.length - 1];
      setShowHistory(data.data.has_history);
      setActiveId(last?.disease_base_id ?? null);
      setActiveTrauma(last?.trauma ?? null);
      dispatch(setLoading(false));
    }
  }, [data]);

  useEffect(() => {
    if (activeId == null || !Array.isArray(list)) return;

    const sel = list.find(d => d.disease_base_id === activeId);
    if (sel) {
      setActiveTrauma(sel.trauma);
    }
  }, [activeId, list, setActiveTrauma]);

  return (
    <DatabaseContext.Provider
      value={{ list, activeId, setActiveId, isLoading, error, showHistory }}
    >
      {children}
    </DatabaseContext.Provider>
  );
};

export function useDatabase() {
  const ctx = useContext(DatabaseContext);
  if (!ctx) throw new Error('useDatabase must be inside DatabaseProvider');
  return ctx;
}
