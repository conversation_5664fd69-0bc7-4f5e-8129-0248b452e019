import { medixSyncBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse, ListResponse } from '@/types/interface';
import { ParamGetListUser } from './type';
import { User } from '../auth/type';
import { buildQueryString } from '@/utils';

export const medixUserSyncManagementApi = medixSyncBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixSyncBaseQueryApi.endpoints,
    getUserList: build.query<APIResponse<ListResponse<User>>, ParamGetListUser>({
      query: params => {
        return {
          url: `/api/users${buildQueryString(params, false)}`,
        };
      },
      providesTags: ['medix-sync-user-list'],
    }),
  }),
});

export const { useGetUserListQuery } = medixUserSyncManagementApi;
