import clsx from 'clsx';
import './HeaderBlock.modules.scss';

interface IHeaderBlockProps {
  icon: React.ReactNode;
  title: React.ReactNode;
  suffix?: React.ReactNode;
  children?: React.ReactNode;
  isPadding?: boolean;
  bordered?: boolean;
}

export const AppBlockHasHeader = ({
  icon,
  title,
  suffix,
  children,
  isPadding = true,
  bordered = false,
}: IHeaderBlockProps) => {
  return (
    <div
      className={clsx('main-content-info', {
        bordered: bordered, // Áp dụng class 'bordered' nếu 'bordered' là true
      })}
    >
      <div className="header-block">
        <div className="header-left">
          {icon}
          {typeof title === 'string' ? <span className="title">{title}</span> : title}
        </div>
        {suffix}
      </div>
      <div
        className={clsx({
          'padded-content-info': isPadding,
        })}
      >
        {children}
      </div>
    </div>
  );
};
