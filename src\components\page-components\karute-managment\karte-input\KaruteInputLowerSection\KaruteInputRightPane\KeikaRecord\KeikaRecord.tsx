import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { KeikaItem } from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputRightPane/KeikaRecord/ProgressRecords/KeikaItem';
import { MainKeika } from '@/store/karute/type';
import { useGetConditionCheckCreateKeikaQuery, useGetMainKeikasQuery } from '@/store/keika/api';
import { PAGE_LIMIT_NUMBER } from '@/types/constants';
import { ButtonType, EKaruteUpsertType } from '@/types/enum';
import { Flex, Spin } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useKaruteInput } from '../../../provider';
import styles from './../../KaruteInputLowerSection.module.scss';

const emptyKeika = {
  keika_id: 0,
  visit_date: '',
  updated_at: '',
  keika_details: [],
  objective_images: [],
  treatment_images: [],
} as unknown as MainKeika;

function KeikaRecord() {
  const { openUpsertModal, karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;

  const [isTop, setIsTop] = useState(true);
  const [isBottom, setIsBottom] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);
  const [hasNext, setHasNext] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<Array<HTMLDivElement | null>>([]);

  const EPS = 2;
  const calcScrollState = () => {
    const c = containerRef.current;
    if (!c) return;

    const { scrollTop, clientHeight, scrollHeight } = c;

    const top = scrollTop <= EPS;
    const bottom = scrollTop + clientHeight >= scrollHeight - EPS;

    if (top !== isTop) setIsTop(top);
    if (bottom !== isBottom) setIsBottom(bottom);
    if (!top !== hasPrev) setHasPrev(!top);
    if (!bottom !== hasNext) setHasNext(!bottom);
  };

  const scrollToItem = (direction: 'up' | 'down') => {
    const container = containerRef.current;
    const refs = itemRefs.current.filter((el): el is HTMLDivElement => !!el);
    if (!container || refs.length === 0) return;

    const scrollTop = container.scrollTop;
    if (direction === 'down') {
      const idx = refs.findIndex(el => el.offsetTop > scrollTop + 1);
      if (idx !== -1) {
        container.scrollTo({ top: refs[idx].offsetTop, behavior: 'smooth' });
      }
    } else {
      const prevIndexes = refs
        .map((el, i) => (el.offsetTop < scrollTop - 1 ? i : -1))
        .filter(i => i >= 0);
      const prevIdx = prevIndexes.pop();
      if (prevIdx !== undefined) {
        container.scrollTo({ top: refs[prevIdx].offsetTop, behavior: 'smooth' });
      }
    }
  };

  const scrollTo = (pos: 'top' | 'bottom') => {
    if (!containerRef.current) return;

    containerRef.current.scrollTo({
      top: pos === 'top' ? 0 : containerRef.current.scrollHeight,
      behavior: 'smooth',
    });
  };

  const { id: karute_id, serviceId } = useParams<{ id: string; serviceId: string }>();

  const { data: apiData, isFetching } = useGetMainKeikasQuery(
    {
      page: 1,
      limit: PAGE_LIMIT_NUMBER,
      karute_id: Number(karute_id),
      service_id: Number(serviceId),
      patient_id: Number(patientId),
    },
    { skip: !karute_id || !serviceId || !patientId }
  );

  const { data: apiConditionData, isFetching: isFetchingCondition } =
    useGetConditionCheckCreateKeikaQuery(
      {
        karute_id: Number(karute_id),
        service_id: Number(serviceId),
        patient_id: Number(patientId),
      },
      { skip: !karute_id || !serviceId || !patientId }
    );

  const keikas: MainKeika[] = [...(apiData?.data?.data ?? [])].sort((a, b) =>
    dayjs(b?.visit_date).diff(dayjs(a?.visit_date))
  );
  const isCreateKeika =
    apiConditionData && apiConditionData.data && !isFetchingCondition
      ? apiConditionData.data.can_create
      : false;

  useEffect(() => {
    const c = containerRef.current;
    if (!c) return;

    const onScroll = () => {
      window.requestAnimationFrame(calcScrollState);
    };

    calcScrollState();

    c.addEventListener('scroll', onScroll, { passive: true });
    return () => c.removeEventListener('scroll', onScroll);
  }, [keikas]);

  return (
    <>
      <div className={styles.keikaRecord}>
        <Flex justify="space-between" align="center" className={styles.sectionHeader}>
          <h2 className={clsx(styles.title, 'fs14-bold')}>経過記録</h2>
          <Flex gap={'middle'}>
            {keikas.length !== 0 && (
              <Flex gap={'small'}>
                <Button
                  customSize="sm"
                  style={{ padding: '6px 5px 6px 5px', width: '36px' }}
                  customType={ButtonType.SECONDARY_GRAY}
                  onClick={() => scrollTo('bottom')}
                  disabled={isBottom}
                >
                  <Icon width={24} height={24} name="arrowDownload" color="var(--gray-500)" />
                </Button>
                <Button
                  customSize="sm"
                  style={{ padding: '6px 5px 6px 5px', width: '36px' }}
                  customType={ButtonType.SECONDARY_GRAY}
                  onClick={() => scrollToItem('down')}
                  disabled={!hasNext}
                >
                  <Icon width={24} height={24} name="arrowSortDown" color="var(--gray-500)" />
                </Button>
                <Button
                  customSize="sm"
                  style={{ padding: '6px 5px 6px 5px', width: '36px' }}
                  customType={ButtonType.SECONDARY_GRAY}
                  onClick={() => scrollToItem('up')}
                  disabled={!hasPrev}
                >
                  <Icon width={24} height={24} name="arrowSortUp" color="var(--gray-500)" />
                </Button>
                <Button
                  customSize="sm"
                  style={{ padding: '6px 5px 6px 5px', width: '36px' }}
                  customType={ButtonType.SECONDARY_GRAY}
                  onClick={() => scrollTo('top')}
                  disabled={isTop}
                >
                  <Icon width={24} height={24} name="arrowUpload" color="var(--gray-500)" />
                </Button>
              </Flex>
            )}
            <Button
              customSize="sm"
              style={{ padding: '8px 16px 8px 16px' }}
              customType={ButtonType.PRIMARY}
              onClick={() => openUpsertModal(EKaruteUpsertType.KEIKA, 'create')}
              disabled={!isCreateKeika}
            >
              <Icon name="plus" />
              新規作成
            </Button>
          </Flex>
        </Flex>
        <div className={styles.keikaSectionContent} ref={containerRef}>
          <Spin spinning={isFetching} style={{ minHeight: 150, paddingTop: 16, paddingBottom: 16 }}>
            {keikas.length !== 0 ? (
              keikas.map((k, idx) => (
                <div
                  key={k.keika_id}
                  ref={el => {
                    itemRefs.current[idx] = el;
                  }}
                >
                  <KeikaItem keika={k} isLatest={idx === 0} />
                </div>
              ))
            ) : (
              <KeikaItem keika={emptyKeika} isLatest={false} isEmpty />
            )}
          </Spin>
        </div>
      </div>
    </>
  );
}

export default KeikaRecord;
