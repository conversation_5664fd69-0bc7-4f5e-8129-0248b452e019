import PageLayout from '@/components/layouts/PageLayout';
import { useParams } from 'react-router-dom';
import UserCreateAction from '../CreateUserPage/components/UserCreateAction/UserCreateAction';
import { useRef } from 'react';
import FormCreateUser from '../CreateUserPage/components/FormCreateUser/FormCreateUser';
import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import { routerPaths } from '@/types/constants';

export default function ViewAndEditUserPage() {
  const { id } = useParams<{ id: string }>();
  const formRef = useRef<any>(null);

  return (
    <PageLayout
      title="ユーザ編集"
      breadcrumb={
        <AppBreadcrumb
          items={[
            { title: <a href={routerPaths.dashboard}>ユーザー一覧</a> },
            { title: 'ユーザ編集' },
          ]}
        />
      }
      headerRight={<UserCreateAction formRef={formRef} mode="edit" />}
    >
      <FormCreateUser ref={formRef} mode="edit" userId={id!} />
    </PageLayout>
  );
}
