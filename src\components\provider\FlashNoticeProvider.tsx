import { ReactNode, useEffect } from 'react';
import { notification } from 'antd';
import { registerFlashNoticeApi } from '@/utils/flashNotice';

interface FlashNoticeProviderProps {
  children: ReactNode;
}

const FlashNoticeProvider = ({ children }: FlashNoticeProviderProps) => {
  const [api, contextHolder] = notification.useNotification();

  useEffect(() => {
    registerFlashNoticeApi(api);
  }, [api]);

  return (
    <>
      {contextHolder}
      {children}
    </>
  );
};

export default FlashNoticeProvider;
