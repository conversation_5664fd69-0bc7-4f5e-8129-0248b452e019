/* src/styles/app.scss */

@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  // default font
  font-family: 'Noto Sans JP', sans-serif;
  color: $gray-900;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
}

// Checkbox style
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #eafcf9 !important; // Brand-100: Color for the checkbox component when it’s active
}
.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: #84c0ca; // Brand-600: Color for the check of the checkbox component
}
.ant-checkbox-checked:hover .ant-checkbox-inner {
  border-color: #84c0ca !important; // Brand-600: Color for the border of the checkbox component when it’s hovered
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #84c0ca !important; // Brand-600: Color for the border of the checkbox component when it’s hovered
}

.ant-checkbox-wrapper-disabled .ant-checkbox-inner {
  border-color: #d0d5dd !important; // Gray-300: Color for the border of the checkbox component when it’s disabled
  background: #f2f4f7 !important;
  color: #d0d5dd !important;
}
.ant-checkbox-wrapper-disabled:hover .ant-checkbox-inner {
  border-color: #d0d5dd !important; // Gray-300: Color for the border of the checkbox component when it’s disabled
  background: #f2f4f7 !important;
  color: #d0d5dd !important;
}

.ant-checkbox.ant-checkbox-checked.ant-checkbox-disabled .ant-checkbox-inner {
  border-color: #e4e7ec !important; // Gray-300: Color for the border of the checkbox component when it’s disabled
  background: #f2f4f7 !important;
}
.ant-checkbox.ant-checkbox-checked.ant-checkbox-disabled .ant-checkbox-inner::after {
  border-color: #e4e7ec !important; // Gray-300: Color for the check of the checkbox component when it’s disabled
  background: #f2f4f7 !important;
}

// Radio style
.ant-radio-checked .ant-radio-inner {
  background-color: #eafcf9 !important; // Brand-100: Color for the radio component when it’s active
}
.ant-radio-checked .ant-radio-inner::after {
  background-color: #84c0ca !important; // Brand-600: Color for the check of the radio component
}

.ant-radio-checked:not(.ant-radio-disabled):hover .ant-radio-inner {
  border-color: #84c0ca !important; // Brand-600: Color for the border of the radio component when it’s hovered
}

.ant-radio-wrapper:not(.ant-radio-disabled):hover .ant-radio-inner {
  border-color: #84c0ca !important; // Brand-600: Color for the border of the radio component when it’s hovered
}

.ant-radio-checked:has(.ant-radio-disabled):hover .ant-radio-inner {
  border-color: #d0d5dd !important; // Brand-600: Color for the border of the radio component when it’s hovered
  background-color: #f2f4f7 !important; // Brand-600: Color for the check of the radio component
}

.ant-radio-wrapper:has(.ant-radio-disabled):hover .ant-radio-inner {
  border-color: #d0d5dd !important; // Gray-300: Color for the border of the checkbox component when it’s disabled

  color: #d0d5dd !important;
}

.ant-radio.ant-radio-checked.ant-radio-disabled .ant-radio-inner {
  border-color: #d0d5dd !important; // Gray-300: Color for the border of the radio component when it’s disabled
  background: #f2f4f7 !important;
}
.ant-radio.ant-radio-checked.ant-radio-disabled .ant-radio-inner::after {
  background-color: #d0d5dd !important; // Gray-300: Color for the check of the radio component when it’s disabled
}

// Switch style
.ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
  background-color: #e4e7ec !important; // Gray-200: Color for the switch when it’s hovered
}

.ant-switch:hover:not(.ant-switch-disabled) {
  background-color: #84c0ca !important; // Brand-600: Color for the switch when it’s hovered
  opacity: 0.8;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.ant-tree-checkbox .ant-tree-checkbox-inner {
  border-radius: 4px !important;
  background-color: var(--white) !important;
}
.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: var(--white) !important;
}
.ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: var(--white) !important;
}
.ant-tree-checkbox-checked {
  .ant-tree-checkbox-inner::after {
    border-color: var(--brand-600) !important;
  }
  .ant-tree-checkbox-inner {
    background-color: var(--brand-100) !important;
    border-radius: 4px !important;
    border: 1px solid var(--brand-500) !important;
    color: var(--brand-600) !important;
    border-width: 1.5px !important;
  }
}

.ant-tree-treenode {
  width: 100%;
}
// Change required sign from left to right
.ant-form-item-required {
  flex-direction: row-reverse;
}

.ant-form-item-required::before {
  margin-left: 4px;
}

.ant-form-item-required::after {
  width: 0;
  margin: 0 !important;
}

.ant-tree-node-content-wrapper:hover:has(.paid-btn-action:hover) {
  background-color: unset !important;
}

.ant-tree .ant-motion-collapse {
  width: 100%;
}
.custom-tree {
  overflow-y: auto;
  overflow-x: hidden;
  color: $gray-700;
  border-radius: 0 !important;
  .ant-tree-node-content-wrapper {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
  }
  .ant-tree-node {
    display: flex !important;
    align-items: flex-start !important;
  }
  .ant-tree-checkbox {
    align-self: start !important;
    margin-top: 6px !important;
  }

  .ant-tree-title {
    order: 1;
    flex: 1;
    width: 100% !important;
  }

  .ant-tree-switcher {
    display: none !important;
  }

  .ant-tree-treenode-wrapper {
    padding: 0;
  }

  .ant-tree-indent {
    display: none !important;
  }

  .ant-checkbox .ant-checkbox-inner {
    width: 16px !important;
    height: 16px !important;
  }

  .ant-tree-list-scrollbar-thumb {
    background-color: $gray-300 !important;
    width: 6px !important;
  }
}

.custom-child-tree {
  .ant-tree-treenode {
    border-bottom: 1px solid $gray-200;
    padding: 8px 0;

    &:last-child {
      border-bottom: none;
    }
  }
}

// TO-DO: consider using a global scrollbar setting
// ::-webkit-scrollbar {
//   width: 6px;
//   height: 6px;
// }
// ::-webkit-scrollbar-thumb {
//   border-radius: 6px;
//   background-color: $gray-300;
// }

.ant-picker-cell-disabled::before {
  background: transparent !important;
}

// dropdown
.ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled) {
  color: $gray-800 !important;
}
.ant-radio-wrapper:not(.ant-radio-wrapper-disabled) .ant-radio + span,
.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled) .ant-checkbox + span {
  color: $gray-700 !important;
}

.ant-modal-title {
  color: $brand-900 !important;
  font-size: 18px !important;
  font-weight: 700 !important;
  line-height: 28px !important;
  letter-spacing: 0 !important;
}
.btn-modal-width > button {
  width: 120px;
}
