import Icon, { IconName } from '@/components/common/core/Icon';
import { Slider } from 'antd';
import clsx from 'clsx';
import { useSchema } from '../../context/SchemaContext';
import { COLORS, Color, DrawTool } from '../../type';
import styles from './Canvas.module.scss';
import { TOOL_CONFIG } from './CanvasContainer';

interface Props {
  disabled: boolean;
  activeColor: Color;
  setActiveColor: (c: Color) => void;
}

const Toolbar: React.FC<Props> = ({ disabled, activeColor, setActiveColor }) => {
  const { tool, setTool, size, setSize, drawnImages } = useSchema();

  const isDisabled = disabled || drawnImages.length === 0;
  return (
    <div className={styles.toolbar}>
      <div className={styles.drawMode}>
        {Object.values(DrawTool)
          .filter(t => t !== DrawTool.NONE)
          .map(key => {
            const cfg = TOOL_CONFIG[key];
            const active = tool === key;
            return (
              <div key={key} className={styles.item}>
                {cfg.icon ? (
                  <div
                    className={clsx(styles.toolBtn, isDisabled && styles.disabled)}
                    onClick={() => !isDisabled && setTool(key)}
                  >
                    <Icon
                      name={cfg.icon as IconName}
                      width={24}
                      height={24}
                      color={active ? 'var(--brand-800)' : 'var(--brand-500)'}
                    />
                  </div>
                ) : (
                  <div
                    className={clsx(styles.toolBtn, isDisabled && styles.disabled)}
                    onClick={() => !isDisabled && setTool(key)}
                  >
                    <span
                      className={clsx(
                        styles.circleIcon,
                        cfg.isFill ? styles.fill : styles.outline,
                        active && styles.active
                      )}
                    />
                  </div>
                )}

                <Slider
                  className={clsx(styles.slider, active && styles.active)}
                  min={cfg.min}
                  max={cfg.max}
                  disabled={isDisabled}
                  value={size[key]}
                  onChange={v => setSize(p => ({ ...p, [key]: v as number }))}
                />
              </div>
            );
          })}
      </div>

      <div className={styles.colorWrap}>
        {Object.entries(COLORS).map(([key, c]) => (
          <div
            key={key}
            className={clsx(styles.colorDot, c === activeColor && styles.active)}
            style={{
              backgroundColor: c,
              cursor: isDisabled ? 'not-allowed' : 'pointer',
              opacity: isDisabled ? 0.5 : 1,
            }}
            onClick={() => !isDisabled && setActiveColor(c)}
          />
        ))}
      </div>
    </div>
  );
};

export default Toolbar;
