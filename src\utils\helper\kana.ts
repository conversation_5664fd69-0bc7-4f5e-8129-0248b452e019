import { isK<PERSON>, toKatakan<PERSON> } from 'wanakana';

export const extractKatakana = (s: string) =>
  Array.from(s)
    .map(c => {
      if (c === ' ' || c === '　') return c;
      return isKana(c) ? toKatakana(c) : '';
    })
    .join('')
    .replace(/ {2,}/g, ' ')
    .replace(/　{2,}/g, '　');

export const mergeKana = (oldKana: string, newKana: string, isDeleting: boolean): string => {
  if (!newKana) return oldKana;
  if (!oldKana) return newKana;

  if (isDeleting && oldKana.startsWith(newKana)) return newKana;

  if (newKana.startsWith(oldKana)) return newKana;

  const max = Math.min(oldKana.length, newKana.length);
  for (let len = max; len > 0; len--) {
    if (oldKana.slice(-len) === newKana.slice(0, len)) {
      return oldKana.slice(0, -len) + newKana;
    }
  }

  return oldKana + newKana;
};
