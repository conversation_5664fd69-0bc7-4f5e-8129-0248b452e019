.menu-select-paid-modal {
  .paid-menu {
    margin-bottom: 24px;
    max-height: 520px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
    @media (max-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
 
}

.menu-paid-item {
  border-radius: 8px;
  border: 1px solid var(--brand-800);
  color: var(--brand-900);
  background-color: var(--brand-100);
  padding: 16px 28px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  font-weight: 700 !important;
  word-break: break-all;
}

.active-menu {
  background-color: var(--brand-500) !important;
}

@media screen and (max-width: 800px) {
  .paid-menu {
    max-height: unset !important;
    height: 100%;
  }
  .menu-paid-item {
    border-radius: 8px;
    border: 1px solid var(--brand-800);
    color: var(--brand-900);
    background-color: var(--brand-100);
    padding: 16px;
    display: flex;
    justify-content: center;
    cursor: pointer;
    height: 100%;
   
  }
  .btn-modal-width {
    width: 100%;
    > button {
      width: 100% !important;
    }
  }
}