.customTab {
  :global {
    .ant-tabs-nav-wrap {
      width: 100%;
      &:before {
        display: none;
      }
    }
    .ant-tabs .ant-tabs-tab + .ant-tabs-tab {
      margin: unset;
    }

    .ant-tabs-nav-list {
      background-color: $gray-200;
      align-items: center;
      width: 100%;
      height: 40px;
      margin: unset;
      padding: 0 4px;
    }
    .ant-tabs-tab {
      width: 50%;
      padding: 8px 0;
      justify-content: center;
      font-size: 14px;
      line-height: 20px;
      height: 36px;
      color: $gray-400;
      &-active {
        background-color: #fff;
        color: $brand-700;
        border-radius: 8px 8px 0 0;
      }
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin: unset;
    }
    .ant-tabs-ink-bar.ant-tabs-ink-bar-animated {
      background-color: #fff;
    }
  }
}
