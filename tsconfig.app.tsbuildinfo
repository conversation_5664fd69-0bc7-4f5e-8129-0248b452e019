{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/common/core/button/button.tsx", "./src/components/common/core/button/index.ts", "./src/components/common/core/checkbox/appcheckbox.tsx", "./src/components/common/core/checkbox/index.ts", "./src/components/common/core/commontable/renderwithtooltip.tsx", "./src/components/common/core/commontable/table.tsx", "./src/components/common/core/commontable/index.ts", "./src/components/common/core/datepicker/appdatepicker.tsx", "./src/components/common/core/datepicker/apprangedatepicker.tsx", "./src/components/common/core/datepicker/index.ts", "./src/components/common/core/formitem/appformitem.tsx", "./src/components/common/core/formitem/index.ts", "./src/components/common/core/icon/icon.tsx", "./src/components/common/core/icon/index.ts", "./src/components/common/core/image/image.tsx", "./src/components/common/core/image/index.ts", "./src/components/common/core/input/appinput.tsx", "./src/components/common/core/input/apptextareainput.tsx", "./src/components/common/core/input/index.ts", "./src/components/common/core/input/inputnumber/appinputnumber.tsx", "./src/components/common/core/input/inputnumber/appinputphonenumber.tsx", "./src/components/common/core/input/inputnumber/helper.ts", "./src/components/common/core/input/inputnumber/index.ts", "./src/components/common/core/loading/loading.tsx", "./src/components/common/core/loading/index.ts", "./src/components/common/core/modal/appmodal.tsx", "./src/components/common/core/modal/index.ts", "./src/components/common/core/modalconfirm/modalconfirm.tsx", "./src/components/common/core/modalconfirm/index.ts", "./src/components/common/core/multiselect/multiselect.tsx", "./src/components/common/core/multiselect/index.ts", "./src/components/common/core/radio/appradio.tsx", "./src/components/common/core/radio/index.ts", "./src/components/common/core/select/appselect.tsx", "./src/components/common/core/select/apptreeselect.tsx", "./src/components/common/core/select/index.ts", "./src/components/common/core/textareainput/textareainput.tsx", "./src/components/common/core/textareainput/index.ts", "./src/components/common/core/textinput/textinput.tsx", "./src/components/common/core/textinput/index.ts", "./src/components/common/layout/breadcrumb/appbreadcrumb.tsx", "./src/components/common/layout/breadcrumb/index.ts", "./src/components/common/layout/footer/footer.tsx", "./src/components/common/layout/footer/index.ts", "./src/components/common/layout/header/header.tsx", "./src/components/common/layout/header/index.ts", "./src/components/common/layout/modaltitle/modaltitle.tsx", "./src/components/common/layout/modaltitle/index.ts", "./src/components/common/layout/pagetitle/pagetitle.tsx", "./src/components/common/layout/pagetitle/index.ts", "./src/components/layouts/authlayout/authlayout.tsx", "./src/components/layouts/authlayout/index.ts", "./src/components/layouts/dashboardlayout/dashboardlayout.tsx", "./src/components/layouts/dashboardlayout/index.ts", "./src/components/layouts/pagelayout/pagelayout.tsx", "./src/components/layouts/pagelayout/index.ts", "./src/components/provider/confirmmodalprovider.tsx", "./src/components/provider/flashnoticeprovider.tsx", "./src/config/antd/themeconfig.ts", "./src/hooks/useauth.tsx", "./src/hooks/usetokenbridgeauth.tsx", "./src/hooks/useuser.tsx", "./src/pages/authen/notauthenpage/notauthenpage.tsx", "./src/pages/authen/notauthenpage/index.ts", "./src/pages/authen/tokenbridge/tokenbridge.tsx", "./src/pages/authen/tokenbridge/index.ts", "./src/pages/dashboard/createuserpage/createuserpage.tsx", "./src/pages/dashboard/createuserpage/index.ts", "./src/pages/dashboard/createuserpage/components/formcreateuser/formcreateuser.tsx", "./src/pages/dashboard/createuserpage/components/formcreateuser/index.ts", "./src/pages/dashboard/createuserpage/components/usercreateaction/usercreateaction.tsx", "./src/pages/dashboard/createuserpage/components/usercreateaction/index.ts", "./src/pages/dashboard/usermanagementpage/usermanagementpage.tsx", "./src/pages/dashboard/usermanagementpage/index.ts", "./src/pages/dashboard/usermanagementpage/components/usermanagementcontent/usermanagementcontent.tsx", "./src/pages/dashboard/usermanagementpage/components/usermanagementcontent/index.ts", "./src/pages/dashboard/userpreviewmodal/userpreviewmodal.tsx", "./src/pages/dashboard/userpreviewmodal/index.ts", "./src/pages/dashboard/viewandedituserpage/viewandedituserpage.tsx", "./src/pages/dashboard/viewandedituserpage/index.ts", "./src/router/index.tsx", "./src/router/routermenu.tsx", "./src/services/medix-sync-api/api.ts", "./src/services/medix-sync-api/loginapi.ts", "./src/services/medix-sync-api/logout.ts", "./src/services/medix-sync-api/userapi.ts", "./src/types/crypto-js.d.ts", "./src/types/global.d.ts", "./src/types/kuroshiro.d.ts", "./src/types/svg.d.ts", "./src/types/constants/app.ts", "./src/types/constants/error.ts", "./src/types/constants/http.ts", "./src/types/constants/index.ts", "./src/types/constants/regex.ts", "./src/types/constants/routerpath.ts", "./src/types/enum/buttontype.ts", "./src/types/enum/esort.ts", "./src/types/enum/index.ts", "./src/types/interface/apiresponse.ts", "./src/types/interface/buttonprops.ts", "./src/types/interface/loginresponse.ts", "./src/types/interface/pagelayoutprops.ts", "./src/types/interface/textareainputprops.ts", "./src/types/interface/textinputprops.ts", "./src/types/interface/user.ts", "./src/types/interface/index.ts", "./src/utils/auth.ts", "./src/utils/flashnotice.tsx", "./src/utils/kana.ts", "./src/utils/userstorage.ts", "./src/utils/helper/filetobase64.ts", "./src/utils/helper/filetosrc.ts", "./src/utils/helper/formvalues.ts", "./src/utils/helper/index.ts"], "version": "5.7.3"}