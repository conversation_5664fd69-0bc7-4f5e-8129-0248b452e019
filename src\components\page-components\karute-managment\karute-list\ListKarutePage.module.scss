.list_karute_page_table_header::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.list_karute_page_table_header::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: $gray-300;
}
.list_karute_page_table_header {
  overflow-x: auto;
  min-width: 100%;
  padding-bottom: 12px;
  margin-bottom: 4px;

  .buttonRow {
    flex-shrink: 0;

    button {
      width: 100px;
      flex-shrink: 0;
    }
  }
  .inputRow {
    .input_common {
      // width: 308px; Comment because of margin from Trung Phan
      width: 220px;
      :global {
        .ant-input,
        .ant-picker {
          font-size: 14px !important;
          font-weight: 400 !important;
          line-height: 20px !important;
          letter-spacing: 0 !important;
          flex-shrink: 0 !important;
          height: 40px !important;
        }

        input.ant-input:placeholder-shown {
          text-overflow: initial !important;
        }
      }
      .datePicker {
        background-color: var(--white);
      }
      .appSelect {
        &:global(.ant-select),
        &:global(.ant-tree-select) {
          height: 40px !important;
        }
      }
    }
  }
}

.list_karute_page_table_data {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: 8px;

  max-height: 376px;

  .total_count {
    font-size: 12px;
    color: $gray-500;
    margin-left: auto;
  }

  .table_header {
    padding: 8px 12px;
  }
}
.delete_user_option {
  color: $error-600;
}
.karute_status_badge {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: unset;
  > span {
    border-radius: 16px;
    padding: 2px 11.5px 3px 11.5px;
    text-wrap: nowrap;
    max-width: 80px;
  }
}
.confirmed_color {
  > span {
    background-color: $brand-100;
    color: $brand-700;
  }
}
.temporary_color {
  > span {
    background-color: $error-200;
    color: $error-600;
  }
}

.reception_date_cell {
  text-align: end;
}

.fixedRowHeight {
  td {
    height: 52px !important;
    line-height: 52px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    overflow: hidden;
    white-space: nowrap;
  }
}

:global(.ant-pagination) {
  align-items: baseline;
  :global(.ant-pagination-item) {
    width: 24px;
    height: 24px;
    font-size: 12px;
    min-width: 24px;
    text-align: center;

    a {
      max-height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// .table {
//   max-height: 376px;
// }
