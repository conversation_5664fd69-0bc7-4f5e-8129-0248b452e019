import Button from '@/components/common/core/Button';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppSelect } from '@/components/common/core/Select';
import { PartIdOption } from '@/components/page-components/karute-managment/karte-input/InjuryNameModal/type';
import useInjuryNameStore from '@/hooks/useInjuryNameStore';
import { ERROR_COMMON_MESSAGE } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import { useInjuryName } from '../../context/InjuryNameContext';
import { LRType, SideLabelOption } from '../../type';
import OptionalPartInformationModal from '../OptionalPartInformationModal';
import styles from './../../InjuryNameModal.module.scss';

interface Option {
  value: number;
  label: string;
}

interface Props {
  form: any;
}

export const InjuryNameSelectBySelect: React.FC<Props> = ({ form }) => {
  const { injuryNamePayload, setInjuryNamePayload, isInjuryNameEditable } = useInjuryName();
  const { partTypes, getPartIdOptions, hasCenterBlock } = useInjuryNameStore();

  const [partTypeOptions, setPartTypeOptions] = useState<Option[]>([]);
  const [partIdRawOptions, setPartIdRawOptions] = useState<PartIdOption[]>([]);
  const [partIdOptions, setPartIdOptions] = useState<Option[]>([]);

  const [inputValue, setInputValue] = useState('');
  const [inputDisabled, setInputDisabled] = useState(true);

  const [infoModalOpen, setInfoModalOpen] = useState(false);

  const handleInfoModalOpen = () => {
    console.log('handleInfoModalOpen');
    setInfoModalOpen(true);
  };

  const handleInfoModalClose = () => {
    setInfoModalOpen(false);
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value;
    setInputValue(v);
    setInjuryNamePayload(p => ({ ...p, name: v }));
    form.setFieldsValue({ injury_name: v });
  };

  const showPartIdSelect = partIdRawOptions.length > 0;
  const selectedRaw = partIdRawOptions.find(o => o.part_id === injuryNamePayload.part_id);
  const showSideSelect = injuryNamePayload.part_id != null && selectedRaw?.lr_type !== LRType.None;
  const needSide =
    showSideSelect && !hasCenterBlock(injuryNamePayload.part_type!, injuryNamePayload.part_id!);

  const handlePartTypeChange = (value: number) => {
    setInjuryNamePayload({
      part_type: value,
      part_id: null,
      lr_type: null,
      name: null,
      name_other: null,
    });
  };

  const handlePartIdChange = (value: number) => {
    setInjuryNamePayload(p => ({
      ...p,
      part_id: value,
      lr_type: null,
      name: null,
    }));
  };

  const handleSideChange = (value: LRType) => {
    setInjuryNamePayload(p => ({
      ...p,
      lr_type: value,
      name: null,
    }));
    form.setFieldsValue({ injury_name: null });
  };

  const handleClear = () => {
    setInjuryNamePayload(prev => ({
      ...prev,
      part_type: null,
      part_id: null,
      lr_type: null,
      name_other: isInjuryNameEditable ? prev.name_other : null,
    }));
  };

  useEffect(() => {
    setPartTypeOptions(partTypes.map(p => ({ value: p.part_type, label: p.name })));
  }, [partTypes]);

  useEffect(() => {
    const pt = injuryNamePayload.part_type;
    if (pt == null) {
      setPartIdRawOptions([]);
      setPartIdOptions([]);
      return;
    }
    const raw = getPartIdOptions(pt);
    setPartIdRawOptions(raw);
    setPartIdOptions(raw.map(o => ({ value: o.part_id, label: o.part_name_original })));
  }, [injuryNamePayload.part_type]);

  useEffect(() => {
    const { part_type, part_id, lr_type, name } = injuryNamePayload;
    const ptLabel = partTypeOptions.find(o => o.value === part_type)?.label || '';
    const pidLabel = partIdOptions.find(o => o.value === part_id)?.label || '';
    const sideLabel = SideLabelOption.find(o => o.value === lr_type)?.label || '';

    let newValue = '';
    let disabled = true;

    if (part_type != null && partIdRawOptions.length === 0) {
      newValue = name ?? ptLabel;
      disabled = false;
    } else if (part_type != null && part_id != null && !lr_type && !needSide) {
      newValue = `${pidLabel} ${ptLabel}`;
      disabled = true;
    } else if (part_type != null && part_id != null && lr_type) {
      newValue = `${sideLabel}${pidLabel} ${ptLabel}`;
      disabled = true;
    }

    setInputValue(newValue);
    form.setFieldsValue({ injury_name: newValue });

    if (isInjuryNameEditable) disabled = true;
    setInputDisabled(disabled);

    setInjuryNamePayload(prev =>
      newValue && prev.name !== newValue ? { ...prev, name: newValue } : prev
    );
  }, [
    injuryNamePayload.part_type,
    injuryNamePayload.part_id,
    injuryNamePayload.lr_type,
    injuryNamePayload.name,
    partTypeOptions,
    partIdOptions,
    partIdRawOptions,
    isInjuryNameEditable,
  ]);

  useEffect(() => {
    if (isInjuryNameEditable && (injuryNamePayload.name || injuryNamePayload.name_other)) {
      setInjuryNamePayload(p =>
        p.name_other === injuryNamePayload.name ? p : { ...p, name_other: injuryNamePayload.name }
      );
      // debugger;
      form.setFieldsValue({
        custom_injury_name: injuryNamePayload?.name_other ?? injuryNamePayload.name,
      });
    }
  }, [isInjuryNameEditable, injuryNamePayload.name, injuryNamePayload.name_other]);
  return (
    <>
      <div className={styles.injurySite}>
        <Flex gap={21}>
          <div className={clsx(styles.label, 'fs14-medium')}>負傷部位</div>
          <div>
            <Flex gap={8}>
              <AppSelect
                placeholder="負傷タイプ"
                size="small"
                style={{ width: '220px' }}
                allowClear
                options={partTypeOptions}
                value={injuryNamePayload.part_type ?? undefined}
                onChange={handlePartTypeChange}
              />
              {showPartIdSelect && (
                <AppSelect
                  placeholder="負傷部位"
                  size="small"
                  style={{ width: '220px' }}
                  allowClear
                  options={partIdOptions}
                  value={injuryNamePayload.part_id ?? undefined}
                  onChange={handlePartIdChange}
                />
              )}
              {showSideSelect && (
                <AppSelect
                  placeholder="左右"
                  size="small"
                  style={{ width: '140px' }}
                  options={SideLabelOption}
                  value={injuryNamePayload.lr_type ?? undefined}
                  onChange={handleSideChange}
                />
              )}
            </Flex>
            <div className="mt-2">
              <Flex gap={21} align="start">
                <AppFormItem
                  name="injury_name"
                  rules={[
                    {
                      required: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('負傷名'),
                      whitespace: true,
                    },
                  ]}
                  initialValue={inputValue}
                  shouldUpdate={false}
                >
                  <AppInput
                    placeholder="負傷名"
                    size="small"
                    style={{ width: '596px' }}
                    value={inputValue}
                    readOnly={inputDisabled}
                    onChange={e => {
                      onInputChange(e);
                      form.setFieldsValue({ injury_name: e.target.value });
                    }}
                    maxLength={!inputDisabled ? 17 : undefined}
                  />
                </AppFormItem>
                <Button
                  customType={ButtonType.SECONDARY_COLOR}
                  onClick={handleClear}
                  style={{ color: 'var(--brand-800)' }}
                >
                  <Icon name="retry" height={20} width={20} />
                  <span style={{ marginLeft: '-4px' }}>クリア</span>
                </Button>
              </Flex>
            </div>
          </div>
        </Flex>
        {isInjuryNameEditable && (
          <Flex className="mt-3" gap={21} align="start">
            <div className={clsx(styles.label, 'fs14-medium', 'mt-2')}>任意部位</div>
            <div>
              <AppFormItem
                name="custom_injury_name"
                rules={[
                  {
                    required: true,
                    message: ERROR_COMMON_MESSAGE.REQUIRED('任意部位'),
                    whitespace: true,
                  },
                ]}
                initialValue={injuryNamePayload.name_other ?? undefined}
                shouldUpdate={false}
              >
                <AppInput
                  placeholder=""
                  size="small"
                  style={{ width: '596px' }}
                  value={injuryNamePayload.name_other ?? ''}
                  onChange={e => {
                    const v = e.target.value;
                    setInjuryNamePayload(p => ({ ...p, name_other: v }));
                    form.setFieldsValue({ custom_injury_name: v });
                  }}
                  maxLength={17}
                />
              </AppFormItem>
            </div>
            <div className="mt-2" onClick={handleInfoModalOpen} style={{ cursor: 'pointer' }}>
              <Icon name="infoFilled" height={24} width={24} color="var(--brand-800)" />
            </div>
          </Flex>
        )}
      </div>
      <OptionalPartInformationModal open={infoModalOpen} onCancel={handleInfoModalClose} />{' '}
    </>
  );
};
