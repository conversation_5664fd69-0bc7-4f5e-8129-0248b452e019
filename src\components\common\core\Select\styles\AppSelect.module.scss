.appSelect {
  &:global(.ant-select),
  &:global(.ant-tree-select) {
    display: flex !important;
    align-items: center !important;
    min-width: 150px;
    height: 44px !important;
    &:not(:global(.ant-select-disabled)) {
      :global(.ant-select-selection-item) {
        color: $gray-800 !important;
      }
    }
    :global(.ant-select-selector) {
      background-color: var(--white) !important;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      height: 100% !important;
      width: 100%;
      box-shadow: none !important;
      border-color: $gray-300 !important;
      padding: 0 12px 0 8px;
      padding: 0 12px 0 12px;
      border-radius: 8px !important;
      // :global(.ant-select-selection-overflow) {
      //   padding: 0 10px !important;
      // }

      :global(.ant-select-selection-item) {
        align-items: center !important;
        border-radius: 8px !important;
        :global(.ant-select-selection-item-content) {
          margin-inline-end: 10px !important;
        }
      }
      :global(.ant-select-selection-search) {
        :global(.ant-select-selection-item) {
          margin-left: 6px !important;
        }
      }

      :global(.ant-select-selection-overflow-item) {
        :global(.ant-select-selection-item) {
          height: 100% !important;
          padding: 2px 10px;
        }
      }
      :global(.ant-select-selection-item) {
        padding: 4px !important;
      }
      :global(.ant-select-selection-wrap) {
        height: 44px !important;
      }

      :global(.ant-select-selection-placeholder) {
        inset-inline-start: 0 !important;
        inset-inline-end: 0 !important;
      }
    }
    &:global(.ant-select-lg) {
      :global(.ant-select-selector) {
        font-size: 18px;
        line-height: 24px;
        width: 100%;
        height: 52px !important;
        :global(.ant-select-selection-overflow-item) {
          :global(.ant-select-selection-item) {
            height: 100% !important;
            padding: 0 10px;
          }
        }
      }
    }

    &:global(.ant-select-sm) {
      :global(.ant-select-selector) {
        font-size: 14px !important;
        // line-height: 20px;
        width: 100%;
        height: 40px !important;
        padding: 10px 12px;
        :global(.ant-select-selection-wrap) {
          height: 40px !important;
        }
        :global(.ant-select-selection-overflow-item) {
          :global(.ant-select-selection-item) {
            height: 100% !important;
            padding: 6px 16px;
          }
        }
        :global(.ant-select-selection-placeholder) {
          padding-left: 4px !important;
        }
        :global(.ant-select-selection-item) {
          padding-left: 4px !important;
        }
      }
    }
  }

  &:global(.ant-select.ant-select-focused) {
    :global(.ant-select-selector) {
      border-color: $brand-800 !important;
    }
  }

  &:global(.ant-select:not(.ant-select-disabled):hover) {
    :global(.ant-select-selector) {
      border-color: $brand-800 !important;
    }
  }

  &:global(.ant-select-disabled) {
    :global(.ant-select-selector) {
      background-color: var(--input-bg-disabled) !important;
    }
  }

  &:global(.ant-select.ant-select-status-error),
  &:global(.ant-select.ant-select-status-error:hover) {
    :global(.ant-select-selector) {
      border-color: $error-600 !important;
    }
  }

  &.kanjiSuggestInput {
    :global(.ant-select-selection-search) {
      :global(input.ant-select-selection-search-input) {
        font-size: 16px !important;
        font-weight: 400 !important;
      }
    }
  }
}
