import { useEffect, useState } from 'react';
import { NavigateOptions, useLocation, useNavigate } from 'react-router-dom';

type CustomNavigateOptions = NavigateOptions & {
  params?: Record<string, string | string[]>;
};
// For navigating and add props
export function useNav() {
  const navigate = useNavigate();

  const customNavigate = (url: string, options?: CustomNavigateOptions) => {
    let paramsString: string = '';
    if (options?.params) {
      const searchParams = new URLSearchParams();

      Object.entries(options.params).forEach(([key, value]) => {
        if (!value) {
          return;
        }
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v));
        } else {
          searchParams.append(key, value);
        }
      });

      paramsString = searchParams.toString();
    }

    navigate({ pathname: url, search: paramsString }, options);
  };

  return customNavigate;
}
// For getting parse query to props

export function useParseQuery() {
  const location = useLocation();
  const [queryParams, setQueryParams] = useState<{ [key: string]: string }>({});
  const parseQueryParams = (search: string) => {
    const params = new URLSearchParams(search);
    const queryObject: { [key: string]: string } = {};
    params.forEach((value, key) => {
      queryObject[key] = value;
    });
    return queryObject;
  };
  useEffect(() => {
    setQueryParams(parseQueryParams(location.search));
  }, [location.search]);
  return { queryParams };
}
