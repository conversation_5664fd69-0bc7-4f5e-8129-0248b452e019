import { Mo<PERSON>, Spin, Avatar, Flex } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { useEffect, useState, useMemo } from 'react';
import { fetchUserById, userDegrees } from '@/services/medix-sync-api/userApi';
import { User } from '@/types/interface';
import { routerPaths } from '@/types/constants';
import styles from './UserPreviewModal.module.scss';
import clsx from 'clsx';
import Button from '@/components/common/core/Button';
import { ButtonType, ROLE } from '@/types/enum';
import Table, { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { getRoleName } from '@/utils/helper';
dayjs.extend(advancedFormat);
import { fetchWorkSchedule } from '@/services/medix-sync-api/userApi';

export type WeekDay = 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun';
export const JP_WEEK_LABEL: Record<WeekDay, string> = {
  mon: '月',
  tue: '火',
  wed: '水',
  thu: '木',
  fri: '金',
  sat: '土',
  sun: '日',
};

export interface WorkRow {
  id: string;
  label?: string;
  // key là 'yyyy-MM-dd', value là true/false
  pattern: Record<string, boolean>;
}

export default function UserPreviewModal() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [activeTab, setActiveTab] = useState<'detail' | 'schedule'>('detail');
  const [fakeSchedule, setFakeSchedule] = useState<WorkRow[]>([]); //TODO

  const close = () => navigate(routerPaths.dashboard, { replace: true });
  useEffect(() => {
    (async () => {
      const res = await fetchUserById(Number(id));
      if (!res?.data || res?.status !== 200) {
        navigate(routerPaths.userNotFound, { replace: true });
        setLoading(false);
        return;
      }
      setUser(res?.data || null);
      setLoading(false);
    })();
  }, [id]);

  const data = useMemo(() => {
    if (!user) return null;

    const address = [user.address1, user.address2].filter(Boolean).join(' ') || '';

    return {
      avatar: user.avatar,
      nameDisplay: `${user.kanji_name || ''} (${user.kana_name || ''})`,
      subName: user.username || '',
      overview: [
        { label: '性別', value: user.gender === 1 ? '男性' : user.gender === 2 ? '女性' : '' },
        { label: '生年月日', value: user.birthday ?? '' },
        {
          label: '資格',
          value: userDegrees
            .filter(u => user.practitioner_info?.degrees?.includes(u.value))
            .map(u => u.label)
            .join('、'),
        },
        { label: '権限タイプ', value: getRoleName(user.role as ROLE) ?? '' },
        { label: '担当コース', value: user.courses?.map(c => c.course_name).join('、') ?? '' },
        { label: '電話番号', value: user.phone ?? '' },
        { label: 'メールアドレス', value: user.email ?? '' },
        { label: '郵便番号', value: user.postal_code ?? '' },
        { label: '住所', value: address },
      ],
      experience: user.practitioner_info?.experience ?? '',
      introduce: user.practitioner_info?.introduce ?? '',

      // TODO
      schedule: [
        {
          id: '1',
          pattern: { mon: true, tue: true, wed: true, thu: true, fri: true, sat: false, sun: true },
        },
      ],
    };
  }, [user]);

  const dateColumns = useMemo(() => {
    const WEEK: WeekDay[] = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    const today = dayjs();
    return Array.from({ length: 7 }).map((_, idx) => {
      const d = today.add(idx, 'day');
      const key = d.format('YYYY-MM-DD');
      const dayNum = d.format('D');
      const weekday = WEEK[d.day()];
      return {
        key,
        title: (
          <div>
            <div>
              {dayNum} ({JP_WEEK_LABEL[weekday]})
            </div>
          </div>
        ),
      };
    });
  }, []);

  const scheduleColumns: ColumnsType<WorkRow> = [
    {
      title: '日付',
      dataIndex: 'label',
      key: 'label',
      // fixed: 'left',
      align: 'center',
      width: 120,
      render: (_, rec) => rec.label || '出荷可否',
    },
    ...dateColumns.map(col => ({
      title: col.title,
      dataIndex: ['pattern', col.key],
      key: col.key,
      align: 'center' as const,
      render: (v: boolean) => (
        <span className={v ? styles.workable : styles.unworkable}>{v ? '○' : '✕'}</span>
      ),
    })),
  ];

  useEffect(() => {
    if (!user) return;
    const start_date = dateColumns[0].key;
    const end_date = dateColumns[dateColumns.length - 1].key;
    (async () => {
      try {
        const res = await fetchWorkSchedule({
          user_id: Number(id),
          start_date,
          end_date,
        });
        if (res.status === 200 && Array.isArray(res.data)) {
          const apiData = res.data;

          const row: WorkRow = {
            id: String(user.user_id),
            label: '出荷可否',
            pattern: dateColumns.reduce((acc, { key }) => {
              const day = apiData.find(item => item.date === key);
              acc[key] = day?.is_working ?? false;
              return acc;
            }, {} as Record<string, boolean>),
          };

          setFakeSchedule([row]);
        } else {
          setFakeSchedule([]);
        }
      } catch (error) {
        console.error('Lỗi khi fetch lịch làm việc:', error);
        setFakeSchedule([]);
      }
    })();
  }, [user, dateColumns, id]);

  return (
    <Modal
      open
      centered
      width={900}
      title={<span style={{ color: '#194060', fontSize: '18px' }}>ユーザー情報</span>}
      onCancel={close}
      footer={null}
      style={{ maxHeight: '90vh' }}
      styles={{
        body: {
          maxHeight: 'calc(90vh - 112px)',
          overflowY: 'auto',
        },
      }}
      loading={loading}
      className={styles.modalBodyScroll}
    >
      {loading || !data ? (
        <Spin />
      ) : (
        <>
          <Flex align="center" justify="space-between" className={styles.header}>
            <Flex align="center" gap={16}>
              <Avatar size={56} src={data.avatar} />
              <div>
                <div className={clsx(styles.name, 'fs16-bold')}>{data.nameDisplay}</div>
                <div className={clsx(styles.sub, 'fs14-regular')}>{data.subName}</div>
              </div>
            </Flex>
            <Flex className={styles.segment}>
              <Button
                customSize="sm"
                customType={
                  activeTab === 'detail' ? ButtonType.SECONDARY_BRAND_2 : ButtonType.SECONDARY_COLOR
                }
                onClick={() => setActiveTab('detail')}
                className={styles.item}
                style={{ width: '106px' }}
              >
                詳細情報
              </Button>
              <Button
                customSize="sm"
                customType={
                  activeTab === 'schedule'
                    ? ButtonType.SECONDARY_BRAND_2
                    : ButtonType.SECONDARY_COLOR
                }
                onClick={() => setActiveTab('schedule')}
                disabled={!user?.is_doctor}
                className={styles.item}
                style={{ width: '106px' }}
              >
                スケジュール
              </Button>
            </Flex>
          </Flex>

          {/* detail tab */}
          {activeTab === 'detail' && (
            <Flex gap={16} className={styles.content}>
              <div className={styles.overviewArea}>
                <div className={clsx(styles.subHeader, 'fs16-bold', 'mb-2')}>概要</div>
                <div className={styles.card}>
                  {data.overview.map(item => (
                    <div key={item.label} className={styles.row}>
                      <span className={clsx(styles.label, 'fs14-regular')}>{item.label}</span>
                      <span className={clsx(styles.value, 'fs14-regular')}>{item.value}</span>
                    </div>
                  ))}
                </div>
              </div>

              <Flex vertical gap={16} style={{ flex: 1 }}>
                <div className={styles.overviewArea}>
                  <div className={clsx(styles.subHeader, 'fs16-bold', 'mb-2')}>職務経歴</div>
                  <div className={clsx(styles.label, 'fs14-regular', styles.preWrap)}>
                    {data.experience}
                  </div>
                </div>
                <div className={styles.overviewArea}>
                  <div className={clsx(styles.subHeader, 'fs16-bold', 'mb-2')}>自己紹介</div>
                  <div className={clsx(styles.label, 'fs14-regular', styles.preWrap)}>
                    {data.introduce}
                  </div>
                </div>
              </Flex>
            </Flex>
          )}

          {/* schedule tab */}
          {activeTab === 'schedule' && (
            <Table
              rowKey="id"
              columns={scheduleColumns}
              dataSource={fakeSchedule}
              pagination={false}
              size="small"
              bordered
            />
          )}
        </>
      )}
    </Modal>
  );
}
