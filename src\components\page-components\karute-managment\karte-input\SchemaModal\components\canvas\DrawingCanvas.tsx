import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Circle, Group, Image as KonvaImage, Layer, Line, Stage } from 'react-konva';
import { useSchema } from '../../context/SchemaContext';
import { DrawTool, SchemaMode } from '../../type';

interface DrawingCanvasProps {
  imgId: string;
  imgUrl: string;
  width: number;
  height: number;
  color: string;
}

// Stroke definitions:
// - PENCIL: freehand, size = strokeWidth (px)
// - ERASER: freehand erase, size = strokeWidth (px)
// - CIRCLE: filled circle stamp, radius = slider size (px)
// - CIRCLE_OUTLINE: outline circle stamp, outer radius = slider size (px), strokeWidth = slider/4 (px)
type Stroke =
  | { tool: DrawTool.PENCIL; points: number[]; color: string; size: number }
  | { tool: DrawTool.ERASER; points: number[]; size: number }
  | { tool: DrawTool.CIRCLE; cx: number; cy: number; radius: number; color: string }
  | {
      tool: DrawTool.CIRCLE_OUTLINE;
      cx: number;
      cy: number;
      radius: number;
      color: string;
      strokeWidth: number;
    };

export interface DrawingCanvasHandle {
  undo: () => void;
  exportImage: () => Promise<Blob>;
}

const DrawingCanvas = forwardRef<DrawingCanvasHandle, DrawingCanvasProps>(
  ({ imgId, imgUrl, width, height, color }, ref) => {
    const [stageSize, setStageSize] = useState({ w: width, h: height });
    const stageRef = useRef<any>(null);
    const [bgImg, setBgImg] = useState<HTMLImageElement | null>(null);
    const [strokes, setStrokes] = useState<Stroke[]>([]);
    const hasDrawn = useRef(false);
    const isDrawing = useRef(false);
    const { markAsDrawn, unMarkAsDrawn, tool, size, mode } = useSchema();
    const disabled = mode !== SchemaMode.DRAW;
    const [transform, setTransform] = useState({
      scale: 1,
      offX: 0,
      offY: 0,
    });

    const screenToContent = (p: { x: number; y: number }) => {
      const { scale } = transform;
      return { x: p.x / scale, y: p.y / scale };
    };

    useEffect(() => {
      if (!bgImg) return;
      const imgW = bgImg.naturalWidth;
      const imgH = bgImg.naturalHeight;

      const scale = Math.min(width / imgW, height / imgH);
      const dispW = imgW * scale;
      const dispH = imgH * scale;

      setTransform({ scale, offX: 0, offY: 0 });
      setStageSize({ w: dispW, h: dispH });
    }, [bgImg, width, height]);

    useEffect(() => {
      const img = new Image();
      let src = imgUrl;
      img.crossOrigin = 'anonymous';
      img.src = src;
      img.onload = () => setBgImg(img);
    }, [imgUrl]);

    const handleMouseDown = (e: any) => {
      if (disabled || tool === DrawTool.NONE) return;
      const stagePos = e.target.getStage().getPointerPosition();
      if (!stagePos) return;
      const pos = screenToContent(stagePos);

      const displaySize = size[tool];
      const realSize = displaySize / transform.scale;

      switch (tool) {
        case DrawTool.PENCIL: {
          isDrawing.current = true;
          if (!hasDrawn.current) {
            markAsDrawn(imgId);
            hasDrawn.current = true;
          }
          setStrokes(prev => [...prev, { tool, points: [pos.x, pos.y], color, size: realSize }]);
          break;
        }

        case DrawTool.ERASER: {
          isDrawing.current = true;
          setStrokes(prev => [...prev, { tool, points: [pos.x, pos.y], size: realSize }]);
          break;
        }

        case DrawTool.CIRCLE: {
          if (!hasDrawn.current) {
            markAsDrawn(imgId);
            hasDrawn.current = true;
          }
          setStrokes(prev => [
            ...prev,
            { tool, cx: pos.x, cy: pos.y, radius: realSize / 2, color },
          ]);
          break;
        }

        case DrawTool.CIRCLE_OUTLINE: {
          if (!hasDrawn.current) {
            markAsDrawn(imgId);
            hasDrawn.current = true;
          }
          const strokeW = realSize / 8;
          const radius = realSize / 2;
          setStrokes(prev => [
            ...prev,
            {
              tool,
              cx: pos.x,
              cy: pos.y,
              radius,
              color,
              strokeWidth: strokeW,
            },
          ]);
          break;
        }
      }
    };

    const handleMouseMove = (e: any) => {
      if (!isDrawing.current || disabled || tool === DrawTool.NONE || e.evt.buttons === 0) return;
      const stagePos = e.target.getStage().getPointerPosition();
      if (!stagePos) return;
      const pos = screenToContent(stagePos);
      setStrokes(prev => {
        const last = prev[prev.length - 1];
        if (last.tool === DrawTool.PENCIL || last.tool === DrawTool.ERASER) {
          return [
            ...prev.slice(0, -1),
            { ...last, points: [...(last as any).points, pos.x, pos.y] },
          ];
        }
        return prev;
      });
    };

    const handleMouseUp = () => {
      isDrawing.current = false;
    };

    const undo = () => {
      setStrokes(prev => {
        const last = prev[prev.length - 1];
        const next = prev.slice(0, -1);
        if (
          last &&
          last.tool === DrawTool.PENCIL &&
          !next.some(s => s.tool === DrawTool.PENCIL) &&
          hasDrawn.current
        ) {
          unMarkAsDrawn(imgId);
          hasDrawn.current = false;
        }
        return next;
      });
    };

    const exportImage = (): Promise<Blob> =>
      new Promise(res => {
        const uri = stageRef.current.toDataURL({ pixelRatio: 2 });
        fetch(uri)
          .then(r => r.blob())
          .then(res);
      });

    useImperativeHandle(ref, () => ({ undo, exportImage }), [strokes]);

    const cursorStyle = useMemo(() => {
      if (disabled) return 'not-allowed';
      if (tool === DrawTool.NONE) return 'default';
      const toolSize = size[tool];
      let svg = '';
      let hotspot = toolSize;
      switch (tool) {
        case DrawTool.PENCIL: {
          const r = toolSize / 2;
          svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${toolSize}" height="${toolSize}"><circle cx="${r}" cy="${r}" r="${r}" fill="${color}"/></svg>`;
          hotspot = r;
          break;
        }
        case DrawTool.ERASER: {
          const r = toolSize / 2;
          svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${toolSize}" height="${toolSize}"><circle cx="${r}" cy="${r}" r="${r}" stroke="black" stroke-width="1" fill="none"/></svg>`;
          hotspot = r;
          break;
        }
        case DrawTool.CIRCLE: {
          const d = toolSize;
          const r = d / 2;
          svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${d}" height="${d}"><circle cx="${r}" cy="${r}" r="${r}" fill="${color}"/></svg>`;
          hotspot = r;
          break;
        }
        case DrawTool.CIRCLE_OUTLINE: {
          const d = toolSize;
          const r = d / 2;
          const sw = r / 4;
          const ir = r - sw / 2;
          svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${d}" height="${d}"><circle cx="${r}" cy="${r}" r="${ir}" stroke="${color}" stroke-width="${sw}" fill="none"/></svg>`;
          hotspot = r;
          break;
        }
        default:
          return 'default';
      }
      const uri = `data:image/svg+xml;base64,${window.btoa(svg)}`;
      return `url("${uri}") ${hotspot} ${hotspot}, auto`;
    }, [disabled, tool, size, color]);

    useEffect(() => {
      const stopDraw = () => {
        isDrawing.current = false;
      };

      window.addEventListener('mouseup', stopDraw);
      window.addEventListener('touchend', stopDraw);

      return () => {
        window.removeEventListener('mouseup', stopDraw);
        window.removeEventListener('touchend', stopDraw);
      };
    }, []);

    return (
      <Stage
        ref={stageRef}
        width={stageSize.w}
        height={stageSize.h}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{ cursor: cursorStyle }}
      >
        <Layer listening={false}>
          {bgImg && (
            <Group
              x={transform.offX}
              y={transform.offY}
              scaleX={transform.scale}
              scaleY={transform.scale}
              listening={false}
            >
              <KonvaImage image={bgImg} width={bgImg.naturalWidth} height={bgImg.naturalHeight} />
            </Group>
          )}
        </Layer>

        <Layer>
          <Group
            x={transform.offX}
            y={transform.offY}
            scaleX={transform.scale}
            scaleY={transform.scale}
          >
            {strokes.map((s, i) => {
              if (s.tool === DrawTool.PENCIL || s.tool === DrawTool.ERASER) {
                return (
                  <Line
                    key={i}
                    points={(s as any).points}
                    stroke={s.tool === DrawTool.ERASER ? '#000' : (s as any).color}
                    strokeWidth={s.size}
                    lineCap="round"
                    lineJoin="round"
                    globalCompositeOperation={
                      s.tool === DrawTool.ERASER ? 'destination-out' : 'source-over'
                    }
                  />
                );
              }

              const c = s as any;
              if (s.tool === DrawTool.CIRCLE) {
                return <Circle key={i} x={c.cx} y={c.cy} radius={c.radius} fill={c.color} />;
              }

              return (
                <Circle
                  key={i}
                  x={c.cx}
                  y={c.cy}
                  radius={c.radius - c.strokeWidth / 2}
                  stroke={c.color}
                  strokeWidth={c.strokeWidth}
                />
              );
            })}
          </Group>
        </Layer>
      </Stage>
    );
  }
);

export default DrawingCanvas;
