import { Flex, Form } from 'antd';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';

import EditableTable from '@/components/common/core/CommonTable/EditableTable';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import Icon from '@/components/common/core/Icon';
import { useGetOptionsQuery } from '@/store/karute/api';
import { DATE_FORMATS, PLACEHOLDER_MESSAGE_DEFAULT } from '@/types/constants';
import { convertMapToOptions } from '@/utils';

import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import { GetKeikaOption } from '@/store/karute/type';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { DatabaseSchema, PatientOption } from '../UpsertDatabaseForm/types';
import { CourseItem } from './ListCourseTable';
import { OptionCourseActionBtn } from './OptionCourseActionBtn';
import styles from './UpsertOptionListForm.module.scss';
// import Button from '@/components/common/core/Button';
// const sampleErrors = {
//   'patient_options.0.option_id': ['初診日はY-m-dの形式に適合していません。 0'],
//   'patient_options.0.quantity': ['数量は必須項目です。 0'],
//   'patient_options.1.option_id': ['初診日はY-m-dの形式に適合していません。'],
//   'patient_options.1.quantity': ['数量は必須項目です。'],
// };

const ADD_NEW_OPTION_BTN = '新規作成';
export type OptionItem = {
  rowId?: string;
  isInit?: boolean;
} & Omit<PatientOption, 'patient_id'>;

// Description
// There will be at least one Option in the same day
export type OptionTableProps = {
  optionDataSource: OptionItem[];
  setOptionDataSource: React.Dispatch<React.SetStateAction<OptionItem[]>>;
  optionSelectedKeys: string[];
  setOptionSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
  service_id?: number;
  // isDisabled?: boolean;
  scrollToFirstSameDayRow?: (treatment_date: string) => void;
  isActionable: (record: OptionItem) => boolean;
  initialOptionData?: PatientOption[] | GetKeikaOption[];
  handleUnsavedValueChange?: ({
    optionData,
    courseData,
    database,
  }: {
    optionData?: OptionItem[];
    courseData?: CourseItem[];
    database?: DatabaseSchema;
  }) => void;
};

export type OptionTableRef = {
  handleOptionErrors: (errorData: Record<string, string[]>) => void;
  handleSubmittingAllEditingRows: () => Promise<OptionItem[] | null>;
  getAllCurrentData: () => OptionItem[];
  scrollToRow: (rowId: string) => void;
};

export const initOptionData: OptionItem = {
  treatment_date: dayjs().format(DATE_FORMATS.DATE),
  quantity: null,
  isInit: true,
  name: '',
  registration_date: dayjs().format(DATE_FORMATS.DATE),
};

export const ListOptionTable = forwardRef<OptionTableRef, OptionTableProps>(
  (
    {
      optionDataSource,
      setOptionDataSource,
      optionSelectedKeys,
      setOptionSelectedKeys,
      service_id,
      scrollToFirstSameDayRow,
      // isDisabled,
      isActionable,
      initialOptionData,
      handleUnsavedValueChange,
    },
    ref
  ) => {
    const [form] = Form.useForm<Record<string, OptionItem>>();
    const { serviceId: activeService } = useParams();
    const [, forceUpdate] = useState(0);
    const triggerRerender = () => {
      forceUpdate(prev => prev + 1);
    };
    const isDisabled =
      (optionDataSource ?? []).length == 0 ||
      (optionDataSource.length > 0 && !isActionable(optionDataSource[0]));
    const { data: optionData } = useGetOptionsQuery(
      { service_id: service_id || activeService },
      {
        skip: !service_id && !activeService,
      }
    );
    const optionMap = useMemo(() => {
      // Logic: to set deleted options
      const map: Record<number, string> = {};

      for (const item of optionData?.data ?? []) {
        map[item.option_id] = item.name;
      }

      for (const item of initialOptionData ?? []) {
        if (item.option_id && !(item.option_id in map)) {
          map[item.option_id as number] = item.name ?? 'TMP';
        }
      }

      return map;
    }, [optionData, initialOptionData]);

    const getOption = (record: OptionItem) => {
      if (record?.option_id) {
        return optionMap[Number(record.option_id)];
      }
      return '';
    };

    const isFetching: boolean = false;
    const mapFormValuesFromRowIds = () => {
      // when changing datasource
      // -> form will be remap -> have to reset form value
      const updatedValues: Record<string, any> = {};
      optionDataSource.forEach(option => {
        if (!optionSelectedKeys.includes(option.rowId!)) return;
        const key = option.rowId;
        const formValue = form.getFieldValue(key);
        if (formValue) {
          updatedValues[key!] = formValue;
        }
      });
      setTimeout(() => {
        form.setFieldsValue({
          ...updatedValues,
        });
      }, 0);
    };
    const scrollToRow = (rowId: string) => {
      setTimeout(() => {
        document
          .querySelector(`.scroll-row_option_${rowId}`)
          ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 1);
    };

    const handleAddRecord = () => {
      if (isDisabled) return;

      const firstOption = optionDataSource[0];
      const firstDate = firstOption?.treatment_date;
      const newRowId = uuidv4();

      const newRecord = {
        rowId: newRowId,
        ...initOptionData,
        treatment_date: firstDate ? firstDate : dayjs().format(DATE_FORMATS.DATE),
        registration_date: '',
      };
      // Push new option to the after latest same treatment date option
      setOptionDataSource(prev => [newRecord, ...prev]);
      setOptionSelectedKeys(prev => [...new Set([...prev, newRowId])]);

      mapFormValuesFromRowIds();
      const currentOptionData = getAllCurrentData() ?? [];

      handleUnsavedValueChange?.({
        optionData: newRecord
          ? [...currentOptionData, newRecord].sort((a, b) =>
              dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
            )
          : currentOptionData,
      });
      setTimeout(() => {
        form.setFieldsValue({
          [newRowId]: newRecord,
        });
      }, 0);
      scrollToRow(newRowId);
    };
    const getAllCurrentData = () => {
      const currentDataSource = optionDataSource.map(item => {
        const targetsData = form.getFieldValue(item.rowId);
        return {
          ...targetsData,
        };
      });
      return currentDataSource;
    };
    const handleDeleteRecord = (rowId: string) => {
      mapFormValuesFromRowIds();
      setOptionDataSource(prev => prev.filter(value => value.rowId != rowId));
      setOptionSelectedKeys(prev => prev.filter(value => value != rowId));
    };
    const handleCancelRecord = (record: OptionItem) => {
      if (record?.isInit) {
        // handleDeleteRecord(record.rowId!);
        return;
      }
      const originalRecord = optionDataSource.find(item => item.rowId === record.rowId);
      form.setFieldValue([record.rowId!, 'option_id'], originalRecord?.option_id);
      form.setFieldValue([record.rowId!, 'quantity'], originalRecord?.quantity);
      setOptionSelectedKeys(optionSelectedKeys.filter(item => item !== record.rowId!));
    };
    const handleConfirmEdit = (record: OptionItem) => {
      // conditions are already handled in OptionCourseActionBtn

      const dataUpdated: OptionItem = form.getFieldValue(record.rowId!);
      const isUpdated = optionDataSource.filter(it => it.rowId === record.rowId);
      if (isUpdated.length > 0) {
        const registration_date = dayjs().format(DATE_FORMATS.DATE);
        const name = optionMap[dataUpdated.option_id as number];
        setOptionDataSource(
          [...optionDataSource].map(data =>
            data.rowId === record.rowId
              ? {
                  ...dataUpdated,
                  registration_date,
                  isInit: false,
                  name,
                }
              : data
          )
        );
      } else {
        const newDataSource = optionDataSource
          .filter(item => item.rowId !== record.rowId)
          .concat({ ...dataUpdated });
        setOptionDataSource(newDataSource);
      }
      setOptionSelectedKeys(optionSelectedKeys.filter(item => item !== String(record.rowId)));
      mapFormValuesFromRowIds();
    };

    useEffect(() => {
      const formValues = optionDataSource.reduce((acc, item) => {
        acc[item.rowId!] = item;
        return acc;
      }, {} as Record<string, object>);
      form.setFieldsValue(formValues);
    }, [form, optionDataSource]);

    const handleOptionErrors = (errorData: Record<string, string[]>) => {
      const optionErrors: { name: (string | number)[]; errors: string[] }[] = [];
      const rowsWithErrors: string[] = [];

      Object.entries(errorData).forEach(([fieldName, errors]) => {
        const fieldPath = fieldName
          .split('.')
          .map(part => (isNaN(Number(part)) ? part : Number(part)));
        if (
          ['patient_options', 'keika_options'].includes(fieldPath[0] as string) &&
          fieldPath.length === 3
        ) {
          const index = Number(fieldPath[1]);
          const field = fieldPath[2];

          const row = optionDataSource[index];

          if (row && row.rowId) {
            optionErrors.push({
              name: [row.rowId, field],
              errors,
            });
            rowsWithErrors.push(row.rowId.toString());
          }
        }
      });

      if (optionErrors.length > 0) {
        setTimeout(() => {
          form.setFields(optionErrors as any);
          triggerRerender();
        }, 1);
      }
      if (rowsWithErrors.length > 0) {
        setOptionSelectedKeys(rowsWithErrors);
        setTimeout(() => {
          form.scrollToField([rowsWithErrors[0], 'option_id'], {
            behavior: 'smooth',
            block: 'center',
          });
        }, 1);
      }
    };
    const handleSubmittingAllEditingRows = async (): Promise<OptionItem[] | null> => {
      try {
        const updatedDataSource = optionDataSource.map(data => {
          if (isEditableKeys.includes(data.rowId!.toString())) {
            const dataUpdated: OptionItem = form.getFieldValue(data.rowId!);
            const registration_date = dayjs().format(DATE_FORMATS.DATE);
            return {
              ...dataUpdated,
              registration_date,
            };
          }
          return data;
        });

        // Update data source
        setOptionDataSource(updatedDataSource);

        // Clear selected keys (exit editing mode for all rows)
        setOptionSelectedKeys([]);

        return updatedDataSource;
      } catch (error) {
        // Handle validation errors
        const errorFields = (error as any).errorFields;
        if (errorFields && errorFields.length > 0) {
          const rowsWithErrors = errorFields
            .map((err: any) => err.name[0].toString())
            .filter((rowId: string) => optionSelectedKeys.includes(rowId));

          if (rowsWithErrors.length > 0) {
            setOptionSelectedKeys(rowsWithErrors); // Keep error rows in edit mode
            form.scrollToField([rowsWithErrors[0], 'option_id'], {
              behavior: 'smooth',
              block: 'center',
            });
          }
        }
        return null;
      }
    };
    const canDelete = (record: OptionItem) => {
      if (!record.treatment_date) return false;
      // Only the second option in the same day can be deleted
      // -> purpose only delete option created from add new
      const recordsWithSameDate = optionDataSource.filter(
        item => item.treatment_date === record.treatment_date
      );

      const currentIndex = recordsWithSameDate.findIndex(item => item.rowId === record.rowId);

      return currentIndex < recordsWithSameDate.length - 1;
    };

    // Expose handleOptionErrors thông qua useImperativeHandle
    useImperativeHandle(ref, () => ({
      handleOptionErrors,
      handleSubmittingAllEditingRows,
      getAllCurrentData,
      scrollToRow,
    }));
    const filteredOptionDataSource = useMemo(() => {
      return optionDataSource.filter(record => isActionable(record));
    }, [optionDataSource, isActionable]);

    const isEditableKeys = useMemo(() => {
      return optionSelectedKeys.filter(key => {
        const record = filteredOptionDataSource.find(item => item.rowId === key);
        return record && isActionable(record);
      });
    }, [optionSelectedKeys, filteredOptionDataSource, isActionable]);
    const isEditable = useCallback(
      (data: OptionItem) => {
        return isEditableKeys.includes(data.rowId?.toString() || '');
      },
      [isEditableKeys]
    );
    const columns: CustomColumnType<OptionItem>[] = [
      {
        title: '初診日',
        dataIndex: 'treatment_date',
        key: 'treatment_date',
        width: 88,
        align: 'right',
        onCell: (record: OptionItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
          // style: {
          //   maxHeight: '84px',
          //   display: 'flex',
          //   alignItems: 'center',
          //   justifyContent: 'right',
          //   width: '100%',
          // },
        }),
        render: (_: unknown, record) => {
          const isDisabled = !isActionable(record);
          return <div style={{ opacity: isDisabled ? 0.5 : 1 }}>{record.treatment_date}</div>;
        },
      },
      {
        title: 'オプション名',
        // dataIndex: 'option_id',
        key: 'option_id',
        width: 202,
        editable: {
          type: 'select',
          options: convertMapToOptions(optionMap),
          showSearch: true,
          style: {
            height: '38px',
            // width: '178px',
            // fontSize: '12px !important',
          },
          placeholder: PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN,
        },
        render: (_, record) => {
          const isDisabled = !isActionable(record);
          return (
            <div className={styles.cellContentWrapper} style={{ opacity: isDisabled ? 0.5 : 1 }}>
              <RenderWithTooltip text={getOption(record)} />
            </div>
          );
        },
        onCell: (record: OptionItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
        }),
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
        width: 140,
        editable: {
          type: 'input',
          maxLength: 100,
          placeholder: PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT,
          style: {
            height: '38px',
            // width: '100px',
          },
        },
        align: 'left',
        render: (_, record) => {
          const isDisabled = !isActionable(record);
          return (
            <div
              className={clsx(styles.cellContentWrapper, {
                [styles.cellContentDisabled]: isDisabled,
              })}
            >
              <RenderWithTooltip text={record.quantity?.toString() || ''} />
            </div>
          );
        },
        onCell: (record: OptionItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
        }),
      },
      {
        title: '登録日',
        dataIndex: 'registration_date',
        key: 'registration_date',
        width: 88,
        align: 'right',
        onCell: (record: OptionItem) => ({
          onDoubleClick: () => {
            scrollToFirstSameDayRow?.(record.treatment_date);
          },
        }),
        render: (_: unknown, record: OptionItem) => {
          const isDisabled = !isActionable(record);
          return <div style={{ opacity: isDisabled ? 0.5 : 1 }}>{record.registration_date}</div>;
        },
      },
      {
        title: '',
        key: 'actions',
        align: 'center',
        width: 70,
        fixed: 'right',
        render: (_: any, record: OptionItem) => {
          return (
            // Cần phải kéo check ra ngoài để lắng nghe isDirty theo từng rowId nhằm hiển thị
            <OptionCourseActionBtn
              key={record.rowId}
              mode="option"
              handleCancelRecord={() => {
                handleCancelRecord(record);
              }}
              handleConfirmEdit={() => {
                handleConfirmEdit(record);
              }}
              handleOpenEdit={() =>
                setOptionSelectedKeys(prevState => [...prevState, record?.rowId?.toString() || ''])
              }
              handleDeleteRecord={() => {
                handleDeleteRecord(record.rowId!);
              }}
              isEdit={isEditable(record)}
              record={record}
              canDelete={canDelete(record)}
              isDisabled={!isActionable(record)}
            />
          );
        },
      },
    ];

    return (
      <>
        <Form
          form={form}
          className={clsx(styles.option_table, styles.table)}
          onValuesChange={() => {
            handleUnsavedValueChange?.({ optionData: getAllCurrentData() });
          }}
        >
          <EditableTable
            rowKey={record => record.rowId!}
            columns={columns}
            editingKeys={isEditableKeys}
            pagination={false}
            dataSource={optionDataSource}
            scroll={{ y: 162 }}
            loading={isFetching}
            isMargin
            rowClassName={record => `scroll-row_option_${record.rowId}`}
          />
          <Flex
            justify="center"
            align="center"
            gap="small"
            className={clsx(
              styles.add_record_btn,
              isDisabled ? styles.disabled_add_record_btn : styles.active_add_record_btn
            )}
            onClick={handleAddRecord}
          >
            <Flex
              justify="center"
              align="center"
              gap="small"
              className={styles.disabled_add_record_btn_text}
            >
              <Icon name="plus" color="#194060" height={11.68} width={11.68} />
              <span className="fs14-bold">{ADD_NEW_OPTION_BTN}</span>
            </Flex>
          </Flex>
        </Form>
      </>
    );
  }
);
