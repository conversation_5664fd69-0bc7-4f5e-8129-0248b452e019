import Table from '@/components/common/core/CommonTable';
import { Ka<PERSON>teMondai } from '@/types/interface/KaruteMondai';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  MONDAI_STATUS_OPTION,
  MONDAI_TYPE,
  PAGINATION_THRESHOLD,
} from '@/types/constants';
import styles from './VisitDetailForm.module.scss';
import { useGetMondaiListQuery } from '@/store/mondai/api';
import { useCallback, useState } from 'react';

import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { ESort } from '@/types/enum';
import { useVisitDetail } from '../provider';
import dayjs from 'dayjs';
export default function VisitDetailMondaiListTable() {
  const { patient } = useVisitDetail();

  const columns: CustomColumnType<Partial<KaruteMondai>>[] = [
    {
      title: '記載日',
      dataIndex: 'updated_at',
      sortable: true,
      sortKey: 'updated_at',
      align: 'right',
      width: '16%',
      render: (updated_at: string) =>
        updated_at ? dayjs(updated_at).format(DATE_FORMATS.DATE) : '',
    },
    {
      title: '問題番号',
      dataIndex: 'trauma_type',
      sortable: true,
      sortKey: 'trauma_type',
      align: 'center',
      width: '16%',

      render(value, record) {
        const mondai_type_mapping =
          MONDAI_TYPE[Number(record.trauma_type) as keyof typeof MONDAI_TYPE];
        return (
          <div className={styles.mondaiType}>
            <p>{mondai_type_mapping}</p>
            <p>
              {mondai_type_mapping}
              {record.trauma_counter}
            </p>
          </div>
        );
      },
    },
    {
      title: '問題名',
      dataIndex: 'subject',
      sortable: true,
      sortKey: 'subject',
      align: 'left',
      width: '16%',
    },
    {
      title: '備考',
      dataIndex: 'remarks',
      sortable: true,
      sortKey: 'remarks',
      align: 'left',
      width: '16%',
    },
    {
      title: '統合先',
      dataIndex: 'mondai_related',
      //TODO: mondai_related should be mapped to the string M1, M2, M3
      sortable: true,
      sortKey: 'related_id',
      width: '16%',

      render: (val: unknown, record: Partial<KaruteMondai>) => {
        if (Array.isArray(record.mondai_related)) {
          return (
            <div>
              {record.mondai_related.map((item, idx: number) => {
                const mondai_type_mapping =
                  MONDAI_TYPE[Number(item.trauma_type) as keyof typeof MONDAI_TYPE];

                if (!mondai_type_mapping) return null; // Skip if mondai_type_mapping is undefined
                return (
                  <span key={item.mondai_id || idx} className={styles.mondaiType}>
                    {mondai_type_mapping}
                    {item.trauma_counter}
                    {Array.isArray(record.mondai_related) && idx < record.mondai_related.length - 1
                      ? COMMA_SEPARATED
                      : ''}
                  </span>
                );
              })}
            </div>
          );
        }
        return record.mondai_related;
      },
      align: 'left',
    },
    {
      title: '活性状態',
      dataIndex: 'status',
      sortable: true,
      sortKey: 'status',
      align: 'center',
      width: '16%',

      render: (_: unknown, record: Partial<KaruteMondai>) => (
        <div className={styles.mondaiStatus}>
          <div className={`${record.status === 1 ? styles.active : styles.inactive}`}>
            {MONDAI_STATUS_OPTION[record.status as keyof typeof MONDAI_STATUS_OPTION]}
          </div>
        </div>
      ),
    },
  ];
  const [queryParams, setQueryParams] = useState<{
    page: number;
    limit: number;
    trauma_type: number | undefined;
    status: number;
    order_by?: string;
    order_type?: ESort;
  }>({
    page: 1,
    limit: PAGINATION_THRESHOLD,
    trauma_type: undefined,
    status: 1,
  });

  const handleSortChange = useCallback((sortData: { order_by: string; order_type: ESort }[]) => {
    const newSort = sortData.length > 0 ? sortData[0] : null;

    if (!newSort || !newSort.order_type) {
      setQueryParams({
        ...queryParams,
        order_by: undefined,
        order_type: undefined,
      });
    } else {
      setQueryParams({
        ...queryParams,
        order_by: newSort.order_by,
        order_type: newSort.order_type,
      });
    }
  }, []);
  const { data: mondaiData, isFetching } = useGetMondaiListQuery(
    { ...queryParams, patient_id: patient?.patient_id },
    {
      skip: !patient?.patient_id,
    }
  );
  const handlePageChange = (page: number) => {
    setQueryParams({
      ...queryParams,
      page,
      limit: PAGINATION_THRESHOLD,
    });
  };
  return (
    <Table<Partial<KaruteMondai>>
      className={styles.visitDetailMondaiListTable}
      rowKey={record => record.mondai_id!}
      dataSource={mondaiData?.data?.data ?? []}
      columns={columns}
      pagination={
        mondaiData && mondaiData?.data?.total && mondaiData?.data?.total > PAGINATION_THRESHOLD
          ? {
              current: mondaiData?.data?.current_page ?? 1,
              pageSize: mondaiData?.data?.per_page ?? PAGINATION_THRESHOLD,
              showSizeChanger: false,
              total: mondaiData?.data?.total ?? 0,
              onChange: handlePageChange,
            }
          : false
      }
      scroll={{ y: 159 }}
      loading={isFetching}
      onSortChange={handleSortChange}
    />
  );
}
