import { HISTORY_CATEGORY_ID } from '@/components/page-components/karute-managment/karte-input/SchemaModal/type';
import { SchemaImage } from '@/store/schema/type';

export const getImgKey = (img: SchemaImage) => String(img.schema_image_id ?? img.id);

export const compressImageToSize = async (
  file: File,
  targetMB = 20,
  mimeType = 'image/jpeg',
  qualityStep = 0.05
): Promise<File> => {
  const blobToFile = (blob: Blob, fname: string) => new File([blob], fname, { type: blob.type });

  const img = await new Promise<HTMLImageElement>((res, rej) => {
    const reader = new FileReader();
    reader.onload = e => {
      const image = new Image();
      image.onload = () => res(image);
      image.onerror = rej;
      image.src = e.target?.result as string;
    };
    reader.onerror = rej;
    reader.readAsDataURL(file);
  });

  const canvas = document.createElement('canvas');
  canvas.width = img.width;
  canvas.height = img.height;
  canvas.getContext('2d')!.drawImage(img, 0, 0);

  let quality = 0.92;
  let blob = await canvasToBlobSafe(canvas, mimeType, quality);

  while (blob.size / 1024 / 1024 > targetMB && quality > qualityStep) {
    quality -= qualityStep;
    blob = await canvasToBlobSafe(canvas, mimeType, quality);
  }

  return blobToFile(blob, file.name.replace(/\.\w+$/, '.jpg'));
};

const canvasToBlobSafe = (cvs: HTMLCanvasElement, type: string, q: number): Promise<Blob> => {
  return new Promise((res, rej) => {
    cvs.toBlob(
      blob => {
        if (blob) res(blob);
        else rej(new Error('toBlob returned null'));
      },
      type,
      q
    );
  });
};

export const urlToDataUrl = async (url: string): Promise<string> => {
  const blob = await fetch(url, { mode: 'cors' }).then(r => r.blob());

  return await new Promise(res => {
    const fr = new FileReader();
    fr.onloadend = () => res(fr.result as string);
    fr.readAsDataURL(blob);
  });
};

const uniqById = (list: SchemaImage[]) =>
  Array.from(new Map(list.map(i => [getImgKey(i), i])).values());

export const mergeList = (
  current: SchemaImage[],
  fetched: SchemaImage[],
  categoryId: number,
  deletedId?: number | string | null
): SchemaImage[] => {
  const keep = current.filter(
    i => i.isTemp || i.category_schema_id !== categoryId || categoryId === HISTORY_CATEGORY_ID
  );

  const map = new Map<string, SchemaImage>();
  keep.concat(fetched).forEach(img => {
    const key = String(img.schema_image_id ?? img.id);
    const prev = map.get(key) || {};
    map.set(key, { ...prev, ...img });
  });

  if (categoryId === HISTORY_CATEGORY_ID) {
    fetched.forEach(img => {
      const key = String(img.schema_image_id ?? img.id);
      map.set(key, { ...map.get(key)!, isHistory: true });
    });
  }

  if (deletedId) {
    map.delete(String(deletedId));
  }

  return uniqById(Array.from(map.values()));
};
