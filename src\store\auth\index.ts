import { LOCAL_STORAGE_KEY } from '@/types/constants/app';
import { ROLE } from '@/types/enum';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from './type';
import { Clinic } from '@/types/interface/User';

interface AuthStates {
  role: ROLE | null;
  expiredAt: number | null;
  isAuthenticated: boolean;
  accessToken: string | null;
  user: User | null;
  clinics: Clinic[];
  currentClinicCd: string | null;
  currentClinicId: number | null;
}

const initialState: AuthStates = {
  user: null,
  role: null,
  expiredAt: null,
  isAuthenticated: false,
  accessToken: '',
  clinics: [],
  currentClinicCd: null,
  currentClinicId: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    login(state, action: PayloadAction<any>) {
      const { access_token, expires_in, user } = action.payload;
      state.accessToken = access_token;
      state.user = user || null;
      state.role = user.role;
      state.expiredAt = Date.now() + expires_in * 1000;
      state.isAuthenticated = true;
      localStorage.setItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, access_token);
    },
    logout(state) {
      state.accessToken = null;
      state.user = null;
      state.role = null;
      state.expiredAt = null;
      state.isAuthenticated = false;
      state.clinics = [];
      state.currentClinicCd = null;
      state.currentClinicId = null;
      localStorage.removeItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
    },
    selectClinic(state, action: PayloadAction<string>) {
      state.currentClinicCd = action.payload;
      state.currentClinicId =
        state.clinics.find(c => c.clinic_cd === action.payload)?.clinic_id || null;
    },
    setClinics(state, action: PayloadAction<{ clinics: Clinic[] | null }>) {
      state.clinics = action.payload.clinics || [];
    },
  },
});

export const { login, logout, selectClinic, setClinics } = authSlice.actions;
// reducer
export const authReducer = authSlice.reducer;
