.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  button {
    outline: none;
    border: none;
    border-radius: 8px;
    transition: border-color 0.2s ease, color 0.2s ease, background-color 0.2s ease;
    font-weight: 700;

    //#region css for size
    &.sm {
      height: 36px;
      padding: 8px 14px 8px 14px;
      font-size: 14px;
      line-height: 20px;
    }
    &.md {
      height: 40px;
      padding: 10px 16px 10px 16px;
      font-size: 14px;
      line-height: 20px;
    }
    &.lg {
      height: 44px;
      padding: 10px 18px 10px 18px;
      font-size: 16px;
      line-height: 24px;
    }
    &.xl {
      height: 48px;
      padding: 12px 20px 12px 20px;
      font-size: 16px;
      line-height: 24px;
    }
    &.xxl {
      height: 60px;
      padding: 16px 28px 16px 28px;
      font-size: 18px;
      line-height: 28px;
    }
    &.xs {
      height: 32px;
      padding: 8px 12px 8px 12px;
      font-size: 12px;
      line-height: 16px;
    }
    &.xxs {
      height: 30px;
      padding: 6px 12px 6px 12px;
      font-size: 12px;
      line-height: 16px;
    }
    //#endregion
  }

  //#region css for type
  &.primary {
    button {
      background-color: $brand-800;
      color: white;
      border: none;
      box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
      &:not([disabled]) {
        &:hover {
          background-color: $brand-900 !important;
          color: white;
          // border: solid 1px $brand-900;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
        }

        &:active {
          background-color: $brand-700 !important;
          color: white;
          outline: none;
          // border: solid 1px $brand-700;
          box-shadow: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }
  &.secondary-brand {
    button {
      background-color: $brand-600;
      color: white;
      border: none;
      box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
      &:not([disabled]) {
        &:hover {
          background-color: $brand-700 !important;
          color: white;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
        }

        &:active {
          color: white;
          outline: none;
          box-shadow: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.secondary-brand-2 {
    button {
      background-color: $brand-700;
      color: white;
      border: none;
      box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
      &:not([disabled]) {
        &:hover {
          background-color: $brand-800 !important;
          color: white;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
        }

        &:active {
          color: white;
          outline: none;
          box-shadow: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.secondary-color {
    button {
      background-color: transparent;
      color: $brand-900;
      border: 1px solid $brand-800;
      box-shadow: none;
      &:not([disabled]) {
        &:hover {
          background-color: $brand-100;
          border: 1px solid $brand-800;
          color: $brand-900;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: transparent;
          border: 1px solid $brand-700 !important;
          color: $brand-900;
          outline: none;
          box-shadow: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.secondary-gray {
    button {
      background-color: white;
      color: $gray-700;
      border: 1px solid $gray-300;
      box-shadow: none;
      &:not([disabled]) {
        &:hover {
          background-color: $gray-50;
          border: 1px solid $gray-300;
          color: $gray-800;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: white;
          border: 1px solid $gray-400;
          color: $gray-700;
          outline: none;
          box-shadow: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.secondary-blue {
    button {
      background-color: $blue-700;
      color: white;
      border: none;
      box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
      &:not([disabled]) {
        &:hover {
          background-color: $blue-900 !important;
          color: white;
          // border: solid 1px $brand-900;
          box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
        }

        &:active {
          // background-color: $brand-700 !important;
          color: white;
          outline: none;
          // border: solid 1px $brand-700;
          box-shadow: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.tertiary-color {
    button {
      background-color: transparent;
      color: $brand-900;
      border: none;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: $brand-100;
          border: none;
          color: $brand-900;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: transparent;
          color: $brand-900;
          border: none;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.tertiary-gray {
    button {
      background-color: white;
      color: $gray-600;
      border: none;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: $gray-50;
          border: none;
          color: $gray-700;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: white;
          color: $gray-600;
          border: none;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.link-color {
    button {
      background-color: transparent;
      color: $brand-900;
      border: none;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: transparent;
          border: none;
          color: $brand-800;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: transparent;
          color: $brand-900;
          border: none;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.link-gray {
    button {
      background-color: transparent;
      color: $gray-600;
      border: none;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: transparent;
          border: none;
          color: $gray-700;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: transparent;
          color: $gray-600;
          border: none;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.red-primary {
    button {
      background-color: $error-700;
      color: white;
      border: solid 1px $error-700;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: $error-800;
          border: solid 1px $error-800;
          color: white;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: $error-600;
          color: white;
          border: none;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        background-color: white;
        color: $error-700;
        border: solid 1px $error-300;
        box-shadow: none;
        outline: none;
        opacity: 30%;
      }
    }
  }

  &.red-secondary-gray {
    button {
      background-color: white;
      color: $error-700;
      border: 1px solid $error-300;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: $error-100;
          border: 1px solid $error-300;
          color: $error-600;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: white;
          color: $error-700;
          border: 1px solid $error-400;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }

  &.red-tertiary-color {
    button {
      background-color: transparent;
      color: $error-800;
      border: none;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: #fef3f2;
          border: none;
          color: $error-800;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: transparent;
          color: $error-800;
          border: none;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        background-color: transparent;
        color: $error-300;
        border: none;
        box-shadow: none;
        outline: none;
      }
    }
  }

  &.red-secondary {
    button {
      background-color: $error-100;
      color: $error-600 !important;
      border: solid 1px $error-100;
      box-shadow: none;
      outline: none;
      &:not([disabled]) {
        &:hover {
          background-color: $error-200;
          border: solid 1px $error-200;
          outline: none;
          box-shadow: none;
        }

        &:active {
          background-color: $error-200;
          border: solid 1px $error-200;
          box-shadow: none;
          outline: none;
        }
      }
      &:disabled {
        opacity: 30%;
      }
    }
  }
  //#endregion

  :global(button.ant-btn .ant-btn-loading-icon) {
    display: none !important;
  }

  :global(button.ant-btn.ant-btn-loading > span:not(.ant-btn-loading-icon)) {
    margin-left: 0 !important;
  }
}
