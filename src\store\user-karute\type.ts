export interface UserUpdateMetaPayload {
  meta?: Record<string, unknown>;
}

export interface DetailUserResponse {
  user_id: number;
  email: string;
  email_verified_at: string | null;
  username: string;
  status: number;
  kana_name: string;
  kanji_name: string;
  provider: string;
  manageable_type: string;
  manageable_id: number;
  is_doctor: number;
  can_access_karute: number;
  can_access_booking: number;
  address1: string;
  address2: string;
  phone: string;
  postal_code: string;
  birthday: string; // ISO date format (e.g. '2025-05-14')
  avatar: string;
  gender: number;
  specialization: string | null;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
  deleted_at: string | null;
  meta: Record<string, unknown>;
}
