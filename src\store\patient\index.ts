import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { PatientItem } from './type';

export type patientStates = {
  list: PatientItem[];
};

const initialState: patientStates = {
  list: [],
};

// slice
export const patientSlice = createSlice({
  name: 'patient',
  initialState,
  reducers: {
    setPatient: (state, actions: PayloadAction<patientStates>) => {
      state.list = actions.payload.list;
    },
  },
});

// actions
export const { setPatient } = patientSlice.actions;

// reducer
export const patientReducer = patientSlice.reducer;
