import { LOCAL_STORAGE_KEY } from '@/types/constants';

export function setToken({ token, from }: { token: string; from: 'karute' | 'booking' }) {
  localStorage.setItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, token);
  localStorage.setItem(LOCAL_STORAGE_KEY.FROM, from);
  window.dispatchEvent(new Event('auth-change'));
}

export function clearToken() {
  localStorage.removeItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_KEY.FROM);
  window.dispatchEvent(new Event('auth-change'));
}
