.main-content-info {
  background-color: var(--white);
  border-radius: 8px;
  overflow: hidden;
  .header-block {
    background-color: var(--brand-600);
    padding: 8px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-left {
      display: flex;
      align-items: center;
      span {
        margin-left: 4px;
        font-size: 14px;
        font-weight: 700;
        color: var(--white);
      }
    }
  }
  .padded-content-info {
    padding: 12px;
  }
}
.bordered {
  border: 1px solid $gray-200;
}

@media screen and (max-width: 800px) {
  .main-content-info {
    padding: 0;
    border-radius: 1px solid $gray-200;
    .header-block {
      padding: 8px;
      .header-left {
        span {
          font-size: 12px;
        }
      }
    }
    .padded-content-info {
      > .ant-flex {
        flex-flow: column;
      }
    }
  }
}
