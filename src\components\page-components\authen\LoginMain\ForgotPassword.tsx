import {
  ENV,
  ERROR_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  STATUS_CODES,
} from '@/types/constants';
import styles from './LoginMain.module.scss';
import { Form } from 'antd';
import { AppFormItem } from '@/components/common/core/FormItem';
import { useState } from 'react';
import Button from '@/components/common/core/Button';

import clsx from 'clsx';
import { useRequestResetPasswordMutation } from '@/store/auth/api';
import { ForgotPasswordParams } from '@/store/auth/type';
import { AuthBox } from '../AuthBox';
import { AppInput } from '@/components/common/core/Input';
import { ButtonType } from '@/types/enum';

const FORM_FIELDS = {
  clinic_cd: {
    label: '治療院ID',
    requiredMessage: ERROR_COMMON_MESSAGE.REQUIRED('治療院ID'),
    type: 'text',
  },
  username: {
    label: 'ユーザーID',
    requiredMessage: ERROR_COMMON_MESSAGE.REQUIRED('ユーザーID'),
    type: 'text',
  },
  email: {
    label: 'メールアドレス',
    requiredMessage: ERROR_COMMON_MESSAGE.REQUIRED('メールアドレス'),
    type: 'email',
  },
} as const;

type Props = {
  returnToLogin: () => void;
};

const listSuccessMessage = {
  line1: '1時間以内にメール記載のリンクをクリックし、パスワードの再設定を行ってください。',
  line2:
    '迷惑メールに振り分けられていたり、フィルターや転送設定で受信ボックス以外に届く場合があります。',
  line3: 'ネットワークの状況により届くまで時間がかかる場合があります。',
  line4: '1時間以内にアクセスしていただけなかった場合は、再度お手続きが必要となります',
};

const SendEmailSuccess = ({ returnToLogin }: Props) => {
  return (
    <div className={styles.reset_password_success}>
      <AuthBox
        title={
          <div style={{ width: '439px', textAlign: 'center' }}>
            <p className={`fs20-medium`}>パスワード再設定ページのURLを送信しました。</p>
            <p className={`fs20-medium`}>受信メールをご確認ください。</p>
          </div>
        }
      >
        {Object.values(listSuccessMessage).map((item, index) => (
          <p key={index} className={clsx('fs14-regular', styles.emailRequestSuccessLine)}>
            ※{item}
          </p>
        ))}
        <Button
          type="primary"
          customSize="2xl"
          onClick={returnToLogin}
          style={{ width: '100%' }}
          className={styles.loginButton}
        >
          戻る
        </Button>
      </AuthBox>
    </div>
  );
};
export default function ForgotPassword({ returnToLogin }: Props) {
  const [form] = Form.useForm<ForgotPasswordParams>();

  const [requestResetPasswordMutation, { isLoading }] = useRequestResetPasswordMutation();
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleSubmit = async (values: ForgotPasswordParams) => {
    requestResetPasswordMutation({
      ...values,
      username: values.username?.trim(),
      clinic_cd: values.clinic_cd?.trim(),
      email: values.email?.trim(),
      domain: ENV.DOMAIN_URL,
    })
      .then(res => {
        const status = res.data?.status;
        if (status !== STATUS_CODES.OK) {
          setErrorMessage(res.data?.message ?? '');
          return;
        }
        setIsSuccess(true);
        form.resetFields();
        setErrorMessage('');
      })
      .catch(err => {
        setErrorMessage(err.error.data.message);
      });
  };

  if (isSuccess) {
    return <SendEmailSuccess returnToLogin={returnToLogin} />;
  }
  const footerContent = (
    <a className={clsx(styles.loginFooter, 'fs14-medium')} onClick={returnToLogin}>
      ログイン画面に戻る
    </a>
  );

  return (
    <AuthBox
      title={<span className={` fs24-medium`}>パスワードを再発行</span>}
      footerContent={footerContent}
    >
      <Form
        form={form}
        className={styles.loginInput}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        {Object.entries(FORM_FIELDS).map(([name, field]) => (
          <AppFormItem
            key={name}
            name={name}
            rules={[{ required: true, whitespace: true, message: field.requiredMessage }]}
            label={field.label}
          >
            <AppInput
              type={field.type}
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
              width="100%"
              disabled={isLoading}
            />
          </AppFormItem>
        ))}
        <div className="fs14-regular">
          <p className={styles.resetPasswordDescription}>
            「パスワード再発行ページのURL」を登録メールアドレスに送信します。
          </p>
          <p className={styles.resetPasswordDescription}>
            治療院ID、ユーザーID、メールアドレスを入力し［送信する］をクリックしてください。
          </p>
        </div>

        <AppFormItem className={styles.forgetPasswordButton}>
          <Button
            type={ButtonType.PRIMARY}
            customSize="2xl"
            htmlType="submit"
            loading={isLoading}
            style={{ width: '100%' }}
          >
            送信する
          </Button>
          <p
            className={clsx(
              styles.forgetPasswordFail,
              'fs14-regular',
              errorMessage ? styles.visible : ''
            )}
          >
            {errorMessage
              ? errorMessage.split(/<br\s*\/?>/i).map((line, idx, arr) => (
                  <span key={idx}>
                    {line}
                    {idx !== arr.length - 1 && <br />}
                  </span>
                ))
              : null}
          </p>
        </AppFormItem>
      </Form>
    </AuthBox>
  );
}
