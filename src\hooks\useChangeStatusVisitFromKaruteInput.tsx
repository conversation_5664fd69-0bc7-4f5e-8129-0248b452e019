import { store } from '@/config/redux/store';
import {
  useChangeVisitStatusInProgressMutation,
  useChangeVisitStatusNotYetMutation,
} from '@/store/visit/api';
import { LOCAL_STORAGE_KEY } from '@/types/constants';
import { useEffect } from 'react';

export function useVisitStatusSync(patientId?: number) {
  const [setInProgress] = useChangeVisitStatusInProgressMutation();
  const [setNotYet] = useChangeVisitStatusNotYetMutation();

  const BASE_URL = import.meta.env.VITE_API_BASE_URL;
  useEffect(() => {
    if (!patientId) return;
    const KEY = `karuteInputOpenTabs:${patientId}`;

    const current = Number(localStorage.getItem(KEY) || 0);
    if (current === 0) setInProgress({ patient_id: patientId });
    localStorage.setItem(KEY, String(current + 1));

    const fireNotYetSync = () => {
      const body = JSON.stringify({ patient_id: patientId });

      const token = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      const clinicCd = store.getState().auth.currentClinicCd;

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'cache-control': 'no-cache',
      };
      if (token) headers.Authorization = `Bearer ${token}`;
      if (clinicCd) headers['clinic-cd'] = clinicCd;

      fetch(`${BASE_URL}/api/visits/status/not-yet`, {
        method: 'PUT',
        headers,
        body,
        keepalive: true,
      });

      setNotYet({ patient_id: patientId });
    };

    const closedRef = { current: false };

    const handleTabClose = () => {
      if (closedRef.current) return;
      closedRef.current = true;
      const after = Math.max(Number(localStorage.getItem(KEY) || 1) - 1, 0);
      if (after <= 0) {
        localStorage.removeItem(KEY);
        fireNotYetSync();
      } else {
        localStorage.setItem(KEY, String(after));
        setInProgress({ patient_id: patientId });
      }
    };

    window.addEventListener('pagehide', handleTabClose, { once: true });
    window.addEventListener('beforeunload', handleTabClose);

    const handleStorage = (e: StorageEvent) => {
      if (e.key !== KEY) return;
      const oldVal = Number(e.oldValue || 0);
      const newVal = Number(e.newValue || 0);
      if (oldVal === 0 && newVal > 0) setInProgress({ patient_id: patientId });
      if (oldVal > 0 && newVal === 0) setNotYet({ patient_id: patientId });
    };
    window.addEventListener('storage', handleStorage);

    return () => {
      handleTabClose();
      window.removeEventListener('pagehide', handleTabClose);
      window.removeEventListener('beforeunload', handleTabClose);
      window.removeEventListener('storage', handleStorage);
    };
  }, [patientId]);
}
