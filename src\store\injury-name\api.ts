import api from '@/config/axios/api';
import { APIResponse } from '@/types/interface';
import { InjuryBlock, OptionsByPartType, OptionsBySide, PartType } from './type';

export const injuryNameApi = {
  getOptionsByBlock: async () => {
    const response = await api.get<APIResponse<InjuryBlock>>('/api/injuries/get-options-by-block');
    return response.data;
  },
  getPartType: async () => {
    const response = await api.get<APIResponse<PartType[]>>('/api/injury-types');
    return response.data;
  },
  getOptionsByPartType: async () => {
    const response = await api.get<APIResponse<OptionsByPartType>>(
      '/api/injuries/get-options-by-part-type'
    );
    return response.data;
  },
  getOptionsBySide: async () => {
    const response = await api.get<APIResponse<OptionsBySide>>('/api/injuries/get-options-by-side');
    return response.data;
  },
};

export const { getOptionsByBlock, getPartType, getOptionsByPartType, getOptionsBySide } =
  injuryNameApi;
