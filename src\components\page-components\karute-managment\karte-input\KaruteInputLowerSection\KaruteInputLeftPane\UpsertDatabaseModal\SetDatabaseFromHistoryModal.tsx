// Screen: K104_データベースの履歴からセット;
import { AppModal } from '@/components/common/core/Modal';
import styles from '../../KaruteInputLowerSection.module.scss';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import { Flex, Form } from 'antd';
import { AppFormItem } from '@/components/common/core/FormItem';
import { AppInput } from '@/components/common/core/Input';
import Button from '@/components/common/core/Button';
import {
  ButtonType,
  EDatabaseUpsertMode,
  EKaruteServicePriority,
  EKaruteUpsertType,
  ESort,
  Order,
} from '@/types/enum';
import Table, { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import {
  //  PAGINATION_THRESHOLD,
  routerPaths,
} from '@/types/constants';
import { useState } from 'react';
import Icon from '@/components/common/core/Icon';
import { useGetDatabaseHistoryQuery } from '@/store/karute/api';
import { DiseaseHistoryItem } from '@/store/karute/type';
import { useParams } from 'react-router-dom';
import { useAppSelector } from '@/config/redux/store';
import { getAuth } from '@/store/auth/selectors';
import { useKaruteInput } from '../../../provider';
import clsx from 'clsx';
import { clearSpace } from '@/utils';
const SetFromHistoryDatabaseHeader = 'データベースの履歴からセット' as const;
const PAGINATION_THRESHOLD = 30;
type SetFromHistoryParams = {
  injury_name?: string;
  search?: string;
};

export function SetDatabaseFromHistoryModal({
  handleClose,
  handleSubmit,
}: {
  handleClose: () => void;
  handleSubmit: (item: DiseaseHistoryItem) => void;
}) {
  const [form] = Form.useForm<SetFromHistoryParams>();
  const [selectedItem, setSelectedItem] = useState<DiseaseHistoryItem | null>(null);
  const { serviceId } = useParams();

  const [searchParams, setSearchPrams] = useState<{
    page: number;
    injury_name?: string;
    search?: string;
    order_by?: string;
    order_type?: Order;
  }>({ page: 1 });
  // States
  const auth = useAppSelector(getAuth);
  // Custom hooks
  const { karuteDetailData } = useKaruteInput();
  const patientId = karuteDetailData?.patient_id;

  const { data: mondaiHistory, isFetching } = useGetDatabaseHistoryQuery(
    {
      service_id: Number(serviceId) as EKaruteServicePriority,
      clinic_id: auth.currentClinicId!,
      patient_id: patientId!,
      limit: PAGINATION_THRESHOLD,
      ...searchParams,
    },
    {
      skip: !patientId || !serviceId,
    }
  );
  // Functions

  const handlePageChange = (page: number) => {
    setSearchPrams({
      ...searchParams,

      page,
    });
  };
  const handleSortChange = (
    sortData: {
      order_by: string;
      order_type: ESort.ASC | ESort.DESC;
    }[]
  ) => {
    const newSort = sortData.length > 0 ? sortData[0] : null;
    if (!newSort || !newSort.order_type) {
      setSearchPrams({
        ...searchParams,
        order_by: undefined,
        order_type: undefined,
      });
    } else {
      setSearchPrams({
        ...searchParams,
        order_by: newSort.order_by,
        order_type: newSort.order_type,
      });
    }
  };

  const showDetailItem = async (record: DiseaseHistoryItem) => {
    const windowPath = window.location.pathname.split(`/karute-management`)[0];
    const path = routerPaths.karuteManagement.karuteUpsertModalSegment
      .replace(':upsertType', EKaruteUpsertType.DATABASE)
      .replace(':upsertMode', `${EDatabaseUpsertMode.UPSERT}_${record.disease_base_id}`);

    const fullPath = `${windowPath}/karute-management/${record.karute_id}/${serviceId}${path}`;
    window.open(fullPath, '_blank', 'noopener,noreferrer');
  };

  const onFormSearchSubmit = (values: SetFromHistoryParams) => {
    if (isFetching) {
      return;
    }
    setSearchPrams({
      ...values,
      injury_name: clearSpace(values.injury_name),
      search: clearSpace(values.search),
      page: 1,
    });
    setSelectedItem(null);
  };
  const handleClear = () => {
    if (isFetching) {
      return;
    }
    form.setFieldsValue({
      injury_name: '',
      search: '',
    });
    setSelectedItem(null);
    // setSearchPrams({
    //   ...searchParams,
    //   page: 1,
    //   injury_name: undefined,
    //   search: undefined,
    //   order_by: undefined,
    //   order_type: undefined,
    // });
  };
  const onHandleSubmit = async () => {
    if (selectedItem) {
      handleSubmit(selectedItem);
    }
  };

  const columns: CustomColumnType<DiseaseHistoryItem>[] = [
    {
      title: '問題名',
      dataIndex: 'injury_name',
      sortable: true,
      sortKey: 'injury_name',
      width: 90,
    },
    {
      title: '主訴（S）',
      dataIndex: 'subjective',
      sortable: true,
      sortKey: 'subjective',
      width: 160,
    },
    {
      title: '現病歴（S）',
      dataIndex: 'present_illness',
      sortable: true,
      sortKey: 'present_illness',
      width: 160,
    },
    {
      title: '現症（O）',
      dataIndex: 'objective',
      sortable: true,
      sortKey: 'objective',
      width: 160,
    },
    {
      title: '病態把握（A）',
      dataIndex: 'assessment',
      sortable: true,
      sortKey: 'assessment',
      width: 160,
    },
    {
      title: '施術計画（P）',
      dataIndex: 'plan',
      sortable: true,
      sortKey: 'plan',
      width: 160,
    },
    {
      title: '施術（T）',
      dataIndex: 'treatment',
      sortable: true,
      sortKey: 'treatment',
      width: 160,
    },
    {
      title: '備考（R）',
      dataIndex: 'remarks',
      sortable: true,
      sortKey: 'remarks',
      width: 160,
    },
    {
      title: '患者名',
      dataIndex: 'patient_name',
      sortable: true,
      sortKey: 'patient_name',
      width: 110,
    },
    {
      title: '施術師',
      dataIndex: 'doctor_name',
      sortable: true,
      sortKey: 'doctor_name',
      width: 80,
    },
    {
      title: '',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 50,

      render: (record: DiseaseHistoryItem) => (
        <button
          onClick={() => {
            showDetailItem(record);
          }}
        >
          <Icon name="eyeFilled" color="#609CAD" />
        </button>
      ),
      onCell: () => {
        return {
          onDoubleClick: e => {
            e.preventDefault();
            e.stopPropagation();
          },
        };
      },
    },
  ];

  return (
    <AppModal
      width="1000px"
      className={styles.set_from_history_modal}
      onCancel={handleClose}
      open
      destroyOnClose
      zIndex={1500}
      isPadding={false}
    >
      <div className={styles.set_from_history_modal_header}>
        <ModalTitle title={SetFromHistoryDatabaseHeader} />

        <Form
          form={form}
          onFinish={onFormSearchSubmit}
          initialValues={{
            mondai_name: null,
            patient_name: null,
          }}
        >
          <Flex gap="small" align="flex-start">
            <AppFormItem className={styles.input_common} name="injury_name">
              <AppInput placeholder="問題名" size="small" disabled={isFetching} />
            </AppFormItem>
            <AppFormItem className={styles.input_common} name="search">
              <AppInput placeholder="氏名・カナ、患者番号" size="small" disabled={isFetching} />
            </AppFormItem>
            <Button
              customType={ButtonType.PRIMARY}
              customSize="md"
              htmlType="submit"
              loading={isFetching}
              className={styles.submit_btn}
            >
              検索
            </Button>
            <Button
              customType={ButtonType.SECONDARY_COLOR}
              onClick={handleClear}
              customSize="md"
              className={styles.submit_btn}
              disabled={isFetching}
            >
              <Icon name="retry" height={20} width={20} />
              クリア
            </Button>
          </Flex>
        </Form>
      </div>
      <Table<DiseaseHistoryItem>
        columns={columns}
        rowKey={record => record?.disease_base_id}
        dataSource={mondaiHistory?.data?.data ?? []}
        pagination={
          mondaiHistory && mondaiHistory?.data && mondaiHistory?.data?.total > PAGINATION_THRESHOLD
            ? {
                current: mondaiHistory?.data?.current_page ?? 1,
                pageSize: mondaiHistory?.data?.per_page ?? PAGINATION_THRESHOLD,
                showSizeChanger: false,
                total: mondaiHistory?.data?.total ?? 0,
                onChange: handlePageChange,
              }
            : false
        }
        onSortChange={handleSortChange}
        scroll={{ y: 540 }}
        loading={isFetching}
        className={styles.set_from_history_modal_table}
        rowClassName={record =>
          selectedItem && record.disease_base_id === selectedItem?.disease_base_id
            ? 'ant-table-row-selected'
            : ''
        }
        onRow={record => {
          return {
            onClick: () => {
              setSelectedItem(record);
            },
            onDoubleClick: () => {
              onHandleSubmit();
            },
            style: {
              cursor: 'pointer',
            },
          };
        }}
        total={mondaiHistory?.data?.total_items}
        style={{ marginBottom: mondaiHistory?.data?.total ? '0' : '20px' }}
      />
      {!!mondaiHistory?.data?.total && (
        <Flex
          className={clsx(
            styles.set_from_history_modal_footer,
            (mondaiHistory?.data?.total ?? 0) > PAGINATION_THRESHOLD ? 'mt-2' : 'mt-6'
          )}
          gap="middle"
          justify="end"
        >
          <Button
            customType={ButtonType.SECONDARY_COLOR}
            onClick={handleClose}
            className="btn-modal-width"
            customSize="lg"
          >
            キャンセル
          </Button>
          <Button
            customType={ButtonType.PRIMARY}
            className="btn-modal-width"
            onClick={onHandleSubmit}
            disabled={!selectedItem || isFetching}
            loading={isFetching}
            customSize="lg"
          >
            適用
          </Button>
        </Flex>
      )}
    </AppModal>
  );
}
