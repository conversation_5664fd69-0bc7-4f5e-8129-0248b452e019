import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse, ListResponse } from '@/types/interface';
import { KaruteMondai } from '@/types/interface/KaruteMondai';
import { KarutesParams } from '../karute/type';

export const mondaiApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,
    getMondaiList: build.query<APIResponse<ListResponse<KaruteMondai>>, KarutesParams>({
      query: params => {
        const filteredParams = Object.fromEntries(
          Object.entries(params || {}).filter(
            ([, value]) => value !== null && value !== undefined && value !== ''
          )
        );
        return {
          url: `/api/mondai`,
          method: 'GET',
          params: filteredParams,
          cache: 'no-cache',
        };
      },
      providesTags: ['mondai-list'],
    }),
    getMondaiHistory: build.query<APIResponse<KaruteMondai[]>, Pick<KaruteMondai, 'mondai_id'>>({
      query: ({ mondai_id }: Pick<KaruteMondai, 'mondai_id'>) => ({
        url: `api/mondai/${mondai_id}/history`,
        method: 'GET',
        cache: 'no-cache',
      }),
    }),
    updateMondaiById: build.mutation<APIResponse<KaruteMondai>, Partial<KaruteMondai>>({
      query: ({ mondai_id, ...body }) => ({
        url: `api/mondai/${mondai_id}`,
        body: body,
        method: 'PUT',
      }),
    }),
    createMondai: build.mutation<APIResponse<KaruteMondai>, Partial<KaruteMondai>>({
      query: ({ ...body }) => ({
        url: `api/mondai`,
        body: body,
        method: 'POST',
      }),
    }),
  }),
});

export const {
  useGetMondaiListQuery,
  useUpdateMondaiByIdMutation,
  useCreateMondaiMutation,
  useLazyGetMondaiHistoryQuery,
  useLazyGetMondaiListQuery,
} = mondaiApi;
