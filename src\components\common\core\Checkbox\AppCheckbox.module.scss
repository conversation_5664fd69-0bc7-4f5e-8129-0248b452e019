.appCheckbox {
  font-size: 16px !important;
  line-height: 22px !important;
  font-weight: 500;
  &:global(.ant-checkbox-wrapper-disabled) {
    // opacity: 0.4;
    :global(.ant-checkbox-label) {
      color: var(--checkbox-text-disabled) !important;
    }
  }
  :global(.ant-checkbox) {
    :global(.ant-checkbox-inner) {
      // background-color: blue;
      background-color: var(--white);
      width: 20px !important;
      height: 20px !important;
      // border-color: var(--checkbox-border-unchecked);
      border-width: 1.5px;
      border-radius: 4px;
    }
  }

  :global(.ant-checkbox-checked) {
    :global(.ant-checkbox-inner) {
      background-color: var(--checkbox-bg-checked) !important;
      border-color: var(--checkbox-border-checked) !important;
    }

    :global(.ant-checkbox-inner)::after {
      border-color: $brand-600 !important;
      inset-inline-start: 28%;
      top: 46%;
    }
  }
}
