import Table from '@/components/common/core/CommonTable';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import Icon from '@/components/common/core/Icon';
import AppSaveReceiveModal from '@/components/common/modal/SaveReceiveModal/SaveReceiveModal';
import { useAppSelector } from '@/config/redux/store';
import { usePatientNumberWarning } from '@/hooks/usePatientNumberWarning';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import {
  useLazyCheckPatientCdQuery,
  useLazyGetDetailsPatientQuery,
  useLazyGetInsuranceInfoQuery,
} from '@/store/patient/api';
import { PatientItem } from '@/store/patient/type';
import { getSelection } from '@/store/visit/selector';
import {
  DATE_FORMATS,
  genderMap,
  NOTICE_COMMON_MESSAGE,
  PAGINATION_THRESHOLD,
} from '@/types/constants';
import { routerPaths } from '@/types/constants/routerPath';
import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Flex, MenuProps } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { EGender } from '../create/FormCreate/type';
import InsuranceTypeFilter from './InsuranceTypeFilter';
import styles from './ListPatientPage.module.scss';
import { useListPatient } from './provider/ListPatientProvider';

type FORM_STATE = 'visit_registration';

export default function ListPatientPageTable() {
  const navigate = useNavigate();
  const { getPath } = useRouterPath();
  const dashboardPath = getPath(routerPaths.dashboard);

  const storedSelectionState = useSelector(getSelection);
  const [searchParams, setSearchParams] = useSearchParams();
  const formState = (searchParams.get('form_state') as FORM_STATE) || '';
  const selectPatientId = (searchParams.get('patient_id') as FORM_STATE) || '';
  const { showPatientNumberWarning } = usePatientNumberWarning();
  const [fetchInsurance] = useLazyGetInsuranceInfoQuery();
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const { handlePageChange, handleSortChange, patients, isFetching } = useListPatient();

  const { doctors: doctorsData, isLoading: isFetchingDoctorData } = useAppSelector(
    state => state.sharedData
  );

  const [fetchPatient, { data: detailPatientData, isFetching: isFetchingPatientData }] =
    useLazyGetDetailsPatientQuery();
  const [checkPatientCd] = useLazyCheckPatientCdQuery();

  useEffect(() => {
    if (selectPatientId) {
      fetchPatient(Number(selectPatientId));
    }
  }, [selectPatientId]);
  const patientUpdatePath = (id: number | string) =>
    getPath(routerPaths.patientManagement.patientUpdate(id));
  const getMenuItems = (record: PatientItem): MenuProps['items'] => [
    {
      label: '受付登録',
      key: '1',
      icon: <Icon name="addSquare" width={20} height={20} />,
      onClick: async () => {
        fetchInsurance(Number(record.patient_id)).then((res: any) => {
          if (!res.data?.data?.has_insurance) {
            showWarningDialog({
              message: NOTICE_COMMON_MESSAGE.WARNING_HOKEN_MSG,
              buttons: [
                {
                  type: 'cancel',
                  label: 'キャンセル',
                  onClick: () => {
                    hideWarningDialog();
                  },
                },
                {
                  type: 'confirm',
                  label: '続行する',
                  onClick: () => {
                    hideWarningDialog();
                    setSearchParams({
                      form_state: 'visit_registration',
                      patient_id: String(record?.patient_id),
                    });
                  },
                },
              ],
            });
          } else {
            setSearchParams({
              form_state: 'visit_registration',
              patient_id: String(record?.patient_id),
            });
          }
        });
      },
    },
    {
      label: '詳細・編集',
      key: '2',
      icon: <Icon name="edit" width={20} height={20} />,
      onClick: async () => {
        await checkPatientCd(record.patient_id).then(data => {
          if (data.data?.data?.has_patient_code) {
            navigate(patientUpdatePath(record.patient_id));
            return;
          } else {
            showPatientNumberWarning(record.patient_id, () => {
              navigate(patientUpdatePath(record.patient_id));
            });
          }
          return;
        });
      },
    },
  ];

  const columns: CustomColumnType<PatientItem>[] = [
    {
      title: 'No',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      // fixed: 'left',
      render: (_: any, record: PatientItem, index: number) =>
        index + 1 + (patients?.per_page ?? 0) * ((patients?.current_page ?? 1) - 1),

      width: 80,
    },
    {
      title: '患者番号',
      dataIndex: 'patient_cd',
      sortable: true,
      align: 'right',
      sortKey: 'patient_cd',
      // fixed: 'left',
      width: 100,
    },
    {
      title: '氏名（漢字）',
      dataIndex: 'name',
      sortable: true,
      sortKey: 'name',
      width: 118,
    },
    {
      title: '氏名（カナ）',
      dataIndex: 'kana',
      sortable: true,
      sortKey: 'kana',
      width: 119,
    },
    {
      title: '性別',
      dataIndex: 'gender',
      sortable: true,
      align: 'center',
      sortKey: 'gender',
      render: (gender: EGender) => genderMap[gender],
      width: 68,
    },
    {
      title: '生年月日',
      dataIndex: 'dob',
      sortable: true,
      align: 'right',
      sortKey: 'birthday',
      render: (_, record: PatientItem) =>
        record.birthday ? dayjs(record.birthday).format(DATE_FORMATS.DATE) : '',
      width: 90,
    },
    {
      title: '住所',
      dataIndex: 'address',
      sortable: true,
      sortKey: 'address',
      width: 95,
      render: (_, record) => `${(record?.address1 ?? '').trim()}${(record?.address2 ?? '').trim()}`,
    },
    {
      title: '電話番号',
      dataIndex: 'cellphone',
      sortable: true,
      sortKey: 'cellphone',
      align: 'right',

      width: 95,
    },
    {
      title: '最新来院日',
      dataIndex: 'intake_date',
      sortable: true,
      align: 'right',
      sortKey: 'latest_intake_date',
      render: (_, record: PatientItem) =>
        record.latest_intake_date ? dayjs(record.latest_intake_date).format(DATE_FORMATS.DATE) : '',
      width: 100,
    },
    {
      title: '保険種類',
      dataIndex: 'hoken_type',
      sortable: true,
      sortKey: 'hoken_type',
      width: 92,
      align: 'left',
      render: (hoken_type: number) => {
        return !hoken_type || hoken_type === 0
          ? ''
          : storedSelectionState.selection.insurance_types.find(
              item => String(item.value).includes(String(hoken_type)) // insured type actually have string type
            )?.label;
      },
    },
    {
      title: '',
      key: 'action',
      align: 'center',
      width: 50,
      render: (_, record) => (
        <Dropdown menu={{ items: getMenuItems(record) }} trigger={['hover']}>
          <MoreOutlined style={{ fontSize: '16px', cursor: 'pointer' }} />
        </Dropdown>
      ),
    },
  ];
  return (
    <>
      <div className={styles.list_patient_page_table_data}>
        <Flex align="center" justify="space-between" className={styles.table_header} gap={'middle'}>
          <div style={{ flexShrink: '0' }}>
            <InsuranceTypeFilter />
          </div>
          <span className={styles.total_count}>
            人数：{patients?.total_filtered || 0}/{patients?.total_items || 0}人
          </span>
        </Flex>
        <Table
          className={styles.table}
          rowKey={record => record.patient_id}
          columns={columns}
          dataSource={patients?.data ?? []}
          pagination={
            patients && patients.total > PAGINATION_THRESHOLD
              ? {
                  current: patients?.current_page ?? 1,
                  pageSize: patients?.per_page ?? PAGINATION_THRESHOLD,
                  showSizeChanger: false,
                  total: patients?.total ?? 0,
                  onChange: handlePageChange,
                }
              : false
          }
          onSortChange={handleSortChange}
          scroll={{ y: 400 }}
          loading={isFetching}
          total={patients?.total_items}
        />
      </div>
      {selectPatientId && formState === 'visit_registration' && (
        <AppSaveReceiveModal
          initialData={detailPatientData?.data ?? {}}
          isModalOpen
          onCancel={() => {
            setSearchParams({});
          }}
          onSubmitVisit={() => navigate(dashboardPath)}
          doctorList={doctorsData || []}
          isLoadingInitialData={isFetchingPatientData || isFetchingDoctorData}
        />
      )}
    </>
  );
}
