.pageContent {
  color: $gray-800;
  .tableWrapper {
    margin-top: 16px;
    background-color: white;
    border-radius: 8px;
    .tableHeader {
      padding: 12px;
    }

    :global {
      .ant-table-tbody .ant-table-row .ant-table-cell div {
        color: $gray-800;
        &:has(.statusTag) {
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: unset;
          p {
            border-radius: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 80px;
            height: 23px;
            text-wrap: nowrap;
            // VISIT_STATUS
            &.status-1 {
              color: $gray-700;
              background-color: $gray-100;
            }
            &.status-2 {
              color: $warning-700;
              background-color: #f6f5de;
            }
            &.status-3 {
              color:  $pink-700;
              background-color: $pink-50;
            }
            &.status-4 {
              color: $success-700;
              background-color: #eef4e2;
            }
            &.status-5 {
              color: $blue-700;
              background-color: #eff8ff;
            }
            &.status-6 {
              color: $error-600;
              background-color: $error-100;
            }
            &.status-7 {
              color: $brand-700;
              background-color: $brand-100;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 800px) {
  .pageContent {
    width: 100%;
    > .ant-input-affix-wrapper {
      max-width: 167px;
      width: 167px;
    }
    .tableWrapper {
      margin-top: 0;
      background-color: white;
      border-radius: 0;
      .tableHeader {
        padding: 0;
        flex-wrap: wrap;
        padding: 12px 16px;
      }
      .filters {
        flex-wrap: wrap;
        margin-bottom: 16px;
        > div {
          width: calc(50% - 4px);
          flex: none;
        }
      }
    }
  }
  :global(.ant-modal-wrap.qr-scan-modal) {
    width: 100% !important;
    max-width: 100% !important;
    height: calc(100% - 56px);
    margin-top: 56px;

    :global(.ant-modal) {
      margin: unset;
      height: 100%;
      width: 100% !important;
      max-width: 100% !important;
    }
    :global(.ant-modal > div) {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
    }
    :global(.ant-modal-content) {
      height: 100%;
      padding: 16px;
    }
  }
  :global(.ant-modal-wrap.save-receive-modal) {
    width: 100% !important;
    max-width: 100% !important;
    height: calc(100% - 56px);
    margin-top: 56px;
    :global(.ant-modal) {
      margin: unset;
      height: 100%;
      width: 100% !important;
      max-width: 100% !important;
    }

    :global(.ant-modal > div) {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-content) {
      max-height: calc(100vh - 56px);
      overflow-y: auto;
      height: 100%;
      padding: 16px !important;
    }
  }
}
.recordCount {
  color: $gray-500;
}
