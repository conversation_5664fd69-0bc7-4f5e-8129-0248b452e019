export enum EKaruteUpsertType {
  KEIKA = 'keika',
  DATABASE = 'database',
}
export enum EDatabaseUpsertMode {
  UPSERT = 'upsert',
  CHANGE_TYPE = 'change_type',
}
export type KeikaUpsertMode = 'create' | string;




export type EKaruteField =
  | 'subjective'
  | 'present_illness'
  | 'objective'
  | 'assessment'
  | 'plan'
  | 'treatment'
  | 'remarks';

export enum EKaruteFieldCode {
  S = 'S',
  O = 'O',
  A = 'A',
  P = 'P',
  T = 'T',
  R = 'R',
}

export enum EDatabaseMedicalField {
  Personal = 'personal',
  Family = 'family',
  Social = 'social',
}

export type KaruteMasterField = EKaruteField | EDatabaseMedicalField;
export type KaruteMasterFieldCode = EKaruteFieldCode | EDatabaseMedicalField;

export type IsReferenceModalOpen = {
  name?: number;
  field: KaruteMasterField;
  label: string;
  code: <PERSON><PERSON><PERSON>MasterFieldCode;
} | null;