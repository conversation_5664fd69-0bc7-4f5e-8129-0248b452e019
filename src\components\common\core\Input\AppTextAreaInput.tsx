import { forwardRef } from 'react';
import { Input, InputRef } from 'antd';
import clsx from 'clsx';

import styles from './AppInput.module.scss';
import { TextAreaProps } from 'antd/es/input';

const { TextArea } = Input;

export const AppTextArea = forwardRef<InputRef, TextAreaProps>(
  ({ className, status, disabled, ...rest }, ref) => {
    const classNames = clsx(className, styles.appInput, {
      [styles.errorInput]: status === 'error',
    });

    return (
      <TextArea ref={ref} disabled={disabled} className={classNames} status={status} {...rest} />
    );
  }
);

AppTextArea.displayName = 'AppTextArea';
