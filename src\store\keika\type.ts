import { KeikaDetail, KeikaImage } from '../karute/type';

export type KeikaRecord = {
  keika_id: number;
  option_checked: number;
  karute_id: number;
  visit_id: number;
  visit_date: string; // ISO datetime string
  service_id: number;
  created_at: string;
  updated_at?: string;
  keika_details: KeikaDetail[];
  objective_images: KeikaImage[];
  treatment_images: KeikaImage[];
};

export type ConditionCheckCreateKeika = {
  can_create: boolean;
};
