import Icon from '@/components/common/core/Icon';
import { Table as AntdTable, DatePicker, Empty, Form, Input, Select, TableProps } from 'antd';
import { useEffect, useState } from 'react';
import { CustomColumnType } from './Table';
import styles from './Table.module.scss';
import { ESort, Order } from '@/types/enum';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { Rule } from 'antd/es/form';

export interface CustomTableProps<T> extends TableProps<T> {
  editingKeys?: string[];
  onSortChange?: (sortData: { order_by: string; order_type: ESort.ASC | ESort.DESC }[]) => void;
}

type Rules = {
  rules?: Rule[];
  maxLength?: number;
  disabled?: <T extends object>(args: T) => boolean;
};

type InputRecord = {
  type: 'input' | 'textarea';
} & Rules;

type SelectRecord = {
  type: 'select';
  mode?: 'multiple' | 'tags';
  options: { label: string; value: string | number }[];
  render?: <T extends object>(args: T) => JSX.Element;
  disabled?: boolean;
} & Rules;

type DateRecord = {
  type: 'date';
  format: string;
} & Rules;

export type EditableRecord = InputRecord | SelectRecord | DateRecord;

interface EditableCellProps<T> extends React.HTMLAttributes<HTMLElement> {
  editing: boolean | EditableRecord;
  record: T;
  index: number;
  value: string | number;
  name: string;
  rowId: number;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps<object>>> = ({
  editing,
  name,
  children,
  value,
  rowId,
  record,
  ...restProps
}) => {
  if (typeof editing === 'boolean' && editing) {
    return (
      <td {...restProps}>
        <Form.Item
          validateTrigger={['onChange', 'onBlur']}
          name={[rowId, name]}
          style={{ margin: 0 }}
        >
          <Input defaultValue={value} />
        </Form.Item>
      </td>
    );
  }

  if (typeof editing === 'object') {
    const { type } = editing;

    const elementNode =
      type === 'select' ? (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-evenly' }}>
          <Form.Item
            validateTrigger={['onChange', 'onBlur']}
            rules={editing.rules}
            name={[rowId, name]}
            style={{ margin: 0 }}
          >
            <Select
              mode={editing.mode}
              defaultValue={value}
              value={value}
              options={editing.options}
              style={{
                width: '80px',
              }}
              allowClear
              disabled={editing.disabled}
            />
          </Form.Item>
          {editing.render && (
            <div style={{ paddingLeft: '16px', borderLeft: '1px solid var(--gray-200)' }}>
              {editing.render(record)}
            </div>
          )}
        </div>
      ) : type === 'date' ? (
        <Form.Item
          validateTrigger={['onChange', 'onBlur']}
          getValueProps={i => ({
            value: i ? dayjs(i) : '',
          })}
          rules={editing.rules}
          name={[rowId, name]}
          style={{ margin: 0 }}
        >
          <DatePicker defaultValue={value} value={value} format={editing.format ?? 'YYYY-MM-dd'} />
        </Form.Item>
      ) : (
        <Form.Item
          validateTrigger={['onChange', 'onBlur']}
          rules={editing.rules}
          name={[rowId, name]}
          style={{ margin: 0 }}
        >
          <Input
            disabled={editing.disabled && editing.disabled(record)}
            defaultValue={value}
            value={value}
          />
        </Form.Item>
      );
    return <td {...restProps}>{elementNode}</td>;
  }
  return <td {...restProps}>{children}</td>;
};

function EditableTable<T extends object>({
  columns,
  dataSource = [],
  editingKeys = [],
  onSortChange,
  ...rest
}: CustomTableProps<T>) {
  const [tableData, setTableData] = useState<T[]>([]);
  const [sortState, setSortState] = useState<{
    order_by: string | null;
    order_type: Order;
  }>({
    order_by: null,
    order_type: null,
  });

  const handleSortClick = (key?: string) => {
    setSortState(prev => {
      let newOrderType: Order = null;

      if (prev.order_by !== key) {
        newOrderType = ESort.ASC;
      } else {
        if (prev.order_type === ESort.ASC) newOrderType = ESort.DESC;
        else if (prev.order_type === ESort.DESC) newOrderType = null;
        else newOrderType = ESort.ASC;
      }

      const newState = {
        order_by: newOrderType ? key ?? null : null,
        order_type: newOrderType,
      };

      if (onSortChange) {
        const payload =
          newState.order_by && newState.order_type
            ? [{ order_by: newState.order_by, order_type: newState.order_type }]
            : [];
        onSortChange(payload);
      }

      return newState;
    });
  };

  useEffect(() => {
    if (dataSource) {
      const transformedData = dataSource.map((item, index) => ({
        ...item,
        rowId: index + 1,
      })) as (T & { rowId: number })[];

      setTableData(transformedData);
    }
  }, [dataSource]);

  const mergedColumns: TableProps<T>['columns'] = columns?.map(col => {
    const customColumn = col as CustomColumnType<T>;
    const isSorted = sortState.order_by === customColumn.sortKey;
    const isSortable = !!customColumn.sortable;

    return {
      ...customColumn,
      title: (
        <div
          className={clsx(styles.headerCell, {
            [styles.sorted]: isSorted,
          })}
          onClick={() => {
            if (isSortable) handleSortClick(customColumn.sortKey);
          }}
          style={{
            cursor: isSortable ? 'pointer' : 'default',
            userSelect: isSortable ? 'none' : 'auto',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 4,
          }}
        >
          {typeof customColumn.title === 'function'
            ? customColumn.title({
                sortOrder: isSorted
                  ? sortState.order_type === ESort.ASC
                    ? 'ascend'
                    : sortState.order_type === ESort.DESC
                    ? 'descend'
                    : undefined
                  : undefined,
                sortColumn: customColumn,
                filters: {},
              })
            : customColumn.title}
          {isSortable && (
            <div className="mt-1">
              <Icon
                name={
                  isSorted
                    ? sortState.order_type === ESort.ASC
                      ? 'sortAsc'
                      : sortState.order_type === ESort.DESC
                      ? 'sortDesc'
                      : 'sort'
                    : 'sort'
                }
                height={12}
                width={12}
              />
            </div>
          )}
        </div>
      ),
      onCell: (record: T) => {
        const rowId = (record as T & { rowId: number }).rowId;
        const matchingRow = tableData.find(item => (item as T & { rowId: number }).rowId === rowId);

        return {
          record,
          editing:
            ('editable' in customColumn || customColumn.editable !== undefined) &&
            editingKeys.includes(String(rowId))
              ? customColumn.editable
              : false,
          value: matchingRow?.[col.key as keyof T],
          name: col.key,
          rowId: rowId,
          className: styles.editableCell,
        };
      },
    };
  });

  return (
    <div className={styles.tableWrapper}>
      <AntdTable
        columns={mergedColumns}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        dataSource={tableData}
        {...rest}
        locale={{
          emptyText: (
            <Empty
              image={<Icon name="emptyTable" native />}
              description="データが登録されていません。"
            />
          ),
        }}
      />
    </div>
  );
}

export default EditableTable;
