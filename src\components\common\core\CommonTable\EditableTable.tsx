import Icon from '@/components/common/core/Icon';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
} from '@/types/constants';
import { ESort, Order } from '@/types/enum';
import {
  Table as AntdTable,
  DatePicker,
  DatePickerProps,
  Empty,
  Form,
  Input,
  Select,
  SelectProps,
  TableProps,
  Tooltip,
} from 'antd';
import { Rule } from 'antd/es/form';
import clsx from 'clsx';
import dayjs from 'dayjs';
import {
  createContext,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { CustomColumnType } from './Table';
import styles from './Table.module.scss';
import { useInView } from 'react-intersection-observer';
import { mergeRefs } from 'react-merge-refs';

// DnD
import { DragHandle, Row } from './TableDnDKit';
import { DndContext, DragEndEvent, UniqueIdentifier } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';

export interface CustomTableProps<T> extends TableProps<T> {
  editingKeys?: string[];
  onSortChange?: (sortData: { order_by: string; order_type: ESort.ASC | ESort.DESC }[]) => void;
  maxLines?: number;
  isMargin?: boolean;
  isNonAdjustable?: boolean;
  isDnD?: boolean;
  onRowOrderChange?: (payload: {
    positionChange: {
      olderPositionData: T;
      olderPositionIndex: number;
      newPositionData: T;
      newPositionIndex: number;
    };
    newData: T[];
  }) => void;
  canDragToEnd?: boolean;
}
const EditableSelect = forwardRef<HTMLSelectElement, SelectProps>(
  ({ ...props }: SelectProps, ref) => {
    const { ref: observerRef, inView } = useInView({
      threshold: 1,
      triggerOnce: false,
    });
    const internalRef = useRef<HTMLElement>(null);
    const triggerBlur = useCallback(() => {
      if (internalRef.current) {
        internalRef.current.blur();
      }
    }, []);
    useEffect(() => {
      if (!inView) {
        triggerBlur();
      }
    }, [inView, triggerBlur]);
    return (
      <div ref={observerRef} style={{ zIndex: 1000 }}>
        <Select ref={mergeRefs([ref, internalRef]) as any} {...props} />
      </div>
    );
  }
);
const EditableDatePicker = forwardRef<HTMLDivElement, DatePickerProps>(
  ({ ...props }: DatePickerProps, ref) => {
    const { ref: observerRef, inView } = useInView({
      threshold: 1,
      triggerOnce: false,
    });
    const internalRef = useRef<HTMLElement>(null);
    const triggerBlur = useCallback(() => {
      if (internalRef.current) {
        internalRef.current.blur();
      }
    }, []);
    useEffect(() => {
      if (!inView) {
        triggerBlur();
      }
    }, [inView, triggerBlur]);
    return (
      <div ref={observerRef} style={{ zIndex: 1000 }}>
        <DatePicker ref={mergeRefs([ref, internalRef]) as any} {...props} />
      </div>
    );
  }
);

const EditableTableContext = createContext<{ isMargin?: boolean; isNonAdjustable?: boolean }>({});

export const EditableTableProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value: any;
}) => {
  return <EditableTableContext.Provider value={value}>{children}</EditableTableContext.Provider>;
};
const useEditableTable = () => {
  const context = useContext(EditableTableContext);
  if (!context) {
    throw new Error('useEditableTable must be used within an EditableTableProvider');
  }
  return context;
};
type Rules = {
  rules?: Rule[];
  maxLength?: number;
  disabled?: <T extends object>(args: T) => boolean;
  placeholder?: string;
  style?: React.CSSProperties;
};

type InputRecord = {
  type: 'input' | 'textarea';
  placeholder?: string;
} & Rules;

type SelectRecord = {
  type: 'select';
  mode?: 'multiple' | 'tags';
  options: { label: string; value: string | number; disabled?: boolean }[];
  render?: <T extends object>(args: T) => JSX.Element;
} & Rules &
  SelectProps;

type DateRecord = {
  type: 'date';
  format: string;
} & Rules;

export type EditableRecord = InputRecord | SelectRecord | DateRecord;
interface EditableCellProps<T> extends React.HTMLAttributes<HTMLElement> {
  editing: boolean | EditableRecord | ((record: any) => EditableRecord & T);
  record: T;
  index: number;
  value: string | number;
  name: string;
  rowId: number | string;
}

const EditableCell = forwardRef<any, EditableCellProps<object>>(
  ({ editing, name, children, value, rowId, record, ...restProps }, ref) => {
    const form = Form.useFormInstance();
    const error = form.getFieldError([rowId, name]);
    const { isMargin, isNonAdjustable } = useEditableTable();

    const isError = !!error.length && !isNonAdjustable;

    if (typeof editing === 'boolean' && editing) {
      return (
        <td
          {...restProps}
          ref={ref}
          onDoubleClick={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <Form.Item
            validateTrigger={['onChange', 'onBlur']}
            name={[rowId, name]}
            style={{ margin: 0 }}
          >
            <Input
              defaultValue={value}
              placeholder={''}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
              onDoubleClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
            />
          </Form.Item>
        </td>
      );
    }

    if (typeof editing === 'object') {
      const { type } = editing;
      const elementNode =
        type === 'select' ? (
          <div
            style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-evenly' }}
            onDoubleClick={e => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <Form.Item
              validateTrigger={['onChange', 'onBlur']}
              rules={editing.rules}
              name={[rowId, name]}
              style={
                isMargin
                  ? {
                      height: isError ? '30px' : 'auto',
                      width: '100%',
                      margin: isError ? undefined : 0,
                    }
                  : {
                      margin: 0,
                    }
              }
            >
              <EditableSelect
                showSearch={editing.showSearch}
                mode={editing.mode}
                style={{ ...editing.style }}
                value={value}
                options={editing.options}
                allowClear
                filterOption={(input, option) =>
                  String(option?.label).toLowerCase().trim().startsWith(input.toLowerCase().trim())
                }
                maxTagPlaceholder={omittedValues => (
                  <Tooltip title={omittedValues.map(({ label }) => label).join(COMMA_SEPARATED)}>
                    <span>+ {omittedValues?.length}</span>
                  </Tooltip>
                )}
                maxTagCount="responsive"
                placeholder={editing.placeholder ?? ''}
                onClick={e => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              />
            </Form.Item>
            {editing.render && (
              <div style={{ paddingLeft: '16px', borderLeft: '1px solid var(--gray-200)' }}>
                {editing.render(record)}
              </div>
            )}
          </div>
        ) : type === 'date' ? (
          <Form.Item
            validateTrigger={['onChange', 'onBlur']}
            getValueProps={i => ({ value: i ? dayjs(i) : '' })}
            rules={editing.rules}
            name={[rowId, name]}
            style={{ margin: 0 }}
          >
            <EditableDatePicker
              defaultValue={value as any}
              value={value as any}
              style={{ ...editing.style }}
              format={editing.format ?? DATE_FORMATS.DATE}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
              onDoubleClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
            />
          </Form.Item>
        ) : (
          <Form.Item
            validateTrigger={['onChange', 'onBlur']}
            rules={editing.rules}
            name={[rowId, name]}
            style={{ margin: 0 }}
          >
            <Input
              disabled={editing.disabled && editing.disabled(record)}
              defaultValue={value}
              value={value}
              style={{ ...editing.style }}
              maxLength={editing.maxLength}
              placeholder={editing.placeholder}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
              onDoubleClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
            />
          </Form.Item>
        );
      return (
        <td {...restProps} ref={ref}>
          {elementNode}
        </td>
      );
    }

    if (typeof editing === 'function') {
      const editableRecord = editing(record);
      const { type } = editableRecord;
      const elementNode =
        type === 'select' ? (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Form.Item
              validateTrigger={['onChange', 'onBlur']}
              rules={editableRecord.rules}
              name={[rowId, name]}
              style={
                isMargin
                  ? {
                      height: isError ? '30px' : 'auto',
                      width: '100%',
                      margin: isError ? undefined : 0,
                    }
                  : {
                      margin: 0,
                    }
              }
            >
              <EditableSelect
                style={editableRecord.style}
                mode={editableRecord.mode}
                defaultValue={value ?? editableRecord.defaultValue}
                value={value ?? editableRecord.defaultValue}
                showSearch={editableRecord.showSearch}
                options={editableRecord.options}
                filterOption={editableRecord.filterOption}
                placeholder={editableRecord.placeholder ?? ''}
                allowClear
                maxTagCount="responsive"
                maxTagPlaceholder={omittedValues => (
                  <Tooltip title={omittedValues.map(({ label }) => label).join(COMMA_SEPARATED)}>
                    <span>+ {omittedValues?.length}</span>
                  </Tooltip>
                )}
                onClick={e => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              />
            </Form.Item>
            {editableRecord.render && editableRecord.render(record)}
          </div>
        ) : type === 'date' ? (
          <Form.Item
            validateTrigger={['onChange', 'onBlur']}
            getValueProps={i => ({ value: i ? dayjs(i) : '' })}
            rules={editableRecord.rules}
            name={[rowId, name]}
            style={{ margin: 0 }}
          >
            <EditableDatePicker
              defaultValue={value as any}
              value={value as any}
              format={editableRecord.format ?? DATE_FORMATS.DATE}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
              style={editableRecord.style}
            />
          </Form.Item>
        ) : (
          <Form.Item
            validateTrigger={['onChange', 'onBlur']}
            rules={editableRecord.rules}
            name={[rowId, name]}
            style={{ margin: 0 }}
          >
            <Input
              disabled={editableRecord.disabled && editableRecord.disabled(record)}
              defaultValue={value}
              value={value}
              placeholder={editableRecord.placeholder}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
              }}
              style={editableRecord.style}
            />
          </Form.Item>
        );
      return (
        <td {...restProps} ref={ref}>
          {elementNode}
        </td>
      );
    }

    return (
      <td {...restProps} ref={ref}>
        {children}
      </td>
    );
  }
);

function InternalEditableTable<T extends object>(
  {
    columns,
    dataSource = [],

    editingKeys = [],
    onSortChange,
    isMargin = false,
    isNonAdjustable = false,
    canDragToEnd = true,
    isDnD = false,
    onRowOrderChange,
    ...rest
  }: CustomTableProps<T>,
  ref: React.Ref<any>
) {
  const [tooltipOpen, setTooltipOpen] = useState(false);
  const [tableData, setTableData] = useState<T[]>([]);
  const [sortState, setSortState] = useState({
    order_by: null,
    order_type: null,
  } as { order_by: string | null; order_type: Order });

  const addRowRef = useRef<HTMLTableRowElement | null>(null);
  const firstRowRef = useRef<HTMLTableRowElement | null>(null);
  useImperativeHandle(ref, () => ({
    scrollToAddRow: () => {
      addRowRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    },
    getAddRowElement: () => addRowRef.current,
    scrollToFirstRow: () => {
      firstRowRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    },
  }));

  const handleSortClick = (key?: string) => {
    setSortState(prev => {
      let newOrderType: Order = null;
      if (prev.order_by !== key) {
        newOrderType = ESort.ASC;
      } else {
        newOrderType =
          prev.order_type === ESort.ASC
            ? ESort.DESC
            : prev.order_type === ESort.DESC
            ? null
            : ESort.ASC;
      }
      const newState = {
        order_by: newOrderType ? key ?? null : null,
        order_type: newOrderType,
      };
      if (onSortChange) {
        const payload =
          newState.order_by && newState.order_type
            ? [{ order_by: newState.order_by, order_type: newState.order_type }]
            : [];
        onSortChange(payload);
      }
      return newState;
    });
  };

  useEffect(() => {
    const transformedData = dataSource.map((item: any, index) => ({
      ...item,
      rowId: item?.rowId === undefined ? index + 1 : item?.rowId,
    })) as (T & { rowId: number | string })[];
    setTableData(transformedData);
  }, [dataSource]);

  const mergedColumns: TableProps<T>['columns'] = columns?.map(col => {
    const customColumn = col as CustomColumnType<T>;
    const isSorted = sortState.order_by === customColumn.sortKey;
    const isSortable = !!customColumn.sortable;
    return {
      ...customColumn,
      title: (
        <div
          className={clsx(styles.headerCell, { [styles.sorted]: isSorted })}
          onClick={() => isSortable && handleSortClick(customColumn.sortKey)}
          style={{
            cursor: isSortable ? 'pointer' : 'default',
            userSelect: 'none',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 4,
          }}
        >
          {typeof customColumn.title === 'function'
            ? customColumn.title({
                sortOrder: isSorted
                  ? sortState.order_type === ESort.ASC
                    ? 'ascend'
                    : sortState.order_type === ESort.DESC
                    ? 'descend'
                    : undefined
                  : undefined,
                sortColumn: customColumn,
                filters: {},
              })
            : customColumn.title}
          {isSortable && (
            <div className="mt-1">
              <Icon
                name={
                  isSorted
                    ? sortState.order_type === ESort.ASC
                      ? 'sortAsc'
                      : sortState.order_type === ESort.DESC
                      ? 'sortDesc'
                      : 'sort'
                    : 'sort'
                }
                height={12}
                width={12}
              />
            </div>
          )}
        </div>
      ),

      onCell: (record: T, index?: number) => {
        const rowId = (record as T & { rowId: number | string }).rowId;
        const matchingRow = tableData.find(
          item => (item as T & { rowId: number | string }).rowId === rowId
        );
        const isAdd = (record as any).mode === 'add';
        const customedOnCell = customColumn?.onCell?.(record);
        return {
          record,
          editing:
            ('editable' in customColumn || customColumn.editable !== undefined) &&
            editingKeys.includes(String(rowId))
              ? customColumn.editable
              : false,
          value: matchingRow?.[col.key as keyof T],
          name: col.key,
          rowId,
          className: styles.editableCell,
          ref: isAdd ? addRowRef : index === 0 ? firstRowRef : undefined,
          width: col.width,
          ...customedOnCell,
        };
      },
    };
  });
  const getRowKey = useMemo(() => {
    if (typeof rest.rowKey === 'function') {
      return rest.rowKey;
    }
    return (record: T) => record[rest.rowKey as keyof T] as string | number;
  }, [rest.rowKey]);
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setTableData(prevState => {
        const activeIndex = prevState.findIndex(record => getRowKey(record) === active.id);
        const overIndex = prevState.findIndex(record => getRowKey(record) === over?.id);
        if (overIndex === prevState.length - 1 && !canDragToEnd) {
          return prevState;
        }
        const updatedData = arrayMove(prevState, activeIndex, overIndex);
        onRowOrderChange?.({
          positionChange: {
            olderPositionData: prevState[activeIndex],
            olderPositionIndex: activeIndex,
            newPositionData: prevState[overIndex],
            newPositionIndex: overIndex,
          },
          newData: updatedData,
        });
        return updatedData;
      });
    }
  };

  const TableRender = (
    <EditableTableProvider value={{ isMargin, isNonAdjustable }}>
      <div className={styles.tableWrapper} id="editable-table">
        <AntdTable
          columns={
            isDnD
              ? [
                  {
                    title: (
                      <Tooltip title={NOTICE_COMMON_MESSAGE.DRAG_DROP} open={tooltipOpen}>
                        <Icon
                          name="infoFilled"
                          width={16.21}
                          height={16.21}
                          color={'var(--brand-600)'}
                          style={{ verticalAlign: 'middle' }}
                        />
                      </Tooltip>
                    ),
                    key: 'sort',
                    align: 'center' as const,
                    width: 48,
                    render: record => (record?.isDraggable === false ? null : <DragHandle />),
                    onHeaderCell: () => ({
                      style: { cursor: 'pointer' },
                      onMouseEnter: () => setTooltipOpen(true),
                      onMouseLeave: () => setTooltipOpen(false),
                      onClick: () => setTooltipOpen(!tooltipOpen),
                    }),
                    onCell: () => {
                      return {
                        style: {
                          verticalAlign: 'middle',
                        },
                      };
                    },
                  },
                  ...(mergedColumns ?? []),
                ]
              : mergedColumns
          }
          components={{
            ...rest.components,
            body: {
              cell: EditableCell,
              row: isDnD ? Row : undefined,
            },
          }}
          dataSource={tableData}
          {...rest}
          locale={{
            emptyText: (
              <Empty
                image={<Icon name="emptyTable" native />}
                description={ERROR_COMMON_MESSAGE.NO_DATA}
              />
            ),
          }}
        />
      </div>
    </EditableTableProvider>
  );

  if (!isDnD) return TableRender;
  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext
        items={tableData.map(item => getRowKey(item) as UniqueIdentifier)}
        strategy={verticalListSortingStrategy}
      >
        {TableRender}
      </SortableContext>
    </DndContext>
  );
}

const EditableTable = forwardRef(InternalEditableTable) as <T extends object>(
  props: CustomTableProps<T> & { ref?: React.Ref<any> }
) => ReturnType<typeof InternalEditableTable>;

export default EditableTable;
