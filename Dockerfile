# Stage 1: Build Vite app
FROM node:20-alpine AS builder

WORKDIR /app

# Accept build-time argument to choose environment
ARG BUILD_MODE=production
ENV NODE_ENV=$BUILD_MODE

COPY package*.json ./
RUN yarn install --frozen-lockfile --production=false
COPY . .

# Build with mode passed in
RUN yarn build --mode $BUILD_MODE

# Stage 2: Serve with Nginx
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
