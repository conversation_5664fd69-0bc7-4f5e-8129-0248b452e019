import { useLazyGetActiveCourseQuery } from '@/store/visit/api';
import { ECourse } from '@/types/enum';
import { Option } from '@/types/interface';
import { useEffect, useState } from 'react';

// get course and other insurance types
export default function useGetCourseOptions() {
  const [fetchActiveCourses, { data: activeCourseData, isFetching }] =
    useLazyGetActiveCourseQuery();
  const [courseOptions, setCourseOptions] = useState<Option<string>[]>([]);

  useEffect(() => {
    fetchActiveCourses(null);
  }, []);
  useEffect(() => {
    if (activeCourseData?.data && !isFetching) {
      const course: Option<string>[] = [];
      (activeCourseData?.data ?? []).forEach(activeCourse => {
        //  const selfPaidCourse = [];
        activeCourse?.data.forEach(subCourse => {
          if (subCourse.payment_course_name === ECourse.SELF_PAID) {
            const subSubChildren: Option<string>[] =
              subCourse.data?.map(subSubCourse => ({
                label: subSubCourse.course_name,
                value: String(subSubCourse.course_id),
              })) || [];
            course.push(...subSubChildren);
          }
        });
      });
      setCourseOptions(course);
    }
  }, [activeCourseData]);
  return { courseOptions };
}
