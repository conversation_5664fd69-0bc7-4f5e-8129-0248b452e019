import Icon from '@/components/common/core/Icon';
import { ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { Flex, Image } from 'antd';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import styles from './ImageGroup.module.scss';

type Props = {
  imgs: { id: string; url: string }[];
  onEdit?: (id: string | string) => void;
  className?: string;
  numberRow?: number;
  maxCount?: number; // for placeholder over couter image
};

export function ImageGroup({ imgs, onEdit, className, numberRow = 3, maxCount = Infinity }: Props) {
  const [active, setActive] = useState<number>(-1);
  const [previewVisible, setPreviewVisible] = useState(false);

  const remainingCount = maxCount && imgs.length > maxCount ? imgs.length - maxCount : 0;

  useEffect(() => {
    if (active >= imgs.length) {
      setActive(imgs.length - 1);
    }
    if (imgs.length === 0) {
      setPreviewVisible(false);
      setActive(-1);
    }
  }, [imgs.length, active]);

  return (
    <Flex
      gap={5}
      className={clsx(styles.image_group, className)}
      style={
        {
          '--number-row': numberRow,
        } as React.CSSProperties
      }
    >
      <Image.PreviewGroup
        preview={{
          visible: previewVisible,
          rootClassName:
            imgs.length > 1 ? styles.myPreview : clsx(styles.myPreview, styles.noProgress),
          minScale: 1,
          maxScale: 4,
          scaleStep: 0.5,
          onVisibleChange: visible => setPreviewVisible(visible),
          current: active === -1 ? undefined : active,
          onChange: (currentIndex, prevIndex) => {
            setActive(currentIndex);
          },
          toolbarRender: (
            _,
            { current, transform: { scale }, actions: { onZoomOut, onZoomIn } }
          ) => (
            <div className={styles.toolbarWrapper}>
              <div
                className={clsx(styles.action, { [styles.disabled]: scale === 1 })}
                onClick={onZoomOut}
              >
                <ZoomOutOutlined disabled={scale === 1} />
              </div>
              <div
                className={clsx(styles.action, { [styles.disabled]: scale === 4 })}
                onClick={onZoomIn}
              >
                <ZoomInOutlined disabled={scale === 4} />
              </div>
              {onEdit && current !== undefined && (
                <div
                  className={styles.action}
                  onClick={() => {
                    onEdit(imgs[current].id);
                  }}
                >
                  <Icon name="edit" width={18} height={18} />
                </div>
              )}
            </div>
          ),
        }}
      >
        <div className={styles.image_grid}>
          {imgs.map((img, index) => (
            <div
              key={img.id}
              className={styles.image_item}
              onClick={() => setActive(index)}
              style={{
                display: !!maxCount && index >= maxCount ? 'none' : 'block',
              }}
            >
              <Image
                src={img.url}
                preview={{
                  mask: (
                    <Flex align="center" justify="center">
                      <Icon name="expand" width={20} height={20} color="white" />
                    </Flex>
                  ),
                }}
              />
            </div>
          ))}
          {remainingCount > 0 && (
            <div
              className={clsx(styles.image_item, styles.remaining_item)}
              onClick={() => {
                setActive(maxCount);
                setPreviewVisible(true);
              }}
            >
              <Image
                src={
                  imgs.length > maxCount
                    ? imgs[maxCount].url
                    : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg=='
                }
                className={styles.remaining_image}
                // preview={false} // Disable default preview mask
                preview={{
                  mask: (
                    <Flex align="center" justify="center">
                      <Icon name="expand" width={20} height={20} color="white" />
                    </Flex>
                  ),
                }}
              />
              <div className={styles.remaining_overlay}>
                <span className={styles.remaining_text}>+{remainingCount}</span>
              </div>
            </div>
          )}
        </div>
      </Image.PreviewGroup>
    </Flex>
  );
}
