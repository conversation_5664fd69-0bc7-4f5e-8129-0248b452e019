export enum EGender {
  MALE = 1,
  FEMALE = 2,
}
export enum ERegisterType {
  MANUAL = 'manual-registration',
  AUTO_RECECON = 'auto-rececon',
}
export interface CreatePatientSchema {
  register_type: string;
  name: string;
  kana: string;
  birthday: string;
  gender: string;
  post: string;
  address1: string;
  address2: string;
  cellphone: string;
  email: string;
  patient_cmt: string;
}

export const initialValuesCreatePatient: CreatePatientSchema = {
  register_type: ERegisterType.MANUAL,
  name: '',
  kana: '',
  birthday: '',
  gender: '',
  post: '',
  address1: '',
  address2: '',
  cellphone: '',
  email: '',
  patient_cmt: '',
};

export interface AddressResponse {
  postalCode: string;
  addresses: AddressItem[];
}

export interface AddressItem {
  prefectureCode: string;
  ja: {
    prefecture: string;
    address1: string;
    address2: string;
    address3: string;
    address4: string;
  };
  kana: {
    prefecture: string;
    address1: string;
    address2: string;
    address3: string;
    address4: string;
  };
  en: {
    prefecture: string;
    address1: string;
    address2: string;
    address3: string;
    address4: string;
  };
}
