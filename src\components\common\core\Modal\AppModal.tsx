import { Modal, ModalProps } from 'antd';
import clsx from 'clsx';

import styles from './AppModal.module.scss';
import Icon from '../Icon';
interface Props extends ModalProps {
  isPadding?: boolean;
}

export const AppModal = ({ wrapClassName, children, isPadding = true, ...props }: Props) => {
  const wrapClassNames = clsx(
    wrapClassName,
    styles.appModal,
    !isPadding && styles.appModalNoPadding
  );

  return (
    <div className={props.className}>
      <Modal
        maskClosable={false}
        wrapClassName={wrapClassNames}
        closeIcon={<Icon name="close" width={16.23} height={16.23} />}
        footer={null}
        width={600}
        centered
        {...props}
      >
        {children}
      </Modal>
    </div>
  );
};
