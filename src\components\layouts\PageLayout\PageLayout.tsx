import React, { useState, useCallback } from 'react';
import styles from './PageLayout.module.scss';
import clsx from 'clsx';
import Footer from '@/components/common/layout/Footer';
import { EventHandlers } from '@/types/interface';

type PageLayoutProps = {
  title: string | React.ReactNode;
  breadcrumb?: React.ReactNode;
  headerRight: React.ReactElement;
  rightSidebar?: React.ReactNode;
  children: React.ReactElement;
};

export default function PageLayout({
  title,
  breadcrumb,
  headerRight,
  rightSidebar,
  children,
}: PageLayoutProps) {
  const [eventHandlers, setEventHandlers] = useState<EventHandlers>({});

  const registerEvents = useCallback((handlers: EventHandlers) => {
    setEventHandlers(prev => ({ ...prev, ...handlers }));
  }, []);

  const enhancedChild = React.cloneElement(children, { registerEvents });
  const enhancedHeaderRight = React.cloneElement(headerRight, { eventHandlers });

  return (
    <div className={clsx(styles.container, { [styles.withSidebar]: !!rightSidebar })}>
      <div className={styles.outerContainer}>
        <div className={styles.pageLayout}>
          <div className={styles.pageLayoutHeader}>
            <div className={styles.headerLeft}>
              {breadcrumb && <div className={styles.breadcrumb}>{breadcrumb}</div>}
              <div className={clsx(styles.title, 'fs20-bold')}>{title}</div>
            </div>
            <div className={styles.headerRight}>{enhancedHeaderRight}</div>
          </div>
          <div className={styles.content}>{enhancedChild}</div>
        </div>
        <Footer />
      </div>
      {rightSidebar && <aside className={styles.rightSidebar}>{rightSidebar}</aside>}
    </div>
  );
}
