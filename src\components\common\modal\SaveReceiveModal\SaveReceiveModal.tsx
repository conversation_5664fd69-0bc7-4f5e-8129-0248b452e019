//Screen B001
import But<PERSON> from '@/components/common/core/Button';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput } from '@/components/common/core/Input';
import { AppModal } from '@/components/common/core/Modal';
import { AppSelect } from '@/components/common/core/Select';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import {
  MultiTreaterSelectFormItem,
  TreeCourseFormItem,
} from '@/components/page-components/visit-management/dashboard/VisitDetail/components/CommonFormItem';
import { PatientItem } from '@/store/patient/type';
import { useVisitRegistrationMutation } from '@/store/visit/api';
import { Doctor, VisitRegistrationPayload } from '@/store/visit/type';
import {
  DATE_FORMATS,
  DEFAULT_VISIT_VIA,
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  REGEX_NUMBER,
  STATUS_CODES,
  VisitTypeOption,
} from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { showFlashNotice } from '@/utils/flashNotice';
import { Flex, Form, ModalProps, Spin } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import ModalTitle from '../../layout/ModalTitle/ModalTitle';
import AppPatientExistsSearchModal from '../PatientExistsSearchModal/PatientExistsSearchModal';
import './SaveReceiveModal.modules.scss';
import { VisitRegistrationSchema } from './type';

import { mapCoursesToCourseGroups } from '@/components/page-components/visit-management/dashboard/VisitDetail/helper';
import { useScreen } from '@/hooks/useScreen';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { useLazyGetInsuranceInfoQuery } from '@/store/patient/api';
import { useSearchParams } from 'react-router-dom';
import useGetCourseServices from './useGetCourseServices';

const { useForm } = Form;

interface Props extends ModalProps {
  isModalOpen: boolean;
  initialData?: Partial<PatientItem> & {
    booking_id?: number;
  } & Partial<VisitRegistrationPayload> & {
      name_kana?: string;
      name_kanji?: string;
    };
  doctorList?: Doctor[];
  onCancel?: () => void;
  onSubmitVisit?: () => void;
  isLoadingInitialData?: boolean;
  visitDate?: string;
}

const initialFormValues: VisitRegistrationSchema = {
  patient_id: undefined,
  name_kanji: '',
  name_kana: '',
  visit_type: undefined,
  treaters: [],
  booking_id: undefined,
  patient_cd: '',
  course_groups: {},
};

const AppSaveReceiveModal = ({
  isModalOpen,
  initialData,
  onCancel,
  onSubmitVisit,
  doctorList,
  isLoadingInitialData,
  visitDate,
  ...props
}: Props) => {
  const [form] = useForm<VisitRegistrationSchema>();
  const [initial, setInitial] = useState<VisitRegistrationSchema>(initialFormValues);
  const [searchInput, setSearchInput] = useState<string>('');
  const [showModalPatientSearch, setShowModalPatientSearch] = useState<boolean>(false);
  const [handleVisitRegistrationMutation, { isLoading }] = useVisitRegistrationMutation();
  const { handleValuesChange, submitHandler } = useUnsavedWarning<VisitRegistrationSchema>(initial);
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const [searchParams] = useSearchParams();
  const [fetchInsurance] = useLazyGetInsuranceInfoQuery();
  const fromQRScan = searchParams.get('from') === 'qr-scan';

  const [patientSelect, setPatientSelect] = useState<
    | (Partial<PatientItem> & {
        booking_id?: number;
      } & Partial<VisitRegistrationPayload> & {
          name_kana?: string;
          name_kanji?: string;
        })
    | undefined
  >();
  const { treeServices, isFetching } = useGetCourseServices();
  const { screen } = useScreen();
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        name_kana: initialData.kana,
        name_kanji: initialData.name,
        patient_cd: initialData.patient_cd,
      });

      setPatientSelect(prev => (prev?.patient_id === initialData.patient_id ? prev : initialData));
      form?.setFieldsValue({
        ...initialData,
        ...(initialData.booking_id && { booking_id: initialData.booking_id }),
        visit_type: initialData.visit_type ? Number(initialData.visit_type) : undefined,
        treaters:
          initialData?.practitioner_ids?.map(id => ({
            value: id,
            label: doctorList?.find(d => d.user_id === id)?.kanji_name,
          })) ?? [],
        course_groups: mapCoursesToCourseGroups(
          treeServices ?? [],
          initialData?.course_ids?.map(cId => String(cId)) ?? []
        ),
      });
    }
  }, [initialData, treeServices]);

  useEffect(() => {
    const init = {
      patient_id: initialData?.patient_id ?? initialFormValues.patient_id,
      patient_cd: initialData?.patient_cd ?? initialFormValues.patient_cd,
      name_kanji: initialData?.name ?? initialFormValues.name_kanji,
      name_kana: initialData?.kana ?? initialFormValues.name_kana,
      visit_type: initialData?.visit_type
        ? Number(initialData.visit_type)
        : initialFormValues.visit_type,
      treaters:
        initialData?.practitioner_ids?.map(id => ({
          value: id,
          label: doctorList?.find(d => d.user_id === id)?.kanji_name,
        })) ?? [],
      booking_id: initialData?.booking_id ?? initialFormValues.booking_id,
      course_groups: mapCoursesToCourseGroups(
        treeServices ?? [],
        initialData?.course_ids?.map(cId => String(cId)) ?? []
      ),
    };
    setInitial(init as any);
  }, [initialData, treeServices]);

  const handleCancel = () => {
    form.resetFields();
    onCancel?.();
  };

  const onFinish = async (values: VisitRegistrationSchema) => {
    try {
      const now = dayjs();
      const visitDateTime = visitDate
        ? dayjs(visitDate)
            .hour(now.hour())
            .minute(now.minute())
            .second(now.second())
            .format(DATE_FORMATS.DATE_TIME)
        : now.format(DATE_FORMATS.DATE_TIME);
      const allCourseIds = Object.values(values.course_groups || {}).flat();
      const payload: VisitRegistrationPayload = {
        booking_id: form.getFieldValue('booking_id') || undefined,
        patient_id: patientSelect?.patient_id as number,
        visit_date: visitDateTime,
        visit_via: DEFAULT_VISIT_VIA,
        visit_type: String(values.visit_type),
        course_ids: allCourseIds.filter(id => REGEX_NUMBER.test(id)).map(id => Number(id)), //  only course_id is number other is text
        practitioner_ids: values.treaters.map(id => id.value),
      };

      await handleVisitRegistrationMutation(payload)
        .unwrap()
        .then(res => {
          if (res.status === 409) {
            if (screen === 'mobile') {
              showFlashNotice({
                type: 'error',
                message: NOTICE_COMMON_MESSAGE.QR_SCAN_ERROR,
              });
            } else {
              showFlashNotice({
                type: 'error',
                message: res.message,
              });
            }
            onCancel?.();
            setSearchInput('');
          } else if (res.status === STATUS_CODES.INVALID_FIELD) {
            const fields = Object.entries<string[]>(res?.data?.data).map(([name, msgs]) => ({
              name,
              errors: msgs,
            }));
            form.setFields(fields as any);
            return;
          } else {
            showFlashNotice({
              type: 'success',
              message: NOTICE_COMMON_MESSAGE.RECEIVED,
            });
            onCancel?.();
            setSearchInput('');
            onSubmitVisit?.();
          }
        });
      form.resetFields();
    } catch (error) {
      return error;
    }
  };

  const handleConfirmModalPatientSearch = async (patient: PatientItem) => {
    const { kana, name } = { ...patient };
    fetchInsurance(Number(patient.patient_id)).then((res: any) => {
      if (!res.data?.data?.has_insurance) {
        showWarningDialog({
          message: NOTICE_COMMON_MESSAGE.WARNING_HOKEN_MSG,
          buttons: [
            {
              type: 'cancel',
              label: 'キャンセル',
              onClick: () => {
                hideWarningDialog();
              },
            },
            {
              type: 'confirm',
              label: '続行する',
              onClick: () => {
                hideWarningDialog();
                confirmSelectPatient();
              },
            },
          ],
        });
      } else {
        confirmSelectPatient();
      }
    });
    const confirmSelectPatient = () => {
      form.setFieldValue('name_kana', kana);
      form.setFieldValue('name_kanji', name);
      form.setFieldValue('patient_cd', patient.patient_cd);
      form.setFieldValue('booking_id', initialData?.booking_id);
      setShowModalPatientSearch(false);
      handleValuesChange({}, form.getFieldsValue());
      setPatientSelect(patient);
    };
  };
  useEffect(() => {
    if (patientSelect?.patient_id && fromQRScan) {
      fetchInsurance(Number(patientSelect.patient_id)).then((res: any) => {
        if (!res.data?.data?.has_insurance) {
          showWarningDialog({
            message: NOTICE_COMMON_MESSAGE.WARNING_HOKEN_MSG,
            buttons: [
              {
                type: 'cancel',
                label: 'キャンセル',
                onClick: () => {
                  onCancel?.();
                  hideWarningDialog();
                },
              },
              {
                type: 'confirm',
                label: '続行する',
                onClick: () => {
                  hideWarningDialog();
                },
              },
            ],
          });
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patientSelect]);
  return (
    <>
      <div>
        <AppModal
          wrapClassName="save-receive-modal"
          width={1000}
          height={screen === 'mobile' ? '100%' : 'auto'}
          destroyOnClose
          open={isModalOpen}
          onCancel={handleCancel}
          mask={screen !== 'mobile'}
          zIndex={1200}
          {...props}
        >
          <Flex justify="space-between" align="center">
            <ModalTitle title="受付登録" style={{ margin: 0 }} />
            {!initialData && screen !== 'mobile' && (
              <Flex gap={8} align="center">
                <AppInput
                  placeholder={'氏名・カナ、患者番号'}
                  style={{ height: '40px', padding: 'unset !important', paddingRight: 12 }}
                  suffix={<Icon name="search" height={20} width={20} />}
                  value={searchInput}
                  onChange={e => setSearchInput(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      setShowModalPatientSearch(true);
                    }
                  }}
                  disabled={isLoading || isLoadingInitialData}
                />
                <Button
                  style={{ width: '100px' }}
                  onClick={() => {
                    setShowModalPatientSearch(true);
                  }}
                  className="mr-6"
                  disabled={isLoading || isLoadingInitialData}
                >
                  検索
                </Button>
              </Flex>
            )}
          </Flex>
          <Form
            layout="vertical"
            initialValues={initial}
            onFinish={values => {
              submitHandler();
              onFinish(values);
            }}
            form={form}
            onValuesChange={(changedValues, allValues) => {
              handleValuesChange(changedValues, allValues);
              if (changedValues.course_groups) {
                form.validateFields(['course_groups']);
              }
            }}
          >
            <div className="content-modal">
              <Flex gap={16} className="information-block">
                <AppFormItem name="patient_cd" label="患者番号" layout="vertical">
                  <AppInput disabled />
                </AppFormItem>
                <AppFormItem
                  name="name_kanji"
                  label="氏名（漢字)"
                  layout="vertical"
                  required={true}
                  rules={[
                    { required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (漢字)') },
                  ]}
                >
                  <AppInput disabled />
                </AppFormItem>
                <AppFormItem
                  name="name_kana"
                  label="氏名（カナ)"
                  layout="vertical"
                  // required={true}
                  // rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('担当者') }]}
                >
                  <AppInput disabled />
                </AppFormItem>
              </Flex>
              <AppBlockHasHeader icon={<Icon name="personInfo" />} title="来院情報" bordered>
                <Flex gap={16}>
                  <AppFormItem
                    name="visit_type"
                    label="来院設定"
                    layout="vertical"
                    rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('来院設定') }]}
                  >
                    <AppSelect
                      size="small"
                      defaultValue={initialData?.visit_type}
                      options={VisitTypeOption}
                      placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DROP_DOWN}
                      disabled={isLoadingInitialData || isLoading}
                    />
                  </AppFormItem>

                  <AppFormItem
                    name="treaters"
                    label="施術師"
                    layout="vertical"
                    required
                    rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('施術師') }]}
                  >
                    <MultiTreaterSelectFormItem
                      defaultValue={initialData?.practitioner_ids ?? []}
                      treaterList={doctorList}
                      showSearch
                      filterOption={(input, option) =>
                        String(option?.label)
                          .toLowerCase()
                          .trim()
                          .startsWith(input.toLowerCase().trim())
                      }
                      disabled={isLoadingInitialData || isLoading}
                    />
                  </AppFormItem>
                </Flex>
              </AppBlockHasHeader>
              <AppBlockHasHeader
                icon={<Icon name="medical" color="white" width={16} height={16} />}
                title={
                  <>
                    <span>受付区分</span>
                    <span className="required_icon">*</span>
                  </>
                }
                bordered
              >
                <Spin spinning={isLoadingInitialData || isLoading}>
                  <AppFormItem
                    name="course_groups"
                    rules={[
                      {
                        validator: (_, value) => {
                          if (!value)
                            return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('受付区分'));

                          const hasAnySelection = Object.values(value).some(
                            group => Array.isArray(group) && group.length > 0
                          );

                          if (!hasAnySelection) {
                            return Promise.reject(ERROR_COMMON_MESSAGE.REQUIRED('受付区分'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    style={{ minHeight: '224px' }}
                  >
                    <Flex justify="space-between" align="stretch" className={'course-wrapper'}>
                      {treeServices.map(service => {
                        return (
                          <Flex vertical className="tree-checkbox" key={service.service_id}>
                            <AppFormItem name={['course_groups', service.service_id]} noStyle>
                              <TreeCourseFormItem
                                serviceCourseTreeData={service.treeData}
                                jihiCourseTreeData={service.jihiCourse}
                                disabled={isFetching}
                              />
                            </AppFormItem>
                          </Flex>
                        );
                      })}
                    </Flex>
                  </AppFormItem>
                </Spin>
              </AppBlockHasHeader>
            </div>

            <Flex gap="12px" justify="end">
              <Button
                customType={ButtonType.SECONDARY_COLOR}
                onClick={handleCancel}
                className="btn-modal-width"
              >
                キャンセル
              </Button>
              <Button
                customType={ButtonType.PRIMARY}
                htmlType="submit"
                color="blue"
                className="btn-modal-width"
                loading={isLoading || isLoadingInitialData}
              >
                登録
              </Button>
            </Flex>
          </Form>
        </AppModal>
      </div>
      <AppPatientExistsSearchModal
        searchValue={searchInput}
        isModalOpen={showModalPatientSearch}
        onCancel={() => setShowModalPatientSearch(false)}
        onConfirm={patient => handleConfirmModalPatientSearch(patient)}
      />
    </>
  );
};
export default AppSaveReceiveModal;
