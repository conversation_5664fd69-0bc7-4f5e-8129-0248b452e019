// Example
// <Image name="empty" height={80} className={styles.imageClass}/>
import EmptyImage from '@/assets/images/empty.png';
import InjuryNameInfoImage from '@/assets/images/injury-name-info.svg';
import NotFoundImage from '@/assets/images/not-found.png';

import React from 'react';

export const imageMap = {
  empty: EmptyImage,
  notFound: NotFoundImage,
  injuryNameInfo: InjuryNameInfoImage,
};

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  name: keyof typeof imageMap;
  width?: number;
  height?: number;
}
const Image: React.FC<ImageProps> = ({ name, width, height, ...props }) => {
  const src = imageMap[name];
  if (!src) {
    console.warn(`⚠️ Image "${name}" not found.`);
    return null;
  }

  return (
    // prevent shrinking and stretching
    <figure style={{ width, height, display: 'inline-block' }}>
      <img
        src={src}
        alt={name}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain', // prevent shrinking and stretching
          display: 'block',
        }}
        {...props}
      />
    </figure>
  );
};
export default React.memo(Image);
