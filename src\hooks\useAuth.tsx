import { useState, useEffect } from 'react';
import { LOCAL_STORAGE_KEY } from '@/types/constants';

export default function useAuth() {
  const [version, setVersion] = useState(0);

  useEffect(() => {
    const listener = () => setVersion(v => v + 1);
    window.addEventListener('auth-change', listener);
    return () => window.removeEventListener('auth-change', listener);
  }, []);

  const accessToken = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);

  return {
    isAuthenticated: !!accessToken,
  };
}
