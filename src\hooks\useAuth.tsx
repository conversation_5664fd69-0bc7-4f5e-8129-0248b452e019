import { useState, useEffect } from 'react';
import { ROLE } from '@/types/enum';
import { RootState } from '@/config/redux/store';
import { useSelector } from 'react-redux';

export default function useAuth() {
  const [version, setVersion] = useState(0);

  useEffect(() => {
    const listener = () => setVersion(v => v + 1);
    window.addEventListener('auth-change', listener);
    return () => window.removeEventListener('auth-change', listener);
  }, []);

  const encodeToken = useSelector((state: RootState) => state.auth.accessToken);
  const expiredAt = useSelector((state: RootState) => state.auth.expiredAt);
  const role = useSelector((state: RootState) => state.auth.role);
  return {
    isAuthenticated: !!encodeToken,
    expiredAt: expiredAt ? expiredAt : null,
    role: role ? (role as ROLE) : null,
  };
}
