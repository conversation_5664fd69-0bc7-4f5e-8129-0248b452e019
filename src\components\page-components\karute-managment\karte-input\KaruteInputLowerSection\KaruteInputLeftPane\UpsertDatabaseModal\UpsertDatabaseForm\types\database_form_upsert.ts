import { EPaymentType, MondaiItem } from './database_schema';

export interface DiseaseImage {
  disease_base_image_id: number;
  disease_base_id?: number; //
  schema_image_id: number | null;
  type?: number; // 1: mondai, 2: treatment, 3: schema ???
  path?: string;
  url: string; // get list only
}
export interface PatientOption {
  patient_option_id?: number; // for get only
  patient_id: string | number;
  registration_date: string; // YYYY/MM/DD
  treatment_date: string; // YYYY/MM/DD
  option_id?: number | string;
  name?: string | null;
  quantity?: number | null;
}

export interface PatientCourse {
  patient_course_id?: number; // for get only
  patient_id: string | number; //
  registration_date: string; // YYYY/MM/DD
  treatment_date: string; // YYYY/MM/DD
  course_id?: number | null;
  name?: string | null;
  payment_course_type?: EPaymentType;
}
export interface PatientMedicalHistory {
  patient_medical_history_id?: number; // for get only

  patient_id: string | number;
  service_id?: number;
  personal: string | null;
  social: string | null;
  family: string | null;
}

export interface SubmitDiseaseBase
  extends Omit<MondaiItem, 'objective_images' | 'treatment_images'> {
  karute_id?: number | string;
  trauma_type: number;
  objective_images: number[];
  treatment_images: number[];
}

export interface DatabaseSubmitSchema {
  karute_disease_base_id?: number | null;
  clinic_id: number | string;
  karute_id: string | number;
  patient_id: string | number;
  service_id: number;
  disease_bases: SubmitDiseaseBase[]; // mondai list
  patient_medical_history: PatientMedicalHistory; //
  patient_options: PatientOption[];
  patient_courses: PatientCourse[];
}
