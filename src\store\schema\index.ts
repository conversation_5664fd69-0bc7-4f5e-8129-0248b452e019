import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { Category } from './type';

export type SchemaState = {
  categories: {
    isInitialized: boolean;
    list: Category[];
  };
};

export const initialState: SchemaState = {
  categories: {
    isInitialized: false,
    list: [],
  },
};

// slice
export const schemaSlice = createSlice({
  name: 'schema',
  initialState,
  reducers: {
    setCategory: (
      state,
      actions: PayloadAction<{ categories: { list: Category[]; isInitialized: boolean } }>
    ) => {
      state.categories = actions.payload.categories;
    },
  },
});

// actions
export const { setCategory } = schemaSlice.actions;

// reducer
export const schemaReducer = schemaSlice.reducer;
