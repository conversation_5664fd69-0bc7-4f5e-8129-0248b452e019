import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { APIResponse, ListResponse } from '@/types/interface';
import { BookingNextTimePayload, BookingResponse } from './type';
import { CourseItem } from '../visit/type';

export const bookingNextTimeAPI = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    getBookings: build.query<APIResponse<ListResponse<BookingResponse>>, number>({
      query: (id: number) => ({ url: `/api/patients/${id}/bookings`, method: 'GET' }),
    }),
    createBooking: build.mutation<
      APIResponse<BookingResponse>,
      { patientId: number; payload: Omit<BookingNextTimePayload, 'booking_to' | 'practitioner_id'> }
    >({
      query: ({ patientId, payload }) => ({
        url: `/api/patients/${patientId}/bookings`,
        method: 'POST',
        body: payload,
      }),
    }),
    getListCourseByPaymentType: build.query<
      APIResponse<CourseItem[]>,
      Record<'payment_type', number>
    >({
      query: (params: Record<'payment_type', number>) => ({
        url: '/api/courses/by-payment-type',
        method: 'GET',
        params,
      }),
    }),

    getListCourseByPractitioner: build.query<
      APIResponse<CourseItem[] | unknown>,
      Record<'practitioner_id', number>
    >({
      query: (params: Record<'practitioner_id', number>) => ({
        url: '/api/courses/by-practitioner',
        method: 'GET',
        params,
      }),
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetBookingsQuery,
  useLazyGetListCourseByPaymentTypeQuery,
  useLazyGetListCourseByPractitionerQuery,
  useCreateBookingMutation,
} = bookingNextTimeAPI;
