.master_modal {
  :global(.hugged_tab_option) {
    width: 113px !important;
    text-align: center;
  }
  .master_title {
    :global(.title-modal) {
      margin-bottom: 0px !important;
    }
  }
}
.category_modal_table {
  :global(.ant-table-row) {
    min-height: 34px;
    max-height: 56px;
    height: 56px;
    :global(.ant-table-cell) {
      vertical-align: unset;
    }
  }
  :global(.ant-select-selection-wrap) {
    height: 100% !important;
  }
}
.master_modal_action_btn {
  padding: 3px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: $gray-200;
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;

    &:hover {
      background-color: var(---white);
    }
  }
}

.sentences_tab {
  border-radius: 8px;
  border: 1px solid $gray-200;
  overflow: hidden;
  min-height: 304px;
  .sentences_tab_sidebar_container {
    border-right: 1px solid $gray-200;
    padding: 16px 4px 16px 16px !important;
    min-width: 200px;
    .content {
      padding-right: 4px;
      overflow-y: auto;
      height: 360px;
      .row_item {
        cursor: pointer;
        padding: 8px 10px;
        color: $gray-500;
        &.active {
          color: $gray-800;
          background-color: $brand-50;
          &:hover {
            color: $gray-900;
            background-color: $brand-100;
          }
        }
        &:not(.active):hover {
          color: $gray-800;
          background-color: $gray-50;
        }
        border-radius: 8px;
        word-break: break-all;
      }
    }
  }
  .sentences_tab_content_container {
    padding: 16px 4px 16px 16px !important;
    max-width: calc(100% - 200px);
    .category {
      height: 72px;
      overflow-y: auto;
      margin-bottom: 16px;
    }
    .editing_row:global(> .ant-table-cell) {
      padding: 8px 12px !important;
    }
    .viewing_row:global(> .ant-table-cell) {
      padding: 18px 12px !important;
    }
  }
}
