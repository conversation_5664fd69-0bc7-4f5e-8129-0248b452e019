import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';

import { LRType } from '@/components/page-components/karute-managment/karte-input/InjuryNameModal/type';
import { DatabaseSubmitSchema } from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import { DatabaseGetSchema } from '@/components/page-components/karute-managment/karte-input/KaruteInputLowerSection/KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types/database_form_get';
import { EKaruteServicePriority } from '@/types/enum';
import { APIResponse, ListResponse } from '@/types/interface';
import { PatientItem } from '../patient/type';
import {
  DatabaseResponse,
  DetailKeikaData,
  DiseaseHistoryItem,
  DiseaseHistoryParams,
  DuplicateInjuryPayload,
  DuplicateInjuryResponse,
  KaruteCourse,
  KaruteDetailItem,
  KaruteItem,
  KaruteLatestUpdate,
  KarutePatientMedicalHistory,
  KarutesParams,
  KaruteSubmitPayload,
  KeikaDetailHistoryItem,
  KeikaHistoryParams,
  KeikaMondaiRegistered,
  OptionItem,
  PreviousKeikaItem,
  RegisteredVisitItem,
  UpsertKeikaPayload,
  ValidateInjuryResponse,
} from './type';

export const patientManagementApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,

    getKaruteList: build.query<APIResponse<ListResponse<KaruteItem>>, KarutesParams>({
      query: params => ({
        url: `/api/karute`,
        method: 'GET',
        params,
      }),
      providesTags: ['karute-list'],
    }),
    getKaruteDetail: build.query<APIResponse<KaruteDetailItem & Partial<PatientItem>>, number>({
      query: karute_id => ({
        url: `/api/karute/${karute_id}`,
        method: 'GET',
      }),
      providesTags: (_r, _e, karute_id) => [{ type: 'karute-detail', id: karute_id }],
    }),
    getKarutePatientMedicalHistory: build.query<
      APIResponse<KarutePatientMedicalHistory>,
      {
        patient_id: string | number;
        service_id: string | number;
      }
    >({
      query: params => ({
        url: '/api/patient-medical-histories/get-by-service',
        method: 'GET',
        params,
      }),
      providesTags: (result, error, params) => [
        { type: 'karute_patient_history', id: `${params.service_id}` },
      ],
    }),
    getDatabaseVersion: build.query<
      APIResponse<DatabaseGetSchema>,
      {
        service_id: EKaruteServicePriority;
        karute_id: number | string;
        version_id?: string; // if not provided, will get latest version
      }
    >({
      query: params => ({
        url: `/api/disease-bases/group-by-visit-date`,
        params: params,
        method: 'GET',
      }),
    }),
    getOptions: build.query<
      APIResponse<OptionItem[]>,
      {
        service_id?: string | number; // if not provided, will get all options from all services
      }
    >({
      query: params => ({
        url: `/api/options/all`,
        method: 'GET',
        params,
      }),
    }),
    getKaruteInputCourses: build.query<
      APIResponse<KaruteCourse[]>,
      { service_id: string | number }
    >({
      query: params => ({
        url: `/api/courses/get-by-service`,
        method: 'GET',
        params,
      }),
    }),
    upsertDatabase: build.mutation<
      APIResponse<{ uuid: string; version: number; new_ids?: number[] }>,
      DatabaseSubmitSchema
    >({
      query: body => ({
        url: `/api/disease-bases`,
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, params) => [
        { type: 'database', id: `${params.service_id}` },
        { type: 'karute_patient_history', id: `${params.service_id}` },
      ],
    }),

    getDatabaseHistory: build.query<
      APIResponse<ListResponse<DiseaseHistoryItem>>,
      Partial<DiseaseHistoryParams>
    >({
      query: params => ({
        url: `api/disease-bases/other-patients`,
        method: 'GET',
        params,
      }),
    }),
    getPatientVisitRegistered: build.query<
      APIResponse<RegisteredVisitItem[]>,
      {
        patient_id: string | number;
        service_id: string | number;
      }
    >({
      query: params => ({
        url: `api/courses/get-patient-registered`,
        method: 'GET',
        params,
      }),
    }),
    validateInjuryName: build.mutation<
      APIResponse<ValidateInjuryResponse[]>,
      { selected_injuries: string[] }
    >({
      query: body => ({
        url: `api/injuries/validate-by-name`,
        method: 'POST',
        body,
      }),
    }),
    getDatabase: build.query<
      APIResponse<DatabaseResponse>,
      {
        karute_id: string | number;
        service_id: string | number;
      }
    >({
      query: params => ({
        url: `/api/disease-bases`,
        method: 'GET',
        params,
      }),
      providesTags: (result, error, params) => [
        { type: 'database', id: `${params.service_id}` },
        { type: 'database', id: 'LIST' },
      ],
    }),

    getKeikaHistory: build.query<
      APIResponse<ListResponse<KeikaDetailHistoryItem>>,
      KeikaHistoryParams
    >({
      query: params => ({
        url: `api/keika-details`,
        method: 'GET',
        params,
      }),
    }),

    getDetailKeika: build.query<APIResponse<DetailKeikaData>, number>({
      query: id => ({
        url: `api/keikas/${id}`,
        method: 'GET',
      }),
    }),
    createKeika: build.mutation<APIResponse<any>, UpsertKeikaPayload>({
      query: body => ({ url: `api/keikas`, method: 'POST', body }),
      invalidatesTags: [{ type: 'mainKeika' as const, id: 'LIST' }],
    }),
    updateKeika: build.mutation<APIResponse<any>, { id: number; body: UpsertKeikaPayload }>({
      query: payload => ({ url: `api/keikas/${payload.id}`, method: 'PUT', body: payload.body }),
      invalidatesTags: (_res, _err, { id }) => [
        { type: 'mainKeika' as const, id },
        { type: 'mainKeika' as const, id: 'LIST' },
        { type: 'historyKeika' as const, id },
      ],
    }),

    submitKarute: build.mutation<APIResponse<any>, KaruteSubmitPayload>({
      query: body => ({ url: `api/karute/submit`, method: 'POST', body }),
    }),
    getBuiCdByType: build.query<
      APIResponse<{ bui_cd: number; disp_name: string }>,
      { part_type: number | null; part_id: number | null; lr_type: LRType | null }
    >({
      query: params => ({
        url: 'api/injuries/get-bui-cd-by-type',
        method: 'GET',
        params,
      }),
    }),

    getLastestUpdate: build.query<APIResponse<KaruteLatestUpdate>, { karute_id: string | number }>({
      query: params => ({
        url: `/api/karute/${params.karute_id}/latest-updated-date`,
        method: 'GET',
      }),
    }),
    deleteKarute: build.mutation<APIResponse<any>, number>({
      query: id => ({
        url: `/api/karute/restore-nearest-version`,
        method: 'POST',
        body: { karute_id: id },
      }),
    }),
    getKeikaMondaiRegisteredByVisitDate: build.query<
      APIResponse<KeikaMondaiRegistered[]>,
      {
        karute_id: number;
        service_id: number;
        patient_id: number;
        type?: 'all';
      }
    >({
      query: params => ({
        url: `api/visits/selection-for-keika`,
        method: 'GET',
        params,
      }),
    }),
    getTotalKeikaHistoryItem: build.query<
      APIResponse<{ count: number }>,
      { service_id: string | number }
    >({
      query: params => ({
        url: `/api/keika-details/count`,
        method: 'GET',
        params,
      }),
    }),
    findDuplicateInjury: build.mutation<
      APIResponse<DuplicateInjuryResponse[]>,
      DuplicateInjuryPayload
    >({
      query: payload => ({
        url: `/api/disease-bases/validate-dup-injury-name`,
        method: 'POST',
        body: payload,
      }),
    }),
    checkExistPreviousKeika: build.query<
      APIResponse<{ exists: boolean }>,
      {
        karute_id: string | number;
        service_id: string | number;
        before_keika_id?: string | number | null;
      }
    >({
      query: params => ({
        url: `/api/keikas/previous/check-exists`,
        method: 'GET',
        params,
      }),
    }),

    getPreviousKeika: build.query<
      APIResponse<PreviousKeikaItem>,
      {
        karute_id: string | number;
        service_id: string | number;
        before_keika_id?: string | number | null;
      }
    >({
      query: params => ({
        url: 'api/keikas/previous',
        method: 'GET',
        params,
      }),
    }),
  }),
});

export const {
  useGetKaruteDetailQuery,
  useGetKarutePatientMedicalHistoryQuery,
  useGetKaruteListQuery,
  useUpsertDatabaseMutation,
  useGetDatabaseVersionQuery,
  useLazyGetDatabaseVersionQuery,
  useGetOptionsQuery,
  useGetKaruteInputCoursesQuery,
  useGetPatientVisitRegisteredQuery,
  useGetDatabaseHistoryQuery,
  useValidateInjuryNameMutation,
  useGetDatabaseQuery,
  useGetKeikaHistoryQuery,
  useCreateKeikaMutation,
  useUpdateKeikaMutation,
  useGetDetailKeikaQuery,
  // useLazyGetLastestKeikaQuery,
  useLazyGetPreviousKeikaQuery,
  useCheckExistPreviousKeikaQuery,
  useSubmitKaruteMutation,
  useLazyGetBuiCdByTypeQuery,
  useLazyGetLastestUpdateQuery,
  useDeleteKaruteMutation,
  useGetKeikaMondaiRegisteredByVisitDateQuery,
  useLazyGetTotalKeikaHistoryItemQuery,
  useFindDuplicateInjuryMutation,
} = patientManagementApi;
