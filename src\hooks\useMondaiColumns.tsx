import { RenderWithTooltip } from '@/components/common/core/CommonTable/RenderWithTooltip';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import Icon from '@/components/common/core/Icon';
import styles from '@/components/page-components/karute-managment/karte-input/KaruteInputUpperSection/KaruteInputUpperSection.module.scss';
import { MONDAI_STATUS_OPTION, MONDAI_TYPE, MONDAI_TYPE_OPTIONS } from '@/types/constants';
import { APIResponse } from '@/types/interface';
import { KaruteMondai } from '@/types/interface/KaruteMondai';
import { Flex } from 'antd';
import { FormInstance } from 'antd/lib';
import { Fragment } from 'react/jsx-runtime';

type TreeDataKaruteMondai = Partial<
  KaruteMondai & { rowId?: string | number; mode: 'add' | 'edit' }
>;

type Props = {
  mondaiData?: KaruteMondai[];
  form?: FormInstance;
  tableData?: TreeDataKaruteMondai[];
  selectedKeys?: string[];
  setSelectedKeys?: React.Dispatch<React.SetStateAction<string[]>>;
  expendedHistoryKeys?: string[];
  setExpendedHistoryKeys?: React.Dispatch<React.SetStateAction<string[]>>;
  handleSubmitMondai?: (
    oldData: Partial<TreeDataKaruteMondai>,
    newData: Partial<TreeDataKaruteMondai>
  ) => Promise<void | APIResponse<KaruteMondai>>;
  isEditable?: (record: Partial<KaruteMondai>) => boolean;
  isHistory?: boolean;
  isModal?: boolean;
  allMondais?: KaruteMondai[];
};

interface IHookReturn {
  columns: CustomColumnType<TreeDataKaruteMondai>[];
  getMondaiRelated: (record: TreeDataKaruteMondai) => string | JSX.Element;
  getMondaiLabel: (record: TreeDataKaruteMondai) => JSX.Element;
  renderMondaiStatus: (record: TreeDataKaruteMondai) => JSX.Element;
}

export const useMondaiColumns = ({
  form,
  tableData,
  setSelectedKeys,
  setExpendedHistoryKeys,
  handleSubmitMondai,
  isEditable,
  isHistory,
  isModal,
  allMondais,
}: Props): IHookReturn => {
  const renderMondaiStatus = (record: TreeDataKaruteMondai) => {
    return (
      <div className={styles.mondaiStatus}>
        <div className={`${record.status === 1 ? styles.active : styles.inactive}`}>
          {MONDAI_STATUS_OPTION[record.status as keyof typeof MONDAI_STATUS_OPTION]}
        </div>
      </div>
    );
  };
  const getMondaiLabel = (record: TreeDataKaruteMondai) => {
    const label = record.trauma_type ? MONDAI_TYPE[record.trauma_type] : '';
    return (
      <p>
        {label}
        {record.trauma_counter}
      </p>
    );
  };

  const renderMondaiRelated = (record: TreeDataKaruteMondai) => {
    if (!Array.isArray(record.mondai_related) || record.mondai_related.length === 0) return '';
    return (
      <div>
        {record.mondai_related.map((item, idx) => {
          const label = item.trauma_type
            ? MONDAI_TYPE[item.trauma_type as keyof typeof MONDAI_TYPE]
            : '';
          if (!label) return null;
          return (
            <span key={item.mondai_id || idx}>
              {label}
              {item.trauma_counter}
            </span>
          );
        })}
      </div>
    );
  };

  return {
    getMondaiRelated: renderMondaiRelated,
    getMondaiLabel,
    renderMondaiStatus,
    columns: [
      {
        title: '記載日',
        dataIndex: 'updated_at',
        key: 'updated_at',
        sortable: true,
        sortKey: 'updated_at',
        align: 'right',
        width: 80,
      },
      {
        title: '問題番号',
        dataIndex: 'trauma_type',
        key: 'trauma_type',
        align: 'center',
        width: 152,
        ...(!isHistory && {
          editable: record => ({
            type: 'select',
            options: Object.entries(MONDAI_TYPE_OPTIONS)
              .filter(([_, val]) => val !== MONDAI_TYPE_OPTIONS[0])
              .map(([key, val]) => ({
                label: val,
                value: Number(key),
                disabled: key === '1' && record.mode === 'add',
              })),
            rules: [{ message: '', required: true }],
            style: {
              height: '40px',
              width: '80px',
              marginRight: '16px',
            },
            render: (record: TreeDataKaruteMondai) => (
              <div
                style={{
                  paddingLeft: '16px',
                  borderLeft: '1px solid var(--gray-200)',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {getMondaiLabel(record)}
              </div>
            ),
          }),
        }),
        render: (_, record: TreeDataKaruteMondai) => {
          const label = record.trauma_type ? MONDAI_TYPE[record.trauma_type] : '';
          const mondai = getMondaiLabel(record);
          return (
            <div className={styles.mondaiType}>
              <p>{label}</p>
              {mondai}
            </div>
          );
        },
        sortable: true,
        sortKey: 'trauma_type',
      },
      {
        title: '問題名',
        dataIndex: 'subject',
        key: 'subject',
        align: 'left',
        sortable: true,
        sortKey: 'subject',
        width: isModal ? 154 : 338,
        ...(!isHistory && {
          editable: {
            type: 'input',
            rules: [{ message: '', required: true, whitespace: true }],
            maxLength: 100,
            disabled: (record: TreeDataKaruteMondai) => record.trauma_type === 1,
            style: {
              width: isModal ? '130px' : '314px',
              height: '40px',
              maxWidth: isModal ? '130px' : '314px',
            },
          },
        }),
        render: (_, record) => (
          <div style={{ maxWidth: isModal ? '130px' : '314px', wordBreak: 'break-word' }}>
            <RenderWithTooltip text={record.subject || ''} maxLines={2} />
          </div>
        ),
      },
      {
        title: '備考',
        dataIndex: 'remarks',
        key: 'remarks',
        sortable: true,
        sortKey: 'remarks',
        width: isModal ? 244 : 350,
        align: 'left',
        ...(!isHistory && {
          editable: {
            type: 'input',
            maxLength: 1000,
            rules: [
              {
                whitespace: true,
                message: '',
              },
            ],
            style: {
              width: isModal ? '220px' : '314px',
              height: '40px',
              maxWidth: isModal ? '220px' : '314px',
            },
          },
        }),
        render: (_, record) => (
          <div style={{ maxWidth: isModal ? '220px' : '314px', wordBreak: 'break-word' }}>
            <RenderWithTooltip text={record.remarks || ''} maxLines={2} />
          </div>
        ),
      },
      {
        title: '統合先',
        dataIndex: 'mondai_related',
        sortKey: 'related_id',
        key: 'related_id',
        align: 'left',
        width: 120,
        render: (_, record) => {
          return renderMondaiRelated(record);
        },
        sortable: true,
        ...(!isHistory && {
          editable: (record: TreeDataKaruteMondai) => {
            const filteredOptions =
              allMondais
                ?.filter(item => item.mondai_id !== record.mondai_id)
                .map(item => ({
                  label: item.trauma_type
                    ? `${MONDAI_TYPE[item.trauma_type]}${item.trauma_counter}`
                    : '',
                  value: item.mondai_id,
                })) || [];

            let relatedOption: { label: string; value: number }[] = [];

            const related = Array.isArray(record.mondai_related)
              ? record.mondai_related[0]
              : undefined;
            const isRelatedValid =
              related?.mondai_id && !filteredOptions.some(opt => opt.value === related.mondai_id);

            if (isRelatedValid) {
              relatedOption = [
                {
                  label: related.trauma_type
                    ? `${MONDAI_TYPE[related.trauma_type as keyof typeof MONDAI_TYPE]}${
                        related.trauma_counter
                      }`
                    : '',
                  value: related.mondai_id,
                },
              ];
            }

            const options = [...relatedOption, ...filteredOptions];

            return {
              type: 'select',
              options,
              showSearch: true,
              filterOption: (input, option) =>
                typeof option?.label === 'string'
                  ? option.label.toLowerCase().includes(input.toLowerCase())
                  : false,
              defaultValue: related?.mondai_id,
              style: {
                height: '40px',
                width: '74px',
              },
            };
          },
        }),
      },
      {
        title: '活性状態',
        dataIndex: 'status',
        key: 'status',
        sortable: true,
        align: 'center',
        sortKey: 'status',
        render: (_, record) => renderMondaiStatus(record as TreeDataKaruteMondai),
        ...(!isHistory && {
          editable: {
            type: 'select',
            options: Object.entries(MONDAI_STATUS_OPTION).map(([key, val]) => ({
              label: val,
              value: isNaN(parseInt(key)) ? key : parseInt(key),
            })),
            rules: [
              {
                required: true,
                message: '',
              },
            ],
            style: {
              height: '40px',
              width: '90px',
            },
          },
        }),
        width: 120,
      },
      {
        title: '',
        dataIndex: 'actions',
        align: 'center',

        ...(!isHistory && {
          render: (_, record) => {
            return isEditable && !isEditable(record) ? (
              <Flex style={{ width: '100%' }} align="center" gap={6}>
                <div
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    if (setSelectedKeys) {
                      setSelectedKeys(prev => [...prev, record?.rowId?.toString() || '']);
                    }
                    form?.setFieldsValue({ [record.rowId!]: record });
                  }}
                >
                  <Icon name="edit" color="$gray-400" width={16} height={16} />
                </div>
              </Flex>
            ) : (
              <Fragment key={record.medical_record_id}>
                <Flex
                  style={{
                    flexFlow: 'row-reverse',
                    justifyContent: 'flex-end',
                  }}
                  align="center"
                  gap={6}
                >
                  <div
                    style={{ cursor: 'pointer' }}
                    onClick={async () => {
                      const data = form?.getFieldValue(record.rowId);
                      const rowFields =
                        data && Object.keys(data).map(field => [record.rowId, field]);
                      await form?.validateFields(rowFields);

                      const res = handleSubmitMondai
                        ? await handleSubmitMondai(record, data)
                        : undefined;
                      if (res?.data) {
                        setExpendedHistoryKeys?.(keys => keys.filter(k => k !== record.rowId));
                      }
                    }}
                  >
                    <Icon name="check" color="green" height={16} width={16} />
                  </div>
                  <div
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      if (record.mode !== 'add') {
                        setSelectedKeys?.(keys =>
                          keys.filter(
                            k =>
                              (k !== String(record.rowId) && record.mode !== 'add') ||
                              k === tableData?.length.toString()
                          )
                        );
                      } else {
                        form?.setFieldValue(record.rowId, undefined);
                        form?.setFields([]);
                      }
                    }}
                  >
                    <Icon name="close" color="red" height={16} width={16} />
                  </div>
                </Flex>
              </Fragment>
            );
          },
        }),
      },
    ],
  };
};
