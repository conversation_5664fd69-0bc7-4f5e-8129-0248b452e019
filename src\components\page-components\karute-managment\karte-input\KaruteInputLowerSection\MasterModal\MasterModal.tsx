// Screen: マスタ

import { useState } from 'react';

import styles from './MasterModal.module.scss';

import { AppModal } from '@/components/common/core/Modal';
import { HuggedTabs } from '@/components/common/core/Tabs';
import CategoryForm from './CategoryForm';
import SentenceForm from './SentenceForm';

import { KaruteMasterField, KaruteMasterFieldCode } from '@/types/enum';
import { useGetMasterReferencesQuery } from '@/store/master/api';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';

const tabItems = [
  {
    key: '1',
    label: 'カテゴリー作',
    value: 'category',
  },
  {
    key: '2',
    label: '文章作成',
    value: 'sentence',
  },
];

type Props = {
  field: KaruteMasterField;
  code: KaruteMasterFieldCode;
  screen: 'database' | 'keika';
  handleCloseModal: () => void;
};

export function MasterModal({ field, code, handleCloseModal }: Props) {
  // States
  const [init] = useState<Record<string, { name: string; references: number[] }>>({});
  const [activeFilterValues, setActiveFilterValues] = useState<string>('category');
  // Custom Hooks
  const { handleValuesChange, confirmLeave, resetInitial, setCurrent, initialRef, resetAll } =
    useUnsavedWarning<Record<string, { name: string; references: number[] }>>(init);
  // Api
  const { data: masterReferencesData, isFetching: isFetchingMasterReferencesData } =
    useGetMasterReferencesQuery({
      type: 'medical_field',
    });
  // Methods
  const handleChangeTab = (key: string) => {
    const confirm = confirmLeave?.();
    if (confirm) {
      setActiveFilterValues(key);
      resetInitial({});
      setCurrent({});
    }
  };
  const handleOnCloseModal = () => {
    const confirm = confirmLeave?.();
    if (confirm) {
      handleCloseModal();
    }
  };

  const isFetchingCommon = isFetchingMasterReferencesData;
  return (
    <AppModal
      width="1000px"
      destroyOnClose
      open
      onCancel={handleOnCloseModal}
      isPadding={false}
      zIndex={1500}
    >
      <div className={styles.master_modal}>
        <HuggedTabs
          items={tabItems}
          activeValues={{ [activeFilterValues]: true }}
          onChange={key => {
            handleChangeTab(key as string);
          }}
          className={'pt-5 px-6'}
        />

        {activeFilterValues === 'category' && (
          <CategoryForm
            masterReferencesData={masterReferencesData?.data ?? []}
            isFetchingCommon={isFetchingCommon}
            handleUnsavedValueChange={handleValuesChange}
            resetInitial={resetInitial}
            setCurrent={setCurrent}
            initialValue={initialRef.current}
            resetAll={resetAll}
          />
        )}
        {activeFilterValues === 'sentence' && (
          <SentenceForm
            masterReferencesData={masterReferencesData?.data ?? []}
            isFetchingCommon={isFetchingCommon}
          />
        )}
      </div>
    </AppModal>
  );
}
