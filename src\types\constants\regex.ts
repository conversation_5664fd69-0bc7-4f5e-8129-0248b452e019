export const REGEX_KANA_NAME = /^[ァ-ヶー\s]+$/u;
export const REGEX_KANJI_NAME = /^[一-龯々〆〤\s]+$/u;
export const REGEX_URL =
  '^((http|https)://)[-a-zA-Z0-9@:%._\\+~#?&//=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%._\\+~#?&//=]*)$';
export const REGEX_HALF_WIDTH_ALNUM = /^[A-Za-z0-9]+$/;
export const REGEX_EMAIL_CHAR = /^[A-Za-z0-9@._-]+$/;
export const REGEX_PW = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z0-9]{6,255}$/;
export const REGEX_KATAKANA = /^[\u30A0-\u30FFー\s]+$/;
export const REGEX_PHONE = /^[0-9\-]+$/;
export const REGEX_DIGIT = /^[0-9]+$/;
export const REGEX_HAS_DIGIT = /\d/;
export const REGEX_IS_ALL_HIRAGANA = /^[\u3040-\u309Fー\s]+$/;
