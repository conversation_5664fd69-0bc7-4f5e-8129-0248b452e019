import { EVisitType } from '../constants';
import { EBooking } from '../enum';
import { Dayjs } from 'dayjs';

export interface HokenSchema {
  hoken_type: string;
  hoken_no: string;
  hoken_name: string;
  hoken_kigo: string;
  hoken_shikaku_date: string;
  hoken_yuko_date: string;
}

export interface KohiSchema {
  kohi_type: string;
  kohi_no: string;
  kohi_jukyusya_no: string;
  kohi_ritsu: number;
  kohi_start_date?: Dayjs;
  kohi_end_date?: Dayjs;
  kohi_jyosei_flg: boolean;
}

export interface EditReceptionSchema extends HokenSchema, KohiSchema {
  visit_via: EBooking | null;
  visit_type: EVisitType;
  booking_time: string;
  course_groups: {
    [serviceId: string]: string[];
  };
  practitioners: { value: number; label: string }[];
}

export type FormField<T> = {
  name: keyof T;
  label: string;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
};
export type PatientDetailSchema = {
  patient_cd: string;
  name: string;
  kana: string;
  gender: string;
  birthday: string;
  post: string;
  address: string;
  cellphone: string;
  email: string;
  patient_cmt: string;
};

export type Option<T> = {
  value: T;
  label: string;
};
