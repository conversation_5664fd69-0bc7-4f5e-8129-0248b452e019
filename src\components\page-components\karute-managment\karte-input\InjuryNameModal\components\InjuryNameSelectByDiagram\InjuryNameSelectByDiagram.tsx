import { FullSizeTabs } from '@/components/common/core/Tabs';
import useInjuryNameStore from '@/hooks/useInjuryNameStore';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useInjuryName } from '../../context/InjuryNameContext';
import {
  BlockOptionSelect,
  DiagramType,
  HumanBodyDiagramImage,
  InjuryNameIdentifier,
  LRType,
  TabOption,
} from '../../type';
import {
  BodyDiagramHandle,
  BodyDiagramBruisesSprainsContusions as DiagramA,
  BodyDiagramDislocationFractureIncompleteFracture as DiagramB,
} from '../BodyDiagram';
import InjurySelectionModal from '../InjurySelectionModal';
import styles from './InjuryNameSelectByDiagram.module.scss';

export const InjuryNameSelectByDiagram: React.FC = () => {
  const [activeTab, setActiveTab] = useState<HumanBodyDiagramImage>(TabOption[0].value);
  const [modalOpen, setModalOpen] = useState(false);
  const [options, setOptions] = useState<BlockOptionSelect[]>([]);
  const [lrTypeModal, setLrTypeModal] = useState<LRType | null>(null);
  const [pending, setPending] = useState<{ id: string; side: DiagramType } | null>(null);
  const lastConfirmed = useRef(false);
  const diagramAref = useRef<BodyDiagramHandle>(null);
  const diagramBref = useRef<BodyDiagramHandle>(null);
  const { injuryNamePayload, setInjuryNamePayload, manualBlock, setManualBlock } = useInjuryName();
  const { getOptionsByBlock, getBlocksByPart } = useInjuryNameStore();
  const [diagramReady, setDiagramReady] = useState(false);

  const currentRef = () =>
    activeTab === HumanBodyDiagramImage.BruisesSprainsContusions ? diagramAref : diagramBref;

  const selectFromDiagram = useCallback(
    (id: string | null, side: DiagramType | undefined) => {
      if (!id || side === undefined) return;
      currentRef().current?.selectActive(id);

      setPending({ id, side });
      setOptions(getOptionsByBlock(`${activeTab}_${side}`, Number(id)).options);
      setLrTypeModal(getOptionsByBlock(`${activeTab}_${side}`, Number(id)).lr_type);
      setModalOpen(true);
    },
    [activeTab, getOptionsByBlock]
  );

  const highlightCurrent = () => {
    if (injuryNamePayload.part_type === 7 || injuryNamePayload.part_type === 8) {
      if (manualBlock) {
        const ref =
          activeTab === HumanBodyDiagramImage.BruisesSprainsContusions ? diagramAref : diagramBref;
        ref.current?.selectActive(manualBlock.id);
      }
      return;
    }

    const res = getBlocksByPart(
      injuryNamePayload.part_type,
      injuryNamePayload.part_id,
      injuryNamePayload.lr_type
    );
    if (!res?.blocks.length) return;

    const ref =
      injuryNamePayload.part_type != null && injuryNamePayload.part_type < 4
        ? diagramAref
        : diagramBref;
    ref.current?.selectActive(String(res.blocks[0]));
  };

  const handleCancel = () => {
    currentRef().current?.clearActive();

    if (manualBlock) {
      currentRef().current?.selectActive(manualBlock.id);
    } else {
      highlightCurrent();
    }

    setPending(null);
    setModalOpen(false);
  };

  const handleConfirm = (payload: InjuryNameIdentifier) => {
    if (pending) {
      currentRef().current?.selectActive(pending.id, pending.side);

      if (payload.part_type === 7 || payload.part_type === 8) {
        setManualBlock({ id: pending.id });
      } else {
        setManualBlock(null);
      }

      lastConfirmed.current = true;
      setInjuryNamePayload(payload);
    }
    setPending(null);
    setModalOpen(false);
  };

  const prev = useRef(injuryNamePayload);
  const onDiagramReady = () => setDiagramReady(true);

  useEffect(() => {
    const sameCore =
      prev.current.part_type === injuryNamePayload.part_type &&
      prev.current.part_id === injuryNamePayload.part_id &&
      prev.current.lr_type === injuryNamePayload.lr_type;

    if (sameCore) {
      prev.current = injuryNamePayload;
      return;
    }

    if (lastConfirmed.current) {
      lastConfirmed.current = false;
      prev.current = injuryNamePayload;
      return;
    }

    diagramAref.current?.clearActive();
    diagramBref.current?.clearActive();
    prev.current = injuryNamePayload;
  }, [injuryNamePayload]);

  useEffect(() => {
    if (injuryNamePayload.part_type == null) return;

    const newTab =
      injuryNamePayload.part_type < 4
        ? HumanBodyDiagramImage.BruisesSprainsContusions
        : HumanBodyDiagramImage.DislocationFractureIncompleteFracture;

    if (newTab !== activeTab) {
      diagramAref.current?.clearActive();
      diagramBref.current?.clearActive();
      setActiveTab(newTab);
    }
  }, [injuryNamePayload.part_type]);

  useEffect(() => {
    if (pending) return;
    if (lastConfirmed.current) return;
    highlightCurrent();
  }, [
    injuryNamePayload.part_type,
    injuryNamePayload.part_id,
    injuryNamePayload.lr_type,
    manualBlock,
    getBlocksByPart,
    pending,
  ]);

  useEffect(() => {
    setDiagramReady(false);
  }, [activeTab]);

  useEffect(() => {
    if (!diagramReady) return;
    if (pending || modalOpen) return;
    highlightCurrent();
  }, [diagramReady]);

  useEffect(() => {
    if (injuryNamePayload.part_type !== 7 && injuryNamePayload.part_type !== 8) {
      setManualBlock(null);
    }
  }, [injuryNamePayload.part_type]);

  return (
    <>
      <div className={styles.wrapper}>
        <FullSizeTabs
          items={TabOption}
          activeValues={activeTab}
          onChange={setActiveTab}
          className={styles.tabs}
          tabOptionConfig={{
            className: styles.tab_option,
            showTitle: true,
          }}
        />
        {activeTab === HumanBodyDiagramImage.BruisesSprainsContusions && (
          <DiagramA
            onSelect={selectFromDiagram}
            ref={diagramAref}
            className={styles.diagram}
            onReady={onDiagramReady}
            defaultColor="rgba(106, 195, 255, 0.5)"
            hoverColor="rgba(7, 131, 255, 0.5)"
            activeColor="rgba(250, 167, 224, 0.5)"
          />
        )}
        {activeTab === HumanBodyDiagramImage.DislocationFractureIncompleteFracture && (
          <DiagramB
            onSelect={selectFromDiagram}
            ref={diagramBref}
            className={styles.diagram}
            onReady={onDiagramReady}
            defaultColor="rgba(189, 239, 238, 0.5)"
            hoverColor="rgba(132, 192, 202, 0.5)"
            activeColor="rgba(250, 167, 224, 0.5)"
          />
        )}
      </div>

      {modalOpen && (
        <InjurySelectionModal
          open={modalOpen}
          options={options}
          lrType={lrTypeModal}
          onCancel={handleCancel}
          onConfirm={handleConfirm}
        />
      )}
    </>
  );
};
