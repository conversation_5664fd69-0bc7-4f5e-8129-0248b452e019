// Screen: K200_経過記録の登録
import { Col, Flex, Form, Row, Spin } from 'antd';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import Button from '@/components/common/core/Button';
import { AppModal } from '@/components/common/core/Modal';
import ModalTitle from '@/components/common/layout/ModalTitle/ModalTitle';
import styles from './UpsertKeikaModal.module.scss';

import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppSelect } from '@/components/common/core/Select';
import { trimField } from '@/components/page-components/visit-management/dashboard/VisitDetail/helper';
import { useAppDispatch } from '@/config/redux/store';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { setChanged } from '@/store/karute';
import {
  useCheckExistPreviousKeikaQuery,
  useCreateKeikaMutation,
  useGetDetailKeikaQuery,
  useGetKeikaMondaiRegisteredByVisitDateQuery,
  useLazyGetPreviousKeikaQuery,
  useUpdateKeikaMutation,
} from '@/store/karute/api';
import {
  KeikaDetailHistoryItem,
  RegisteredMondai,
  UpsertKeikaMondai,
  UpsertKeikaPayload,
} from '@/store/karute/type';
import { DiseaseType, SchemaType } from '@/store/schema/type';
import {
  COMMA_SEPARATED,
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  keikaEmptyWarningField,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  SPACE,
  STATUS_CODES,
  SUBMIT_BTN,
} from '@/types/constants';
import {
  KeikaImage,
  KeikaItem,
  REMOVE_CONFIRM,
  UpsertKeikaSchema,
  WARNING_REMOVE_COURSE_OPTION,
} from '@/types/constants/karute';
import { ButtonType, IsReferenceModalOpen } from '@/types/enum';
import { clearSpace, showFlashNotice } from '@/utils';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import { useParams } from 'react-router-dom';
import { useKaruteInput } from '../../../provider';
import {
  EPaymentType,
  EStatus,
  RegisteredVisit,
} from '../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types';
import { DoctorByVisitDate } from '../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertDatabaseForm/types/database_form_get';
import {
  CourseItem,
  CourseTableRef,
  ListCourseTable,
} from '../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertOptionListForm/ListCourseTable';
import {
  initOptionData,
  ListOptionTable,
  OptionItem,
  OptionTableRef,
} from '../../KaruteInputLeftPane/UpsertDatabaseModal/UpsertOptionListForm/ListOptionTable';
import { SetKeikaFromHistoryModal } from './SetKeikaFromHistoryModal';
import { DEFAULT_DISEASE_ID, UpsertKeikaForm, UpsertKeikaFormRef } from './UpsertKeikaForm';
import { UnsavedHandlers } from '@/types/interface';
import { ReferenceModal } from '../../ReferenceModal';
type Props = {
  mode: 'update' | 'create';
  id?: number;
  handleClose: () => void;
  disease_base_id?: number;
};
interface DefaultKeikaMondai {
  treatment: string;
  objective: string;
  plan: string;
  remarks: string;
  option_checked: boolean | 0 | 1 | null | undefined;
  treatment_images: number[];
  objective_images: number[];
}
const defaultFormValues = {
  keika: [
    {
      treatment: '',
      objective: '',
      plan: '',
      remarks: '',
      option_checked: null,
      treatment_images: [],
      objective_images: [],
    },
  ],
};
const WARNING_DATABASE_MSG = 'が未入力です。このまま登録してもよろしいでしょうか？';
const MAX_TEXT_LENGTH = 1000;
const concatField = (current: string | undefined, history: string | undefined) =>
  current || history
    ? trimField(
        `${current ?? ''}${current && history ? '\n' : ''}${history ?? ''}`,
        MAX_TEXT_LENGTH
      )
    : '';
function UpsertKeikaModal({
  mode,
  handleClose,
  id: keikaId,
  disease_base_id,
  unsavedHandlers,
}: Props & { unsavedHandlers: UnsavedHandlers }) {
  const hasSetUp = useRef(false);
  const hasOpenSchemaSetUp = useRef(false);
  const formRef = useRef<UpsertKeikaFormRef>(null);

  const courseTableRef = useRef<CourseTableRef>(null);
  const optionTableRef = useRef<OptionTableRef>(null);

  const {
    serviceId,
    id: karuteId,
    schemaType,
    disease,
    diseaseBaseId,
  } = useParams<{
    id: string; // number
    serviceId: string; // number
    schemaType: SchemaType;
    disease: DiseaseType;
    diseaseBaseId: string; // number
  }>();
  const { unsavedChange, unsavedSubmit, resetInitial } = unsavedHandlers;

  // States
  const [isSetFromHistoryModalOpen, setIsSetFromHistoryModalOpen] = useState<number | null>(null);
  const [isFirstActiveMondai, setIsFirstActiveMondai] = useState<
    Record<number, Partial<KeikaItem> | null>
  >({});
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState<IsReferenceModalOpen>(null);

  // Course
  const [courseDataSource, setCourseDataSource] = useState<CourseItem[]>([]);
  const [courseSelectedKeys, setCourseSelectedKeys] = useState<string[]>([]);
  // Options
  const [optionDataSource, setOptionDataSource] = useState<OptionItem[]>([]);
  const [optionSelectedKeys, setOptionSelectedKeys] = useState<string[]>([]);
  // Initial value for tracking changes
  const [isSetFromPreviousDisabled, setIsSetFromPreviousDisabled] = useState(false);
  // Custom hooks
  const { showWarningDialog, hideWarningDialog } = useWarningDialog();
  const dispatch = useAppDispatch();
  const { isInitalLoading: isFetchingKarute, setDrawnImages, karuteDetailData } = useKaruteInput();

  const [createKeikaMutation, { isLoading: isCreatingKeikaLoading }] = useCreateKeikaMutation();
  const patientId = karuteDetailData?.patient_id;
  const { data: keikaDetail, isFetching: isFetchingKeikaDetail } = useGetDetailKeikaQuery(
    keikaId!,
    {
      skip: mode === 'create' || !keikaId,
    }
  );
  const { data: keikaRegistedData, isFetching: isFetchingKeikaRegistedData } =
    useGetKeikaMondaiRegisteredByVisitDateQuery(
      {
        karute_id: Number(karuteId!),
        service_id: Number(serviceId!),
        patient_id: Number(patientId!),
        type: mode === 'update' ? 'all' : undefined,
      },
      {
        skip: !karuteId || !serviceId || !patientId,
      }
    );

  const [fetchPreviousKeika, { isFetching: isFetchingPreviousKeika }] =
    useLazyGetPreviousKeikaQuery();
  const { data: isExistPreviousKeikaResponse } = useCheckExistPreviousKeikaQuery(
    {
      karute_id: karuteId!,
      service_id: serviceId!,
      before_keika_id: keikaId,
    },
    {
      skip: !karuteId || !serviceId,
    }
  );
  const isExistPreviousKeika = useMemo(() => {
    return isExistPreviousKeikaResponse?.data?.exists;
  }, [isExistPreviousKeikaResponse?.data]);
  const [updateKeikaMutation, { isLoading: isUpdatingKeikaLoading }] = useUpdateKeikaMutation();

  const [form] = Form.useForm<UpsertKeikaSchema>();

  // Memo
  const allRegisteredKeikaMondai = useMemo(() => {
    if (
      !keikaRegistedData?.data ||
      keikaRegistedData?.data.length === 0 ||
      isFetchingKeikaRegistedData
    )
      return [];
    return (keikaRegistedData?.data ?? []).map(item => {
      const mapDiseasebase: Record<number, RegisteredMondai> = {};

      Object.values(item?.disease_bases ?? []).forEach(diseaseBase => {
        mapDiseasebase[diseaseBase.disease_base_id] = diseaseBase;
      });
      return {
        value: dayjs(item.visit_date, DATE_FORMATS.DATE_HOUR_MINUTE).format(DATE_FORMATS.DATE),
        label: dayjs(item.visit_date, DATE_FORMATS.DATE_HOUR_MINUTE).format(DATE_FORMATS.DATE),
        visit_id: item.visit_id,
        courses: (item?.selected_jihi_courses ?? []).map(item => ({
          id: item.course_id,
          name: item.course_name,
        })),
        doctor: item.doctor,
        payment_type_checked:
          item?.payment_type_checked !== undefined && item?.payment_type_checked != null
            ? String(item.payment_type_checked)
            : null,
        addon_option_checked: item.addon_option_checked,
        hoken_courses: item?.hoken_courses ?? [],
        disease_bases: mapDiseasebase,
      };
    });
  }, [keikaRegistedData]);
  const initialDoctorByVisitDate: DoctorByVisitDate = useMemo(() => {
    // Get doctor from
    // 1. registered vist
    // 2. upserted doctor if mode is update
    // -> avoid doctor deleted when access modal
    const keikaDoctors =
      mode === 'update'
        ? (keikaDetail?.data?.keika_details ?? []).map(item => ({
            visit_date: dayjs(keikaDetail?.data?.visit_date).format(DATE_FORMATS.DATE),
            disease_base_id: item.disease_base_id,
            user_id: item.doctor_id,
            kanji_name: item.doctor_name ?? '',
          }))
        : [];
    // const visitDateDoctors = (allRegisteredKeikaMondai ?? [])
    //   .map(item => ({
    //     visit_date: dayjs(item.value, DATE_FORMATS.DATE).format(DATE_FORMATS.DATE),
    //     user_id: item.doctor?.user_id,
    //     kanji_name: item.doctor?.kanji_name ?? '',
    //   }))
    //   .filter(doctor => doctor.user_id && doctor.kanji_name && doctor.visit_date);

    const allDoctors = [...keikaDoctors];
    const result = allDoctors.reduce<DoctorByVisitDate>(
      (acc, { visit_date, user_id, kanji_name, disease_base_id }) => {
        if (!visit_date || !user_id || !disease_base_id) return acc;

        if (acc[disease_base_id]) {
          if (
            !acc[disease_base_id].doctors.some(
              doctor => doctor.user_id === user_id && doctor.kanji_name === kanji_name
            )
          ) {
            acc[disease_base_id].doctors.push({ user_id, kanji_name });
          }
        } else {
          acc[disease_base_id] = {
            doctors: [{ user_id, kanji_name }],
            visit_date,
          };
        }

        return acc;
      },
      {}
    );
    return result;
  }, [mode, keikaDetail]);
  // Functions
  const calculateFirstActiveMondai = useCallback(
    (keika: Partial<KeikaItem>[]): Record<number, Partial<KeikaItem> | null> => {
      const firstActiveMap: Record<number, Partial<KeikaItem> | null> = {};
      keika.forEach((_, index) => {
        firstActiveMap[index] = null;
      });

      const firstActiveIndex = keika.findIndex(item => item.status === EStatus.ACTIVE);

      if (firstActiveIndex === -1) {
        // If no keika is ACTIVE, mark the first one (index 0) as ACTIVE
        if (keika.length > 0) {
          firstActiveMap[0] = keika[0];
        }
      } else {
        firstActiveMap[firstActiveIndex] = keika[firstActiveIndex];
      }
      setIsFirstActiveMondai(firstActiveMap);
      return firstActiveMap;
    },
    []
  );
  const handleUnsavedValueChange = useCallback(
    ({
      optionData,
      courseData,
      upsertKeika,
    }: {
      optionData?: OptionItem[];
      courseData?: CourseItem[];
      upsertKeika?: UpsertKeikaSchema;
    }) => {
      const currentOptionData = optionData ?? optionTableRef.current?.getAllCurrentData();
      const currentCourseData = courseData ?? courseTableRef.current?.getAllCurrentData();
      const currentKeika: KeikaItem[] = upsertKeika?.keika ?? form.getFieldValue(['keika']);
      const currentVisitDate = upsertKeika?.visit_date ?? form.getFieldValue('visit_date');
      unsavedChange(null, {
        keika: currentKeika,
        visitDate: currentVisitDate,
        keika_options: currentOptionData,
        keika_courses: currentCourseData,
      });
    },
    [form, unsavedChange]
  );
  const handleSetFromHistory = useCallback(
    (historyItem: Partial<KeikaDetailHistoryItem>) => {
      // keep consistency with upsertDatabase modal
      // Note*: handleSubmit also handle reassign common field again
      if (isSetFromHistoryModalOpen === null) return;

      const currentKeikas = form.getFieldValue(['keika']);
      const newKeikaDiseases = [...(currentKeikas || [])];
      const currentKeikaDatabase: KeikaItem = newKeikaDiseases[isSetFromHistoryModalOpen as number];
      const isInFirstActiveMondai = !!isFirstActiveMondai[isSetFromHistoryModalOpen];

      const commonFields = isInFirstActiveMondai
        ? {
            objective: concatField(
              currentKeikaDatabase?.objective ?? '',
              historyItem?.objective ?? ''
            ),
            objective_images: historyItem?.objective_images ?? [],
            treatment: concatField(
              currentKeikaDatabase?.treatment ?? '',
              historyItem?.treatment ?? ''
            ),
            treatment_images: historyItem?.treatment_images ?? [],
            plan: concatField(currentKeikaDatabase?.plan ?? '', historyItem?.plan ?? ''),
            remarks: concatField(currentKeikaDatabase?.remarks ?? '', historyItem?.remarks ?? ''),
          }
        : {
            objective: currentKeikaDatabase?.objective ?? '',
            objective_images: currentKeikaDatabase?.objective_images ?? [],
            treatment: currentKeikaDatabase?.treatment ?? '',
            treatment_images: currentKeikaDatabase?.treatment_images ?? [],
            plan: currentKeikaDatabase?.plan ?? '',
            remarks: currentKeikaDatabase?.remarks ?? '',
          };

      const updatedKeika: Partial<KeikaItem> = {
        disease_base_id: currentKeikaDatabase?.disease_base_id,
        status: currentKeikaDatabase?.status,
        payment_type: currentKeikaDatabase?.payment_type,
        option_checked: currentKeikaDatabase?.option_checked,
        subjective: concatField(currentKeikaDatabase?.subjective, historyItem?.subjective) || '',
        doctor_id:
          historyItem?.doctor_status === EStatus.ACTIVE
            ? historyItem?.doctor_id
            : currentKeikaDatabase?.doctor_id,
        assessment: concatField(currentKeikaDatabase?.assessment, historyItem?.assessment) || '',
        ...commonFields,
      };

      if (isInFirstActiveMondai) {
        newKeikaDiseases.forEach((keika: KeikaItem, index: number) => {
          newKeikaDiseases[index] = {
            ...newKeikaDiseases[index],
            ...commonFields,
          };
        });
      }

      newKeikaDiseases[isSetFromHistoryModalOpen as number] = {
        ...newKeikaDiseases[isSetFromHistoryModalOpen as number],
        ...updatedKeika,
      };
      form.setFieldsValue({ keika: newKeikaDiseases });
      handleUnsavedValueChange({
        upsertKeika: {
          visit_date: form.getFieldValue(['visit_date']),
          keika: newKeikaDiseases,
        },
      });
      setIsSetFromHistoryModalOpen(null);
    },
    [form, isSetFromHistoryModalOpen, isFirstActiveMondai, handleUnsavedValueChange]
  );

  const handleSetFromPrevious = useCallback(async () => {
    if (!isExistPreviousKeika) return;
    try {
      const res = await fetchPreviousKeika({
        karute_id: karuteId!,
        service_id: serviceId!,
        before_keika_id: keikaId,
      }).unwrap();
      if (res.status === STATUS_CODES.BAD_REQUEST) {
        showFlashNotice({ type: 'error', message: res.message });
        return;
      }
      if (res?.status !== STATUS_CODES.OK) return;

      const keikaMondais: KeikaItem[] = form.getFieldValue('keika');
      const objective_images: KeikaImage[] = res?.data?.objective_images ?? [];
      const treatment_images: KeikaImage[] = res?.data?.treatment_images ?? [];

      // Common fields: Apply to all keikas regardless of active status
      const commonFields = {
        objective: '',
        objective_images: objective_images,
        treatment: '',
        treatment_images: treatment_images,
        plan: '',
        remarks: '',
        // option_checked: !!res.data?.option_checked,
      };

      const updatedKeikaMondais: KeikaItem[] = [...keikaMondais].map(item => {
        const latestMondai = (res?.data?.keika_details ?? []).find(
          latestMondai =>
            item.disease_base_id === latestMondai.disease_base_id && item?.status === EStatus.ACTIVE
        );

        if (!latestMondai) return item;

        commonFields.objective = concatField(item?.objective ?? '', latestMondai?.objective ?? '');
        commonFields.treatment = concatField(item?.treatment ?? '', latestMondai?.treatment ?? '');
        commonFields.plan = concatField(item?.plan ?? '', latestMondai?.plan ?? '');
        commonFields.remarks = concatField(item?.remarks ?? '', latestMondai?.remarks ?? '');

        const updatedKeika: KeikaItem = {
          ...item,
          subjective: concatField(item?.subjective ?? '', latestMondai?.subjective ?? ''),
          // payment_type: String(latestMondai?.payment_type) as EPaymentType,
          assessment: concatField(item?.assessment ?? '', latestMondai?.assessment ?? ''),
          doctor_id:
            latestMondai?.doctor?.status === EStatus.ACTIVE
              ? latestMondai?.doctor?.doctor_id
              : item?.doctor_id,
          ...commonFields,
        };

        return updatedKeika;
      });

      form.setFieldsValue({ keika: updatedKeikaMondais });
      handleUnsavedValueChange({
        upsertKeika: {
          visit_date: form.getFieldValue(['visit_date']),
          keika: updatedKeikaMondais,
        },
      });
    } catch (error) {
      console.error('⚠️ Error in handleSetFromPrevious:', error);
    }
  }, [
    form,
    fetchPreviousKeika,
    handleUnsavedValueChange,
    karuteId,
    keikaId,
    mode,
    serviceId,
    isExistPreviousKeika,
  ]);

  const mapBackendErrorsToForm = useCallback((errorData: Record<string, string[]>) => {
    const databaseErrors: { name: (string | number)[]; errors: string[] }[] = [];
    const patientOptionsErrors: Record<string, string[]> = {};
    const patientCoursesErrors: Record<string, string[]> = {};

    Object.entries(errorData).forEach(([fieldName, errors]) => {
      const fieldPath = fieldName.split('.').map(part => {
        return isNaN(Number(part)) ? part : Number(part);
      });

      if (fieldPath[0] === 'keika_details' && fieldPath.length >= 2) {
        fieldPath[0] = 'keika';
        databaseErrors.push({
          name: fieldPath,
          errors,
        });
      } else if (fieldPath[0] === 'keika_options' && fieldPath.length === 3) {
        patientOptionsErrors[fieldName] = errors;
      } else if (fieldPath[0] === 'keika_courses' && fieldPath.length === 3) {
        patientCoursesErrors[fieldName] = errors;
      }
    });
    if (Object.keys(patientOptionsErrors).length > 0 && optionTableRef.current) {
      optionTableRef.current.handleOptionErrors(patientOptionsErrors);
    }
    if (Object.keys(patientCoursesErrors).length > 0 && courseTableRef.current) {
      courseTableRef.current.handleCourseErrors(patientCoursesErrors);
    }

    return databaseErrors;
  }, []);
  const clearFieldErrors = useCallback(
    (fieldPath: (string | number)[]) => {
      form.setFields([{ name: fieldPath as any, errors: [] }]);
    },
    [form]
  );

  const handleOpenReference = (isReferenceModalOpen: IsReferenceModalOpen) => {
    setIsReferenceModalOpen(isReferenceModalOpen);
  };
  const handleSubmitReference = (text: string) => {
    if (!isReferenceModalOpen || !isReferenceModalOpen?.name || !isReferenceModalOpen?.field)
      return;
    const currentValue = form.getFieldValue([
      'keika',
      isReferenceModalOpen?.name,
      isReferenceModalOpen?.field as any,
    ]);
    form.setFieldValue(
      ['keika', isReferenceModalOpen?.name, isReferenceModalOpen?.field as any],
      concatField(currentValue, text)
    );
  };
  const handleCloseReference = () => {
    setIsReferenceModalOpen(null);
  };
  // put in callBack
  const handleValuesChange = useCallback(
    (changedValues: any) => {
      if (!clearFieldErrors) return;
      if (changedValues?.keika) {
        Object.entries(changedValues?.keika).forEach(([index, changedFields]: [string, any]) => {
          const idx = Number(index);
          Object.keys(changedFields).forEach(field => {
            clearFieldErrors(['keika', idx, field]);
          });
        });
      }
    },
    [clearFieldErrors]
  );
  const closeCleanUpModal = useCallback(() => {
    setDrawnImages({});
    handleClose();
  }, [setDrawnImages, handleClose]);
  const handleSubmit = useCallback(
    async (
      values: UpsertKeikaSchema,
      updatedKeika: UpsertKeikaMondai[],
      defaultMondaiValue: DefaultKeikaMondai,
      optionData: OptionItem[],
      courseData: CourseItem[]
    ) => {
      const currentVisitRegisteredData = allRegisteredKeikaMondai?.find(
        item => item.value === values.visit_date
      );

      const upsertPayloadKeika: UpsertKeikaPayload = {
        patient_id: patientId!,
        karute_id: Number(karuteId)!,
        visit_id: Number(currentVisitRegisteredData?.visit_id),
        option_checked: defaultMondaiValue?.option_checked ? 1 : 0,
        visit_date: dayjs(values?.visit_date).format(DATE_FORMATS.SUBMIT_DATE),
        service_id: serviceId!,
        objective_images: defaultMondaiValue?.objective_images,
        treatment_images: defaultMondaiValue?.treatment_images,
        keika_options: optionData.map(item => {
          return {
            registration_date: dayjs(item.registration_date).format(DATE_FORMATS.SUBMIT_DATE),
            treatment_date: dayjs(item.treatment_date).format(DATE_FORMATS.SUBMIT_DATE),
            option_id: Number(item?.option_id),
            name: item?.name ?? '',
            quantity: item?.quantity as number,
          };
        }),
        keika_courses: courseData.flatMap(item =>
          item.course_ids.length > 0
            ? item.course_ids.map((course_id, index) => ({
                registration_date: dayjs(item.registration_date).format(DATE_FORMATS.SUBMIT_DATE),
                treatment_date: dayjs(item.treatment_date).format(DATE_FORMATS.SUBMIT_DATE),
                course_id: Number(course_id),
                name: item?.names?.[index] ?? '',
              }))
            : [
                {
                  registration_date: dayjs(item.registration_date).format(DATE_FORMATS.SUBMIT_DATE),
                  treatment_date: dayjs(item.treatment_date).format(DATE_FORMATS.SUBMIT_DATE),
                  course_id: NaN,
                  name: '',
                },
              ]
        ),
        keika_details: updatedKeika,
      };
      const res =
        mode === 'create'
          ? await createKeikaMutation(upsertPayloadKeika).unwrap()
          : await updateKeikaMutation({ id: keikaId!, body: upsertPayloadKeika }).unwrap();
      if (res.status === STATUS_CODES.INVALID_FIELD) {
        const fields = mapBackendErrorsToForm(res.data as any);
        form.setFields(fields as any);
        return;
      }
      if (res.status === STATUS_CODES.OK) {
        showFlashNotice({
          type: 'success',
          message: NOTICE_COMMON_MESSAGE.KEIKA_CREATED,
        });
        unsavedSubmit();
        closeCleanUpModal();
        dispatch(setChanged());
      }
    },
    [
      createKeikaMutation,
      form,
      patientId,
      serviceId,
      allRegisteredKeikaMondai,
      karuteId,
      closeCleanUpModal,
      updateKeikaMutation,
      mode,
      keikaId,
      mapBackendErrorsToForm,
      unsavedSubmit,
    ]
  );
  const handleGetWarningRequiredValue = useCallback(
    (value: UpsertKeikaMondai[]) => {
      // K106: show warning empty fields
      const indices = new Set<number>();
      const emptyFields: { label: string; priority: number }[] = [];
      const seenLabels = new Set<string>();

      value.forEach((item, index) => {
        if (item?.status !== EStatus.ACTIVE) return;

        keikaEmptyWarningField.forEach(field => {
          const fieldValue = item[field.key as keyof UpsertKeikaMondai];

          if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
            if (field.isCommon && isFirstActiveMondai[index]) {
              indices.add(index + 1);
            } else if (!field.isCommon) {
              indices.add(index + 1);
            }
            if (!seenLabels.has(field.label)) {
              emptyFields.push({ label: field.label, priority: field.priority });
              seenLabels.add(field.label);
            }
          }
        });
      });

      if (indices.size > 0) {
        const sortedIndices = Array.from(indices)
          .sort((a, b) => a - b)
          .map(item => `#${item}`)
          .join(COMMA_SEPARATED);
        const fields = emptyFields
          .sort((a, b) => a.priority - b.priority)
          .map(item => item.label)
          .join(COMMA_SEPARATED);
        return `${sortedIndices}${SPACE.repeat(3)}${fields}${WARNING_DATABASE_MSG}`;
      }

      return '';
    },
    [isFirstActiveMondai]
  );
  const allRegisteredKeikaMondaiMap = useMemo(
    () =>
      new Map<string, RegisteredVisit & { disease_bases: Record<number, RegisteredMondai> }>(
        allRegisteredKeikaMondai.map(item => [item.value, item])
      ),
    [allRegisteredKeikaMondai]
  );

  const handleFinish = async (value: UpsertKeikaSchema) => {
    const courseOptionValidate = await Promise.all([
      optionTableRef.current?.handleSubmittingAllEditingRows(),
      courseTableRef.current?.handleSubmittingAllEditingRows(),
    ]);
    if (!courseOptionValidate) return;
    const firstKey = Object.entries(isFirstActiveMondai).find(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ([_, value]) => !!value
    )?.[0];
    const activeIndxMondai = firstKey !== undefined ? Number(firstKey) : undefined;

    const activeKeika =
      activeIndxMondai !== undefined ? value?.keika?.[activeIndxMondai] : undefined;

    const defaultMondaiValue = {
      treatment: clearSpace(activeKeika?.treatment),
      objective: clearSpace(activeKeika?.objective),
      plan: clearSpace(activeKeika?.plan),
      remarks: clearSpace(activeKeika?.remarks),
      option_checked: activeKeika?.option_checked,
      treatment_images: (activeKeika?.treatment_images ?? []).map(item => item.keika_image_id),
      objective_images: (activeKeika?.objective_images ?? []).map(item => item.keika_image_id),
    };
    const hokenCourseDataSourceByVisitDate: Map<string, CourseItem> = new Map();
    const currentVisitDate = form.getFieldValue('visit_date');
    const payloadKeiKaMondai: UpsertKeikaMondai[] = (value?.keika ?? []).map(item => {
      const hokenId = allRegisteredKeikaMondaiMap
        ?.get(currentVisitDate)
        ?.hoken_courses?.find(
          course => String(course.payment_course_type) === item.payment_type
        )?.course_id;
      if (hokenId) {
        const currentHoken = hokenCourseDataSourceByVisitDate.get(currentVisitDate);

        hokenCourseDataSourceByVisitDate.set(currentVisitDate, {
          rowId: String(hokenId),
          course_ids: [...new Set([...(currentHoken?.course_ids ?? []), String(hokenId)])],
          isInit: true,
          names: [],
          treatment_date: currentVisitDate,
          registration_date: dayjs().format(DATE_FORMATS.DATE),
        });
      }
      return {
        payment_type: Number(item?.payment_type),
        subjective: item?.subjective ?? '',
        disease_base_id: item?.disease_base_id,
        // objective: defaultValue.objective,
        assessment: item?.assessment ?? '',
        // plan: defaultValue.plan,
        // treatment: defaultValue.treatment,
        // remarks: defaultValue.remarks,
        doctor_id: item?.doctor_id ?? null,
        status: item?.status,
        ...defaultMondaiValue,
      };
    });
    const mergedCourseData = [
      ...(courseOptionValidate[1] ?? []),
      ...hokenCourseDataSourceByVisitDate.values(),
    ];
    const fullWarningMessage = handleGetWarningRequiredValue(payloadKeiKaMondai);

    if (fullWarningMessage.length > 0) {
      // K106: Show warning empty fields
      showWarningDialog({
        message: fullWarningMessage,
        buttons: [
          {
            type: 'cancel',
            label: 'キャンセル',
            onClick: () => {
              hideWarningDialog();
              return;
            },
          },
          {
            type: 'confirm',
            label: '登録する',
            onClick: async () => {
              hideWarningDialog();
              await handleSubmit(
                value,
                payloadKeiKaMondai,
                defaultMondaiValue,
                courseOptionValidate[0] ?? [],
                mergedCourseData
              );
              return;
            },
          },
        ],
      });
      return;
    }
    await handleSubmit(
      value,
      payloadKeiKaMondai,
      defaultMondaiValue,
      courseOptionValidate[0] ?? [],
      mergedCourseData
    );
    return;
  };

  const handleCleanCourse = useCallback(
    (providedIsFirstActiveMondai?: Record<number, Partial<KeikaItem> | null>) => {
      const currentFirstActiveMondai = providedIsFirstActiveMondai ?? isFirstActiveMondai;
      const firstKey = Object.entries(currentFirstActiveMondai).find(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ([_, value]) => !!value
      )?.[0];

      const activeIndxMondai = firstKey !== undefined ? Number(firstKey) : undefined;
      const keikaMondais: KeikaItem[] = form.getFieldValue('keika');
      const currentVisitDate = form.getFieldValue('visit_date');
      const isSelfPaid = (keikaMondais ?? []).some(
        item => item.payment_type === EPaymentType.SELF_PAID
      );
      const activeKeika =
        activeIndxMondai !== undefined ? keikaMondais?.[activeIndxMondai] : undefined;
      const isOptionChecked = !!activeKeika?.option_checked;
      setCourseDataSource(prev => {
        if (!(isSelfPaid || isOptionChecked)) {
          setCourseSelectedKeys([]);
          return [];
        }

        const filteredDataSource = prev.filter(item => item.treatment_date === currentVisitDate);
        const validRowIds = new Set(filteredDataSource.map(item => item.rowId));

        setCourseSelectedKeys(prevKeys => prevKeys.filter(key => validRowIds.has(key)));

        return filteredDataSource.map(item => ({
          ...item,
          course_ids: [...new Set(item.course_ids)],
        }));
      });
    },
    [form, isFirstActiveMondai]
  );

  const handleCleanOption = useCallback(
    (providedIsFirstActiveMondai?: Record<number, Partial<KeikaItem> | null>) => {
      if (!setOptionDataSource || !setOptionSelectedKeys) return;
      const currentFirstActiveMondai = providedIsFirstActiveMondai ?? isFirstActiveMondai;
      const firstActiveKeikaKey = Object.entries(currentFirstActiveMondai).find(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ([_, value]) => !!value
      )?.[0];
      if (isNil(firstActiveKeikaKey)) return;

      const keika = form.getFieldValue('keika');
      const visitDate = form.getFieldValue('visit_date');
      const hasValidOption = !!keika?.[Number(firstActiveKeikaKey)]?.option_checked;
      setOptionDataSource(prev => {
        const updatedDataSource = hasValidOption
          ? prev.filter(item => visitDate === item.treatment_date)
          : [];

        const removedRowIds = prev
          .filter(item => !updatedDataSource.some(opt => opt.rowId === item.rowId))
          .map(item => item?.rowId?.toString());

        if (removedRowIds.length > 0) {
          setOptionSelectedKeys(prevKeys => prevKeys.filter(key => !removedRowIds.includes(key)));
        }

        return updatedDataSource;
      });
    },
    [form, setOptionDataSource, setOptionSelectedKeys, isFirstActiveMondai]
  );

  const handleAddCourse = useCallback(
    (isInit?: boolean) => {
      // AddCourse only when payment_type === jihi / self-paid
      const rowId = uuidv4();
      const currentVisit = form.getFieldValue('visit_date');
      const selectedVisitData = allRegisteredKeikaMondai?.find(item => item.value === currentVisit);
      // Source 1: From changing visit_date
      // Source 2: From changing payment type
      // Source 3: From changing option_checked
      setCourseDataSource(prev => {
        const exists = prev.some(item => item.treatment_date === currentVisit);
        const courseIds = (selectedVisitData?.courses ?? []).map(course => String(course.id));

        if (exists) {
          // No need to find -> just to keep consistency with UpsertDatabaseModal
          const existingItem = prev.find(item => item.treatment_date === currentVisit);
          if (existingItem) {
            setCourseSelectedKeys(prevKeys => [...new Set([...prevKeys, existingItem.rowId])]);
          }

          // return prev.map(item =>
          //   item.treatment_date === currentVisit
          //     ? { ...item, course_ids: [...new Set([...item.course_ids, ...courseIds])] }
          //     : item
          // );
          return prev;
        }

        const newCourseData = {
          rowId,
          isInit,
          treatment_date: currentVisit,
          course_ids: courseIds,
          registration_date: courseIds.length ? dayjs().format(DATE_FORMATS.DATE) : '',
        };
        const currentCourseData = courseTableRef.current?.getAllCurrentData() ?? [];
        if (!isInit) {
          handleUnsavedValueChange({
            courseData: newCourseData
              ? [newCourseData, ...currentCourseData].sort((a, b) =>
                  dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
                )
              : currentCourseData,
          });
        }

        setCourseSelectedKeys(prevKeys => [...new Set([...prevKeys, rowId])]);
        // courseTableRef.current?.scrollToRow(rowId); // Don't really need -> placeholder if specs updated
        return [...prev, newCourseData].sort((a, b) =>
          dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
        );
      });
    },
    [form, allRegisteredKeikaMondai, handleUnsavedValueChange]
  );

  // Handles adding a new option record and cleaning optionDataSource
  const handleAddOption = useCallback(
    (isInit?: boolean) => {
      // Add option to current visit date
      // Remove option that is not on current visit_date
      if (!setOptionDataSource || !setOptionSelectedKeys) return;

      const visitDate = form.getFieldValue('visit_date');

      setOptionDataSource(prev => {
        // No need to re-focus record because will check/uncheck option will delete and add new option

        const removedRowIds = (prev ?? [])
          .filter(item => item.treatment_date !== visitDate)
          .map(item => String(item.rowId));

        const filteredData = prev.filter(item => item.treatment_date === visitDate);

        const newRowId = uuidv4();
        const newRecord = {
          ...initOptionData,
          rowId: newRowId,
          isInit,
          treatment_date: dayjs(visitDate).format(DATE_FORMATS.DATE),
          registration_date: '',
        };
        const currentOptionData = optionTableRef.current?.getAllCurrentData() ?? [];
        if (!isInit) {
          handleUnsavedValueChange({
            optionData: newRecord
              ? [...currentOptionData, newRecord].sort((a, b) =>
                  dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
                )
              : currentOptionData,
          });
        }

        setOptionSelectedKeys(prevKeys => [
          ...prevKeys.filter(key => !removedRowIds.includes(key)),
          newRowId,
        ]);

        return [...filteredData, newRecord].sort((a, b) =>
          dayjs(b.treatment_date).diff(dayjs(a.treatment_date))
        );
      });
    },
    [form, setOptionDataSource, setOptionSelectedKeys, handleUnsavedValueChange]
  );
  const getRemovedOptionDates = useCallback((): string[] => {
    const currentVisitDate = form.getFieldValue('visit_date');
    const optionIndex = Number(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      Object.entries(isFirstActiveMondai).find(([_, value]) => value)?.[0]
    );
    const isOptionChecked = form.getFieldValue(['keika', optionIndex, 'option_checked']);

    const validVisitDatesWithOption = new Set(isOptionChecked ? [currentVisitDate] : []);

    return (optionDataSource ?? [])
      .filter(item => !validVisitDatesWithOption.has(item.treatment_date))
      .map(item => item.treatment_date);
  }, [form, optionDataSource, isFirstActiveMondai]);

  const getRemovedCourseDates = useCallback((): string[] => {
    const keikaItem: KeikaItem[] = form.getFieldValue('keika') || [];
    const currentVisitDate = form.getFieldValue('visit_date');
    const optionIndex = Number(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      Object.entries(isFirstActiveMondai).find(([_, value]) => value)?.[0]
    );
    const hasSelfPaid = keikaItem.some(
      (_, index) => form.getFieldValue(['keika', index, 'payment_type']) === EPaymentType.SELF_PAID
    );

    const isOptionChecked = form.getFieldValue(['keika', optionIndex, 'option_checked']);

    const validVisitDates = new Set(hasSelfPaid || isOptionChecked ? [currentVisitDate] : []);

    const result = (courseDataSource ?? [])
      .filter(item => !validVisitDates.has(item.treatment_date))
      .map(item => item.treatment_date);

    return result;
  }, [form, courseDataSource, isFirstActiveMondai]);
 
  const handelCancelHideCourseOption = useCallback(
    ({
      name,
      changeType,
      removedOptionDates,
      removedCourseDates,
    }: {
      name?: number;
      changeType: 'visit_date' | 'payment_type' | 'option_checked';
      removedOptionDates: string[];
      removedCourseDates: string[];
    }) => {
      // Handle Option-related cancellations
      if (removedOptionDates.length) {
        if (changeType === 'visit_date') {
          form.setFieldValue('visit_date', removedOptionDates[0]);
        }
        if (changeType === 'option_checked') {
          const keika: KeikaItem[] = form.getFieldValue('keika') || [];
          keika.forEach((_, index) => {
            form.setFieldValue(['keika', index, 'option_checked'], true);
          });
        }
      }

      // Handle Course-related cancellations
      if (removedCourseDates.length) {
        if (changeType === 'visit_date') {
          form.setFieldValue('visit_date', removedCourseDates[0]);
        }
        if (changeType === 'payment_type' && name !== undefined) {
          form.setFieldValue(['keika', name, 'payment_type'], EPaymentType.SELF_PAID);
        }
        if (changeType === 'option_checked') {
          const keika: KeikaItem[] = form.getFieldValue('keika') || [];
          keika.forEach((_, index) => {
            form.setFieldValue(['keika', index, 'option_checked'], true);
          });
        }
      }
    },
    [form]
  );
  const handleWarningRemoveCourseOption = useCallback(
    ({
      name,
      changeType,
    }: {
      name?: number;
      changeType: 'visit_date' | 'payment_type' | 'option_checked';
    }): Promise<boolean> => {
      const removedOptionDates = getRemovedOptionDates();
      const removedCourseDates = getRemovedCourseDates();
      if (!removedOptionDates.length && !removedCourseDates.length) {
        return Promise.resolve(true);
      }

      return new Promise<boolean>(resolve => {
        showWarningDialog({
          message: WARNING_REMOVE_COURSE_OPTION,
          buttons: [
            {
              type: 'cancel',
              label: 'キャンセル',
              onClick: () => {
                hideWarningDialog();
                handelCancelHideCourseOption({
                  name,
                  changeType,
                  removedOptionDates,
                  removedCourseDates,
                });
                resolve(false);
              },
            },
            {
              type: 'confirm',
              label: REMOVE_CONFIRM,
              onClick: () => {
                hideWarningDialog();
                showFlashNotice({
                  message: NOTICE_COMMON_MESSAGE.COURSE_OPTION_UPDATED,
                  type: 'success',
                });
                resolve(true);
              },
            },
          ],
          onCancel: () => {
            handelCancelHideCourseOption({
              name,
              changeType,
              removedOptionDates,
              removedCourseDates,
            });
            resolve(false);
          },
        });
      });
    },
    [
      getRemovedCourseDates,
      getRemovedOptionDates,
      hideWarningDialog,
      showWarningDialog,
      handelCancelHideCourseOption,
    ]
  );
  const handleChangeVisitDate = useCallback(
    async (visitDate: string) => {
      const isVisitDateChangeChangableCourseOption = await handleWarningRemoveCourseOption({
        changeType: 'visit_date',
      });
      if (!isVisitDateChangeChangableCourseOption) return;

      const visitRegisteredData = allRegisteredKeikaMondaiMap.get(visitDate);

      const currentKeikaDatabase: KeikaItem[] = form.getFieldValue('keika');
      const visitKeikaMondaisMap: Record<number, Partial<KeikaItem | any>> = {
        ...JSON.parse(JSON.stringify(visitRegisteredData?.disease_bases ?? {})),
      };

      [...currentKeikaDatabase].forEach(item => {
        if (visitKeikaMondaisMap[item.disease_base_id]) {
          visitKeikaMondaisMap[item.disease_base_id] = {
            ...visitKeikaMondaisMap[item.disease_base_id],
            subjective: item?.subjective ?? '',
            objective: item?.objective ?? '',
            objective_images: item?.objective_images ?? [],
            assessment: item?.assessment ?? '',
            plan: item?.plan ?? '',
            treatment: item?.treatment ?? '',
            treatment_images: item?.treatment_images ?? [],
            remarks: item?.remarks ?? '',
          };
        }
      });

      const updatedMondais = Object.values(visitKeikaMondaisMap);
      calculateFirstActiveMondai(updatedMondais);
      form.setFieldValue('keika', updatedMondais);
      for (let i = 0; i < updatedMondais.length; i++) {
        form.setFieldValue(
          ['keika', i, 'option_checked'],
          visitRegisteredData?.addon_option_checked
        );
        form.setFieldValue(['keika', i, 'payment_type'], visitRegisteredData?.payment_type_checked);
        form.setFieldValue(['keika', i, 'doctor_id'], visitRegisteredData?.doctor?.user_id);
      }
      if (visitRegisteredData?.payment_type_checked === EPaymentType.SELF_PAID) {
        handleAddCourse();
      }
      if (visitRegisteredData?.addon_option_checked) {
        handleAddOption();
        handleAddCourse();
      }
      if (
        !!visitRegisteredData?.payment_type_checked &&
        visitRegisteredData?.payment_type_checked !== EPaymentType.SELF_PAID &&
        visitRegisteredData?.addon_option_checked
      ) {
        handleAddCourse();
      }
      handleCleanCourse();
      handleCleanOption();
      calculateFirstActiveMondai(currentKeikaDatabase);
    },
    [
      form,
      handleAddCourse,
      handleAddOption,
      handleCleanCourse,
      handleCleanOption,
      allRegisteredKeikaMondaiMap,
    ]
  );
  useEffect(() => {
    // Set initial value
    if (
      mode === 'create' &&
      !hasSetUp.current &&
      allRegisteredKeikaMondai &&
      !isFetchingKeikaRegistedData
    ) {
      if (!allRegisteredKeikaMondai?.length) {
        form.setFieldsValue({ keika: [] });
        return;
      }
      const initialRegisteredValue = allRegisteredKeikaMondai?.[0];

      const initalKeikaMondais = (
        Object.values(allRegisteredKeikaMondai?.[0]?.disease_bases) ?? []
      ).map(item => ({
        disease_base_id: item.disease_base_id,
        // trauma_name: item?.trauma_name,
        trauma_counter: item?.trauma_counter,
        trauma_id: item?.trauma_type,
        injury_name: item?.injury_name,
        status: item?.status,
        option_checked: initialRegisteredValue?.addon_option_checked,
        payment_type: initialRegisteredValue?.payment_type_checked as EPaymentType,
        doctor_id: initialRegisteredValue?.doctor?.user_id,
      }));
      const currentFirstActiveMondai = calculateFirstActiveMondai(initalKeikaMondais);
      form.setFieldsValue({ keika: [] });
      const initialFormValue = {
        keika: initalKeikaMondais,
        visit_date: initialRegisteredValue?.value,
      };
      const isAnyMondaiActive = initalKeikaMondais?.find(item => item.status === EStatus.ACTIVE);
      setIsSetFromPreviousDisabled(!isAnyMondaiActive);
      form.setFieldsValue(initialFormValue);

      if (initialRegisteredValue?.payment_type_checked === EPaymentType.SELF_PAID) {
        handleAddCourse(true);
      }
      if (initialRegisteredValue?.addon_option_checked) {
        handleAddOption(true);
        handleAddCourse(true);
      }
      if (
        !!initialRegisteredValue?.payment_type_checked &&
        initialRegisteredValue?.payment_type_checked !== EPaymentType.SELF_PAID &&
        initialRegisteredValue?.addon_option_checked
      ) {
        handleAddCourse(true);
      }
      handleCleanCourse(currentFirstActiveMondai);
      handleCleanOption(currentFirstActiveMondai);
      resetInitial(initialFormValue);
      hasSetUp.current = true;
      hasOpenSchemaSetUp.current = true;
    }
    if (mode === 'update' && !hasSetUp.current && !isFetchingKeikaDetail && keikaDetail?.data) {
      const initalVisitDate = dayjs(keikaDetail?.data?.visit_date).format(DATE_FORMATS.DATE);
      const initalKeikaMondais: KeikaItem[] = (keikaDetail?.data?.keika_details ?? []).map(
        item => ({
          disease_base_id: item?.disease_base_id,
          trauma_name: item?.disease_base?.trauma_name,
          trauma_counter: item?.disease_base?.trauma_counter,
          trauma_type: item?.disease_base?.trauma_type,
          injury_name: item?.disease_base?.injury_name,
          status: item?.disease_base?.status as EStatus,
          subjective: item?.subjective,
          objective: item?.objective,
          assessment: item?.assessment,
          plan: item?.plan,
          payment_type: String(item?.payment_type) as EPaymentType,
          option_checked: keikaDetail?.data?.option_checked,
          treatment: item?.treatment,
          remarks: item?.remarks,
          doctor_id: item?.doctor_id,
          treatment_images: keikaDetail?.data?.treatment_images ?? [],
          objective_images: keikaDetail?.data?.objective_images ?? [],
        })
      );
      const isAnyMondaiActive = initalKeikaMondais?.find(item => item.status === EStatus.ACTIVE);
      setIsSetFromPreviousDisabled(!isAnyMondaiActive);
      const initialCourseData: CourseItem[] = (keikaDetail?.data?.keika_courses ?? []).reduce<
        CourseItem[]
      >((acc, item) => {
        // 0 is Jihi/ Self paid
        if (String(item.payment_course_type ?? EPaymentType.SELF_PAID) !== EPaymentType.SELF_PAID)
          return acc;
        const date = dayjs(item.treatment_date).format(DATE_FORMATS.DATE);

        const existing = acc.find(entry => dayjs(entry.treatment_date).isSame(dayjs(date)));
        const registrationDate = dayjs(item.registration_date).format(DATE_FORMATS.DATE);
        if (existing && item.course_id) {
          if (!item.course_id) return acc;
          existing.course_ids.push(String(item.course_id));
          existing.names?.push(item.name);
          if (new Date(registrationDate) > new Date(existing.registration_date)) {
            existing.registration_date = registrationDate;
          }
        } else {
          acc.push({
            rowId: uuidv4(),
            treatment_date: date,
            course_ids: item.course_id ? [String(item.course_id)] : [],
            registration_date: registrationDate,
            names: [item.name],
          });
        }

        return acc;
      }, []);
      const intialOptionData: OptionItem[] = (keikaDetail?.data?.keika_options ?? []).map(item => ({
        rowId: uuidv4(),
        registration_date: dayjs(item?.registration_date).format(DATE_FORMATS.DATE),
        option_id: item?.option_id ? String(item?.option_id) : undefined,
        quantity: item?.quantity,
        treatment_date: dayjs(item?.treatment_date).format(DATE_FORMATS.DATE),
        name: item?.name,
      }));
      const initialFormValue = {
        keika: initalKeikaMondais,
        visit_date: initalVisitDate,
        keika_options: intialOptionData,
        keika_courses: initialCourseData,
      };
      setCourseDataSource(initialFormValue.keika_courses);
      setOptionDataSource(initialFormValue.keika_options);
      calculateFirstActiveMondai(initalKeikaMondais);
      form.setFieldsValue({});

      form.setFieldsValue({
        keika: initialFormValue.keika,
        visit_date: initialFormValue.visit_date,
      });
      resetInitial(initialFormValue);

      hasSetUp.current = true;
    }
  }, [
    resetInitial,
    form,
    keikaDetail?.data,
    mode,
    isFetchingKeikaDetail,
    allRegisteredKeikaMondai,
    calculateFirstActiveMondai,
    isFetchingKeikaRegistedData,
    unsavedChange,
    handleAddCourse,
    handleAddOption,
    handleCleanCourse,
    handleCleanOption,
  ]);
  useEffect(() => {
    // To: set Draw Image when open schema from for keika list
    // use hasOpenSchemaSetUp to avoid re-run when open in KeikaForm
    if (
      mode === 'update' &&
      keikaDetail?.data &&
      schemaType === SchemaType.KEIKA &&
      (DiseaseType.O === disease || DiseaseType.T === disease) &&
      diseaseBaseId &&
      !hasOpenSchemaSetUp.current
    ) {
      const edittedImage = {
        [SchemaType.KEIKA]: {
          [String(DEFAULT_DISEASE_ID)]: {
            [DiseaseType.O]: (keikaDetail?.data?.objective_images ?? []).map(item => ({
              id: `${item.keika_id}_${item.keika_image_id}`,
              keika_image_id: item.keika_image_id,
              url: item.url,
              schema_image_id: item?.schema_image_id,
            })),
            [DiseaseType.T]: (keikaDetail?.data?.treatment_images ?? []).map(item => ({
              id: `${item.keika_id}_${item.keika_image_id}`,
              keika_image_id: item.keika_image_id,
              url: item.url,
              schema_image_id: item?.schema_image_id,
            })),
          },
        },
      };
      setDrawnImages(edittedImage as any);
      hasOpenSchemaSetUp.current = true;
    }
  }, [keikaDetail, schemaType, disease, diseaseBaseId, mode, setDrawnImages]);
  const isInitialLoading = useMemo(() => {
    return isFetchingKeikaRegistedData || isFetchingKarute || isFetchingKeikaDetail;
  }, [isFetchingKeikaRegistedData, isFetchingKarute, isFetchingKeikaDetail]);

  const isSubmitting = useMemo(() => {
    return isUpdatingKeikaLoading || isCreatingKeikaLoading || isFetchingPreviousKeika;
  }, [isUpdatingKeikaLoading, isCreatingKeikaLoading, isFetchingPreviousKeika]);
  const hasActiveMondai = useMemo(() => {
    if (!isFirstActiveMondai) return { isJihi: false, isOptionChecked: false };

    const currentKeika: KeikaItem[] = form.getFieldValue(['keika']) ?? [];
    if (!currentKeika.length) return { isJihi: false, isOptionChecked: false };

    let isJihi = false;
    let isOptionChecked = false;

    for (const [index, item] of currentKeika.entries()) {
      if (item.status !== EStatus.ACTIVE) continue;

      isJihi = isJihi || item.payment_type === EPaymentType.SELF_PAID;
      isOptionChecked = isOptionChecked || !!item.option_checked;

      // Break early to reduce iterating
      if (isJihi && isOptionChecked) break;

      if (isJihi && isFirstActiveMondai[index]) break;
    }

    return { isJihi, isOptionChecked };
  }, [form, isFirstActiveMondai]);
  const isCourseNullableByDate = useMemo(() => {
    const currentMondais: KeikaItem[] = form.getFieldValue(['keika']) ?? [];
    let isJihi = false;
    let isOptionChecked = false;
    currentMondais.forEach((item, index) => {
      if (item.status === EStatus.INACTIVE) return;
      if (item.payment_type === EPaymentType.SELF_PAID && !isJihi) {
        isJihi = true;
      }
      if (isFirstActiveMondai[index] && item.option_checked && !isOptionChecked) {
        isOptionChecked = true;
      }
    });

    return !isJihi && isOptionChecked;
    // isFirstActiveMondai is for rerender the current mondai states if pyament or option changed
  }, [isFirstActiveMondai, form]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isCourseNullable = (treatmentDate: string) => {
    return isCourseNullableByDate;
  };
  const isActionable = useCallback(
    (record: OptionItem | CourseItem, mode: 'course' | 'option') => {
      // allow edit only for option | course has active mondai
      if (mode === 'course') {
        return hasActiveMondai.isJihi || hasActiveMondai.isOptionChecked;
      }
      if (mode === 'option') {
        return hasActiveMondai.isOptionChecked;
      }
      return false;
    },
    [hasActiveMondai]
  );
  const findMondaiToSCroll = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (treatment_date: string) => {
      const firstActiveMondai = Object.values(isFirstActiveMondai).find(item => !!item);
      formRef.current?.scrollToMondai(firstActiveMondai);
    },
    [isFirstActiveMondai]
  );
  return (
    <AppModal
      width="1376px"
      destroyOnClose
      open
      onCancel={closeCleanUpModal}
      isPadding={false}
      zIndex={1500}
    >
      <Spin
        spinning={isSubmitting || isInitialLoading}
        children={
          <Form
            form={form}
            name="upsert_keika_form"
            layout="vertical"
            onFinish={handleFinish}
            onValuesChange={(changedValues, allValues) => {
              handleValuesChange(changedValues);
              handleUnsavedValueChange({ upsertKeika: allValues });
            }}
            scrollToFirstError
            initialValues={defaultFormValues}
          >
            <div className={` pt-5  px-6`}>
              <Flex justify="space-between" align="center">
                <ModalTitle title={mode === 'create' ? '経過記録の登録' : '経過記録の編集'} />
              </Flex>
              <Flex gap="middle" align="start">
                <AppFormItem
                  name={'visit_date'}
                  label="施術日"
                  required
                  style={{ width: '346px' }}
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('施術日'),
                    },
                  ]}
                >
                  <AppSelect
                    suffixIcon={<Icon width={20} height={20} name="calendar" />}
                    size="small"
                    options={allRegisteredKeikaMondai?.map(item => ({
                      value: item.value,
                      label: item.label,
                    }))}
                    placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
                    disabled={mode === 'update'}
                    onChange={value => {
                      handleChangeVisitDate(value);
                    }}
                    allowClear={false}
                  />
                </AppFormItem>
                <Flex gap="small" style={{ marginTop: '30px' }}>
                  <Button
                    customType={ButtonType.PRIMARY}
                    onClick={handleSetFromPrevious}
                    disabled={
                      isInitialLoading ||
                      isSubmitting ||
                      isSetFromPreviousDisabled ||
                      !isExistPreviousKeika
                    }
                  >
                    前回コピー
                  </Button>
                </Flex>
              </Flex>
            </div>

            <Flex
              vertical
              gap="middle"
              className={`${styles.create_keika_modal_content} pl-6`}
              style={{
                paddingRight: '18px',
              }}
            >
              <UpsertKeikaForm
                ref={formRef}
                hasSetUp={hasSetUp.current}
                isFirstActiveMondai={isFirstActiveMondai}
                calculateFirstActiveMondai={calculateFirstActiveMondai}
                // Course
                courseDataSource={courseDataSource}
                // setCourseDataSource={setCourseDataSource}
                courseSelectedKeys={courseSelectedKeys}
                // setCourseSelectedKeys={setCourseSelectedKeys}
                // Options
                optionDataSource={optionDataSource}
                setOptionDataSource={setOptionDataSource}
                optionSelectedKeys={optionSelectedKeys}
                setOptionSelectedKeys={setOptionSelectedKeys}
                // set from hisotry
                setIsSetFromHistoryModalOpen={setIsSetFromHistoryModalOpen}
                handleAddCourse={handleAddCourse}
                handleCleanCourse={handleCleanCourse}
                handleAddOption={handleAddOption}
                handleCleanOption={handleCleanOption}
                disease_base_id={disease_base_id}
                initialDoctorByVisitDate={initialDoctorByVisitDate}
                handleOpenReference={handleOpenReference}
                handleWarningRemoveCourseOption={handleWarningRemoveCourseOption}
              />
              <Flex gap="middle" vertical>
                <Row gutter={24} className={styles.lower_section_tables}>
                  <Col span={12}>
                    <ListCourseTable
                      ref={courseTableRef}
                      courseDataSource={courseDataSource}
                      setCourseDataSource={setCourseDataSource}
                      courseSelectedKeys={courseSelectedKeys}
                      setCourseSelectedKeys={setCourseSelectedKeys}
                      isActionable={(record: CourseItem) => isActionable(record, 'course')}
                      allRegisteredVisit={[...allRegisteredKeikaMondai]}
                      isCourseNullable={isCourseNullable}
                      scrollToFirstSameDayRow={(treatment_date: string) => {
                        findMondaiToSCroll(treatment_date);
                      }}
                      handleUnsavedValueChange={handleUnsavedValueChange}
                    />
                  </Col>
                  <Col span={12}>
                    <ListOptionTable
                      ref={optionTableRef}
                      optionDataSource={optionDataSource}
                      setOptionDataSource={setOptionDataSource}
                      optionSelectedKeys={optionSelectedKeys}
                      setOptionSelectedKeys={setOptionSelectedKeys}
                      // isDisabled={isAllFormDisabled}
                      isActionable={(record: OptionItem) => isActionable(record, 'option')}
                      initialOptionData={keikaDetail?.data?.keika_options}
                      scrollToFirstSameDayRow={(treatment_date: string) => {
                        findMondaiToSCroll(treatment_date);
                      }}
                      handleUnsavedValueChange={handleUnsavedValueChange}
                    />
                  </Col>
                </Row>
              </Flex>

              <Flex gap="middle" justify="end" className="mt-2 mb-6">
                <Button
                  customType={ButtonType.SECONDARY_COLOR}
                  onClick={handleClose}
                  className="btn-modal-width"
                  customSize="lg"
                >
                  キャンセル
                </Button>
                <Button
                  customType={ButtonType.PRIMARY}
                  htmlType="submit"
                  className="btn-modal-width"
                  customSize="lg"
                  loading={isSubmitting}
                >
                  {mode == 'create' && SUBMIT_BTN.CREATE}
                  {mode == 'update' && SUBMIT_BTN.CREATE}
                  {/* Spec required usign the same btn */}
                </Button>
              </Flex>
            </Flex>
          </Form>
        }
      />
      {isSetFromHistoryModalOpen !== null && (
        <SetKeikaFromHistoryModal
          handleClose={() => setIsSetFromHistoryModalOpen(null)}
          handleSubmit={handleSetFromHistory}
        />
      )}
      {!!isReferenceModalOpen && (
        <ReferenceModal
          handleCloseModal={handleCloseReference}
          handleSubmitModal={handleSubmitReference}
          label={isReferenceModalOpen?.label}
          screen="keika"
          field={isReferenceModalOpen.field}
          code={isReferenceModalOpen.code}
        />
      )}
    </AppModal>
  );
}
export default memo(UpsertKeikaModal);
