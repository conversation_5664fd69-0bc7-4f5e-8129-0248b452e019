import clsx from 'clsx';
import { isNil } from 'lodash';

import { Flex, Radio } from 'antd';
import { useWatch } from 'antd/es/form/Form';

import styles from '../UpsertDatabaseForm.module.scss';

import { AppFormItem } from '@/components/common/core/FormItem';
import { AppRadio } from '@/components/common/core/Radio';
import { AppCheckbox } from '@/components/common/core/Checkbox';
import { AppInput } from '@/components/common/core/Input';
import LabelRender from '../LabelRender';

import { EKaruteServieId } from '@/types/enum';
import { EPaymentType, EStatus, MondaiItem, paymentOptions } from '../types';
import { ERROR_COMMON_MESSAGE, PLACEHOLDER_MESSAGE_DEFAULT } from '@/types/constants';
import { useMemo } from 'react';
import { isInjuryNameEditable } from '@/utils';

type Props = {
  currentMondai: MondaiItem;
  name: number;
  traumaName: string;
  serviceId: EKaruteServieId;
  handleInjuryName: (name: number) => void;
  handleCheckPaymentType: (paymentType: EPaymentType, name: number) => void;
  handleChangeOption: (value: boolean, name: number) => void;
  restField: any;
  hasVisitDate: boolean;
  isFormDisabled: boolean;
  isInjuryNameDisabled: boolean;
};

export function InjuryNamePaymenFormItem({
  currentMondai,
  name,
  traumaName,
  serviceId,
  handleInjuryName,
  handleCheckPaymentType,
  handleChangeOption,
  restField,
  hasVisitDate,
  isFormDisabled,
  isInjuryNameDisabled,
}: Props) {
  const updatedPaymentType = useWatch(['database', name, 'payment_type']);
  const isInjuryNameOther = useMemo(() => {
    return isInjuryNameEditable(updatedPaymentType);
  }, [updatedPaymentType]);

  return (
    <div className={styles.database_form_item}>
      <LabelRender
        required
        label={`#${name + 1} ${traumaName} 問題名/主訴（又は負傷名）`}
        firstBtn={
          serviceId == EKaruteServieId.Judo
            ? {
                label: '負傷名',
                onClick: () => {
                  handleInjuryName(name);
                },
                disabled: !hasVisitDate || isFormDisabled || isNil(updatedPaymentType),
              }
            : undefined
        }
      />
      <Flex gap="7px" vertical>
        <Flex className={styles.treatment_header_row} justify="space-between" wrap>
          <AppFormItem
            {...restField}
            name={[name, 'payment_type']}
            required
            style={{ flex: 1, flexShrink: 'none' }}
            className={styles.payment_type}
            rules={
              currentMondai?.status === EStatus.INACTIVE || !hasVisitDate
                ? []
                : [
                    {
                      required: true,
                      message: ERROR_COMMON_MESSAGE.REQUIRED('請求区分'),
                    },
                  ]
            }
          >
            <Radio.Group
              disabled={!hasVisitDate || isFormDisabled}
              onChange={value => {
                handleCheckPaymentType(value.target.value, name);
              }}
            >
              <Flex gap="small" wrap>
                {paymentOptions.map(item => (
                  <AppRadio value={item.value} key={item.value}>
                    {item.label}
                  </AppRadio>
                ))}
              </Flex>
            </Radio.Group>
          </AppFormItem>
          <AppFormItem
            {...restField}
            name={[name, 'option_checked']}
            valuePropName="checked"
            className={clsx(styles.option)}
          >
            <AppCheckbox
              disabled={!hasVisitDate || isFormDisabled}
              onChange={value => {
                handleChangeOption(value.target.checked, name);
              }}
            >
              オプション
            </AppCheckbox>
          </AppFormItem>
        </Flex>
        <AppFormItem
          {...restField}
          name={[name, isInjuryNameOther ? 'injury_name_other' : 'injury_name']}
          required
          rules={
            currentMondai?.status === EStatus.INACTIVE || !hasVisitDate
              ? []
              : [
                  {
                    required: true,
                    whitespace: true,
                    message: ERROR_COMMON_MESSAGE.REQUIRED('問題名又は負傷名'),
                  },
                ]
          }
        >
          <AppInput
            placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            maxLength={100}
            disabled={
              !hasVisitDate || isFormDisabled || isInjuryNameDisabled || isNil(updatedPaymentType)
            }
            size={'small'}
          />
        </AppFormItem>
      </Flex>
    </div>
  );
}
