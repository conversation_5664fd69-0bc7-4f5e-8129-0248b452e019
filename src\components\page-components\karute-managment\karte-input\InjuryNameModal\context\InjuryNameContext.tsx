import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { InjuryNameIdentifier, initialInjuryName } from '../type';

export interface ManualBlock {
  id: string;
}

interface CtxType {
  injuryNamePayload: InjuryNameIdentifier;
  setInjuryNamePayload: React.Dispatch<React.SetStateAction<InjuryNameIdentifier>>;

  manualBlock: ManualBlock | null;
  setManualBlock: React.Dispatch<React.SetStateAction<ManualBlock | null>>;
  isInjuryNameEditable: boolean;
  setIsInjuryNameEditable: React.Dispatch<React.SetStateAction<boolean>>;
}

const Ctx = createContext<CtxType>(null as never);
export const useInjuryName = () => useContext(Ctx);

export const InjuryNameProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [injuryNamePayload, setInjuryNamePayload] =
    useState<InjuryNameIdentifier>(initialInjuryName);
  const [manualBlock, setManualBlock] = useState<ManualBlock | null>(null);
  const [isInjuryNameEditable, setIsInjuryNameEditable] = useState<boolean>(false);

  const prevType = useRef<number | null>(null);
  useEffect(() => {
    if (
      manualBlock &&
      [7, 8].includes(prevType.current ?? -1) &&
      injuryNamePayload.part_type !== prevType.current
    ) {
      setManualBlock(null);
    }
    prevType.current = injuryNamePayload.part_type;
  }, [injuryNamePayload.part_type, manualBlock]);

  return (
    <Ctx.Provider
      value={{
        injuryNamePayload,
        setInjuryNamePayload,
        manualBlock,
        setManualBlock,
        isInjuryNameEditable,
        setIsInjuryNameEditable,
      }}
    >
      {children}
    </Ctx.Provider>
  );
};
