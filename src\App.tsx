import FlashNoticeProvider from '@/components/provider/FlashNoticeProvider';
import AppRouter from '@/router';
import '@/styles/app.scss';
import { ConfigProvider } from 'antd';
import jaJP from 'antd/es/locale/ja_JP';
import dayjs from 'dayjs';
import 'dayjs/locale/ja';
import { unstable_HistoryRouter as HistoryRouter } from 'react-router-dom';
import ConfirmModalProvider from './components/provider/ConfirmModalProvider';
import theme from './config/antd/themeConfig';
import { createBrowserHistory } from 'history';

dayjs.locale('ja');
const history = createBrowserHistory() as any;
export default function App() {
  return (
    <ConfigProvider theme={theme} wave={{ disabled: true }} locale={jaJP}>
      <HistoryRouter history={history}>
        <ConfirmModalProvider>
          <FlashNoticeProvider>
            <AppRouter />
          </FlashNoticeProvider>
        </ConfirmModalProvider>
      </HistoryRouter>
    </ConfigProvider>
  );
}
