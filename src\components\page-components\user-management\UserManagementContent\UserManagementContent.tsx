import { AppInput } from '@/components/common/core/Input';
import { Dropdown, Flex } from 'antd';
import styles from './UserManagement.module.scss';
import Table from '@/components/common/core/CommonTable';
import { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ParamGetListUser, User } from '@/store/user-sync/type';
import { useGetUserListQuery } from '@/store/user-sync/api';
import { ButtonType, Order, ROLE } from '@/types/enum';
import { MoreOutlined } from '@ant-design/icons';
import Icon from '@/components/common/core/Icon';
import Button from '@/components/common/core/Button';
import { getRoleName, getRoleOptionsView, getUserRole, getViewOnlyOptions } from '@/utils/helper';
import clsx from 'clsx';
import { redirectToMedixSync } from '@/utils';
import { useAppSelector } from '@/config/redux/store';
import { getAccessToken, getAuth } from '@/store/auth/selectors';
import { PAGINATION_THRESHOLD } from '@/types/constants';
import { MultiSelect } from '@/components/common/core/MultiSelect';
import { skipToken } from '@reduxjs/toolkit/query';

function UserManagementContent() {
  const [userList, setUserList] = useState<User[] | []>([]);
  const [total, setTotal] = useState(0);
  const auth = useAppSelector(getAuth);
  const [params, setParams] = useState<ParamGetListUser>({
    username: null,
    name: null,
    email: null,
    manageable_type: null,
    is_doctor: null,
    can_access_karute: null,
    can_access_booking: null,
    order_by: null,
    order_type: null,
    page: 1,
    limit: PAGINATION_THRESHOLD,
    clinic_id: auth.currentClinicId,
  });
  const [inputs, setInputs] = useState({
    username: '',
    name: '',
    email: '',
  });

  const accessToken = useAppSelector(getAccessToken);
  const [shouldFetchUserList, setShouldFetchUserList] = useState<boolean>(true);

  const { data: fetchUserList, isFetching: loading } = useGetUserListQuery(
    shouldFetchUserList ? params : skipToken
  );
  const viewOnlyOptions = getViewOnlyOptions(getUserRole());
  const handleInputChange = (key: keyof typeof inputs, value: string) =>
    setInputs(prev => ({ ...prev, [key]: value }));

  const runSearch = () => {
    setParams(prev => ({
      ...prev,
      username: inputs.username || null,
      name: inputs.name || null,
      email: inputs.email || null,
      page: 1,
    }));
    setShouldFetchUserList(true);
  };

  const clearSearch = () => {
    setInputs({ username: '', name: '', email: '' });
    setParams(prev => ({
      ...prev,
      username: null,
      name: null,
      email: null,
      page: 1,
    }));
    setShouldFetchUserList(false);
  };

  const handleSortChange = (sortData: { order_by: string; order_type: Order }[]) => {
    setParams(prev => ({
      ...prev,
      order_by: sortData[0]?.order_by,
      order_type: sortData[0]?.order_type,
    }));
  };

  useEffect(() => {
    if (fetchUserList?.data) {
      setUserList(fetchUserList.data.data || []);
      setTotal(fetchUserList.data.total);
    } else {
      setUserList([]);
      setTotal(0);
    }
  }, [fetchUserList]);

  const getValidDisplay = useCallback((value: boolean) => {
    return (
      <div className="validTag">
        <span className={`${value ? 'success' : 'error'}`}>{value ? '有効' : '無効'}</span>
      </div>
    );
  }, []);

  const columns: CustomColumnType<User>[] = useMemo(
    () => [
      {
        title: 'No.',
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        // fixed: 'left',
        width: 80,

        render: (_: any, record, index: number) =>
          index +
          1 +
          (fetchUserList?.data?.per_page ?? 0) * ((fetchUserList?.data?.current_page ?? 1) - 1),
      },
      {
        title: 'ユーザーID',
        dataIndex: 'user_id',
        sortable: true,
        align: 'left',
        sortKey: 'username',
        // fixed: 'left',
        width: 118,
        render: (_, record) => record.username,
      },
      {
        title: '氏名（漢字）',
        dataIndex: 'kanji_name',
        sortable: true,
        sortKey: 'kanji_name',
        width: 118,
      },
      {
        title: '氏名（カナ）',
        dataIndex: 'kana_name',
        sortable: true,
        sortKey: 'kana_name',
        width: 119,
      },
      {
        title: '施術師のマスター',
        dataIndex: 'is_doctor',
        sortable: true,
        sortKey: 'is_doctor',
        width: 140,
        align: 'center',
        render: (_, { is_doctor }) => getValidDisplay(is_doctor),
      },
      {
        title: '予約システム',
        dataIndex: 'can_access_booking',
        sortable: true,
        sortKey: 'can_access_booking',
        width: 119,
        align: 'center',
        render: (_, { can_access_booking }) => getValidDisplay(can_access_booking),
      },
      {
        title: '電子カルテシステム',
        dataIndex: 'can_access_karute',
        sortable: true,
        sortKey: 'can_access_karute',
        width: 150,
        align: 'center',
        render: (_, { can_access_karute }) => getValidDisplay(can_access_karute),
      },
      {
        title: ' 権限タイプ',
        dataIndex: 'role_name',
        sortable: true,
        sortKey: 'manageable_type',
        width: 119,
        align: 'center',
        render: (_, { role }) => getRoleName(role as ROLE),
      },
      {
        title: '',
        key: 'action',
        align: 'center',
        width: 50,
        render: (_, { user_id, role }) => (
          <Dropdown
            menu={{
              items: [
                {
                  key: 'view',
                  label: (
                    <Flex
                      align="center"
                      gap={6}
                      onClick={() => {
                        redirectToMedixSync({
                          from: 'karute',
                          path: `dashboard/user/${user_id}`,
                          token: accessToken,
                        });
                      }}
                    >
                      <Icon name="eye" height={20} width={20} color="#667085" />
                      <span>プレビュー </span>
                    </Flex>
                  ),
                },
                {
                  key: 'edit',
                  disabled: viewOnlyOptions.some(option => option.value === role),
                  label: (
                    <Flex
                      align="center"
                      gap={6}
                      onClick={() => {
                        const disabled = viewOnlyOptions.some(option => option.value === role);
                        if (disabled) return;
                        redirectToMedixSync({
                          from: 'karute',
                          path: `edit/${user_id}`,
                          token: accessToken,
                        });
                      }}
                    >
                      <Icon name="edit" height={20} width={20} color="#667085" />
                      <span>編集</span>
                    </Flex>
                  ),
                },
                {
                  key: 'delete',
                  disabled: viewOnlyOptions.some(option => option.value === role),
                  label: (
                    <Flex
                      align="center"
                      gap={6}
                      onClick={() => {
                        // const disabled = viewOnlyOptions.some(option => option.value === role);
                        // if (disabled) return;
                        // show({
                        //   title: '’氏名（漢字）’を削除する',
                        //   content: (
                        //     <span>
                        //       この操作は元に戻せません。’患者名’を削除してもよろしいでしょうか？
                        //     </span>
                        //   ),
                        //   buttons: [
                        //     <Button
                        //       key="キャンセル"
                        //       customType={ButtonType.SECONDARY_COLOR}
                        //       onClick={hide}
                        //       customSize="lg"
                        //     >
                        //       キャンセル
                        //     </Button>,
                        //     <Button
                        //       key="削除"
                        //       customType={ButtonType.RED_PRIMARY}
                        //       onClick={() => {}}
                        //       customSize="lg"
                        //     >
                        //       削除
                        //     </Button>,
                        //   ],
                        //   width: '464px',
                        //   onClose: hide,
                        // });
                      }}
                      className={styles.deleteAction}
                      style={{
                        cursor: viewOnlyOptions.some(option => option.value === role)
                          ? 'not-allowed'
                          : 'pointer',
                      }}
                    >
                      <Icon name="trash" height={20} width={20} />
                      <span>削除</span>
                    </Flex>
                  ),
                },
              ],
            }}
            trigger={['hover']}
          >
            <MoreOutlined style={{ fontSize: 18, cursor: 'pointer' }} />
          </Dropdown>
        ),
      },
    ],
    [getRoleName, getValidDisplay, fetchUserList]
  );

  return (
    <>
      <Flex
        justify="space-between"
        align="center"
        style={{ overflowX: 'auto', paddingBottom: '4px' }}
        gap={'small'}
      >
        <Flex justify="flex-start" gap={8} align="center">
          <div>
            <AppInput
              placeholder="ユーザーID"
              size="small"
              style={{ width: 220 }}
              value={inputs.username}
              onChange={e => handleInputChange('username', e.target.value)}
              onPressEnter={runSearch}
            />
          </div>
          <div>
            <AppInput
              placeholder="氏名 (漢字）、カナ"
              size="small"
              style={{ width: 220 }}
              value={inputs.name}
              onChange={e => handleInputChange('name', e.target.value)}
              onPressEnter={runSearch}
            />
          </div>
        </Flex>
        <Flex justify="flex-end" gap={8} className={styles.buttonRow}>
          <Button
            customSize="md"
            customType={ButtonType.PRIMARY}
            onClick={runSearch}
            loading={loading}
          >
            検索
          </Button>
          <Button
            customSize="md"
            customType={ButtonType.SECONDARY_COLOR}
            onClick={clearSearch}
            disabled={loading}
          >
            <Icon name="retry" height={20} width={20} />
            クリア
          </Button>
        </Flex>
      </Flex>
      <div className={styles.tableWrapper}>
        <Flex justify="space-between" align="center" className={styles.filterWrapper} gap={'small'}>
          <Flex justify="flex-start" gap={8}>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '権限タイプ',
                  style: {
                    height: '32px',
                    width: '160px',
                  },
                  size: 'small',
                }}
                options={getRoleOptionsView(getUserRole())}
                value={params.manageable_type}
                onSelectChange={v =>
                  setParams(p => ({ ...p, manageable_type: v ?? null, page: 1 }))
                }
              />
            </div>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '施術師のマスター',
                  style: {
                    height: '32px',
                    width: '160px',
                  },

                  size: 'small',
                }}
                options={[
                  { value: 1, label: '有効' },
                  { value: 0, label: '無効' },
                ]}
                value={params.is_doctor}
                onSelectChange={v => setParams(p => ({ ...p, is_doctor: v ?? null, page: 1 }))}
              />
            </div>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '予約システム',
                  style: {
                    height: '32px',
                    width: '160px',
                  },

                  size: 'small',
                }}
                options={[
                  { value: 1, label: '有効' },
                  { value: 0, label: '無効' },
                ]}
                value={params.can_access_booking}
                onSelectChange={v =>
                  setParams(p => ({ ...p, can_access_booking: v ?? null, page: 1 }))
                }
              />
            </div>
            <div>
              <MultiSelect
                inputProps={{
                  placeholder: '電子カルテシステム',
                  style: {
                    height: '32px',
                    width: '160px',
                  },

                  size: 'small',
                }}
                options={[
                  { value: 1, label: '有効' },
                  { value: 0, label: '無効' },
                ]}
                value={params.can_access_karute}
                onSelectChange={v =>
                  setParams(p => ({ ...p, can_access_karute: v ?? null, page: 1 }))
                }
              />
            </div>
          </Flex>
          <div className={clsx(styles.recordCount, 'fs12-regular')}>
            人数: {fetchUserList?.data?.total_filtered}/{fetchUserList?.data?.total_items}人
          </div>
        </Flex>
        <Table
          rowKey="user_id"
          columns={columns}
          dataSource={userList}
          loading={loading}
          scroll={{ y: 500 }}
          onSortChange={handleSortChange}
          pagination={
            total > PAGINATION_THRESHOLD
              ? {
                  current: params.page || 1,
                  pageSize: params.limit || PAGINATION_THRESHOLD,
                  total: total,
                  showSizeChanger: false,
                  onChange: (page: number, pageSize: number) =>
                    setParams(prev => ({ ...prev, page, limit: pageSize })),
                }
              : false
          }
          total={fetchUserList?.data?.total_items}
        />
      </div>
    </>
  );
}
export default UserManagementContent;
