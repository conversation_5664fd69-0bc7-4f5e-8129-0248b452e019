import styles from './ListKarutePage.module.scss';
import { MoreOutlined } from '@ant-design/icons';
import Table, { CustomColumnType } from '@/components/common/core/CommonTable/Table';
import { KaruteItem } from '@/store/karute/type';
import { Dropdown, Flex } from 'antd';
import { DATE_FORMATS, PAGINATION_THRESHOLD, routerPaths } from '@/types/constants';
import { useListKarute } from './provider';
import { karuteStatusMap } from '@/types/constants/karute';
import { EKaruteStatus } from '@/types/enum';
import clsx from 'clsx';
import Icon from '@/components/common/core/Icon';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
// import DnDWrapper from '@/components/common/core/CommonTable/DnDWrapper';

const KaruteStatusBadge = ({ confirmed }: { confirmed: boolean }) => {
  const badgeClassName = clsx(styles.karute_status_badge, {
    [styles.confirmed_color]: confirmed,
    [styles.temporary_color]: !confirmed,
  });
  return (
    <p className={badgeClassName}>
      <span>{karuteStatusMap[confirmed ? EKaruteStatus.CONFIRMED : EKaruteStatus.TEMPORARY]}</span>
    </p>
  );
};
const getReceptionDateString = (record: KaruteItem) => {
  return (
    <div>
      <p className={styles.reception_date_cell}>
        {record?.first_submit_date
          ? dayjs(record?.first_submit_date, DATE_FORMATS.DDMMYYYYHHmm, true).format(
              DATE_FORMATS.DATE_HOUR_MINUTE
            )
          : ''}
      </p>
      {record?.latest_submit_date && record?.latest_submit_date != record?.first_submit_date && (
        <p className={styles.reception_date_cell}>
          最新更新:{' '}
          {record?.latest_submit_date
            ? dayjs(record?.latest_submit_date, DATE_FORMATS.DDMMYYYYHHmm, true).format(
                DATE_FORMATS.DATE_HOUR_MINUTE
              )
            : ''}
        </p>
      )}
    </div>
  );
};
export function ListKarutePageTable() {
  const { handlePageChange, handleSortChange, karutes, isFetching } = useListKarute();
  const navigate = useNavigate();
  const { clinicCd } = useParams();

  const toKaruteUpdate = (karute_id: number | string) => {
    return routerPaths.karuteManagement.karuteUpdate
      .replace(':clinicCd', String(clinicCd))
      .replace(':id', String(karute_id));
  };

  const columns: CustomColumnType<KaruteItem>[] = [
    {
      title: 'No',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      // fixed: 'left',
      render: (_: any, record: KaruteItem, index: number) =>
        index + 1 + (karutes?.per_page ?? 0) * ((karutes?.current_page ?? 1) - 1),
      width: 80,
    },
    {
      title: '患者番号',
      dataIndex: 'patient_cd',
      sortable: true,
      align: 'right',
      sortKey: 'patient_cd',
      width: 100,
    },
    {
      title: '氏名（漢字）',
      dataIndex: 'patient_name',
      sortable: true,
      sortKey: 'patient_name',
      width: 118,
    },
    {
      title: '氏名（カナ）',
      dataIndex: 'patient_kana',
      sortable: true,
      sortKey: 'patient_kana',
      width: 119,
    },
    {
      title: '活性問題数',
      dataIndex: 'mondais_count',
      key: 'mondais_count',
      align: 'right',
      width: 150,
      sortable: true,
      sortKey: 'mondais_count',
    },
    {
      title: '登録日時',
      dataIndex: 'submit_date',
      align: 'right',
      width: 170,
      render: (_: any, record: KaruteItem) => getReceptionDateString(record),
      sortable: true,
      sortKey: 'first_submit_date',
    },
    {
      title: '最終施術日',
      dataIndex: 'treatment_date',
      sortable: true,
      align: 'right',
      sortKey: 'latest_treatment_date',
      render: (_, record: KaruteItem) =>
        record?.latest_treatment_date
          ? dayjs(record?.latest_treatment_date, DATE_FORMATS.DDMMYYYYHHmm, true).format(
              DATE_FORMATS.DATE_HOUR_MINUTE
            )
          : '',
      width: 130,
    },

    {
      title: 'ステータス',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 120,
      render: (status: EKaruteStatus) => (
        <KaruteStatusBadge confirmed={status === EKaruteStatus.CONFIRMED} />
      ),
      sortable: true,
      sortKey: 'status',
    },
    {
      title: '',
      key: 'action',
      align: 'center',
      width: 50,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                label: '詳細・編集',
                key: '2',
                icon: <Icon name="edit" />,
                onClick: () => {
                  navigate(toKaruteUpdate(record.karute_id));
                  return;
                },
              },
            ],
          }}
          trigger={['hover']}
        >
          <MoreOutlined style={{ fontSize: '16px', cursor: 'pointer' }} />
        </Dropdown>
      ),
    },
  ];
  return (
    <div className={styles.list_karute_page_table_data}>
      <Flex align="center" justify="end" className={styles.table_header}>
        <span className={styles.total_count}>
          人数：{karutes?.total_filtered || 0}/{karutes?.total_items || 0}人
        </span>
      </Flex>
      <Table
        className={styles.table}
        rowKey={record => record.karute_id}
        columns={columns}
        dataSource={karutes?.data ?? []}
        pagination={
          karutes && karutes.total > PAGINATION_THRESHOLD
            ? {
                current: karutes?.current_page ?? 1,
                pageSize: karutes?.per_page ?? PAGINATION_THRESHOLD,
                showSizeChanger: false,
                total: karutes?.total ?? 0,
                onChange: handlePageChange,
              }
            : false
        }
        onSortChange={handleSortChange}
        scroll={{ y: karutes && karutes.total > PAGINATION_THRESHOLD ? 235 : 300 }}
        loading={isFetching}
        total={karutes?.total_items}
        rowClassName={() => styles.fixedRowHeight}
      />
    </div>
  );
}
