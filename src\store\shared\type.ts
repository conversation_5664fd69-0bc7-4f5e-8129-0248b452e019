import { DetailUserResponse } from '../user-karute/type';
import { ActiveCourses, Doctor } from '../visit/type';

export type ClinicService = {
  service_id: number;
  name: string;
};
export type ServiceActiveByVisit = {
  service_id: ClinicService['service_id'][];
};

export type SharedData = {
  services: ClinicService[];
  doctors: Doctor[];
  courses: ActiveCourses[];
  hasContractBooking: boolean;
  userInfo: DetailUserResponse | null;
};
