import { AppDatePicker } from '@/components/common/core/Datepicker';
import { AppFormItem } from '@/components/common/core/FormItem';
import Icon from '@/components/common/core/Icon';
import { AppInput, AppTextArea } from '@/components/common/core/Input';
import { AppRadio } from '@/components/common/core/Radio';
import { AppBlockHasHeader } from '@/components/layouts/HeaderBlock/HeaderBlock';
import { EGender } from '@/components/page-components/patient-management/create/FormCreate/type';
import { useAppSelector } from '@/config/redux/store';
import { useKanaAutoFill } from '@/hooks/useKanaAutoFill';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useUnsavedWarning } from '@/hooks/useUnsavedWarning';
import { useWarningDialog } from '@/hooks/useWarningDialog';
import { getUserInfo } from '@/store/auth/selectors';
import { useCheckDuplicatePatientMutation, useUpdatePatientMutation } from '@/store/patient/api';
import { CheckDuplicatePatientPayload, UpdatePatientPayload } from '@/store/patient/type';
import {
  DATE_FORMATS,
  ERROR_COMMON_MESSAGE,
  NOTICE_COMMON_MESSAGE,
  PLACEHOLDER_MESSAGE_DEFAULT,
  REGEX_KANA_NAME,
  REGEX_PHONE,
  STATUS_CODES,
} from '@/types/constants';
import { routerPaths } from '@/types/constants/routerPath';
import { showFlashNotice } from '@/utils/flashNotice';
import { clearSpace, formatDate, formatGender } from '@/utils/helper';
import { Divider, Flex, Form, Radio } from 'antd';
import dayjs from 'dayjs';
import { forwardRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import './FormUpdatePatient.modules.scss';
import { ERelationship, UpdatePatientSchema } from './type';
import Button from '@/components/common/core/Button';
import { ButtonType } from '@/types/enum';
import { usePostalCodeSearch } from '@/hooks/usePostalCodeSearch';
const { useForm } = Form;

interface IFormUpdatePatient {
  initialValues: UpdatePatientSchema;
}
const FormUpdatePatient = forwardRef<any, IFormUpdatePatient>((props, ref) => {
  const [form] = useForm<UpdatePatientSchema>();
  const params = useParams();

  useKanaAutoFill(form, 'name', 'kana', !props.initialValues?.patient_id);

  const navigate = useNavigate();
  const [updatePatientMutation, { isLoading }] = useUpdatePatientMutation();
  const [checkDuplicatePatient, { isLoading: isLoadingCheckDuplication }] =
    useCheckDuplicatePatientMutation();
  const { showWarningDialog } = useWarningDialog();

  const { getPath } = useRouterPath();
  const patientListPath = getPath(routerPaths.patientManagement.patientList);
  const user = useAppSelector(getUserInfo);

  const { handleValuesChange, submitHandler } = useUnsavedWarning(props.initialValues);
  const { searchPostalCode, isLoading: loadingPostal } = usePostalCodeSearch();

  const handleFinish = async (value: UpdatePatientSchema) => {
    if (isLoading) return;
    try {
      const newPatientEmail = value?.email?.trim();
      const newPatientCmt = value?.patient_cmt?.trim();
      const payload: { id: any; body: UpdatePatientPayload } = {
        id: Number(params.id),
        body: {
          kana: clearSpace(value.kana),
          name: clearSpace(value.name),
          birthday: dayjs(value.birthday).format(DATE_FORMATS.SUBMIT_DATE),
          gender: Number(value.gender),
          address1: clearSpace(value.address1),
          cellphone: value.cellphone?.trim() ?? '',
          post: value.post?.trim(),
          patient_cmt: newPatientCmt?.length ? newPatientCmt : undefined,
          email: newPatientEmail?.length ? newPatientEmail : undefined,
          address2: clearSpace(value.address2),
        },
      };
      await updatePatientMutation(payload)
        .unwrap()
        .then(res => {
          if (res.status === STATUS_CODES.INVALID_FIELD) {
            const fields = Object.entries<string[]>(res.data).map(([name, msgs]) => ({
              name,
              errors: msgs,
            }));
            form.setFields(fields as any);
            return;
          }
          if (res.status === STATUS_CODES.OK) {
            navigate(patientListPath);
            showFlashNotice({ type: 'success', message: NOTICE_COMMON_MESSAGE.UPDATED_PATIENT });
          } else {
            showFlashNotice({ type: 'error', message: res.message });
          }
        });
    } catch (error) {
      console.error(error);
    }
  };
  const handleClickSubmit = async (value: UpdatePatientSchema) => {
    if (isLoadingCheckDuplication) return;
    try {
      const payload: CheckDuplicatePatientPayload = {
        name: clearSpace(value.name),
        birthday: dayjs(value.birthday).format(DATE_FORMATS.SUBMIT_DATE),
        gender: Number(value.gender),
        clinic_id: user?.clinic?.clinic_id || null,
        patient_id: Number(params.id),
      };
      const res = await checkDuplicatePatient(payload).unwrap();
      if (!res.data) {
        handleFinish(value);
      } else {
        const content = `${clearSpace(value.name)}(${clearSpace(
          value.kana
        )})_${formatDate.toJapanDate(value.birthday)}_${formatGender(Number(value.gender))}`;
        showWarningDialog({
          message: `${content}と同じ患者が登録済みです。本当に登録してもよろしいでしょうか？`,
          onConfirm: () => {
            handleFinish(value);
          },
          confirmLabel: '登録',
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handlePostalCodeSearch = async (postalCode: string) => {
    if (postalCode.length < 7) return;
    const address = await searchPostalCode(postalCode)
    form.setFieldValue('address1', (address));
  }

  return (
    <Form
      ref={ref}
      form={form}
      layout="vertical"
      initialValues={props.initialValues}
      onValuesChange={handleValuesChange}
      onFinish={values => {
        submitHandler();
        handleClickSubmit(values);
      }}
      className="form-create-patient"
    >
      <AppBlockHasHeader
        icon={<Icon name="userFile" width={20} height={20} color="white" />}
        title="基本情報"
      >
        <Flex gap={24} flex={1} className="mb-4">
          <AppFormItem
            name="name"
            label="氏名 (漢字)"
            layout="vertical"
            required
            rules={[
              {
                required: true,
                whitespace: true,
                message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (漢字)'),
              },
            ]}
          >
            <AppInput maxLength={64} placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} />
          </AppFormItem>
          <AppFormItem
            name="kana"
            label="氏名 (カナ)"
            layout="vertical"
            required
            rules={[
              {
                required: true,
                whitespace: true,
                message: ERROR_COMMON_MESSAGE.REQUIRED('氏名 (カナ)'),
              },
              {
                pattern: new RegExp(REGEX_KANA_NAME),
                message: ERROR_COMMON_MESSAGE.ENTER_ONLY_KANA,
              },
            ]}
          >
            <AppInput maxLength={64} placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} />
          </AppFormItem>
        </Flex>
        <Flex gap={24} className="mb-4">
          <AppFormItem
            name="birthday"
            label="生年月日"
            layout="vertical"
            required
            rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('生年月日') }]}
          >
            <AppDatePicker
              size="small"
              disabledDate={current => current && current.isAfter(dayjs(), 'day')}
              style={{ height: '44px' }}
              format={DATE_FORMATS.DATE}
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.DATE_PICKER}
              inputReadOnly={false}
            />
          </AppFormItem>
          <AppFormItem
            name="gender"
            label="性別"
            layout="vertical"
            required
            rules={[{ required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('性別') }]}
          >
            <Radio.Group className="radio-button">
              <AppRadio value={EGender.MALE}>男性</AppRadio>
              <AppRadio value={EGender.FEMALE}>女性</AppRadio>
            </Radio.Group>
          </AppFormItem>
        </Flex>
        <Flex gap={24} className="mb-4">
          <AppFormItem
            name="post"
            label="郵便番号"
            layout="vertical"
            required
            rules={[
              { required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('郵便番号') },
              { min: 7, max: 7, message: ERROR_COMMON_MESSAGE.MAX_LENGTH_NUMBER(7) },
            ]}
          >
            <Flex gap={12}>
              <AppInput
                maxLength={7}
                placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
                type="number"
              />
              <Button disabled={loadingPostal} customType={ButtonType.SECONDARY_COLOR} customSize='lg' onClick={() => handlePostalCodeSearch(form.getFieldValue('post'))}>検索</Button>
            </Flex>

          </AppFormItem>
          <Flex gap={24} className="w-full">
            <AppFormItem
              name="address1"
              label="住所"
              layout="vertical"
              validateFirst
              required
              rules={[
                { required: true, message: ERROR_COMMON_MESSAGE.REQUIRED('住所') },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!getFieldValue('address2')) {
                      return Promise.reject(new Error(ERROR_COMMON_MESSAGE.REQUIRED('住所')));
                    }
                    if (
                      !/\d/.test(getFieldValue('address2')?.toString()) &&
                      !/\d/.test(value?.toString())
                    ) {
                      return Promise.reject(
                        new Error(ERROR_COMMON_MESSAGE.REQUIRED_NUMBER_IN_ADDRESS)
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <AppInput maxLength={100} placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} />
            </AppFormItem>
            <AppFormItem
              name="address2"
              className="address2"
              layout="vertical"
              rules={[{ required: true, message: '' }]}
            >
              <AppInput
                onChange={() => form.validateFields(['address1'])}
                maxLength={100}
                placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
              />
            </AppFormItem>
          </Flex>
        </Flex>
        <Flex gap={24} className="mb-4">
          <AppFormItem
            name="cellphone"
            label="電話番号"
            layout="vertical"
            validateFirst
            required
            rules={[
              {
                required: true,
                whitespace: true,
                message: ERROR_COMMON_MESSAGE.REQUIRED('電話番号'),
              },
              {
                validator: (_, v: string) => {
                  if (!v) return Promise.resolve();
                  if (v.length > 20) {
                    return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_HYPHEN));
                  }
                  if (!REGEX_PHONE.test(v)) {
                    return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MAX_LENGTH_HYPHEN));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <AppInput placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT} maxLength={20} />
          </AppFormItem>
          <AppFormItem
            name="email"
            label="メールアドレス"
            layout="vertical"
            validateFirst
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (
                    value &&
                    (getFieldValue('email')?.length < 6 || getFieldValue('email')?.length > 255)
                  ) {
                    return Promise.reject(new Error(ERROR_COMMON_MESSAGE.MIN_MAX_LENGTH(6, 254)));
                  }
                  return Promise.resolve();
                },
              }),
              { type: 'email', message: ERROR_COMMON_MESSAGE.EMAIL_INVALID },
            ]}
          >
            <AppInput
              minLength={6}
              maxLength={255}
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            />
          </AppFormItem>
        </Flex>
        <Flex className="mb-4">
          <AppFormItem name="patient_cmt" label="患者のメモ" layout="vertical">
            <AppTextArea maxLength={1000} rows={4} />
          </AppFormItem>
        </Flex>
        <Divider />
        <Flex gap={24} flex={1} className="mb-4">
          <AppFormItem name="zokugara" label="続柄" layout="vertical" required>
            <Radio.Group disabled className="radio-button">
              <AppRadio value={ERelationship.INDIVIDUAL}>本人</AppRadio>
              <AppRadio value={ERelationship.FAMILY}>家族</AppRadio>
              <AppRadio value={ERelationship.CUSTOM}>67退 (本人)</AppRadio>
            </Radio.Group>
          </AppFormItem>
        </Flex>
        <Flex gap={24} flex={1} className="mb-4">
          <AppFormItem
            name={['defaultInsurance', 'hihoken_name']}
            label="被保険者/組合人世帯主"
            layout="vertical"
            required
          >
            <AppInput
              disabled
              maxLength={64}
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            />
          </AppFormItem>
          <AppFormItem
            name={['defaultInsurance', 'hihoken_kana']}
            label="被保険者カタカナ"
            layout="vertical"
            required
          >
            <AppInput
              maxLength={64}
              disabled
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            />
          </AppFormItem>
        </Flex>
        <Flex gap={24} flex={1} className="mb-4">
          <AppFormItem
            name={['defaultInsurance', 'hihoken_post']}
            label="郵便番号"
            layout="vertical"
            required
          >
            <AppInput
              maxLength={64}
              disabled
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            />
          </AppFormItem>

          <AppFormItem
            name={['defaultInsurance', 'hihoken_address1']}
            label="住所"
            layout="vertical"
            required
          >
            <AppInput
              maxLength={64}
              disabled
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            />
          </AppFormItem>
        </Flex>
        <Flex gap={24} flex={1} className="mb-4">
          <AppFormItem
            name={['defaultInsurance', 'hihoken_tel']}
            label="電話番号"
            layout="vertical"
            required
          >
            <AppInput
              maxLength={64}
              disabled
              placeholder={PLACEHOLDER_MESSAGE_DEFAULT.TEXT_INPUT}
            />
          </AppFormItem>
        </Flex>
      </AppBlockHasHeader>
    </Form>
  );
});

export default FormUpdatePatient;
