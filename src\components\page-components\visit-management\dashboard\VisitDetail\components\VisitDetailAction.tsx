import Button from '@/components/common/core/Button';
import { Flex } from 'antd';
import { ButtonType } from '@/types/enum';
import { useVisitDetail } from '../provider';

export default function VisitDetailAction() {
  const { handleClickSubmitForm, navigateToDashboard, isSubmitting, isFetching } = useVisitDetail();

  const isLoading = isSubmitting || isFetching;

  return (
    <Flex gap="small">
      <Button customType={ButtonType.SECONDARY_COLOR} customSize="md" onClick={navigateToDashboard}>
        キャンセル
      </Button>
      <Button
        customType={ButtonType.PRIMARY}
        customSize="md"
        loading={isLoading}
        onClick={handleClickSubmitForm}
      >
        保存
      </Button>
      <Button
        customType={ButtonType.RED_PRIMARY}
        customSize="md"
        disabled={isLoading}
        onClick={() => {
          // TODO
          //  put delete mutation here
        }}
      >
        削除
      </Button>
    </Flex>
  );
}
