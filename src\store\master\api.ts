import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';
import { KaruteMasterField } from '@/types/enum';
import { APIResponse, ListResponse } from '@/types/interface';
import {
  ClinicCategoryWithReferences,
  ClinicReferenceCategory,
  ClinicReferenceSentence,
  CreateCategoryPayload,
  CreateSentencePayload,
  MasterCategoryPayload,
  UpdateCategoryPayload,
  UpdateSentencePayload,
} from './type';
import { MasterReference } from '../patient/type';

export const masterApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    ...medixKaruteBaseQueryApi.endpoints,
    getReferenceCategory: build.query<
      APIResponse<ClinicReferenceCategory[]>,
      {
        reference_cd: KaruteMasterField;
      }
    >({
      query: params => ({
        url: '/api/clinic-references/categories/get-by-reference-cd',
        method: 'GET',
        params,
      }),
    }),
    getReferenceMasterByCategory: build.query<
      APIResponse<ClinicReferenceSentence[]>,
      {
        reference_cd: KaruteMasterField;
        category_id: number;
      }
    >({
      query: params => ({
        url: `/api/clinic-references/sentences/get-by-category`,
        method: 'GET',
        params,
      }),
    }),
    getMasterCategories: build.query<
      APIResponse<ListResponse<ClinicCategoryWithReferences>>,
      MasterCategoryPayload
    >({
      query: params => ({
        url: '/api/clinic-references/categories',
        method: 'GET',
        params,
      }),
    }),
    updateMasterCategory: build.mutation<APIResponse<any>, UpdateCategoryPayload>({
      query: payload => ({
        url: `/api/clinic-references/categories/${payload.category_id}`,
        method: 'PUT',
        body: { name: payload.name, references: payload.references },
      }),
    }),
    createMasterCategory: build.mutation<APIResponse<any>, CreateCategoryPayload>({
      query: payload => ({
        url: '/api/clinic-references/categories',
        method: 'POST',
        body: payload,
      }),
    }),
    changeOrderCategory: build.mutation<APIResponse<any>, { category_id: number; order: number }>({
      query: payload => ({
        url: `/api/clinic-references/categories/${payload.category_id}/change-order`,
        method: 'PUT',
        body: { order: payload.order },
      }),
    }),
    deleteCategory: build.mutation<APIResponse<any>, { category_id: number }>({
      query: param => ({
        url: `/api/clinic-references/categories/${param.category_id}`,
        method: 'DELETE',
      }),
    }),
    getMasterReferences: build.query<
      APIResponse<MasterReference[]>,
      {
        type: 'medical_field';
      }
    >({
      query: params => ({
        url: '/api/references/get-by-type',
        method: 'GET',
        params,
      }),
    }),
    // Sentences
    getMasterSentences: build.query<
      APIResponse<ClinicReferenceSentence[]>,
      {
        reference_cd: string;
        category_id: number;
      }
    >({
      query: params => ({
        url: '/api/clinic-references/sentences/get-by-category',
        method: 'GET',
        params,
      }),
    }),
    updateMasterSentence: build.mutation<APIResponse<any>, UpdateSentencePayload>({
      query: payload => ({
        url: `/api/clinic-references/sentences/${payload.sentence_id}`,
        method: 'PUT',
        body: { content: payload.content },
      }),
    }),
    createMasterSentence: build.mutation<APIResponse<any>, CreateSentencePayload>({
      query: payload => ({
        url: '/api/clinic-references/sentences',
        method: 'POST',
        body: payload,
      }),
    }),
    changeOrderSentence: build.mutation<
      APIResponse<any>,
      {
        category_id: number;
        reference_id: number;
        order: number;
      }
    >({
      query: payload => ({
        url: `/api/clinic-references/sentences/${payload.category_id}/change-order`,
        method: 'PUT',
        body: {
          category_id: payload.category_id,
          reference_id: payload.reference_id,
          order: payload.order,
        },
      }),
    }),
    deleteSentence: build.mutation<APIResponse<any>, { sentence_id: number }>({
      query: param => ({
        url: `/api/clinic-references/sentences/${param.sentence_id}`,
        method: 'DELETE',
      }),
    }),
  }),
});
export const {
  useLazyGetReferenceCategoryQuery,
  useLazyGetReferenceMasterByCategoryQuery,
  useLazyGetMasterCategoriesQuery,
  useUpdateMasterCategoryMutation,
  useCreateMasterCategoryMutation,
  useChangeOrderCategoryMutation,
  useDeleteCategoryMutation,
  useGetMasterReferencesQuery,
  useLazyGetMasterSentencesQuery,
  useUpdateMasterSentenceMutation,
  useCreateMasterSentenceMutation,
  useChangeOrderSentenceMutation,
  useDeleteSentenceMutation,
} = masterApi;
