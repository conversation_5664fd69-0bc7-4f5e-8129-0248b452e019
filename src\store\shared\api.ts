import { medixKaruteBaseQueryApi } from '@/config/redux/base-query-api';
import { PatientItem } from './../patient/type';

import { APIResponse } from '@/types/interface';
import { Clinic } from '@/types/interface/User';
import { Doctor } from '../visit/type';
import { ClinicService, ServiceActiveByVisit } from './type';
import { DetailUserResponse } from '../user-karute/type';

export const sharedApi = medixKaruteBaseQueryApi.injectEndpoints({
  endpoints: build => ({
    getClinics: build.query<APIResponse<Clinic[]>, void>({
      query: () => ({ url: '/api/clinics/manager-by-me' }),
    }),
    getServicesByClinicId: build.query<APIResponse<ClinicService[]>, unknown>({
      query: () => ({
        url: `/api/services`,
        method: 'GET',
      }),
    }),
    getDoctors: build.query<APIResponse<Doctor[]>, unknown>({
      query: (args: unknown) => ({
        url: 'api/doctors',
        method: 'get',
        ...(typeof args === 'object' && args !== null ? { params: args } : {}),
      }),
    }),
    getPatientServiceActive: build.query<
      APIResponse<ServiceActiveByVisit>,
      Pick<PatientItem, 'patient_id'>
    >({
      query: ({ patient_id }: Pick<PatientItem, 'patient_id'>) => ({
        url: `/api/patients/${patient_id}/service-active-by-visit-date`,
        method: 'GET',
      }),
    }),
    checkHasContractBooking: build.query<APIResponse<Record<'has_contract', boolean>>, unknown>({
      query: () => ({
        url: '/api/clinics/has-contract-booking',
        method: 'GET',
      }),
    }),
    getUserInfo: build.query<APIResponse<DetailUserResponse>, unknown>({
      query: () => {
        return {
          url: `/api/user-info`,
          method: 'GET',
        };
      },
    }),
  }),
  overrideExisting: false,
});

export const {
  useLazyGetClinicsQuery,
  useLazyGetServicesByClinicIdQuery,
  useLazyGetDoctorsQuery,
  useLazyGetPatientServiceActiveQuery,
  useLazyGetUserInfoQuery,
} = sharedApi;
