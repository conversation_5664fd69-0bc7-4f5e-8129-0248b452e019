import dayjs from 'dayjs';

const RAW_FALSY = new Set<any>([null, undefined, '', 0, false]);
const RAW_FALSY_EMPTY = new Set<any>([null, undefined, '']);

const isFalsyLike = (v: any): boolean => {
  if (RAW_FALSY.has(v)) return true;
  if (Array.isArray(v) && v.length === 0) return true;
  return false;
};

const isFalsyLikeEmpty = (v: any): boolean => {
  if (RAW_FALSY_EMPTY.has(v)) return true;
  if (Array.isArray(v) && v.length === 0) return true;
  return false;
};

const isPlainObject = (x: any): x is Record<string, any> =>
  x !== null &&
  typeof x === 'object' &&
  !Array.isArray(x) &&
  !dayjs.isDayjs(x) &&
  !(x instanceof Date);

export const deepCompare = (a: any, b: any): boolean => {
  if (isFalsyLike(a) && isFalsyLike(b)) return true;

  if (dayjs.isDayjs(a) && dayjs.isDayjs(b)) return a.valueOf() === b.valueOf();
  if (a instanceof Date && b instanceof Date) return a.getTime() === b.getTime();

  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepCompare(a[i], b[i])) return false;
    }
    return true;
  }

  if (isPlainObject(a) && isPlainObject(b)) {
    const keys = new Set([...Object.keys(a), ...Object.keys(b)]);
    for (const key of keys) {
      if (!deepCompare(a[key], b[key])) return false;
    }
    return true;
  }

  return a === b;
};

export const deepCompareEmpty = (a: any, b: any): boolean => {
  if (isFalsyLikeEmpty(a) && isFalsyLikeEmpty(b)) return true;

  if (dayjs.isDayjs(a) && dayjs.isDayjs(b)) return a.valueOf() === b.valueOf();
  if (a instanceof Date && b instanceof Date) return a.getTime() === b.getTime();

  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepCompareEmpty(a[i], b[i])) return false;
    }
    return true;
  }

  if (isPlainObject(a) && isPlainObject(b)) {
    const keys = new Set([...Object.keys(a), ...Object.keys(b)]);
    for (const key of keys) {
      if (!deepCompareEmpty(a[key], b[key])) return false;
    }
    return true;
  }

  return a === b;
};
