.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1100;
  width: 100%;
  height: $header-height;
  background-color: $brand-900;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  color: white;
  overflow: visible;

  .logoArea {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .userArea {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    position: relative;

    .icon {
      margin-top: 2px;
    }
  }
}
.customDropdown {
  z-index: 1001;
  top: $header-height !important;
  li {
    min-width: 120px;
  }
  .menuItem {
    display: flex;
    align-items: center;
    gap: 8px;
    color: $gray-800;
    .menuIcon {
      color: $gray-500;
    }
  }
}
