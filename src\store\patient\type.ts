import { EGender } from '@/components/page-components/patient-management/create/FormCreate/type';
import { ESort } from '@/types/enum';
type PatientDefaultInsurance = {
  patient_insurance_id: number;
  hihoken_name: string;
  hihoken_kana: string;
  hihoken_post: string;
  hihoken_address1: string;
  hihoken_tel: string;
  hoken_type: number;
  shoukan_flg: number;
  hoken_no: string;
  hokensya_type: number;
  hoken_name: string;
  futan_ritsu: number;
  futan_shubetsu: number;
  hoken_kigo: string;
  hoken_bango: string;
  hoken_shikaku_date: string; // ISO date string
  hoken_yuko_date: string; // ISO date string
  kohi_type: number;
  kohi_no: string;
  kohi_jukyusya_no: string;
  kohi_ritsu: number;
  kohi_start_date: string; // ISO date string
  kohi_end_date: string; // ISO date string
  kohi_jyosei_flg: number;
  is_default: number;
  office_name: string;
  office_post: string;
  office_address: string;
};

export interface PatientItem {
  patient_id: number;
  clinic_id: number;
  patient_cd: string;
  name: string;
  kana: string;
  gender: EGender;
  birthday: string;
  post: string;
  email: string;
  address1: string;
  address2: string;
  cellphone: string;
  latest_intake_date?: string;
  hoken_type?: number; //insurance
  zokugara: number | null; //relationship
  patient_cmt: string | null;
  defaultInsurance?: PatientDefaultInsurance;
  line_id?: string;
  age?: number;
  remarks?: string;
}

export interface PatientsParams extends OrderItem {
  search?: string;
  type_search?: string;
  intake_case_1?: IsInRange;
  intake_date_from_1?: string;
  intake_date_to_1?: string;
  intake_case_2?: IsInRange;
  intake_date_from_2?: string;
  intake_date_to_2?: string;
  insured_type?: string;
  limit: number;
  page: number;
}
export enum IsInRange {
  TRUE = 'in',
  FALSE = 'not_in',
}
export interface OrderItem {
  order_by?: string;
  order_type?: Order;
}

export type Order = ESort.ASC | ESort.DESC | null;

export interface CreatePatientPayload {
  name: string;
  kana: string;
  birthday: string;
  gender: number;
  post: string;
  address1: string;
  address2: string;
  cellphone: string;
  email: string;
  patient_cmt: string;
}
export interface UpdatePatientPayload {
  name: string;
  kana: string;
  birthday: string;
  gender: EGender;
  post: string;
  address1: string;
  address2: string;
  cellphone: string;
  email?: string;
  patient_cmt?: string;
}
export interface CheckDuplicatePatientPayload {
  name: string;
  kana?: string;
  birthday: string;
  gender: number;
  clinic_id: number | null;
  patient_id?: number | null;
}

export interface MasterReference {
  reference_id: number;
  reference_cd: string;
  name: string;
  type: 'medical_field';
}
