// example:
// <TextAreaInput
//   label="<PERSON><PERSON> tả"
//   placeholder="Nhập mô tả của bạn"
//   message="<PERSON><PERSON><PERSON> là thông báo"
//   validationStatus={{ error: false }}
//   rows={6}
//   width="400px"
// />

import React, { useState } from 'react';
import { Input } from 'antd';
import classNames from 'classnames';
import styles from './TextAreaInput.module.scss';
import { TextAreaInputProps } from '@/types/interface';
import clsx from 'clsx';

const TextAreaInput: React.FC<TextAreaInputProps> = ({
  label,
  message,
  validationStatus = { error: false },
  className,
  width = '320px',
  rows = 4,
  ...rest
}) => {
  const [focused, setFocused] = useState(false);
  const [hovered, setHovered] = useState(false);
  const hasValue = !!rest.value;

  const wrapperClass = classNames(styles.wrapper, className, {
    [styles.error]: validationStatus.error,
  });

  const inputWrapperClass = classNames(styles.inputWrapper, {
    [styles.focused]: focused,
    [styles.hovered]: hovered,
    [styles.filled]: hasValue,
    [styles.disabled]: rest.disabled,
  });

  return (
    <div className={wrapperClass}>
      {label && <label className={clsx('fs14-medium', styles.label)}>{label}</label>}

      <div className={inputWrapperClass}>
        <Input.TextArea
          {...rest}
          onFocus={e => {
            setFocused(true);
            rest.onFocus?.(e);
          }}
          onBlur={e => {
            setFocused(false);
            rest.onBlur?.(e);
          }}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
          rows={rows}
          style={{
            width,
          }}
        />
      </div>

      {message && <div className={clsx('fs14-regular', styles.message)}>{message}</div>}
    </div>
  );
};

export default TextAreaInput;
