import Icon from '@/components/common/core/Icon';
import { useWatch } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { Flex } from 'antd';
import styles from './MasterModal.module.scss';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import clsx from 'clsx';
import { CagoryFormItem } from './CategoryForm';

type Props = {
  record: CagoryFormItem;
  isEdit: boolean;
  handleCancelRecord: () => void;
  handleConfirmEdit: () => void;
  handleOpenEdit: () => void;
  handleDeleteRecord?: () => void;
};

export function MasterActionBtn({
  record,
  isEdit,
  handleCancelRecord,
  handleConfirmEdit,
  handleOpenEdit,
  handleDeleteRecord,
}: Props) {
  const [isAllowConfirmed, setIsAllowConfirmed] = useState(true);
  const [isAllowCancel, setIsAllowCancel] = useState(true);
  const form = useFormInstance<Record<string, CagoryFormItem>>();

  const currentData = useWatch(record.rowId, form);

  useEffect(() => {
    const isFormDirty = form.isFieldTouched(record.rowId);

    if (record?.isInit) {
      setIsAllowCancel(false);
      setIsAllowConfirmed(true);
    } else {
      setIsAllowCancel(true);
      setIsAllowConfirmed(isFormDirty);
    }
  }, [currentData, record.rowId, record.isInit, form]);
  return (
    <Flex align="center" gap={8} justify="end">
      {isEdit && (
        <>
          <button
            className={clsx(styles.master_modal_action_btn, {
              [styles.disabled]: !isAllowCancel,
              // || isDisabled
            })}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (!isAllowCancel) return;
              handleCancelRecord();
            }}
          >
            <Icon name="close" color="red" height={16} width={16} />
          </button>
          <button
            className={clsx(styles.master_modal_action_btn, {
              [styles.disabled]: !isAllowConfirmed,
              // || isDisabled,
            })}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (!isAllowConfirmed) return;
              handleConfirmEdit();
            }}
          >
            <Icon name="check" color="green" height={16} width={16} />
          </button>
        </>
      )}
      {!isEdit && (
        <>
          <button
            className={clsx(
              styles.master_modal_action_btn
              // { [styles.disabled]: isDisabled }
            )}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              handleOpenEdit();
            }}
          >
            <Icon name="edit" color="gray" height={16} width={16} />
          </button>
          {/* {mode === 'option' && ( */}
          <button
            className={clsx(
              styles.master_modal_action_btn
              // { [styles.disabled]: !canDelete || isDisabled }
            )}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              handleDeleteRecord?.();
            }}
          >
            <Icon name="trash" color="red" height={16} width={16} />
          </button>
          {/* )} */}
        </>
      )}
    </Flex>
  );
}
