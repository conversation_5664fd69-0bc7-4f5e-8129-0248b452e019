// B104_保険の警告
import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { useModalConfirm } from '@/components/provider/ConfirmModalProvider';
import { NOTICE_COMMON_MESSAGE } from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { Flex } from 'antd';
import React from 'react';

export default function useInsuranceWarningModal() {
  const { show, hide } = useModalConfirm();
  const showInsuranceWarning = (onConfirm: () => void) => {
    show({
      title: '',
      content: (
        <Flex align="center" justify="space-between" gap={16}>
          <Icon name="warning" native className="mt-2" />
          <span>{NOTICE_COMMON_MESSAGE.WARNING_HOKEN_MSG}</span>
        </Flex>
      ),
      buttons: [
        <Button
          key="キャンセル"
          customType={ButtonType.SECONDARY_COLOR}
          onClick={hide}
          customSize="lg"
        >
          キャンセル
        </Button>,
        <Button
          key="登録"
          customType={ButtonType.PRIMARY}
          onClick={() => {
            hide();
            onConfirm();
          }}
          customSize="lg"
        >
          続行する
        </Button>,
      ],
      width: '464px',
      onClose: hide,
    });
  };
  return showInsuranceWarning;
}
