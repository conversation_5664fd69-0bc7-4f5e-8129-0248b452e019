import { AppBreadcrumb } from '@/components/common/layout/Breadcrumb';
import PageLayout from '@/components/layouts/PageLayout';
import { routerPaths } from '@/types/constants';
import { useRef } from 'react';
import UserCreateAction from './components/UserCreateAction/UserCreateAction';
import FormCreateUser from './components/FormCreateUser/FormCreateUser';

function CreateUserPage() {
  const formRef = useRef<any>(null);
  return (
    <PageLayout
      title="ユーザー登録"
      breadcrumb={
        <AppBreadcrumb
          items={[
            { title: <a href={routerPaths.dashboard}>ユーザー一覧</a> },
            { title: 'ユーザー登録' },
          ]}
        />
      }
      headerRight={<UserCreateAction formRef={formRef} mode="create" />}
    >
      <FormCreateUser ref={formRef} mode="create" />
    </PageLayout>
  );
}

export default CreateUserPage;
