.hugged_tabs {
  display: flex;
  align-items: center;
  .tabs_options::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  .tabs_options::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: $gray-300;
  }
  .tabs_options {
    display: flex;
    border: 1px solid $gray-200;
    border-radius: 8px;
    background-color: var(--white);
    overflow: auto;

    .tab_option {
      padding: 5px 14px;
      cursor: pointer;
      color: $brand-900;
      transition: all 0.3s;

      //fs14-medium
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: 0;

      &.active {
        background-color: $brand-700;
        color: var(--white);

        //fs14-bold
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        letter-spacing: 0;
      }
    }
  }
}

.fullsize_tabs {
  display: flex;
  align-items: center;
  .tabs_options {
    background-color: var(--white);
    display: flex;
    width: 100%;
    // border: 1px solid $gray-200;
    border-radius: 7px;
    overflow: hidden;
    .tab_option {
      flex: 1;
      padding: 8px 14px;
      cursor: pointer;
      color: $brand-900;
      border-right: 1px solid $gray-200;
      transition: all 0.3s;
      text-align: center;
      &.active {
        background-color: $brand-700;
        color: var(--white);
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.paging_hugged_tabs {
  position: relative;
  margin-right: 10px;
  margin-left: 10px;
  .paging_controls {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .tabs_container {
    flex: 1;
    overflow-x: hidden;
    position: relative;
    width: 100%;
    border-radius: 8px;
    border: 1px solid $gray-200;
  }

  .tabs_options {
    display: flex;
    width: max-content;
    transition: transform 0.3s ease;
    border: none;
  }

  .paging_button {
    background: transparent;
    border: none;
    cursor: pointer;
    flex-shrink: 0;
    padding: 5px;
    color: $brand-600;
    border-radius: 100%;
    background-color: $brand-800;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    &:disabled {
      cursor: not-allowed;
      background-color: $gray-300;
    }

    &:hover:not(:disabled) {
      color: #2c4250;
    }
  }
  .prev_button {
    left: -10px;
    z-index: 50;
  }
  .next_button {
    right: -10px;
    // top: 5px;
  }
}
