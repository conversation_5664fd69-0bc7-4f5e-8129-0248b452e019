// K115_任意部位の情報
import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import Image from '@/components/common/core/Image';
import { ButtonType } from '@/types/enum';
import { Modal } from 'antd';
import clsx from 'clsx';
import styles from './OptionalPartInformationModal.module.scss';

interface InjuryNameModalProps {
  open: boolean;
  onCancel: () => void;
}

const OptionalPartInformationModal: React.FC<InjuryNameModalProps> = ({ open, onCancel }) => {
  return (
    <Modal
      title="印刷時のご注意"
      open={open}
      width="908px"
      footer={null}
      onCancel={onCancel}
      centered
      destroyOnClose
      maskClosable={false}
      className={styles.modal}
    >
      <div className={styles.modalContent}>
        <div className={clsx(styles.title, 'fs18-medium')}>任意部位の設定に関する注意事項</div>
        <div className={clsx(styles.content, 'fs14-regular')}>
          <p>
            自賠労災施術入力に限り、レセコンに登録されていない負傷部位（任意部位）の設定が可能です。
          </p>
          <p>
            負傷部位を選択する場合は「<span className={styles.highlight}>同等の負傷名</span>
            」を選択してから「任意部位」を直接編集します。
          </p>
          <p>
            不適当な負傷部位選択後に任意部位の編集をした場合、「
            <span className={styles.highlight}>金額に影響を及ぼす可能性</span>
            」がございますのでご注意ください。
          </p>
          <p>
            ※「同等の負傷名」とは、レセコンに登録されている負傷名の中で最も近い負傷名のことです。
          </p>
        </div>
        <div className={styles.example}>
          <span className={clsx(styles.exampleTitle, 'fs18-medium')}>任意部位の入力例</span>
          <span className={clsx(styles.exampleContent, 'fs14-regular')}>
            負傷部位：「右上腕骨骨幹部骨折」の登録
          </span>

          <ol className={clsx(styles.stepList, 'fs14-regular')}>
            <li className={styles.stepItem}>
              負傷部位
              <span className={styles.stepNum}>①</span>
              は同等負傷部位の「右上腕骨骨折」を選択します。
              <div className={styles.tagRow}>
                <span className={styles.tag}>「骨折」</span>
                <span className={styles.tag}>「上腕骨」</span>
                <span className={styles.tag}>「右」</span>
              </div>
            </li>

            <li className={styles.stepItem}>
              任意部位欄
              <span className={styles.stepNum}>②</span>
              は編集が可能です
              <div className={styles.tagRow}>
                <span className={clsx(styles.tag, 'mr-1')}>「右上腕骨骨幹部骨折」</span>
                を消して「右上腕骨骨幹部骨折」に書き換えます。
              </div>
            </li>
          </ol>
          <Image name="injuryNameInfo" />
          <div className={styles.warning}>
            <div>
              <Icon name="warning24x24Red" native />
            </div>
            <div>
              <span className={clsx(styles.warningTitle, 'fs14-medium')}>ご注意ください</span>
              <div className={clsx(styles.warningContent, 'fs12-regular')}>
                <p>
                  負傷部位は、必ず「<span className={styles.highlight}>同等の負傷部位</span>
                  」で入力をします。
                </p>
                <p>
                  「捻挫」「腰部」を選択した場合でも任意部位欄で「右上腕骨骨幹部骨折」に書き換えることが可能です。しかし、このような入力をした場合「捻挫」の金額で計算されますので請求金額に影響します。
                </p>
                <p>入力の際は、お気をつけください。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={clsx('mt-4', styles.footer)}>
        <Button
          customType={ButtonType.PRIMARY}
          customSize="lg"
          onClick={onCancel}
          style={{ width: '120px' }}
        >
          確認
        </Button>
      </div>
    </Modal>
  );
};

export default OptionalPartInformationModal;
