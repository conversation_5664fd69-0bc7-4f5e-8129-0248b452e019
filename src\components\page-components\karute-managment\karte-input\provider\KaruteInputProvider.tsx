import { DiseaseType, SchemaImage, SchemaType } from '@/store/schema/type';
import { UserItem } from '@/store/user-sync/type';
import {
  FE_DEFINED_META_KEYS,
  routerPaths,
  SPLIT_SCREEN_DEFAULT_RATIO,
  STATUS_CODES,
} from '@/types/constants';
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

type DrawnImagesState = {
  [schemaType in SchemaType]?: {
    [id: string]: {
      [diseaseId in DiseaseType]?: SchemaImage[];
    };
  };
};

import { useAppDispatch, useAppSelector } from '@/config/redux/store';
import { setPatientInfo } from '@/store/karute';
import { useGetKaruteDetailQuery } from '@/store/karute/api';
import { KaruteDetailItem } from '@/store/karute/type';
import { useLazyGetUserInfoQuery } from '@/store/shared/api';
import { ClinicService } from '@/store/shared/type';
import { EDatabaseUpsertMode, EKaruteUpsertType } from '@/types/enum';
import { isSameBasePath } from '@/utils';

interface KaruteInputContextType {
  //for patient basic info
  karuteDetailData?: KaruteDetailItem | null;
  doctorList: UserItem[];
  isInitalLoading: boolean;
  openSchema: (
    schemaType: SchemaType,
    disease: DiseaseType,
    disease_base_id: number | string,
    id?: number,
    focusImageId?: number | string
  ) => void;

  //submit schema
  handleSubmitSchema: (
    schemaType: SchemaType,
    disease: DiseaseType,
    data: SchemaImage[] | null
  ) => void;

  //drawn images
  drawnImages: DrawnImagesState;
  setDrawnImages: (value: React.SetStateAction<DrawnImagesState>) => void;
  services: ClinicService[];
  activeTrauma: string | null;
  setActiveTrauma: React.Dispatch<React.SetStateAction<string | null>>;
  openUpsertModal: (type: EKaruteUpsertType, mode: string) => void;
  ignoreSchemaNav: (from: string, to: string) => boolean;
  lowerHorizontalSizes: number[];
  lowerVerticalSizes: number[];
  mainVerticalSizes: number[];
  setMainVerticalSizes: React.Dispatch<React.SetStateAction<number[]>>;
  setLowerHorizontalSizes: React.Dispatch<React.SetStateAction<number[]>>;
  setLowerVerticalSizes: React.Dispatch<React.SetStateAction<number[]>>;
}
const KaruteInputContext = createContext<KaruteInputContextType>({
  doctorList: [],
  isInitalLoading: false,
  openSchema: () => {},
  handleSubmitSchema: () => {},
  drawnImages: {},
  setDrawnImages: () => {},
  services: [],
  activeTrauma: null,
  setActiveTrauma: () => {},
  openUpsertModal: () => {},
  ignoreSchemaNav: () => false,
  lowerHorizontalSizes: SPLIT_SCREEN_DEFAULT_RATIO.LOWER_HORIZONTAL_SIZES,
  lowerVerticalSizes: SPLIT_SCREEN_DEFAULT_RATIO.LOWER_VERTICAL_SIZES,
  mainVerticalSizes: SPLIT_SCREEN_DEFAULT_RATIO.MAIN_VERTICAL_SIZES,
  setMainVerticalSizes: () => {},
  setLowerHorizontalSizes: () => {},
  setLowerVerticalSizes: () => {},
});

export const KaruteInputProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [lowerHorizontalSizes, setLowerHorizontalSizes] = useState(
    SPLIT_SCREEN_DEFAULT_RATIO.LOWER_HORIZONTAL_SIZES
  );
  const [lowerVerticalSizes, setLowerVerticalSizes] = useState(
    SPLIT_SCREEN_DEFAULT_RATIO.LOWER_VERTICAL_SIZES
  );
  const [mainVerticalSizes, setMainVerticalSizes] = useState(
    SPLIT_SCREEN_DEFAULT_RATIO.MAIN_VERTICAL_SIZES
  );

  const { clinicCd, id: karuteId, diseaseBaseId, serviceId } = useParams();

  const [drawnImages, setDrawnImages] = useState<DrawnImagesState>({});
  const [activeTrauma, setActiveTrauma] = useState<string | null>(null);

  const {
    doctors,
    services,
    isLoading: isAppSelectedLoading,
  } = useAppSelector(state => state.sharedData);
  const { data: fetchedKaruteDetailData, isLoading: isLoadingKaruteDetail } =
    useGetKaruteDetailQuery(Number(karuteId), {
      skip: !karuteId,
    });
  const [getUserInfo] = useLazyGetUserInfoQuery();
  useEffect(() => {
    if (fetchedKaruteDetailData) {
      if (fetchedKaruteDetailData?.data) {
        const {
          patient_id,
          patient_cd,
          patient_name,
          patient_kana,
          patient_cmt,
          birthday,
          age,
          line_id,
          email,
        } = fetchedKaruteDetailData.data;
        dispatch(
          setPatientInfo({
            patient_id,
            patient_cd,
            name: patient_name,
            kana: patient_kana,
            patient_cmt: patient_cmt,
            birthday,
            age: age,
            line_id: line_id,
            email: email,
          })
        );
      }
    }
  }, [fetchedKaruteDetailData, diseaseBaseId, karuteId, serviceId]);

  const openSchema = useCallback(
    (
      schemaType: SchemaType,
      disease: DiseaseType,
      disease_base_id: number | string,
      id?: number,
      focusImageId?: number | string
    ) => {
      if (!clinicCd || !karuteId) return;
      const { pathname, search } = window.location;
      const schemaSeg = routerPaths.karuteManagement.schemaSegment
        .replace(':schemaType', String(schemaType))
        .replace(':disease', String(disease))
        .replace(':diseaseBaseId', String(disease_base_id));

      if (pathname.endsWith(schemaSeg) && (!focusImageId || search === `?focus=${focusImageId}`)) {
        return;
      }

      const windowPath = window.location.pathname;
      const path = routerPaths.karuteManagement.schemaSegment
        .replace(':schemaType', schemaType)
        .replace(':disease', String(disease))
        .replace(':diseaseBaseId', String(disease_base_id));

      const makeUrl = (prefix: string) =>
        focusImageId ? `${prefix}${path}?focus=${focusImageId}` : `${prefix}${path}`;
      let fullPath = '';
      if (
        windowPath.includes(EKaruteUpsertType.DATABASE) ||
        windowPath.includes(EKaruteUpsertType.KEIKA)
      ) {
        fullPath = makeUrl(windowPath);
      }
      if (!windowPath.includes(EKaruteUpsertType.KEIKA) && schemaType === SchemaType.KEIKA) {
        fullPath = makeUrl(`${windowPath}/${EKaruteUpsertType.KEIKA}/${id}`);
      }
      if (!windowPath.includes(EKaruteUpsertType.DATABASE) && schemaType === SchemaType.DATABASE) {
        fullPath = makeUrl(
          `${windowPath}/${EKaruteUpsertType.DATABASE}/${EDatabaseUpsertMode.UPSERT}`
        );
      }
      navigate(fullPath, { replace: true });
    },
    [clinicCd, navigate]
  );

  const openUpsertModal = (type: EKaruteUpsertType, mode: string) => {
    if (!clinicCd || !karuteId || !serviceId) return;
    const base = `/${clinicCd}/karute-management/${karuteId}/${serviceId}`;
    navigate(`${base}/${type}/${mode}`, { replace: true });
  };

  const handleSubmitSchema = useCallback(
    (schemaType: SchemaType, disease: DiseaseType, data: SchemaImage[] | null) => {
      const id = String(diseaseBaseId);
      setDrawnImages(prev => {
        const prevForSchema = prev[schemaType] ?? {};
        const prevForId = prevForSchema[id] ?? {};

        return {
          ...prev,
          [schemaType]: {
            ...prevForSchema,
            [id]: {
              ...prevForId,
              [disease]: data ?? [],
            },
          },
        };
      });
    },
    [diseaseBaseId]
  );

  const ignoreSchemaNav = (from: string, to: string) => isSameBasePath(from, to, ['schema']);

  // Effect
  const isInitalLoading = useMemo(() => {
    return isLoadingKaruteDetail || isAppSelectedLoading;
  }, [isLoadingKaruteDetail, isAppSelectedLoading]);
  useEffect(() => {
    getUserInfo({})
      .unwrap()
      .then(res => {
        if (res.status === STATUS_CODES.OK) {
          const initialLowerHorizontalSizes = res.data?.meta?.[
            FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.MAIN
          ] as string[];
          const initialLowerVerticalSizes = res.data?.meta?.[
            FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.RIGHT
          ] as string[];
          const initialMainVerticalSizes = res.data?.meta?.[
            FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.MAIN
          ] as string[];
          if (initialLowerHorizontalSizes) {
            setLowerHorizontalSizes([
              Number(initialLowerHorizontalSizes[0]),
              Number(initialLowerHorizontalSizes[1]),
            ]);
          }
          if (initialLowerVerticalSizes) {
            setLowerVerticalSizes([
              Number(initialLowerVerticalSizes[0]),
              Number(initialLowerVerticalSizes[1]),
            ]);
          }
          if (initialMainVerticalSizes) {
            setMainVerticalSizes([
              Number(initialMainVerticalSizes[0]),
              Number(initialMainVerticalSizes[1]),
            ]);
          }
        } else {
          console.error('⚠️ Fetch initial user info failed');
        }
      });
  }, [getUserInfo]);
  return (
    <KaruteInputContext.Provider
      value={{
        doctorList: doctors ?? [],
        isInitalLoading: isInitalLoading || false,
        openSchema,
        handleSubmitSchema,
        drawnImages,
        setDrawnImages,
        services: services ?? [],
        activeTrauma,
        setActiveTrauma,
        karuteDetailData: fetchedKaruteDetailData?.data,
        openUpsertModal,
        ignoreSchemaNav,
        lowerHorizontalSizes,
        lowerVerticalSizes,
        mainVerticalSizes,
        setMainVerticalSizes,
        setLowerHorizontalSizes,
        setLowerVerticalSizes,
      }}
    >
      {children}
    </KaruteInputContext.Provider>
  );
};

export const useKaruteInput = () => useContext(KaruteInputContext);
