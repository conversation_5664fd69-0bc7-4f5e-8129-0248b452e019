import Button from '@/components/common/core/Button';
import Icon from '@/components/common/core/Icon';
import { useAppSelector } from '@/config/redux/store';
import { useRouterPath } from '@/hooks/useRouterPath';
import { useSubmitKaruteMutation } from '@/store/karute/api';
import {
  FE_DEFINED_META_KEYS,
  NOTICE_COMMON_MESSAGE,
  routerPaths,
  SPLIT_SCREEN_DEFAULT_RATIO,
  STATUS_CODES,
} from '@/types/constants';
import { ButtonType } from '@/types/enum';
import { showFlashNotice } from '@/utils';
import { Divider, Dropdown, Flex } from 'antd';
import { useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { BookingNextTimeModal } from './BookingNextTimeModal';
import { useKaruteInput } from './provider';
import dayjs from 'dayjs';
import { useUpdateUserMetaMutation } from '@/store/user-karute/api';

export function KaruteInputAction() {
  const [isCreateBookingOpen, setIsCreateBookingOpen] = useState(false);

  const { isChanged } = useAppSelector(state => state.karute.karute);
  const { hasContractBooking } = useAppSelector(state => state.sharedData);

  const { getPath } = useRouterPath();

  const navigate = useNavigate();
  const karuteId = useParams().id;

  const {
    isInitalLoading,
    karuteDetailData,
    setMainVerticalSizes,
    setLowerHorizontalSizes,
    setLowerVerticalSizes,
  } = useKaruteInput();
  const [updateUserMetaMutation] = useUpdateUserMetaMutation();
  const [submitKarute] = useSubmitKaruteMutation();

  const submit = async () => {
    const res = await submitKarute({
      karute_id: Number(karuteId),
    });
    showFlashNotice({
      message: res.data?.message || '',
      type: res.data?.status === STATUS_CODES.OK ? 'success' : 'error',
    });
    if (res.data?.status === STATUS_CODES.OK) {
      navigate(getPath(routerPaths.karuteManagement.karuteList));
    }
  };

  const isEnabledSubmitButton = useMemo(
    () =>
      (!dayjs(karuteDetailData?.created_at).isSame(dayjs(karuteDetailData?.updated_at)) &&
        karuteDetailData?.status === 1) ||
      isChanged,
    [karuteDetailData, isChanged]
  );
  const menu = {
    style: {
      marginTop: '3px',
    },
    items: [
      {
        key: 'logout',
        label: (
          <Flex
            onClick={() => {
              const defaultMeta = {
                meta: {
                  [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.MAIN]:
                    SPLIT_SCREEN_DEFAULT_RATIO.MAIN_VERTICAL_SIZES,
                  [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.MAIN]:
                    SPLIT_SCREEN_DEFAULT_RATIO.LOWER_HORIZONTAL_SIZES,
                  [FE_DEFINED_META_KEYS.KARUTE_INPUT_PANES.LOWER_SECTION.RIGHT]:
                    SPLIT_SCREEN_DEFAULT_RATIO.LOWER_VERTICAL_SIZES,
                },
              };
              setMainVerticalSizes(SPLIT_SCREEN_DEFAULT_RATIO.MAIN_VERTICAL_SIZES);
              setLowerHorizontalSizes(SPLIT_SCREEN_DEFAULT_RATIO.LOWER_HORIZONTAL_SIZES);
              setLowerVerticalSizes(SPLIT_SCREEN_DEFAULT_RATIO.LOWER_VERTICAL_SIZES);
              showFlashNotice({
                message: NOTICE_COMMON_MESSAGE.RESET_LAYOUT,
                type: 'success',
              });
              updateUserMetaMutation(defaultMeta);
            }}
            align="center"
            gap="small"
            style={{ height: '30px' }}
          >
            <Icon name="arrowClockWiseDashed" width={16} height={16} />
            <span className="fs14-medium" style={{ color: '#101828' }}>
              初期レイアウトにリセット
            </span>
          </Flex>
        ),
      },
    ],
  };
  return (
    <>
      <Flex gap="small" align="center">
        {hasContractBooking && (
          <>
            <Button
              customType={ButtonType.SECONDARY_BRAND} // TODO -UPDATE COLOR
              customSize="md"
              onClick={() => {
                setIsCreateBookingOpen(true);
              }}
              style={{ minWidth: '137px' }}
            >
              <Icon name="addTab" />
              次回予約
            </Button>

            <Divider type="vertical" style={{ height: '40px', color: 'var(--gray-200)' }} />
          </>
        )}
        <Button
          customType={ButtonType.SECONDARY_COLOR}
          customSize="md"
          onClick={() => {
            navigate(getPath(routerPaths.dashboard));
          }}
          style={{ width: '130px' }}
        >
          ダッシュボード
        </Button>
        <Button
          customType={ButtonType.PRIMARY}
          customSize="md"
          loading={isInitalLoading}
          onClick={submit}
          style={{ width: '109px' }}
          disabled={!isEnabledSubmitButton}
        >
          確定
        </Button>

        <Dropdown menu={menu} trigger={['hover', 'click']} getPopupContainer={() => document.body}>
          <button>
            <Icon name="moreVertical" width={24} height={24} />
          </button>
        </Dropdown>
      </Flex>
      {isCreateBookingOpen && (
        <BookingNextTimeModal
          handleCancel={() => {
            setIsCreateBookingOpen(false);
          }}
        />
      )}
    </>
  );
}
