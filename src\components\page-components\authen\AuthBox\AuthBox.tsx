import { ReactNode } from 'react';
import styles from '../LoginMain/LoginMain.module.scss';

interface AuthBoxProps {
  children: ReactNode;
  footerContent?: ReactNode;
  title: ReactNode;
}

export default function AuthBox({ children, title, footerContent }: AuthBoxProps) {
  return (
    <div className={styles.loginMain}>
      {/* <div className={styles.logo}>
        <span className={styles.logoText}>LOGO</span>
      </div> */}
      <div className={styles.loginForm}>
        <div className={styles.title}>{title}</div>
        {children}
      </div>
      {footerContent && <div className={styles.loginFooter}>{footerContent}</div>}
    </div>
  );
}
