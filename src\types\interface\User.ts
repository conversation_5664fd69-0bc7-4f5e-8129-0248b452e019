import { Order, ROLE } from '../enum';
import { Dayjs } from 'dayjs';

export interface Doctor extends User {}

export interface User {
  user_id: number;
  username: string;
  email: string;
  phone: string | null;
  provider: string | null;
  kana_name: string;
  kanji_name: string;
  status: number;
  role: string;
  manageable_type: string;
  manageable_id: number;
  is_doctor: boolean;
  can_access_karute: boolean;
  can_access_booking: boolean;
  birthday: string | null;
  postal_code: string | null;
  address1: string | null;
  address2: string | null;
  avatar: string | null;
  gender: number;
  practitioner_info?: PractitionerInfo | null;
  clinic: Clinic;
  company: Company;
  courses?: Course[];
}
export interface Company {
  company_id: number;
  company_cd: string;
  founded_date: string | null;
  tax_id: string | null;
  name_kana: string;
  name: string;
  postal_code: string;
  city: string;
  address1: string;
  address2: string;
  phone: string;
  email: string;
  website_url: string | null;
  logo_url: string | null;
  clinics: Clinic[];
  status: string; // Nếu "status" là enum thì có thể sửa thành: '1' | '2' | ...
}

export interface UserInfo {
  user_id: number;
  email: string;
  email_verified_at: string | null;
  username: string;
  phone: string | null;
  status: number;
  kana_name: string;
  kanji_name: string;
  provider: string;
  manageable_type: ROLE | null;
  manageable_id: number;
  is_doctor: number;
  can_access_karute: number;
  can_access_booking: number;
  address1: string;
  address2: string;
  postal_code: string | null;
  birthday: string;
  avatar: string;
  gender: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  manageable: Clinic;
  courses?: Course[];
}

export interface PractitionerInfo {
  practitioner_info_id: number | null;
  experience: string | null;
  degrees: string[] | undefined;
  introduce: string | null;
  city: string | null;
  address: string | null;
  citizen_identification: string | null;
  education_level: string | null;
}

export interface Clinic {
  clinic_id: number;
  clinic_cd: string;
  company_id: number;
  clinic_name: string;
  clinic_name_kana: string;
  start_date: string;
  end_date: string;
  address1: string;
  address2: string;
  address3: string;
  phone_number: string;
  fax: string;
  email: string;
  status: number;
  longitude: number | null;
  latitude: number | null;
  manager_name: string | null;
  manager_kana_name: string | null;
  m2_clinic_id: number | null;
  m2_flg: number;
  a2_clinic_id: number | null;
  a2_flg: number;
  r2_clinic_id: number | null;
  r2_flg: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  clinic_images: any[];
  sub_contract_active: SubContractActive;
}

export interface ParamGetListUser {
  username?: string | null;
  name?: string | null;
  email?: string | null;
  manageable_type?: ROLE[] | null;
  is_doctor?: boolean[] | null;
  can_access_karute?: boolean[] | null;
  can_access_booking?: boolean[] | null;
  order_by?: string | null;
  order_type?: Order;
  page?: number | null;
  limit?: number | null;
}

export interface UserCreatePayload {
  username: string;
  email: string;
  phone: string | null;
  password: string | null;
  password_confirmation: string | null;
  kanji_name: string;
  kana_name: string;
  manageable_type: string | null;
  provider: string;
  gender: number | null;
  avatar?: File | string | null;
  birthday: string | null | Dayjs;
  postal_code: string;
  address1: string | null;
  address2: string | null;
  experience?: string | null;
  introduce?: string | null;
  degrees?: string[] | undefined;
  is_doctor: boolean | 0 | 1 | null;
  can_access_karute: boolean | 0 | 1 | null;
  can_access_booking: boolean | 0 | 1 | null;
  manageable_id: number | null;
  clinic_id: number | null;
  company_id: number | null;
  remove_avatar: number;
}

export interface Company {
  company_id: number;
  company_cd: string;
  founded_date: string | null;
  tax_id: string | null;
  name_kana: string;
  name: string;
  postal_code: string;
  city: string;
  address1: string;
  address2: string;
  phone: string;
  email: string;
  website_url: string | null;
  logo_url: string | null;
  status: string;
}

export interface SubContractActive {
  a2: boolean;
  booking: boolean;
  karute: boolean;
  m2: boolean;
  r2: boolean;
}

export interface Course {
  course_id?: number;
  course_name?: string;
}

export interface WorkSchedule {
  date: string;
  day_of_week: string;
  is_working: boolean;
  working_time: [];
}
