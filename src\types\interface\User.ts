import { ROLE } from '../enum';

export interface Clinic {
  clinic_id: number;
  clinic_cd: string;
  company_id: number;
  clinic_name: string;
  clinic_name_kana: string;
  start_date: string;
  end_date: string;
  address1: string;
  address2: string;
  address3: string;
  phone_number: string;
  fax: string;
  email: string;
  status: number;
  longitude: number | null;
  latitude: number | null;
  manager_name: string | null;
  manager_kana_name: string | null;
  m2_clinic_id: number | null;
  m2_flg: number;
  a2_clinic_id: number | null;
  a2_flg: number;
  r2_clinic_id: number | null;
  r2_flg: number;
  avatar: string | null;
}

export interface User {
  user_id: number;
  username: string;
  email: string;
  phone: string | null;
  provider: string | null;
  kana_name: string;
  kanji_name: string;
  status: number;
  role: string;
  manageable_type: string;
  manageable_id: number;
  is_doctor: boolean;
  can_access_karute: boolean;
  can_access_booking: boolean;
  birthday: string | null;
  postal_code: string | null;
  address1: string | null;
  address2: string | null;
  avatar: string | null;
  gender: number;
  practitioner_info?: PractitionerInfo | null;
  clinic: Clinic;
}
export interface PractitionerInfo {
  practitioner_info_id: number | null;
  experience: string | null;
  degree: string | null;
  introduce: string | null;
  city: string | null;
  address: string | null;
  citizen_identification: string | null;
  education_level: string | null;
}

export interface UserInfo {
  user_id: number;
  email: string;
  email_verified_at: string | null;
  username: string;
  phone: string | null;
  status: number;
  kana_name: string;
  kanji_name: string;
  provider: string;
  manageable_type: ROLE | null;
  manageable_id: number;
  is_doctor: number;
  can_access_karute: number;
  can_access_booking: number;
  address1: string;
  address2: string;
  postal_code: string | null;
  birthday: string;
  avatar: string;
  gender: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  manageable: Clinic;
}
