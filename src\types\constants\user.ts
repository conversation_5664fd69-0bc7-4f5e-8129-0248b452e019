import { EGender } from '@/components/page-components/patient-management/create/FormCreate/type';
import { ECanAccess, EKarute, ROLE } from '../enum';
import { Option } from '../interface';

export const karuteOptions: Option<EKarute>[] = [
  {
    value: EKarute.VALID,
    label: '有効',
  },
  {
    value: EKarute.INVALID,
    label: '無効',
  },
];
export const karuteMap = {
  [EKarute.VALID]: '有効',
  [EKarute.INVALID]: '無効',
} as const;

export const roleMap = {
  [ROLE.STORE_ADMIN]: '管理者',
  [ROLE.COMPANY_ADMIN]: '管理者',
  [ROLE.MEDIX_ADMIN]: '管理者',
  [ROLE.STAFF]: 'スタッフ',
} as const;
export const genderMap = {
  [EGender.MALE]: '男性',
  [EGender.FEMALE]: '女性',
} as const;
export const canAccessMap = {
  [ECanAccess.YES]: '有効',
  [ECanAccess.NO]: '無効',
};
