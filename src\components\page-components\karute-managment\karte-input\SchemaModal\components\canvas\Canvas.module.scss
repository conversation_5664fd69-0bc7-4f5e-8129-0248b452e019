$size: 20px;

.viewer {
  padding: 16px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  max-height: 100%;
  min-width: 580px;

  > :first-child {
    flex: 1;
  }
  .mainView {
    position: relative;
    width: 100%;
    max-height: 100%;

    .actionBtns {
      .navBtn {
        button {
          min-width: unset;
          width: 28px;
          height: 28px;
        }
        position: absolute;
        z-index: 10;
        top: 50%;
        transform: translateY(-50%);
        &.ant-btn {
          border: none;
          background: #fff;
          box-shadow: 0 1px 3px rgba(0 0 0 / 10%);
        }
        &.prev {
          left: 0;
        }
        &.next {
          right: 0;
        }
      }

      .removeBtn {
        button {
          min-width: unset;
          width: 28px;
          height: 28px;
        }
        position: absolute;
        z-index: 10;
        top: 0;
        right: 0;
      }
    }
    .canvasArea {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .dots {
      display: flex;
      justify-content: center;
      gap: 6px;
      margin: 8px 0;
      .dot,
      .dotActive {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: $gray-300;
        cursor: pointer;
      }
      .dotActive {
        background: $brand-700;
      }
    }
  }

  .toolbar {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .drawMode {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      column-gap: 20px;
      row-gap: 16px;

      .item {
        display: flex;
        align-items: center;
        justify-content: center;
        .toolBtn {
          cursor: pointer;
          transition: border-color 0.15s;

          &:has(.circleIcon) {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .circleIcon {
            width: $size;
            height: $size;
            border: 1px solid transparent;
            border-radius: 6px;
            background: #fff;
            cursor: unset;
            &.active {
              border-color: $brand-800;
              background-color: $brand-50;
            }
            &.fill {
              border-radius: 50%;
              background: $brand-500;
            }
            &.fill.active {
              background: $brand-800;
            }

            &.outline {
              border-radius: 50%;
              border: 2px solid $brand-500;
            }
            &.outline.active {
              border-color: $brand-800;
            }
          }

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
        .slider {
          flex: 1;

          &.active {
            :global {
              .ant-slider-track {
                background: var(--brand-900);
              }
              .ant-slider-handle {
                border-color: var(--brand-900);
              }
            }
          }

          :global {
            .ant-slider-track {
              background: var(--brand-500);
            }
            .ant-slider-handle {
              border-color: var(--brand-500);
            }
          }
        }
      }
    }
    .colorWrap {
      display: inline-flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
      align-self: flex-start;
      background-color: $gray-100;
      padding: 8px 16px;
      border-radius: 8px;
    }
    .colorDot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform;
      transform-origin: center center;

      &.active {
        transform: scale(1.25);
      }
    }
  }
}

.empty {
  height: 100%;
}

.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gray500 {
  color: $gray-500;
}

.shortcut {
  margin-top: 12px;
}
