import React from 'react';
import { Modal, ModalProps } from 'antd';
import styles from './ModalConfirm.module.scss';
import clsx from 'clsx';

export type ModalConfirmProps = Omit<ModalProps, 'title' | 'footer' | 'onCancel'> & {
  open: boolean;
  title: React.ReactNode;
  content: React.ReactNode;
  onClose: () => void;
  footerButtons?: React.ReactNode[];
};

const ModalConfirm: React.FC<ModalConfirmProps> = ({
  open,
  title,
  content,
  onClose,
  footerButtons = [],
  ...rest
}) => {
  return (
    <Modal
      {...rest}
      style={{
        zIndex: 1100,
        height: '100% !important',
      }}
      open={open}
      title={<div className={clsx(styles.title, 'fs18-bold ')}>{title}</div>}
      onCancel={onClose}
      footer={
        <div className={styles.footer}>
          {footerButtons.map((btn, index) => (
            <div key={index}>{btn}</div>
          ))}
        </div>
      }
      closable
      centered
      maskClosable={false}
      className={clsx(styles.modalConfirm, rest.className)}
    >
      <div className={clsx(styles.content, 'fs14-regular')}>{content}</div>
    </Modal>
  );
};

export default ModalConfirm;
