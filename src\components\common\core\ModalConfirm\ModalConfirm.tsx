import { Modal, ModalProps } from 'antd';
import clsx from 'clsx';
import React from 'react';
import styles from './ModalConfirm.module.scss';

export type ModalConfirmProps = Omit<ModalProps, 'title' | 'footer' | 'onCancel'> & {
  open: boolean;
  title: React.ReactNode;
  content: React.ReactNode;
  onClose: () => void;
  onCancel?: () => void;
  footerButtons?: React.ReactNode[];
  height?: string | number;
  warning?: boolean;
};

const ModalConfirm: React.FC<ModalConfirmProps> = ({
  open,
  title,
  content,
  onClose,
  footerButtons = [],
  warning = false,
  ...rest
}) => {
  return (
    <Modal
      {...rest}
      style={{
        height: '100% !important',
      }}
      height={rest.height || 'auto'}
      open={open}
      title={title ? <div className={clsx(styles.title, 'fs18-bold, mb-6')}>{title}</div> : null}
      onCancel={() => {
        onClose();
        rest.onCancel?.();
      }}
      footer={
        <div className={styles.footer}>
          {footerButtons.map((btn, index) => (
            <div key={index}>{btn}</div>
          ))}
        </div>
      }
      closable
      centered
      maskClosable={false}
      className={clsx(styles.modalConfirm, rest.className)}
      zIndex={9999}
    >
      <div className={clsx(styles.content, 'fs14-regular', warning ? styles.warning : '')}>
        {content}
      </div>
    </Modal>
  );
};

export default ModalConfirm;
