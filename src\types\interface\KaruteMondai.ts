import { MONDAI_TYPE } from '@/types/constants';
export type KaruteMondai = {
  medical_record_id: number;
  patient_id: number;
  patient_name: string;
  patient_name_kana: string;
  trauma_type: keyof typeof MONDAI_TYPE;
  trauma_counter: number;
  subject: string;
  remarks: string;
  create_by: number;
  version: number;
  status: number;
  mondai_related: MondaiRelated[] | number;
  date: string;
  mondai_id: number;
  updated_at?: string;
  karute_id?: number | null;
  disease_base_id?: number | null;
  mondai_relations?: number[] | MondaiRelated[];
  has_history?: boolean;
};

interface MondaiRelated {
  mondai_id: number;
  disease_base_id?: null;
  trauma_type: number;
  trauma_counter: number;
}
