import { Tooltip } from 'antd';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import styles from './Table.module.scss';

const MAX_LINES = 2;

export const RenderWithTooltip = ({
  text,
  maxLines,
  style,
}: {
  text: string;
  maxLines?: number;
  style?: React.CSSProperties;
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isOverflow, setIsOverflow] = useState(false);
  useEffect(() => {
    const el = contentRef.current;
    if (el) {
      const computedStyle = window.getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 16;
      const lines = Math.round(el.scrollHeight / lineHeight);
      setIsOverflow(lines > (maxLines ?? MAX_LINES));
    }
  }, [text, maxLines, contentRef.current?.clientWidth]);

  const content = (
    <div
      ref={contentRef}
      className={clsx(styles.tooltip_content, { [styles.overflowed]: true })}
      style={{
        WebkitLineClamp: maxLines ?? MAX_LINES,
        ...style,
      }}
    >
      {text}
    </div>
  );

  return isOverflow ? (
    <Tooltip
      title={<div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{text}</div>}
      arrow={{ pointAtCenter: true }}
      placement="top"
      trigger={['hover', 'click']}
      color="#101828"
      getPopupContainer={() => document.body}
      styles={{
        root: {
          maxWidth: '800px',
          width: 'fit-content',
        },
        body: {
          maxWidth: '800px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
        },
      }}
    >
      {content}
    </Tooltip>
  ) : (
    content
  );
};
