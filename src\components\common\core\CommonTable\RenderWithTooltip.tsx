import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import styles from './Table.module.scss';
import { Tooltip } from 'antd';

const MAX_LINES = 2;

export const RenderWithTooltip = ({ text }: { text: string }) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isOverflow, setIsOverflow] = useState(false);

  useEffect(() => {
    const el = contentRef.current;
    if (el) {
      const computedStyle = window.getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 16;
      const lines = Math.round(el.scrollHeight / lineHeight);
      setIsOverflow(lines > MAX_LINES);
    }
  }, [text]);

  const content = (
    <div
      ref={contentRef}
      className={clsx(styles.cellContentWrapper, { [styles.overflowed]: isOverflow })}
    >
      {text}
    </div>
  );

  return isOverflow ? (
    <Tooltip
      title={<div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{text}</div>}
      arrow={{ pointAtCenter: true }}
      placement="top"
      color="#101828"
      getPopupContainer={() => document.body}
      styles={{
        root: {
          maxWidth: '800px',
          width: 'fit-content',
        },
        body: {
          maxWidth: '800px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
        },
      }}
    >
      {content}
    </Tooltip>
  ) : (
    content
  );
};
