import { DatePicker } from 'antd';
import clsx from 'clsx';
import { forwardRef, useState } from 'react';
import { Dayjs } from 'dayjs';
import Icon from '../Icon';

import styles from './AppDatePicker.module.scss';
const gray_500 = '#667085';

type RangePickerProps = React.ComponentProps<typeof DatePicker.RangePicker> & {
  singlePlaceholder?: string;
};

export const AppRangeDatePicker = forwardRef<any, RangePickerProps>(
  ({ className, value, defaultValue, onChange, singlePlaceholder, placeholder, ...props }, ref) => {
    const formattedValue: [Dayjs | null | undefined, Dayjs | null | undefined] | undefined =
      value && Array.isArray(value) ? value : [null, null];

    const formattedDefaultValue: [Dayjs | null | undefined, Dayjs | null | undefined] | undefined =
      defaultValue && Array.isArray(defaultValue) ? defaultValue : [null, null];

    const classNames = clsx(className, styles.appPicker, {
      [styles.errorInput]: props.status === 'error',
      [styles.notDisabled]: !props.disabled,
    });

    const actualPlaceholder = placeholder || ['', ''];

    const hasValue = value && Array.isArray(value) && (value[0] || value[1]);

    const [isOpen, setIsOpen] = useState(false);

    const handleOpenChange = (open: boolean) => {
      setIsOpen(open);
      if (props.onOpenChange) {
        props.onOpenChange(open);
      }
    };

    return (
      <div style={{ position: 'relative' }}>
        <DatePicker.RangePicker
          ref={ref}
          className={classNames}
          value={formattedValue}
          defaultValue={formattedDefaultValue}
          onChange={onChange}
          suffixIcon={<Icon name="calendar" width={20} height={20} color={gray_500} />}
          placeholder={actualPlaceholder}
          onOpenChange={handleOpenChange}
          {...props}
        />
        {/* Overlay current separate placeholder */}
        {singlePlaceholder && !hasValue && !isOpen && (
          <p
            className={styles.rangePlaceHolder}
            style={!props.disabled ? { backgroundColor: 'white' } : undefined}
          >
            {singlePlaceholder}
          </p>
        )}
      </div>
    );
  }
);

AppRangeDatePicker.displayName = 'AppRangeDatePicker';


