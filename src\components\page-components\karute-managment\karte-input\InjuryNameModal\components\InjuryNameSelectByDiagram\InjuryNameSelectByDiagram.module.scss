.wrapper {
  flex: 1;
  padding: 16px;
  width: var(--diagram-max-height);
  min-width: calc(var(--diagram-max-height) - 60px);
  max-width: 100%;
  display: flex;
  flex-direction: column;

  .tabs {
    border: 1px solid $gray-200;
    border-radius: 8px;

    .tab_option {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .selection {
    display: flex;
    background-color: $gray-50;
    padding: 12px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    align-items: center;
    gap: 12px;
  }

  .diagram {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
