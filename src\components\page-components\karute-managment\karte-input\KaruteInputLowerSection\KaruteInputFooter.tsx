import { useGetKarutePatientMedicalHistoryQuery } from '@/store/karute/api';
import styles from './KaruteInputLowerSection.module.scss';
import { useKaruteInput } from '../provider';
import { useParams } from 'react-router-dom';
import { Spin, Tooltip } from 'antd';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

const MAX_LINES = 1;

export function KaruteInputFooter() {
  const { karuteDetailData, isInitalLoading } = useKaruteInput();
  const { serviceId } = useParams();
  const { data: patientHistoryData, isFetching: isFetchingPatientHistoryData } =
    useGetKarutePatientMedicalHistoryQuery(
      {
        patient_id: Number(karuteDetailData?.patient_id),
        service_id: serviceId!,
      },
      {
        skip: !karuteDetailData?.patient_id || !serviceId,
      }
    );

  const personalRef = useRef<HTMLParagraphElement>(null);
  const familyRef = useRef<HTMLParagraphElement>(null);
  const socialRef = useRef<HTMLParagraphElement>(null);

  const [overflowStates, setOverflowStates] = useState({
    personal: false,
    family: false,
    social: false,
  });

  useEffect(() => {
    const checkOverflow = (ref: React.RefObject<HTMLParagraphElement>) => {
      if (!ref.current) return false;
      const el = ref.current;
      const computedStyle = window.getComputedStyle(el);
      const lineHeight = parseFloat(computedStyle.lineHeight) || 16;
      const lines = Math.round(el.scrollHeight / lineHeight);
      return lines > MAX_LINES;
    };

    setOverflowStates({
      personal: checkOverflow(personalRef),
      family: checkOverflow(familyRef),
      social: checkOverflow(socialRef),
    });
  }, [patientHistoryData]);

  const renderItem = (
    label: string,
    value: string | undefined,
    ref: React.RefObject<HTMLParagraphElement>,
    isOverflow: boolean
  ) => {
    const content = (
      <p className={styles.footer_clamp} ref={ref}>
        <span className={clsx(styles.footer_label, 'fs14-regular')}>{label}</span>
        <span className={clsx(styles.footer_value, 'fs14-medium')}>{value}</span>
      </p>
    );

    return isOverflow ? (
      <Tooltip
        title={<div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{value}</div>}
        arrow={{ pointAtCenter: true }}
        placement="top"
        trigger={['hover', 'click']}
        color="#101828"
        getPopupContainer={() => document.body}
        styles={{
          root: { maxWidth: '800px', width: 'fit-content' },
          body: { maxWidth: '800px', whiteSpace: 'pre-wrap', wordBreak: 'break-word' },
        }}
      >
        <div style={{ cursor: 'pointer' }}>{content}</div>
      </Tooltip>
    ) : (
      content
    );
  };

  return (
    <Spin spinning={isFetchingPatientHistoryData || isInitalLoading}>
      <div className={styles.mondai_footer}>
        <div className={styles.footer_item}>
          {renderItem(
            '既往歴:',
            patientHistoryData?.data?.personal,
            personalRef,
            overflowStates.personal
          )}
        </div>
        <div className={styles.footer_item}>
          {renderItem(
            '家族歴:',
            patientHistoryData?.data?.family,
            familyRef,
            overflowStates.family
          )}
        </div>
        <div className={styles.footer_item}>
          {renderItem(
            '社会歴:',
            patientHistoryData?.data?.social,
            socialRef,
            overflowStates.social
          )}
        </div>
      </div>
    </Spin>
  );
}
